import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailRecord {
  id: string
  to_email: string
  subject: string
  content: string
  template_name?: string
  template_data?: any
  status: 'pending' | 'sent' | 'failed' | 'cancelled'
  attempts: number
  max_attempts: number
  scheduled_for: string
  error_message?: string
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get batch size from request or default to 10
    const { batch_size = 10 } = await req.json().catch(() => ({}))

    // Fetch pending emails
    const { data: emails, error: fetchError } = await supabase
      .from('email_queue')
      .select('*')
      .eq('status', 'pending')
      .lte('scheduled_for', new Date().toISOString())
      .lt('attempts', supabase.rpc('max_attempts'))
      .order('scheduled_for', { ascending: true })
      .limit(batch_size)

    if (fetchError) {
      console.error('Error fetching emails:', fetchError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch emails' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (!emails || emails.length === 0) {
      return new Response(
        JSON.stringify({ message: 'No emails to process', processed: 0 }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Processing ${emails.length} emails...`)

    let processed = 0
    let failed = 0

    // Process each email
    for (const email of emails) {
      try {
        // Update attempts count
        await supabase
          .from('email_queue')
          .update({ 
            attempts: email.attempts + 1,
            updated_at: new Date().toISOString()
          })
          .eq('id', email.id)

        // Send email using your preferred service
        const emailSent = await sendEmail(email)

        if (emailSent) {
          // Mark as sent
          await supabase
            .from('email_queue')
            .update({
              status: 'sent',
              sent_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('id', email.id)

          processed++
          console.log(`✅ Email sent to ${email.to_email}`)
        } else {
          throw new Error('Email sending failed')
        }

      } catch (error) {
        console.error(`❌ Failed to send email to ${email.to_email}:`, error)

        // Check if we've exceeded max attempts
        if (email.attempts + 1 >= email.max_attempts) {
          await supabase
            .from('email_queue')
            .update({
              status: 'failed',
              error_message: error.message,
              updated_at: new Date().toISOString()
            })
            .eq('id', email.id)
        } else {
          // Schedule retry (exponential backoff)
          const retryDelay = Math.pow(2, email.attempts) * 60 * 1000 // 1min, 2min, 4min, etc.
          const retryAt = new Date(Date.now() + retryDelay)

          await supabase
            .from('email_queue')
            .update({
              scheduled_for: retryAt.toISOString(),
              error_message: error.message,
              updated_at: new Date().toISOString()
            })
            .eq('id', email.id)
        }

        failed++
      }
    }

    return new Response(
      JSON.stringify({
        message: 'Email processing completed',
        processed,
        failed,
        total: emails.length
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Unexpected error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function sendEmail(email: EmailRecord): Promise<boolean> {
  try {
    // Get SMTP configuration from environment
    const smtpConfig = {
      host: Deno.env.get('SMTP_HOST'),
      port: parseInt(Deno.env.get('SMTP_PORT') || '587'),
      secure: Deno.env.get('SMTP_SECURE') === 'true',
      auth: {
        user: Deno.env.get('SMTP_USER'),
        pass: Deno.env.get('SMTP_PASSWORD')
      }
    }

    // For now, we'll use a simple fetch to a mail service
    // In production, you might want to use a service like SendGrid, Mailgun, etc.
    
    // Example using a hypothetical email service API
    const response = await fetch('https://api.your-email-service.com/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${Deno.env.get('EMAIL_SERVICE_API_KEY')}`
      },
      body: JSON.stringify({
        to: email.to_email,
        subject: email.subject,
        html: email.content,
        from: Deno.env.get('SMTP_FROM') || '<EMAIL>'
      })
    })

    return response.ok

  } catch (error) {
    console.error('Error sending email:', error)
    return false
  }
}
