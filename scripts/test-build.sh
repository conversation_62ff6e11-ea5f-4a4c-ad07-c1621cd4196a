#!/bin/bash

# Test Build Script for DNSB Vakarai
# This script tests the Docker build process with different environment variable scenarios

set -e

echo "🔧 Testing DNSB Vakarai Docker Build Process"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Test 1: Build without environment variables (should succeed with placeholders)
echo ""
echo "Test 1: Building without environment variables"
echo "----------------------------------------------"

if docker build -t dnsb-vakarai-test-1 .; then
    print_status "Build succeeded without environment variables"
    docker rmi dnsb-vakarai-test-1 2>/dev/null || true
else
    print_error "Build failed without environment variables"
    exit 1
fi

# Test 2: Build with environment variables from .env.local (if exists)
echo ""
echo "Test 2: Building with environment variables (if .env.local exists)"
echo "----------------------------------------------------------------"

if [ -f ".env.local" ]; then
    # Source environment variables
    export $(grep -v '^#' .env.local | xargs)
    
    if docker build \
        --build-arg NEXT_PUBLIC_SUPABASE_URL="$NEXT_PUBLIC_SUPABASE_URL" \
        --build-arg NEXT_PUBLIC_SUPABASE_ANON_KEY="$NEXT_PUBLIC_SUPABASE_ANON_KEY" \
        --build-arg SUPABASE_SERVICE_ROLE_KEY="$SUPABASE_SERVICE_ROLE_KEY" \
        -t dnsb-vakarai-test-2 .; then
        print_status "Build succeeded with environment variables"
        docker rmi dnsb-vakarai-test-2 2>/dev/null || true
    else
        print_error "Build failed with environment variables"
        exit 1
    fi
else
    print_warning ".env.local not found, skipping environment variable test"
fi

# Test 3: Test docker-compose build
echo ""
echo "Test 3: Testing docker-compose build"
echo "-----------------------------------"

if [ -f ".env.local" ]; then
    if docker-compose build; then
        print_status "Docker Compose build succeeded"
    else
        print_error "Docker Compose build failed"
        exit 1
    fi
else
    print_warning "Skipping docker-compose test (no .env.local file)"
fi

# Test 4: Verify Next.js build works locally
echo ""
echo "Test 4: Testing local Next.js build"
echo "----------------------------------"

# Set placeholder environment variables for local build test
export NEXT_PUBLIC_SUPABASE_URL="https://placeholder.supabase.co"
export NEXT_PUBLIC_SUPABASE_ANON_KEY="placeholder-anon-key"
export SUPABASE_SERVICE_ROLE_KEY="placeholder-service-role-key"

if npm run build; then
    print_status "Local Next.js build succeeded with placeholders"
    # Clean up build artifacts
    rm -rf .next 2>/dev/null || true
else
    print_error "Local Next.js build failed"
    exit 1
fi

echo ""
echo "🎉 All build tests passed!"
echo "========================="
echo ""
echo "Next steps for Coolify deployment:"
echo "1. Set environment variables in Coolify:"
echo "   - NEXT_PUBLIC_SUPABASE_URL"
echo "   - NEXT_PUBLIC_SUPABASE_ANON_KEY"
echo "   - SUPABASE_SERVICE_ROLE_KEY"
echo ""
echo "2. Deploy using the docker-compose.yml configuration"
echo ""
echo "3. Verify deployment with /api/health endpoint"
