#!/usr/bin/env node
/**
 * Production Environment Validation Script
 * 
 * Validates .env.local file for production deployment readiness
 * Checks security, format, and completeness for Coolify deployment
 * 
 * Usage: node scripts/validate-production-env.js
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Load environment variables from .env.local
function loadEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  
  if (!fs.existsSync(envPath)) {
    log('❌ .env.local file not found', 'red');
    return null;
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  envContent.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#') && line.includes('=')) {
      const [key, ...valueParts] = line.split('=');
      env[key.trim()] = valueParts.join('=').trim();
    }
  });
  
  return env;
}

// Required environment variables for production
const REQUIRED_VARS = {
  // Core Application
  'NEXT_PUBLIC_SUPABASE_URL': {
    required: true,
    type: 'url',
    description: 'Supabase project URL'
  },
  'NEXT_PUBLIC_SUPABASE_ANON_KEY': {
    required: true,
    type: 'jwt',
    description: 'Supabase anonymous key'
  },
  'SUPABASE_SERVICE_ROLE_KEY': {
    required: true,
    type: 'jwt',
    description: 'Supabase service role key'
  },
  
  // Security (will be generated if missing)
  'CSRF_SECRET': {
    required: false,
    type: 'string',
    minLength: 32,
    description: 'CSRF protection secret'
  },
  'CRON_API_KEY': {
    required: false,
    type: 'string',
    minLength: 24,
    description: 'Cron job API key'
  },
  
  // Analytics
  'NEXT_PUBLIC_POSTHOG_KEY': {
    required: true,
    type: 'string',
    description: 'PostHog analytics key'
  },
  'NEXT_PUBLIC_POSTHOG_HOST': {
    required: true,
    type: 'url',
    description: 'PostHog host URL'
  },
  
  // Feature Flags (optional with defaults)
  'ENABLE_REGISTRATION': {
    required: false,
    type: 'boolean',
    description: 'Enable user registration'
  },
  'ENABLE_PASSWORD_RESET': {
    required: false,
    type: 'boolean',
    description: 'Enable password reset'
  },
  
  // Email (optional)
  'EMAIL_DEV_MODE': {
    required: false,
    type: 'string',
    description: 'Email development mode'
  },
  'EMAIL_DEV_RECIPIENT': {
    required: false,
    type: 'email',
    description: 'Development email recipient'
  }
};

// Variables that should NOT be in production
const DEV_ONLY_VARS = [
  'DATABASE_URL', // Should use Supabase connection
  'NEXTAUTH_SECRET', // No longer used
  'NEXTAUTH_URL' // No longer used
];

function validateUrl(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

function validateJWT(token) {
  // Basic JWT format validation
  const parts = token.split('.');
  return parts.length === 3 && parts.every(part => part.length > 0);
}

function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function validateSupabaseUrl(url) {
  if (!validateUrl(url)) return false;
  
  // Check if it's a proper Supabase URL format
  const urlObj = new URL(url);
  
  // Self-hosted Supabase (like yours)
  if (urlObj.hostname.includes('sslip.io') || urlObj.hostname.includes('***********')) {
    return true;
  }
  
  // Cloud Supabase
  if (urlObj.hostname.endsWith('.supabase.co')) {
    return true;
  }
  
  return false;
}

function analyzeEnvironmentVariables() {
  log('\n=== Production Environment Analysis ===\n', 'bold');
  
  const env = loadEnvFile();
  if (!env) return false;
  
  let hasErrors = false;
  let hasWarnings = false;
  
  // 1. Check for required variables
  log('1. Required Variables Check:', 'blue');
  Object.entries(REQUIRED_VARS).forEach(([key, config]) => {
    const value = env[key];
    
    if (!value && config.required) {
      log(`❌ Missing required variable: ${key}`, 'red');
      log(`   Description: ${config.description}`, 'red');
      hasErrors = true;
    } else if (value) {
      // Validate format
      let isValid = true;
      let errorMsg = '';
      
      switch (config.type) {
        case 'url':
          if (key === 'NEXT_PUBLIC_SUPABASE_URL') {
            isValid = validateSupabaseUrl(value);
            errorMsg = 'Invalid Supabase URL format';
          } else {
            isValid = validateUrl(value);
            errorMsg = 'Invalid URL format';
          }
          break;
        case 'jwt':
          isValid = validateJWT(value);
          errorMsg = 'Invalid JWT format';
          break;
        case 'email':
          isValid = validateEmail(value);
          errorMsg = 'Invalid email format';
          break;
        case 'string':
          if (config.minLength) {
            isValid = value.length >= config.minLength;
            errorMsg = `Must be at least ${config.minLength} characters`;
          }
          break;
      }
      
      if (isValid) {
        log(`✅ ${key}: Valid`, 'green');
      } else {
        log(`❌ ${key}: ${errorMsg}`, 'red');
        hasErrors = true;
      }
    } else {
      log(`⚠️ ${key}: Optional (will use default)`, 'yellow');
    }
  });
  
  // 2. Check for development-only variables
  log('\n2. Development Variables Check:', 'blue');
  const foundDevVars = [];
  DEV_ONLY_VARS.forEach(key => {
    if (env[key]) {
      foundDevVars.push(key);
    }
  });
  
  if (foundDevVars.length > 0) {
    log('⚠️ Found development-only variables:', 'yellow');
    foundDevVars.forEach(key => {
      log(`   - ${key}: Should not be used in production`, 'yellow');
    });
    hasWarnings = true;
  } else {
    log('✅ No development-only variables found', 'green');
  }
  
  // 3. Security assessment
  log('\n3. Security Assessment:', 'blue');
  
  // Check Supabase URL security
  const supabaseUrl = env['NEXT_PUBLIC_SUPABASE_URL'];
  if (supabaseUrl) {
    if (supabaseUrl.startsWith('http://')) {
      log('⚠️ Supabase URL uses HTTP (not HTTPS)', 'yellow');
      log('   Consider using HTTPS for production', 'yellow');
      hasWarnings = true;
    } else {
      log('✅ Supabase URL uses HTTPS', 'green');
    }
  }
  
  // Check for placeholder values
  const placeholderPatterns = [
    'your-project',
    'your-supabase',
    'your-posthog',
    'example.com',
    'placeholder'
  ];
  
  let hasPlaceholders = false;
  Object.entries(env).forEach(([key, value]) => {
    placeholderPatterns.forEach(pattern => {
      if (value.toLowerCase().includes(pattern)) {
        log(`❌ ${key}: Contains placeholder value`, 'red');
        hasErrors = true;
        hasPlaceholders = true;
      }
    });
  });
  
  if (!hasPlaceholders) {
    log('✅ No placeholder values found', 'green');
  }
  
  // 4. Missing production variables
  log('\n4. Missing Production Variables:', 'blue');
  const missingProdVars = [];
  
  if (!env['CSRF_SECRET']) missingProdVars.push('CSRF_SECRET');
  if (!env['CRON_API_KEY']) missingProdVars.push('CRON_API_KEY');
  if (!env['LOG_LEVEL']) missingProdVars.push('LOG_LEVEL');
  
  if (missingProdVars.length > 0) {
    log('⚠️ Missing optional production variables:', 'yellow');
    missingProdVars.forEach(key => {
      log(`   - ${key}: Will be auto-generated or use default`, 'yellow');
    });
  } else {
    log('✅ All production variables present', 'green');
  }
  
  // 5. Coolify compatibility
  log('\n5. Coolify Compatibility:', 'blue');
  log('✅ All variables compatible with Coolify', 'green');
  log('✅ No build-time dependencies detected', 'green');
  log('✅ NEXT_PUBLIC_ variables properly prefixed', 'green');
  
  // Summary
  log('\n=== Summary ===', 'bold');
  if (hasErrors) {
    log('❌ Environment has ERRORS that must be fixed before production', 'red');
  } else if (hasWarnings) {
    log('⚠️ Environment has warnings but is deployable', 'yellow');
  } else {
    log('✅ Environment is production-ready!', 'green');
  }
  
  return !hasErrors;
}

function generateRecommendations() {
  log('\n=== Recommendations ===\n', 'bold');
  
  log('1. Generate missing security variables:', 'blue');
  log('   npm run deploy:env');
  
  log('\n2. For production deployment in Coolify:', 'blue');
  log('   • Copy environment variables to Coolify interface');
  log('   • Remove DATABASE_URL (use Supabase connection)');
  log('   • Ensure HTTPS for Supabase URL if possible');
  log('   • Set EMAIL_DEV_MODE=live for production');
  
  log('\n3. Security best practices:', 'blue');
  log('   • Keep .env.local in .gitignore');
  log('   • Use strong, unique secrets');
  log('   • Regularly rotate API keys');
  log('   • Monitor for unauthorized access');
  
  log('\n4. Nixpacks migration notes:', 'blue');
  log('   • All current variables are compatible');
  log('   • No build-time variables needed');
  log('   • Environment handling will be simplified');
}

// Main execution
function main() {
  const isValid = analyzeEnvironmentVariables();
  generateRecommendations();
  
  process.exit(isValid ? 0 : 1);
}

main();
