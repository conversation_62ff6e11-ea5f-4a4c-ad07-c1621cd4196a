#!/usr/bin/env node
/**
 * Development Environment Verification Script
 * 
 * This script checks if the development environment is properly configured
 * for the DNSB Vakarai application.
 * 
 * Usage: node scripts/verify-development.js
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');

// Configuration check
const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'CSRF_SECRET'
];

const optionalEnvVars = [
  'EMAIL_DEV_MODE',
  'EMAIL_DEV_RECIPIENT',
  'NEXT_PUBLIC_POSTHOG_KEY',
  'LOG_LEVEL',
  'ENABLE_REGISTRATION',
  'ENABLE_PASSWORD_RESET',
  'CRON_API_KEY'
];

const databaseStoredSettings = [
  'SMTP_HOST',
  'SMTP_PORT',
  'SMTP_USER',
  'SMTP_PASSWORD',
  'SMTP_FROM',
  'SMTP_REPLY_TO',
  'SMTP_SECURE'
];

/**
 * Check environment variables
 */
function checkEnvironmentVariables() {
  console.log('🔍 Checking environment variables...\n');
  
  const missing = [];
  const present = [];
  const optional = [];

  // Check required variables
  requiredEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      present.push(envVar);
    } else {
      missing.push(envVar);
    }
  });

  // Check optional variables
  optionalEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      optional.push(envVar);
    }
  });

  // Report results
  if (present.length > 0) {
    console.log('✅ Required environment variables present:');
    present.forEach(envVar => {
      const value = process.env[envVar];
      const displayValue = envVar.includes('KEY') || envVar.includes('SECRET') || envVar.includes('PASSWORD')
        ? '***' + value.slice(-4)
        : value;
      console.log(`   ${envVar}=${displayValue}`);
    });
    console.log('');
  }

  if (optional.length > 0) {
    console.log('✅ Optional environment variables present:');
    optional.forEach(envVar => {
      console.log(`   ${envVar}=${process.env[envVar]}`);
    });
    console.log('');
  }

  // Show database-stored settings info
  console.log('ℹ️  Settings stored in database (not environment variables):');
  databaseStoredSettings.forEach(setting => {
    console.log(`   ${setting} - configured in admin settings`);
  });
  console.log('');

  if (missing.length > 0) {
    console.log('❌ Missing required environment variables:');
    missing.forEach(envVar => {
      console.log(`   ${envVar}`);
    });
    console.log('');
    console.log('💡 To fix this:');
    console.log('   1. Copy .env.example to .env');
    console.log('   2. Fill in the missing values');
    console.log('   3. Run this script again');
    console.log('');
  }

  return missing.length === 0;
}

/**
 * Check if required files exist
 */
function checkRequiredFiles() {
  console.log('🔍 Checking required files...\n');
  
  const requiredFiles = [
    'package.json',
    'next.config.js',
    'docker-compose.yml',
    'Dockerfile',
    '.env.example',
    'app/layout.tsx',
    'lib/supabase/client.ts',
    'lib/supabase/server.ts'
  ];

  const missing = [];
  const present = [];

  requiredFiles.forEach(file => {
    if (fs.existsSync(path.join(process.cwd(), file))) {
      present.push(file);
    } else {
      missing.push(file);
    }
  });

  if (present.length > 0) {
    console.log('✅ Required files present:');
    present.forEach(file => console.log(`   ${file}`));
    console.log('');
  }

  if (missing.length > 0) {
    console.log('❌ Missing required files:');
    missing.forEach(file => console.log(`   ${file}`));
    console.log('');
  }

  return missing.length === 0;
}

/**
 * Check package.json scripts
 */
function checkPackageScripts() {
  console.log('🔍 Checking package.json scripts...\n');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const scripts = packageJson.scripts || {};
    
    const requiredScripts = [
      'dev',
      'build',
      'start',
      'lint',
      'test',
      'deploy:env',
      'deploy:verify',
      'supabase:test'
    ];

    const missing = [];
    const present = [];

    requiredScripts.forEach(script => {
      if (scripts[script]) {
        present.push(script);
      } else {
        missing.push(script);
      }
    });

    if (present.length > 0) {
      console.log('✅ Required scripts present:');
      present.forEach(script => console.log(`   npm run ${script}`));
      console.log('');
    }

    if (missing.length > 0) {
      console.log('❌ Missing required scripts:');
      missing.forEach(script => console.log(`   ${script}`));
      console.log('');
    }

    return missing.length === 0;
  } catch (error) {
    console.error('❌ Error reading package.json:', error.message);
    return false;
  }
}

/**
 * Provide setup guidance
 */
function provideSetupGuidance() {
  console.log('📋 Development Setup Guide:\n');
  
  console.log('1. 🔧 Environment Setup:');
  console.log('   cp .env.example .env');
  console.log('   # Edit .env with your actual values\n');
  
  console.log('2. 📦 Install Dependencies:');
  console.log('   npm install\n');
  
  console.log('3. 🗄️ Supabase Setup:');
  console.log('   - Create a Supabase project (or use existing shared database)');
  console.log('   - Get your project URL and anon key');
  console.log('   - Update NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY\n');

  console.log('4. 📧 SMTP Setup:');
  console.log('   - SMTP settings are stored in the database');
  console.log('   - Configure via Admin → Settings → SMTP Settings');
  console.log('   - No environment variables needed for SMTP\n');

  console.log('5. 🚀 Start Development:');
  console.log('   npm run dev\n');

  console.log('6. ✅ Verify Setup:');
  console.log('   npm run deploy:verify (requires running app)\n');
}

/**
 * Main function
 */
function main() {
  console.log('🔍 DNSB Vakarai Development Environment Verification\n');
  console.log('=' .repeat(60) + '\n');
  
  const envCheck = checkEnvironmentVariables();
  const filesCheck = checkRequiredFiles();
  const scriptsCheck = checkPackageScripts();
  
  console.log('📊 Verification Summary:');
  console.log('--------------------------------');
  console.log(`Environment Variables: ${envCheck ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Required Files:        ${filesCheck ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Package Scripts:       ${scriptsCheck ? '✅ PASS' : '❌ FAIL'}`);
  console.log('--------------------------------\n');
  
  const allPassed = envCheck && filesCheck && scriptsCheck;
  
  if (allPassed) {
    console.log('🎉 Development environment is properly configured!');
    console.log('You can now run: npm run dev');
  } else {
    console.log('❌ Development environment needs configuration.');
    console.log('');
    provideSetupGuidance();
  }
  
  process.exit(allPassed ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  checkEnvironmentVariables,
  checkRequiredFiles,
  checkPackageScripts
};
