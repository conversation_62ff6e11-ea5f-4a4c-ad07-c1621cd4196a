#!/usr/bin/env ts-node

// Simple test to verify PGMQ functionality
console.log('Starting PGMQ simple test...');

// Load environment variables
import * as dotenv from 'dotenv';
import * as path from 'path';

// Set NODE_ENV to development for scripts
process.env.NODE_ENV = 'development';

// Load .env.local first, then .env
dotenv.config({ path: path.join(process.cwd(), '.env.local') });
dotenv.config({ path: path.join(process.cwd(), '.env') });

console.log('Environment variables loaded');
console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set');
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set');

import { createClient } from '@supabase/supabase-js';

async function testPGMQ() {
  try {
    console.log('Creating Supabase client...');
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    console.log('Supabase client created successfully');
    
    // Test 1: Check if PGMQ queue exists
    console.log('Testing PGMQ queue status...');
    const { data: metrics, error: metricsError } = await supabase
      .rpc('pgmq_metrics', { queue_name: 'email_queue' });
    
    if (metricsError) {
      console.error('Error getting queue metrics:', metricsError);
    } else {
      console.log('Queue metrics:', metrics);
    }
    
    // Test 2: Try to send a message to PGMQ
    console.log('Testing PGMQ send...');
    const testMessage = {
      emailType: 'test',
      recipient: '<EMAIL>',
      subject: 'PGMQ Test',
      content: 'This is a test message for PGMQ'
    };
    
    const { data: sendResult, error: sendError } = await supabase
      .rpc('pgmq_send', {
        queue_name: 'email_queue',
        message: testMessage
      });
    
    if (sendError) {
      console.error('Error sending to queue:', sendError);
    } else {
      console.log('Message sent successfully, ID:', sendResult);
    }
    
    // Test 3: Check queue metrics again
    console.log('Checking queue metrics after send...');
    const { data: metrics2, error: metricsError2 } = await supabase
      .rpc('pgmq_metrics', { queue_name: 'email_queue' });
    
    if (metricsError2) {
      console.error('Error getting queue metrics after send:', metricsError2);
    } else {
      console.log('Queue metrics after send:', metrics2);
    }
    
    // Test 4: Try to read from queue
    console.log('Testing PGMQ read...');
    const { data: messages, error: readError } = await supabase
      .rpc('pgmq_read', {
        queue_name: 'email_queue',
        visibility_timeout: 30,
        quantity: 1
      });
    
    if (readError) {
      console.error('Error reading from queue:', readError);
    } else {
      console.log('Messages read from queue:', messages);
      
      // Test 5: If we got a message, delete it to clean up
      if (messages && messages.length > 0) {
        const msgId = messages[0].msg_id;
        console.log('Cleaning up test message:', msgId);
        
        const { error: deleteError } = await supabase
          .rpc('pgmq_delete', {
            queue_name: 'email_queue',
            msg_id: msgId
          });
        
        if (deleteError) {
          console.error('Error deleting message:', deleteError);
        } else {
          console.log('Test message deleted successfully');
        }
      }
    }
    
    console.log('PGMQ test completed successfully!');
    
  } catch (error) {
    console.error('PGMQ test failed:', error);
    process.exit(1);
  }
}

testPGMQ();