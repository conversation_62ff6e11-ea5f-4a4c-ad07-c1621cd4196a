import { createClient } from '@supabase/supabase-js'
import { config } from 'dotenv'

// Load environment variables from .env.local
config({ path: '.env.local' })

async function testSupabaseConnection() {
  console.log('🔍 Testing Supabase connection and checking users table...\n')
  
  // Get Supabase credentials from environment
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  
  // Validate environment variables
  if (!supabaseUrl || !supabaseServiceKey) {
    console.log('❌ Missing Supabase credentials in .env.local file')
    console.log('Required variables:')
    console.log('- NEXT_PUBLIC_SUPABASE_URL')
    console.log('- SUPABASE_SERVICE_ROLE_KEY')
    return
  }
  
  console.log('✅ Environment variables found')
  console.log(`URL: ${supabaseUrl}`)
  console.log(`Service Key: ${supabaseServiceKey.substring(0, 20)}...`)
  
  try {
    // Create Supabase client with service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    console.log('\n🔌 Testing database connection...')
    
    // Test basic connection by checking if users table exists
    const { data, error, count } = await supabase
      .from('users')
      .select('*', { count: 'exact' })
      .limit(5) // Only get first 5 records for preview
    
    if (error) {
      console.log('❌ Connection failed:', error.message)
      
      if (error.message.includes('relation') && error.message.includes('does not exist')) {
        console.log('\n💡 The users table does not exist in Supabase yet.')
        console.log('You need to run the schema migration first.')
      } else if (error.message.includes('JWS')) {
        console.log('\n💡 Authentication error - check your service role key.')
      }
      
      return
    }
    
    console.log('✅ Successfully connected to Supabase!')
    console.log(`📊 Users table contains ${count} total records`)
    
    if (data && data.length > 0) {
      console.log('\n👥 Sample users data:')
      data.forEach((user, index) => {
        console.log(`${index + 1}. ID: ${user.id}, Username: ${user.username || 'N/A'}, Email: ${user.email || 'N/A'}`)
      })
    } else {
      console.log('\n📝 Users table is empty - no data has been migrated yet.')
    }
    
    // Test auth.users table
    console.log('\n🔐 Testing Supabase Auth users...')
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers()
    
    if (authError) {
      console.log('❌ Auth users check failed:', authError.message)
    } else {
      console.log(`✅ Found ${authUsers.users.length} users in Supabase Auth`)
      
      if (authUsers.users.length > 0) {
        console.log('Sample auth users:')
        authUsers.users.slice(0, 3).forEach((user, index) => {
          console.log(`${index + 1}. ID: ${user.id}, Email: ${user.email}`)
        })
      }
    }
    
    console.log('\n🎉 Supabase connection test completed successfully!')
    
  } catch (error: any) {
    console.log('❌ Unexpected error occurred:', error.message)
    console.log('\nPlease check:')
    console.log('1. Your internet connection')
    console.log('2. Supabase project is active')
    console.log('3. Credentials are correct')
  }
}

// Run the test if script is executed directly
if (require.main === module) {
  testSupabaseConnection().catch(console.error)
}

export { testSupabaseConnection }