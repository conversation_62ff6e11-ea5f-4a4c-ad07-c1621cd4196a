import { db } from '../lib/db';

async function listUsers() {
  try {
    console.log('Fetching users from database...');
    
    const result = await db.query(`
      SELECT 
        u.id, 
        u.username, 
        u.name, 
        u.email, 
        u.role, 
        u.is_profile_updated,
        u.created_at,
        h.name as house_name,
        f.number as flat_number
      FROM 
        users u
      LEFT JOIN
        flats f ON u.flat_id = f.id
      LEFT JOIN
        houses h ON f.house_id = h.id
      ORDER BY 
        u.role, u.name
    `);
    
    const users = result.rows;
    
    console.log('\nTotal users found:', users.length);
    console.log('\nUSERS TABLE:');
    console.log('=============================================================');
    console.log('ID  | Username | Name            | Email                | Role');
    console.log('------------------------------------------------------------');
    
    users.forEach((user: any) => {
      console.log(
        `${user.id.toString().padEnd(3)} | ${user.username.padEnd(8)} | ${user.name.padEnd(15)} | ${user.email.padEnd(20)} | ${user.role}`
      );
    });
    
    console.log('=============================================================');
  } catch (error) {
    console.error('Error fetching users:', error);
  } finally {
    await db.closePool();
  }
}

listUsers(); 