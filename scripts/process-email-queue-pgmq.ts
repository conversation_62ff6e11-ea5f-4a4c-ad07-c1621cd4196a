#!/usr/bin/env ts-node

// Load environment variables for script execution
import * as dotenv from 'dotenv';
import * as path from 'path';

// Set NODE_ENV to development for scripts
process.env.NODE_ENV = 'development';

// Load .env.local first, then .env
dotenv.config({ path: path.join(process.cwd(), '.env.local') });
dotenv.config({ path: path.join(process.cwd(), '.env') });

import { createServiceClient } from '@/lib/supabase/server';
import { sendEmail } from '@/lib/email';
import { addAuditLog } from '@/lib/utils';

interface EmailMessage {
  emailType: string;
  recipient: string;
  subject: string;
  content: string;
  entityType?: string;
  entityId?: number;
  metadata?: any;
  replyTo?: string;
  announcementId?: number;
  pollId?: number;
  testMode?: boolean;
  priority?: number;
  scheduledFor?: string;
  userId?: number;
  retryCount?: number;
}

async function processEmailQueue() {
  const supabase = await createServiceClient();
  const batchSize = parseInt(process.env.EMAIL_BATCH_SIZE || '10');
  
  console.log(`[Email Worker] Starting to process email queue (batch size: ${batchSize})...`);
  
  try {
    // Read messages from PGMQ
    const { data: messages, error: readError } = await supabase
      .rpc('pgmq_read', {
        queue_name: 'email_queue',
        visibility_timeout: 30, // 30 second visibility timeout
        quantity: batchSize
      });

    if (readError) {
      console.error('[Email Worker] Error reading from queue:', readError);
      return;
    }

    if (!messages || messages.length === 0) {
      console.log('[Email Worker] No messages to process');
      return;
    }

    console.log(`[Email Worker] Processing ${messages.length} messages...`);

    // Process each message
    for (const msg of messages) {
      const startTime = Date.now();
      const emailData: EmailMessage = msg.message;
      
      console.log(`[Email Worker] Processing message ${msg.msg_id}:`, {
        to: emailData.recipient,
        type: emailData.emailType,
        testMode: emailData.testMode
      });

      try {
        // Check if scheduled for future
        if (emailData.scheduledFor) {
          const scheduledTime = new Date(emailData.scheduledFor).getTime();
          if (scheduledTime > Date.now()) {
            console.log(`[Email Worker] Message ${msg.msg_id} scheduled for future, skipping`);
            continue;
          }
        }

        // Send the actual email
        const result = await sendEmail({
          to: emailData.recipient,
          subject: emailData.subject,
          html: emailData.content,
          replyTo: emailData.replyTo,
        });

        if (result.success) {
          // Delete the message from queue
          const { error: deleteError } = await supabase.rpc('pgmq_delete', {
            queue_name: 'email_queue',
            msg_id: msg.msg_id
          });

          if (deleteError) {
            console.error(`[Email Worker] Error deleting message ${msg.msg_id}:`, deleteError);
          }

          // Update status in database
          await supabase
            .from('email_queue')
            .update({ 
              status: 'sent',
              sent_at: new Date().toISOString(),
              processing_time_ms: Date.now() - startTime
            })
            .eq('pgmq_msg_id', msg.msg_id);

          console.log(`[Email Worker] Successfully sent email ${msg.msg_id}`);

          // Add audit log if userId is present
          if (emailData.userId) {
            await addAuditLog({
              action: 'send_email',
              userId: emailData.userId,
              entityType: 'email_queue',
              entityId: msg.msg_id,
              changes: {
                emailType: emailData.emailType,
                recipient: emailData.recipient,
                testMode: emailData.testMode
              }
            });
          }
        } else {
          throw new Error(result.error || 'Failed to send email');
        }
      } catch (error) {
        console.error(`[Email Worker] Failed to process message ${msg.msg_id}:`, error);
        
        const retryCount = (emailData.retryCount || 0) + 1;
        const maxRetries = 5;
        
        if (retryCount >= maxRetries) {
          // Archive the message as permanently failed
          const { error: archiveError } = await supabase.rpc('pgmq_archive', {
            queue_name: 'email_queue',
            msg_id: msg.msg_id
          });

          if (archiveError) {
            console.error(`[Email Worker] Error archiving message ${msg.msg_id}:`, archiveError);
          }
          
          // Update status in database
          await supabase
            .from('email_queue')
            .update({ 
              status: 'failed',
              last_error: error instanceof Error ? error.message : String(error),
              retry_count: retryCount,
              failed_at: new Date().toISOString()
            })
            .eq('pgmq_msg_id', msg.msg_id);
          
          console.error(`[Email Worker] Message ${msg.msg_id} permanently failed after ${retryCount} retries`);
        } else {
          // Message will automatically reappear after visibility timeout
          // Update retry count in database
          await supabase
            .from('email_queue')
            .update({ 
              status: 'retrying',
              last_error: error instanceof Error ? error.message : String(error),
              retry_count: retryCount,
              last_retry_at: new Date().toISOString()
            })
            .eq('pgmq_msg_id', msg.msg_id);
          
          console.log(`[Email Worker] Message ${msg.msg_id} will be retried (attempt ${retryCount}/${maxRetries})`);
        }
      }
    }
    
    console.log('[Email Worker] Batch processing completed');
  } catch (error) {
    console.error('[Email Worker] Fatal error:', error);
    process.exit(1);
  }
}

// Run the processor
if (require.main === module) {
  processEmailQueue()
    .then(() => {
      console.log('[Email Worker] Processing complete');
      process.exit(0);
    })
    .catch((error) => {
      console.error('[Email Worker] Fatal error:', error);
      process.exit(1);
    });
}

export { processEmailQueue };