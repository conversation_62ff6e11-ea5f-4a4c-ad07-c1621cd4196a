#!/usr/bin/env node
const fs = require('fs');
const path = require('path');
const xlsx = require('xlsx');

// Configuration
const EXCEL_FILE = process.argv[2]; // Pass Excel file as argument
const OUTPUT_FILE = process.argv[3] || 'output.csv'; // Optional output file name

// Column mapping
const columnMap = {
  0: 'Gatve',
  1: 'Namo Nr.',
  2: 'But<PERSON>',
  3: 'Moketojo <PERSON>das',
  4: 'Vardas <PERSON>e',
  5: 'Email',
  6: 'Telefonas',
  7: 'Kontaktinis Asmuo',
  8: 'Kontaktinio Asmens Telefonas'
};

async function convertExcelToCSV() {
  try {
    if (!EXCEL_FILE) {
      console.error('Please provide an Excel file path as an argument');
      process.exit(1);
    }

    console.log(`🔍 Reading Excel data from ${EXCEL_FILE}`);
    
    // Read the Excel file
    const workbook = xlsx.readFile(EXCEL_FILE);
    
    // Get the first sheet
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    // Convert to JSON with headers option explicitly set
    const jsonData = xlsx.utils.sheet_to_json(worksheet, {
      header: 1,  // Use first row as headers
      range: 1    // Skip the first row
    });
    
    console.log(`📊 Found ${jsonData.length} records in the Excel file`);
    
    // Check if data was properly extracted
    if (jsonData.length === 0) {
      console.warn("Warning: No data found in the Excel file after skipping the first row");
      return;
    }
    
    // Transform data to include field names
    const data = jsonData.map(row => {
      const record = {};
      
      for (let i = 0; i < row.length; i++) {
        if (columnMap[i]) {
          record[columnMap[i]] = row[i];
        }
      }
      
      return record;
    });
    
    // Convert JSON data back to CSV
    const csvOutput = data.map(record => {
      const values = [];
      for (const key of Object.keys(columnMap).map(i => columnMap[i])) {
        values.push(record[key] !== undefined ? record[key] : '');
      }
      return values.join(',');
    });
    
    // Add header row
    const headers = Object.values(columnMap).join(',');
    const csv = headers + '\n' + csvOutput.join('\n');
    
    // Write CSV file
    fs.writeFileSync(OUTPUT_FILE, csv);
    console.log(`✅ Converted data (${data.length} records) saved to ${OUTPUT_FILE}`);
    
    // If no mapping needed just converting directly
    /*
    const csv = xlsx.utils.sheet_to_csv(worksheet);
    fs.writeFileSync(OUTPUT_FILE, csv);
    console.log(`✅ Converted data saved to ${OUTPUT_FILE}`);
    */
  } catch (error) {
    console.error("❌ Error converting Excel to CSV:", error);
    process.exit(1);
  }
}

// Run the script
convertExcelToCSV(); 