#!/usr/bin/env node
/**
 * Nixpacks Migration Helper Script
 * 
 * This script helps prepare for and validate the migration from Docker Compose to Nixpacks
 * 
 * Usage:
 *   node scripts/nixpacks-migration-helper.js [command]
 * 
 * Commands:
 *   prepare    - Prepare for migration (validate current setup)
 *   compare    - Compare current vs Nixpacks configuration
 *   validate   - Validate Nixpacks configuration
 *   env        - Generate environment variables for Nixpacks
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

function readPackageJson() {
  try {
    return JSON.parse(fs.readFileSync('package.json', 'utf8'));
  } catch (error) {
    log(`Error reading package.json: ${error.message}`, 'red');
    return null;
  }
}

function validateCurrentSetup() {
  log('\n=== Validating Current Setup ===\n', 'bold');
  
  const checks = [
    {
      name: 'package.json exists',
      check: () => checkFileExists('package.json'),
      required: true
    },
    {
      name: 'docker-compose.yml exists',
      check: () => checkFileExists('docker-compose.yml'),
      required: true
    },
    {
      name: 'Dockerfile exists',
      check: () => checkFileExists('Dockerfile'),
      required: true
    },
    {
      name: 'next.config.js exists',
      check: () => checkFileExists('next.config.js'),
      required: true
    },
    {
      name: '.env.local exists',
      check: () => checkFileExists('.env.local'),
      required: false
    },
    {
      name: 'Node.js version >= 22',
      check: () => {
        const pkg = readPackageJson();
        return pkg && pkg.engines && pkg.engines.node && pkg.engines.node.includes('22');
      },
      required: true
    },
    {
      name: 'Next.js standalone output configured',
      check: () => {
        try {
          const nextConfig = fs.readFileSync('next.config.js', 'utf8');
          return nextConfig.includes('output: \'standalone\'');
        } catch {
          return false;
        }
      },
      required: true
    }
  ];
  
  let allPassed = true;
  
  checks.forEach(check => {
    const passed = check.check();
    const status = passed ? '✅' : (check.required ? '❌' : '⚠️');
    const color = passed ? 'green' : (check.required ? 'red' : 'yellow');
    
    log(`${status} ${check.name}`, color);
    
    if (!passed && check.required) {
      allPassed = false;
    }
  });
  
  log(`\n=== Validation ${allPassed ? 'PASSED' : 'FAILED'} ===\n`, allPassed ? 'green' : 'red');
  
  return allPassed;
}

function compareConfigurations() {
  log('\n=== Configuration Comparison ===\n', 'bold');
  
  log('Current Setup (Docker Compose):', 'blue');
  log('• Build System: Multi-stage Dockerfile');
  log('• Base Image: node:22-alpine');
  log('• Build Process: npm ci → npm run build → copy files');
  log('• Runtime: Custom user (nextjs:nodejs)');
  log('• Environment: Build args + runtime env vars');
  log('• Health Check: Built-in at /api/health');
  log('• Volumes: ./logs, ./uploads');
  
  log('\nNixpacks Setup:', 'green');
  log('• Build System: Automatic detection + optimization');
  log('• Base Image: Automatically selected (likely Node.js 22)');
  log('• Build Process: Automatic (npm ci → npm run build)');
  log('• Runtime: Automatically configured');
  log('• Environment: Runtime env vars only');
  log('• Health Check: Same (/api/health)');
  log('• Volumes: Same (./logs, ./uploads)');
  
  log('\nKey Differences:', 'yellow');
  log('• No custom Dockerfile maintenance required');
  log('• Build arguments become runtime environment variables');
  log('• Automatic optimization for Next.js applications');
  log('• Simplified deployment configuration');
  log('• Potentially faster builds due to better caching');
}

function validateNixpacksConfig() {
  log('\n=== Validating Nixpacks Configuration ===\n', 'bold');
  
  if (!checkFileExists('nixpacks.toml')) {
    log('❌ nixpacks.toml not found', 'red');
    log('Run this script with "prepare" command to create it', 'yellow');
    return false;
  }
  
  try {
    const config = fs.readFileSync('nixpacks.toml', 'utf8');
    
    const checks = [
      {
        name: 'Node.js version specified',
        check: () => config.includes('NODE_VERSION = "22"')
      },
      {
        name: 'Build command configured',
        check: () => config.includes('npm run build')
      },
      {
        name: 'Start command configured',
        check: () => config.includes('node server.js')
      },
      {
        name: 'Build environment variables set',
        check: () => config.includes('[buildEnvs]')
      }
    ];
    
    let allPassed = true;
    
    checks.forEach(check => {
      const passed = check.check();
      const status = passed ? '✅' : '❌';
      const color = passed ? 'green' : 'red';
      
      log(`${status} ${check.name}`, color);
      
      if (!passed) {
        allPassed = false;
      }
    });
    
    log(`\n=== Nixpacks Configuration ${allPassed ? 'VALID' : 'INVALID'} ===\n`, allPassed ? 'green' : 'red');
    
    return allPassed;
    
  } catch (error) {
    log(`❌ Error reading nixpacks.toml: ${error.message}`, 'red');
    return false;
  }
}

function generateEnvironmentVariables() {
  log('\n=== Environment Variables for Nixpacks ===\n', 'bold');
  
  log('The following environment variables should be configured in Coolify:', 'blue');
  log('(These are the same as your current Docker Compose setup)\n');
  
  // Use the existing coolify-generate-env.js script
  try {
    const { execSync } = require('child_process');
    execSync('node scripts/coolify-generate-env.js', { stdio: 'inherit' });
  } catch (error) {
    log('Error running environment generation script:', 'red');
    log('Please run: npm run deploy:env', 'yellow');
  }
}

function showMigrationSteps() {
  log('\n=== Migration Steps ===\n', 'bold');
  
  log('1. Backup Current Configuration', 'blue');
  log('   • Export environment variables from Coolify');
  log('   • Document current deployment settings');
  log('   • Create backup of working deployment\n');
  
  log('2. Create Nixpacks Application in Coolify', 'blue');
  log('   • Create new application in same project');
  log('   • Select same Git repository');
  log('   • Choose "Nixpacks" as build pack');
  log('   • Set base directory to "/" (root)');
  log('   • Set port to 3000\n');
  
  log('3. Configure Environment Variables', 'blue');
  log('   • Copy all environment variables from current deployment');
  log('   • Ensure NEXT_PUBLIC_* variables are set');
  log('   • Verify Supabase configuration\n');
  
  log('4. Configure Volumes', 'blue');
  log('   • Set up ./logs volume mount');
  log('   • Set up ./uploads volume mount (if used)\n');
  
  log('5. Deploy and Test', 'blue');
  log('   • Deploy the Nixpacks application');
  log('   • Run: npm run deploy:verify');
  log('   • Test all functionality');
  log('   • Monitor performance\n');
  
  log('6. Switch Traffic', 'blue');
  log('   • Update domain configuration');
  log('   • Monitor for issues');
  log('   • Keep Docker Compose deployment as backup\n');
}

function main() {
  const command = process.argv[2] || 'help';
  
  switch (command) {
    case 'prepare':
      log('🚀 Preparing for Nixpacks Migration', 'bold');
      const isValid = validateCurrentSetup();
      if (isValid) {
        log('✅ Current setup is ready for migration!', 'green');
        showMigrationSteps();
      } else {
        log('❌ Please fix the issues above before migrating', 'red');
      }
      break;
      
    case 'compare':
      compareConfigurations();
      break;
      
    case 'validate':
      validateNixpacksConfig();
      break;
      
    case 'env':
      generateEnvironmentVariables();
      break;
      
    case 'help':
    default:
      log('Nixpacks Migration Helper', 'bold');
      log('\nAvailable commands:');
      log('  prepare    - Validate current setup and show migration steps');
      log('  compare    - Compare Docker Compose vs Nixpacks configuration');
      log('  validate   - Validate nixpacks.toml configuration');
      log('  env        - Generate environment variables for Nixpacks');
      log('\nExample: node scripts/nixpacks-migration-helper.js prepare');
      break;
  }
}

main();
