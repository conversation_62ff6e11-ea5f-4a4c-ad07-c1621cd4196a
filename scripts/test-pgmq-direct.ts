#!/usr/bin/env ts-node

// Direct PGMQ test - bypasses dev mode restrictions

// Load environment variables
import * as dotenv from 'dotenv';
import * as path from 'path';

// Set NODE_ENV to development for scripts
process.env.NODE_ENV = 'development';

// Load .env.local first, then .env
dotenv.config({ path: path.join(process.cwd(), '.env.local') });
dotenv.config({ path: path.join(process.cwd(), '.env') });

import { createClient } from '@supabase/supabase-js';
import { processEmailQueue } from './process-email-queue-pgmq';

async function testPGMQDirect() {
  console.log('[Test] Starting direct PGMQ email test...');
  
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    // Step 1: Directly queue a message to PGMQ (bypassing queueEmail validation)
    console.log('[Test] Step 1: Directly queueing message to PGMQ...');
    
    const testMessage = {
      emailType: 'test',
      recipient: '<EMAIL>',
      subject: 'Direct PGMQ Test',
      content: `
        <div style="font-family: Arial, sans-serif;">
          <h2>Direct PGMQ Test</h2>
          <p>This email was queued directly to PGMQ for testing.</p>
          <p>Timestamp: ${new Date().toISOString()}</p>
        </div>
      `,
      entityType: 'test',
      entityId: 999,
      priority: 1,
      userId: 1,
      testMode: true, // Mark as test mode
      metadata: {
        directTest: true,
        timestamp: new Date().toISOString()
      }
    };
    
    const { data: sendResult, error: sendError } = await supabase
      .rpc('pgmq_send', {
        queue_name: 'email_queue',
        message: testMessage
      });
    
    if (sendError) {
      console.error('[Test] Error sending to PGMQ:', sendError);
      return;
    }
    
    console.log('[Test] ✅ Message queued to PGMQ, message ID:', sendResult);
    
    // Step 2: Also add to email_queue table for tracking
    console.log('[Test] Step 2: Adding to email_queue table...');
    
    const { data: dbResult, error: dbError } = await supabase
      .from('email_queue')
      .insert({
        email_type: testMessage.emailType,
        recipient: testMessage.recipient,
        subject: testMessage.subject,
        content: testMessage.content,
        entity_type: testMessage.entityType,
        entity_id: testMessage.entityId,
        priority: testMessage.priority,
        status: 'pending',
        pgmq_msg_id: sendResult
      })
      .select('id')
      .single();
    
    if (dbError) {
      console.error('[Test] Error adding to database:', dbError);
    } else {
      console.log('[Test] ✅ Added to email_queue table, ID:', dbResult.id);
    }
    
    // Step 3: Check queue metrics
    console.log('[Test] Step 3: Checking queue metrics...');
    
    const { data: metrics, error: metricsError } = await supabase
      .rpc('pgmq_metrics', { queue_name: 'email_queue' });
    
    if (metricsError) {
      console.error('[Test] Error getting metrics:', metricsError);
    } else {
      console.log('[Test] Queue metrics:', metrics);
    }
    
    // Step 4: Process the queue
    console.log('[Test] Step 4: Processing the queue...');
    
    await processEmailQueue();
    
    console.log('[Test] ✅ Queue processed');
    
    // Step 5: Check final metrics
    console.log('[Test] Step 5: Checking final metrics...');
    
    const { data: finalMetrics, error: finalMetricsError } = await supabase
      .rpc('pgmq_metrics', { queue_name: 'email_queue' });
    
    if (finalMetricsError) {
      console.error('[Test] Error getting final metrics:', finalMetricsError);
    } else {
      console.log('[Test] Final queue metrics:', finalMetrics);
    }
    
    console.log('[Test] ✅ Direct PGMQ email test completed successfully!');
    console.log('[Test] The email queue migration from BullMQ to PGMQ is working!');
    
  } catch (error) {
    console.error('[Test] Direct PGMQ test failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testPGMQDirect()
    .then(() => {
      console.log('[Test] All tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('[Test] Test failed:', error);
      process.exit(1);
    });
}

export { testPGMQDirect };