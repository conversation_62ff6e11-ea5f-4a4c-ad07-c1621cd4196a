import { db } from '../lib/db';

async function initializeSmtpReplyToSetting() {
  console.log('Checking for smtp_reply_to setting...');
  
  try {
    // Check if the setting already exists
    const result = await db.query(
      `SELECT key FROM admin_settings WHERE key = 'smtp_reply_to'`
    );
    
    if (result.rows.length > 0) {
      console.log('smtp_reply_to setting already exists.');
    } else {
      // Insert the setting if it doesn't exist
      await db.query(
        `INSERT INTO admin_settings (key, value, description)
         VALUES ($1, $2, $3)`,
        ['smtp_reply_to', '', 'Reply-to email address for system emails']
      );
      console.log('smtp_reply_to setting created successfully.');
    }
    
    // Check if migrations table exists before trying to add migration entry
    try {
      const tablesResult = await db.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'migrations'
        )`
      );
      
      const migrationsTableExists = tablesResult.rows[0].exists;
      
      if (migrationsTableExists) {
        // Also add migration entry to ensure this doesn't run again
        const migrationResult = await db.query(
          `SELECT name FROM migrations WHERE name = 'add_smtp_reply_to'`
        );
        
        if (migrationResult.rows.length === 0) {
          await db.query(
            `INSERT INTO migrations (name, applied_at) VALUES ($1, NOW())`,
            ['add_smtp_reply_to']
          );
          console.log('Migration record created.');
        }
      } else {
        console.log('Migrations table does not exist, skipping migration record.');
      }
    } catch (error) {
      console.log('Error checking migrations table, skipping migration record:', error);
    }
    
  } catch (error) {
    console.error('Error initializing smtp_reply_to setting:', error);
  }
}

// Run the initialization and exit when done
initializeSmtpReplyToSetting()
  .then(() => {
    console.log('Initialization complete.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Initialization failed:', error);
    process.exit(1);
  }); 