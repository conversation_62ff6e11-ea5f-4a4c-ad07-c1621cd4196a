/**
 * User Data Validator
 * 
 * This script validates CSV data for importing users, checking for:
 * - Valid Lithuanian characters
 * - Required fields
 * - Data format issues
 * - Potential duplicates
 * 
 * Usage: node user-data-validator.js <path-to-csv-file>
 */

const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

// Set of valid characters (including Lithuanian)
const validChars = new Set([
  ...Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i)), // A-Z
  ...Array.from({ length: 26 }, (_, i) => String.fromCharCode(97 + i)), // a-z
  ...Array.from('0123456789'), // Digits
  ...Array.from(' ,.;:-_()[]{}\'"/\\+*&%$#@!?=<>|'), // Common punctuation
  ...Array.from('ĄąČčĘęĖėĮįŠšŲųŪūŽž'), // Lithuanian characters
  ...Array.from('\n\r\t') // Whitespace
]);

// Extract row values
const extractAndFormatRow = (row) => {
  return {
    // Main identifiers
    payerCode: row['MOKĖTOJO KODAS'] || '',
    street: row['GATVĖ'] || '',
    houseNumber: row['NAMO NR.'] || '',
    flatNumber: row['BUTAS'] || '',
    
    // Flat details
    flatArea: row['Buto Plotas'] || '',
    registrationBasis: row['Patalpu ireg. pagrindas'] || '',
    notes: row['Pastaba - Note'] || '',
    
    // User details
    username: row['Vartotojo Vardas'] || '',
    firstName: row['Vardas'] || '',
    lastName: row['Pavarde'] || '',
    email: row['El. pastas'] || '',
    phone: row['Tel numeris'] || '',
    
    // Emergency contact 1
    contact1Name: row['Kontaktinis asmuo 1'] || '',
    contact1Phone: row['Kontaktinio asmens telefonas 1'] || '',
    contact1Email: row['Kontaktinio asmens el pastas 1'] || '',
    contact1Relation: row['Kontaktinio asmens rysys 1'] || '',
    
    // Emergency contact 2
    contact2Name: row['Kontaktinis asmuo 2'] || '',
    contact2Phone: row['Kontaktinio asmens telefonas 2'] || '',
    contact2Email: row['Kontaktinio asmens el pastas 2'] || '',
    contact2Relation: row['Kantaktinio asmens rysys 2'] || '',
    
    // Emergency contact 3
    contact3Name: row['Kontaktinis asmuo 3'] || '',
    contact3Phone: row['Kontaktinio asmens telefonas 3'] || '',
    contact3Email: row['Kontaktinio asmens el pastas 3'] || '',
    contact3Relation: row['Kantaktinio asmens rysys 3'] || ''
  };
};

// Check for invalid characters
const checkInvalidChars = (text) => {
  if (!text) return [];
  
  const invalidChars = [];
  for (const char of text) {
    if (!validChars.has(char)) {
      invalidChars.push(char);
    }
  }
  
  return [...new Set(invalidChars)]; // Return unique invalid chars
};

// Format flat area from comma to dot decimal
const formatFlatArea = (area) => {
  if (!area) return null;
  return area.replace(',', '.');
};

// Validate a single row
const validateRow = (row, rowIndex) => {
  const extractedRow = extractAndFormatRow(row);
  const errors = [];
  
  // Check required fields
  if (!extractedRow.payerCode) errors.push('Missing payer code');
  if (!extractedRow.street) errors.push('Missing street');
  if (!extractedRow.houseNumber) errors.push('Missing house number');
  if (!extractedRow.flatNumber) errors.push('Missing flat number');
  
  // If there are fatal errors, return immediately
  if (errors.length > 0) {
    return {
      rowIndex,
      identifier: `${extractedRow.street} ${extractedRow.houseNumber}-${extractedRow.flatNumber}`,
      isValid: false,
      errors,
      warnings: [],
      invalidChars: [],
      formattedRow: extractedRow
    };
  }
  
  // Check for invalid characters
  const allFields = Object.values(extractedRow).join('');
  const invalidChars = checkInvalidChars(allFields);
  
  // Generate warnings for missing but optional fields
  const warnings = [];
  if (!extractedRow.firstName && !extractedRow.lastName) {
    warnings.push('Missing name information');
  }
  
  // Format flat area
  extractedRow.flatArea = formatFlatArea(extractedRow.flatArea);
  
  return {
    rowIndex,
    identifier: `${extractedRow.street} ${extractedRow.houseNumber}-${extractedRow.flatNumber}`,
    isValid: invalidChars.length === 0,
    errors,
    warnings,
    invalidChars,
    formattedRow: extractedRow
  };
};

// Check for duplicates in data
const findDuplicates = (validatedRows) => {
  const uniqueKeys = new Map();
  const duplicates = [];
  
  validatedRows.forEach(row => {
    const key = `${row.formattedRow.street}-${row.formattedRow.houseNumber}-${row.formattedRow.flatNumber}`;
    if (uniqueKeys.has(key)) {
      duplicates.push({
        key,
        rows: [uniqueKeys.get(key), row.rowIndex]
      });
    } else {
      uniqueKeys.set(key, row.rowIndex);
    }
  });
  
  return duplicates;
};

// Main validation function
const validateCsvFile = (filePath) => {
  return new Promise((resolve, reject) => {
    if (!fs.existsSync(filePath)) {
      reject(new Error(`File not found: ${filePath}`));
      return;
    }

    const results = [];
    const knownDuplicates = [235, 255, 274, 284, 289, 484, 505, 524, 622];
    
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (row) => {
        results.push(row);
      })
      .on('end', () => {
        console.log(`Found ${results.length} rows in ${filePath}`);
        
        let rowIndex = 1; // 1-based indexing for CSV rows (account for header)
        const validatedRows = [];
        const invalidRows = [];
        
        for (const row of results) {
          rowIndex++;
          
          // Skip known duplicate rows
          if (knownDuplicates.includes(rowIndex)) {
            console.log(`Skipping known duplicate row ${rowIndex}`);
            continue;
          }
          
          const validationResult = validateRow(row, rowIndex);
          if (validationResult.isValid && validationResult.errors.length === 0) {
            validatedRows.push(validationResult);
          } else {
            invalidRows.push(validationResult);
          }
        }
        
        const duplicates = findDuplicates(validatedRows);
        
        resolve({
          totalRows: results.length,
          validRows: validatedRows.length,
          invalidRows: invalidRows,
          duplicateRows: duplicates,
          validatedData: validatedRows
        });
      })
      .on('error', (error) => {
        reject(error);
      });
  });
};

// Function to display validation results
const displayValidationResults = (results) => {
  console.log('\n======= VALIDATION RESULTS =======');
  console.log(`Total rows: ${results.totalRows}`);
  console.log(`Valid rows: ${results.validRows}`);
  console.log(`Invalid rows: ${results.invalidRows.length}`);
  console.log(`Duplicate keys: ${results.duplicateRows.length}`);
  
  if (results.invalidRows.length > 0) {
    console.log('\n--- INVALID ROWS ---');
    results.invalidRows.forEach(invalidRow => {
      console.log(`Row ${invalidRow.rowIndex}: ${invalidRow.identifier}`);
      
      if (invalidRow.errors.length > 0) {
        console.log('  Errors:');
        invalidRow.errors.forEach(err => console.log(`    - ${err}`));
      }
      
      if (invalidRow.invalidChars.length > 0) {
        console.log('  Invalid characters:');
        console.log(`    - [${invalidRow.invalidChars.map(c => `'${c}'`).join(', ')}]`);
      }
    });
  }
  
  if (results.duplicateRows.length > 0) {
    console.log('\n--- DUPLICATES ---');
    results.duplicateRows.forEach(duplicate => {
      console.log(`${duplicate.key} in rows: ${duplicate.rows.join(', ')}`);
    });
  }
  
  if (results.invalidRows.length === 0 && results.duplicateRows.length === 0) {
    console.log('\n✅ All data is valid and ready for import!');
    return true;
  } else {
    console.log('\n❌ Data validation failed. Please fix the errors above before importing.');
    return false;
  }
};

// Main execution when run directly
if (require.main === module) {
  const filePath = process.argv[2];
  
  if (!filePath) {
    console.error('Please provide a CSV file path');
    console.error('Usage: node user-data-validator.js <path-to-csv-file>');
    process.exit(1);
  }

  validateCsvFile(filePath)
    .then(results => {
      const isValid = displayValidationResults(results);
      process.exit(isValid ? 0 : 1);
    })
    .catch(error => {
      console.error('Error during validation:', error);
      process.exit(1);
    });
}

module.exports = {
  validateCsvFile,
  displayValidationResults,
  checkInvalidChars
};