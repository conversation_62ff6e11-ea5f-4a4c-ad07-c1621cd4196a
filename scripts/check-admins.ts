import { db } from "../lib/db";
import { compare } from "bcryptjs";
import { config } from "dotenv";

// Load environment variables
config({ path: ".env.local" });

async function checkAdmins() {
  console.log("🔎 Checking admin accounts...");

  try {
    // Query directly with SQL
    const admins = await db.query(`
      SELECT id, email, name, role, username, password_hash 
      FROM users 
      WHERE role = 'super_admin' OR role = 'editor'
      ORDER BY role, name
    `);
    
    if (admins.rows.length === 0) {
      console.log("❌ No admin accounts found!");
      return;
    }
    
    console.log(`✅ Found ${admins.rows.length} admin accounts:`);
    
    for (const admin of admins.rows) {
      console.log(`\n👤 User: ${admin.name} (${admin.role})`);
      console.log(`   Email: ${admin.email}`);
      console.log(`   Username: ${admin.username || '(not set)'}`);
      
      // Check if password_hash is set
      if (admin.password_hash) {
        console.log(`   Password hash: ${admin.password_hash.substring(0, 20)}...`);
        
        // Try to verify a default password (for test accounts only)
        const defaultAdminPassword = admin.role === 'super_admin' ? 'Admin123!' : 'Editor123!';
        
        try {
          const passwordMatches = await compare(defaultAdminPassword, admin.password_hash);
          console.log(`   Default password (${defaultAdminPassword}): ${passwordMatches ? '✅ Valid' : '❌ Invalid'}`);
        } catch (error) {
          console.log(`   Error checking password: ${error}`);
        }
      } else {
        console.log(`   ❌ No password hash set!`);
      }
    }
  } catch (error) {
    console.error("❌ Error checking admin accounts:", error);
  }
}

// Run the function
checkAdmins()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Unhandled error during admin check:", error);
    process.exit(1);
  }); 