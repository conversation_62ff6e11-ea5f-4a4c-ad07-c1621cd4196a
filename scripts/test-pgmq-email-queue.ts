#!/usr/bin/env ts-node

// Load environment variables
import * as dotenv from 'dotenv';
import * as path from 'path';

// Set NODE_ENV to development
process.env.NODE_ENV = 'development';

// Load .env.local first, then .env
dotenv.config({ path: path.join(process.cwd(), '.env.local') });
dotenv.config({ path: path.join(process.cwd(), '.env') });

import { createServiceClient } from '@/lib/supabase/server';
import { queueEmail } from '@/lib/supabase/email-queue';
import { processEmailQueue } from './process-email-queue-pgmq';

async function testPGMQEmailQueue() {
  console.log('[Test] Starting PGMQ email queue test...');
  
  try {
    const supabase = await createServiceClient();
    
    // 1. Check if PGMQ extension and queue exists
    console.log('[Test] Checking PGMQ setup...');
    
    const { data: extensions, error: extError } = await supabase
      .rpc('pgmq_extension_status')
      .single();
    
    if (extError) {
      console.log('[Test] PGMQ extension not available, checking manually...');
      
      // Try to create queue to test
      const { data: queueTest, error: queueError } = await supabase
        .rpc('pgmq_create', { queue_name: 'email_queue' });
      
      if (queueError) {
        console.error('[Test] PGMQ setup failed:', queueError);
        return;
      }
    }
    
    console.log('[Test] PGMQ setup verified');
    
    // 2. Queue a test email
    console.log('[Test] Queueing test email...');
    
    const queueResult = await queueEmail({
      emailType: 'test',
      recipient: '<EMAIL>',
      subject: 'PGMQ Test Email',
      content: 'This is a test email to verify PGMQ functionality.',
      entityType: 'test',
      entityId: 1,
      priority: 1,
      userId: 1,
      metadata: {
        test: true,
        timestamp: new Date().toISOString()
      }
    });
    
    console.log('[Test] Queue result:', queueResult);
    
    if (!queueResult.success) {
      console.error('[Test] Failed to queue email:', queueResult.error || queueResult);
      return;
    }
    
    // 3. Check queue status
    console.log('[Test] Checking queue status...');
    
    const { data: queueStats, error: statsError } = await supabase
      .rpc('pgmq_metrics', { queue_name: 'email_queue' });
    
    if (statsError) {
      console.log('[Test] Could not get queue metrics:', statsError);
    } else {
      console.log('[Test] Queue metrics:', queueStats);
    }
    
    // 4. Process the queue
    console.log('[Test] Processing email queue...');
    
    await processEmailQueue();
    
    console.log('[Test] Email queue processing complete');
    
    // 5. Check final status
    const { data: finalStats, error: finalError } = await supabase
      .rpc('pgmq_metrics', { queue_name: 'email_queue' });
    
    if (finalError) {
      console.log('[Test] Could not get final queue metrics:', finalError);
    } else {
      console.log('[Test] Final queue metrics:', finalStats);
    }
    
    // 6. Check email_queue table
    const { data: emailRecords, error: recordsError } = await supabase
      .from('email_queue')
      .select('*')
      .eq('email_type', 'test')
      .order('created_at', { ascending: false })
      .limit(5);
    
    if (recordsError) {
      console.error('[Test] Error fetching email records:', recordsError);
    } else {
      console.log('[Test] Recent email records:', emailRecords);
    }
    
    console.log('[Test] PGMQ email queue test completed successfully!');
    
  } catch (error) {
    console.error('[Test] Fatal error during test:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testPGMQEmailQueue()
    .then(() => {
      console.log('[Test] Test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('[Test] Test failed:', error);
      process.exit(1);
    });
}

export { testPGMQEmailQueue };