import { config } from "dotenv";
import { initializeDefaultSettings, areSettingsInitialized } from "../lib/init-settings";

// Load environment variables
config({ path: ".env.local" });

async function main() {
  console.log("🔧 Initializing default settings...");
  
  try {
    // Check if settings are already initialized
    const alreadyInitialized = await areSettingsInitialized();
    
    if (alreadyInitialized) {
      console.log("✅ Settings are already initialized");
      return;
    }
    
    // Initialize default settings
    const success = await initializeDefaultSettings();
    
    if (success) {
      console.log("✅ Default settings initialized successfully");
    } else {
      console.error("❌ Failed to initialize settings");
      process.exit(1);
    }
  } catch (error) {
    console.error("❌ Error initializing settings:", error);
    process.exit(1);
  }
}

main();
