import { db } from "../lib/db";
import { hash } from "bcryptjs";
import { config } from "dotenv";

// Load environment variables
config({ path: ".env.local" });

async function seed() {
  console.log("🌱 Seeding database...");

  try {
    // Create default super admin
    const hashedPassword = await hash("Admin123!", 10);
    
    console.log("Creating super admin user...");
    await db.query(
      `INSERT INTO users 
         (name, email, username, password_hash, role, is_profile_updated, created_at, updated_at)
       VALUES 
         ($1, $2, $3, $4, $5, $6, $7, $8)
       ON CONFLICT (email) DO NOTHING`,
      [
        "Administrator",
        "<EMAIL>",
        "admin",
        hashedPassword,
        "super_admin",
        true,
        new Date(),
        new Date()
      ]
    );
    
    // Create sample house
    console.log("Creating sample house...");
    const houseResult = await db.query(
      `INSERT INTO houses 
         (name, address, created_at, updated_at)
       VALUES 
         ($1, $2, $3, $4)
       ON CONFLICT (name) DO NOTHING
       RETURNING id`,
      [
        "Lazdynų 5",
        "Lazdynų g. 5, Vilnius",
        new Date(),
        new Date()
      ]
    );
    
    if (houseResult.rows.length > 0) {
      const houseId = houseResult.rows[0].id;
      
      // Create sample flats for this house
      console.log("Creating sample flats...");
      
      for (let i = 1; i <= 10; i++) {
        await db.query(
          `INSERT INTO flats 
             (house_id, number, floor, created_at, updated_at)
           VALUES 
             ($1, $2, $3, $4, $5)
           ON CONFLICT (house_id, number) DO NOTHING`,
          [
            houseId,
            `${i}`,
            Math.ceil(i / 2),
            new Date(),
            new Date()
          ]
        );
      }
      
      // Create sample users with different roles
      console.log("Creating sample users...");
      const editorPassword = await hash("Editor123!", 10);
      const userPassword = await hash("User123!", 10);
      
      // Add editor user
      await db.query(
        `INSERT INTO users 
           (name, email, username, password_hash, role, is_profile_updated, created_at, updated_at)
         VALUES 
           ($1, $2, $3, $4, $5, $6, $7, $8)
         ON CONFLICT (email) DO NOTHING`,
        [
          "Editor User",
          "<EMAIL>",
          "editor",
          editorPassword,
          "editor",
          true,
          new Date(),
          new Date()
        ]
      );
      
      // Get first flat ID
      const flatResult = await db.query(
        `SELECT id FROM flats WHERE house_id = $1 ORDER BY number LIMIT 1`,
        [houseId]
      );
      
      if (flatResult.rows.length > 0) {
        const flatId = flatResult.rows[0].id;
        
        // Add standard user
        await db.query(
          `INSERT INTO users 
             (name, email, username, password_hash, role, is_profile_updated, flat_id, created_at, updated_at)
           VALUES 
             ($1, $2, $3, $4, $5, $6, $7, $8, $9)
           ON CONFLICT (email) DO NOTHING`,
          [
            "Standard User",
            "<EMAIL>",
            "5-1",
            userPassword,
            "user",
            false,
            flatId,
            new Date(),
            new Date()
          ]
        );
      }
    }
    
    console.log("✅ Seed completed successfully!");
  } catch (error) {
    console.error("❌ Seed failed:", error);
    process.exit(1);
  }
}

// Run the seed function
seed()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Unhandled error during seed:", error);
    process.exit(1);
  });