#!/usr/bin/env node
/**
 * <PERSON>ript to generate environment variables for Coolify deployment
 * 
 * Usage:
 *   node scripts/coolify-generate-env.js [--output .env.coolify]
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Generate a secure random string
function generateSecureString(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

// Create environment variables for Coolify
function generateCoolifyEnv() {
  const env = {
    // Application settings
    NODE_ENV: 'production',
    NEXT_TELEMETRY_DISABLED: '1',
    
    // Supabase Configuration (to be updated with actual values)
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co',
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-supabase-anon-key',
    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-supabase-service-role-key',
    
    // Application specific settings
    EMAIL_DEV_MODE: 'live',
    EMAIL_DEV_RECIPIENT: '<EMAIL>',
    
    // Security (generate secure values)
    CSRF_SECRET: generateSecureString(32),
    CRON_API_KEY: generateSecureString(24),
    
    // Analytics & Logging
    NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY || 'your-posthog-project-api-key',
    NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://app.posthog.com',
    LOG_LEVEL: 'info',
    
    // Feature Flags
    ENABLE_REGISTRATION: 'true',
    ENABLE_PASSWORD_RESET: 'true',
    
    // External Services (optional)
    // CLOUDINARY_URL: 'cloudinary://api_key:api_secret@cloud_name',
    // GOOGLE_MAPS_API_KEY: 'your-google-maps-api-key',
  };
  
  return env;
}

// Convert environment object to string format
function envToString(env) {
  return Object.entries(env)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');
}

// Write environment variables to file
function writeEnvFile(env, outputPath) {
  try {
    fs.writeFileSync(outputPath, envToString(env));
    console.log(`Environment variables written to ${outputPath}`);
    console.log('\nImportant environment values that need manual updates:');
    console.log('1. NEXT_PUBLIC_SUPABASE_URL - Set to your Supabase URL (self-hosted or cloud)');
    console.log('2. NEXT_PUBLIC_SUPABASE_ANON_KEY - Set to your Supabase anon key');
    console.log('3. SUPABASE_SERVICE_ROLE_KEY - Set to your Supabase service role key');
    console.log('4. NEXT_PUBLIC_POSTHOG_KEY - Set to your PostHog project API key');
    console.log('5. SMTP settings are stored in database - configure via Admin → Settings\n');
  } catch (error) {
    console.error(`Error writing environment file: ${error.message}`);
    process.exit(1);
  }
}

// Display environment variables to console
function displayEnv(env) {
  console.log('\n=== Coolify Environment Variables ===\n');
  
  // Format as key=value pairs for easy copy-paste
  console.log(envToString(env));
  
  console.log('\n=== End of Environment Variables ===\n');
  console.log('Copy these values to your Coolify environment variables configuration.');
  console.log('Important: Make sure to update Supabase and PostHog settings with actual values.');
  console.log('Note: SMTP settings are stored in database - configure via Admin → Settings.');
}

// Main function
function main() {
  const args = process.argv.slice(2);
  const outputArg = args.find(arg => arg.startsWith('--output='));
  const outputPath = outputArg 
    ? outputArg.split('=')[1] 
    : path.join(process.cwd(), '.env.coolify');
  
  const env = generateCoolifyEnv();
  
  // If output file is specified, write to file
  if (args.includes('--output') || outputArg) {
    writeEnvFile(env, outputPath);
  } else {
    // Otherwise just display to console
    displayEnv(env);
  }
}

// Run the script
main();
