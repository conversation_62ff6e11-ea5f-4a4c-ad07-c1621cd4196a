// Simple script to check PostHog environment variables
console.log('Checking PostHog environment variables...');

try {
  const fs = require('fs');
  const path = require('path');
  
  // Try to load .env.local file
  const envPath = path.join(__dirname, '.env.local');
  
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    console.log('\n.env.local file exists. Checking for PostHog variables:');
    
    const posthogKeyMatch = envContent.match(/NEXT_PUBLIC_POSTHOG_KEY=(.+)/);
    const posthogHostMatch = envContent.match(/NEXT_PUBLIC_POSTHOG_HOST=(.+)/);
    
    if (posthogKeyMatch) {
      const key = posthogKeyMatch[1].trim();
      console.log(`✅ NEXT_PUBLIC_POSTHOG_KEY is set to: ${key.substring(0, 5)}...${key.substring(key.length - 5)}`);
    } else {
      console.log('❌ NEXT_PUBLIC_POSTHOG_KEY is not set in .env.local');
    }
    
    if (posthogHostMatch) {
      console.log(`✅ NEXT_PUBLIC_POSTHOG_HOST is set to: ${posthogHostMatch[1].trim()}`);
    } else {
      console.log('❌ NEXT_PUBLIC_POSTHOG_HOST is not set in .env.local');
    }
  } else {
    console.log('\n❌ .env.local file does not exist. This is required for PostHog configuration.');
  }
  
  // Check package.json for posthog dependency
  const packagePath = path.join(__dirname, 'package.json');
  if (fs.existsSync(packagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    if (packageJson.dependencies && packageJson.dependencies['posthog-js']) {
      console.log(`\n✅ posthog-js is installed (version: ${packageJson.dependencies['posthog-js']})`);
    } else {
      console.log('\n❌ posthog-js dependency is not found in package.json');
    }
  }
  
  console.log('\nRecommended next steps:');
  console.log('1. Check the browser console for any PostHog errors');
  console.log('2. Visit /examples/posthog-debug to see detailed PostHog configuration');
  console.log('3. Ensure your IP address is not blocked in PostHog');
  
} catch (error) {
  console.error('Error checking environment:', error);
}