# 🏢 DNSB Vakarai - Namo Gyventojų Komunikacijos Portalas

<div align="center">
  
![Next.js](https://img.shields.io/badge/Next.js-15.2.4-black?style=for-the-badge&logo=next.js)
![React](https://img.shields.io/badge/React-19.0.0-61DAFB?style=for-the-badge&logo=react)
![PostgreSQL](https://img.shields.io/badge/PostgreSQL-latest-336791?style=for-the-badge&logo=postgresql)
![TailwindCSS](https://img.shields.io/badge/Tailwind-3.4-38B2AC?style=for-the-badge&logo=tailwind-css)
![Supabase](https://img.shields.io/badge/Supabase-latest-3ECF8E?style=for-the-badge&logo=supabase)

</div>

DNSB Vakarai – modernios komunikacijos platforma daugiabučio namo bendrijai, kurioje administratoriai ir gyventojai gali efektyviai komunikuoti, dalintis svarbia informacija ir valdyti daugiabučio dokumentus.

## ✨ Pagrindinės funkcijos

<table>
  <tr>
    <td width="50%">
      <h3>🔔 Pranešimai ir komunikacija</h3>
      <ul>
        <li>Tikslinis pranešimų siuntimas (visiems, namui, butui ar konkretiems vartotojams)</li>
        <li>Pranešimų perskaitymo sekimas</li>
        <li>In-app ir el. pašto pranešimai</li>
      </ul>
    </td>
    <td width="50%">
      <h3>👤 Vartotojų administravimas</h3>
      <ul>
        <li>Masinis vartotojų importas iš Excel failo</li>
        <li>Patogus profilio valdymas</li>
        <li>Išsami kontaktų valdymo sistema</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td width="50%">
      <h3>🏠 Namų ir butų valdymas</h3>
      <ul>
        <li>Automatinis namų ir butų sukūrimas importo metu</li>
        <li>Intuityvus ryšys tarp vartotojų ir butų</li>
        <li>Patobulintas kontaktų valdymas</li>
      </ul>
    </td>
    <td width="50%">
      <h3>🔐 Saugumo ir administravimo galimybės</h3>
      <ul>
        <li>Išsami auditavimo sistema</li>
        <li>Keturios vartotojų rolės (Developer, Super Admin, Editor, User)</li>
        <li>Detali veiksmų analizė ir protokolavimas</li>
        <li>CSRF apsauga visose POST užklausose</li>
      </ul>
    </td>
  </tr>
</table>

## 🚀 Naujausi patobulinimai

### 🎯 Sentry ir PostHog integracija (2025-01)

- **Klaidų sekimas su Sentry**:
  - Automatinis klaidų registravimas ir raportavimas
  - Detalūs klaidų kontekstai ir stack trace
  - Našumo monitoringas

- **Analitika su PostHog**:
  - Vartotojų elgsenos sekimas
  - Funkcijų naudojimo statistika
  - A/B testavimo palaikymas

### 📧 PGMQ el. pašto eilės sistema (2025-01)

- **Patikima el. pašto siuntimo sistema**:
  - PostgreSQL pagrindu veikianti eilės sistema (PGMQ)
  - Automatinis pakartojimas su exponential backoff
  - Darbo procesų valdymas
  - Vartotojų preferencijų palaikymas

### 🏠 Patrauklus priekinis puslapis (2024-08-31)

- **Profesionalus įėjimo puslapis**:
  - Modernus ir informatyvus priekinis puslapis
  - Responsive dizainas, pritaikytas mobiliesiems įrenginiams
  - Keičiamos nuotraukos ir lengvai pritaikomas turinys

- **Informatyvus turinys**:
  - Bendrijos veiklos aprašymas ir teikiamos paslaugos
  - Svarbūs kontaktiniai numeriai
  - Patogios prisijungimo nuorodos

<details>
<summary>📄 Daugiau informacijos</summary>
Daugiau informacijos apie šį funkcionalumą rasite <a href="./LANDING_PAGE.md">LANDING_PAGE.md</a> faile.
</details>

### 🔄 Next.js 15.2.4 ir React 19 atnaujinimas (2024-12)

- **Maršrutų parametrų struktūros atnaujinimas**:
  - Atnaujinta API maršrutų struktūra pagal naujas specifikacijas
  - Pataisyti visi dinaminiai maršrutai 
  - Sustiprintos tipų saugos funkcijos

<details>
<summary>📄 Daugiau informacijos</summary>
Daugiau informacijos apie šį atnaujinimą rasite <a href="./UPDATE.md">UPDATE.md</a> faile.
</details>

### 👥 Kontaktų valdymo atnaujinimai (2024-07-04)

- **Išplėstos ryšio pasirinkimo galimybės**:
  - Naujos ryšio parinktys: "Buto savininkas" ir "Buto nuomininkas"
  - Individualūs ryšių aprašymai
  - Ekstremalių situacijų kontaktų žymėjimas

### 💾 Duomenų bazės supaprastinimas (2024-07-15)

- **Pašalintas Drizzle ORM**:
  - Supaprastinta duomenų prieiga naudojant SQL
  - Stabilesni API maršrutai
  - Aiškesnė schema ir dokumentacija

## 🛠️ Technologijos

<div align="center">
  
| Kategorija | Technologija |
|------------|--------------|
| **Frontend** | Next.js 15.2.4 (App Router), React 19.0.0 |
| **Duomenų bazė** | Supabase PostgreSQL |
| **Duomenų prieiga** | Supabase klientas + TanStack Query |
| **Eilės sistema** | PGMQ (PostgreSQL Message Queue) |
| **Stilius** | Tailwind CSS 3.4, shadcn/ui komponentai |
| **Autentifikacija** | Supabase Auth su vartotojo vardu/slaptažodžiu |
| **El. paštas** | SMTP2Go + PGMQ eilės sistema |
| **Saugumas** | bcryptjs, isomorphic-dompurify |
| **Klaidų sekimas** | Sentry |
| **Analitika** | PostHog |

</div>

## 🚀 Pradžia

### 📋 Reikalavimai

- Node.js 18+ versija
- Supabase projektas
- NPM arba pnpm

### ⚙️ Instaliavimas

<details>
<summary>Išplėsti instaliavimo instrukcijas</summary>

1. Klonuokite repozitoriją:
```bash
git clone https://github.com/jusu-vartotojai/dnsb-vakarai.git
cd dnsb-vakarai
```

2. Įdiekite priklausomybes:
```bash
npm install
# arba
pnpm install
```

3. Sukonfigūruokite aplinkos kintamuosius (žr. [Aplinkos kintamieji](#aplinkos-kintamieji))

4. Paleiskite aplikaciją:
```bash
npm run dev
```

5. Atidarykite naršyklėje [http://localhost:3000](http://localhost:3000)
</details>


## 📁 Projekto struktūra

```
dnsb-vakarai/
├── app/                    # Next.js App Router
│   ├── auth/               # Autentifikacijos puslapiai
│   ├── dashboard/          # Valdymo skydelio puslapiai
│   └── api/                # API maršrutai
├── components/             # React komponentai
│   ├── ui/                 # UI komponentai (shadcn/ui)
│   └── dashboard/          # Valdymo skydelio komponentai
├── lib/                    # Pagalbinės funkcijos
│   ├── supabase/           # Supabase klientas ir konfigūracija
│   ├── tanstack/           # TanStack Query konfigūracija
│   └── utils/              # Įvairios pagalbinės funkcijos
├── __tests__/              # Jest testai
├── scripts/                # Utility skriptai
├── public/                 # Statiniai failai
└── docker/                 # Docker konfigūracija
```

## 🤖 MCP (Model Context Protocol)

Projektas palaiko MCP integraciją AI asistentams. Nukopijuokite `.mcp.json.template` į `.mcp.json` ir atnaujinkite su savo kredencialais:

```bash
cp .mcp.json.template .mcp.json
```

Daugiau informacijos: [docs/MCP_SETUP.md](./docs/MCP_SETUP.md)

## 🔧 Aplinkos kintamieji

Sukurkite `.env.local` failą su šiais kintamaisiais:

```
# Supabase
NEXT_PUBLIC_SUPABASE_URL=jūsų_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=jūsų_anon_key
SUPABASE_SERVICE_ROLE_KEY=jūsų_service_role_key

# SMTP2Go
SMTP_HOST=mail.smtp2go.com
SMTP_PORT=587
SMTP_USER=jūsų_smtp_vartotojas
SMTP_PASSWORD=jūsų_smtp_slaptažodis
SMTP_FROM=<EMAIL>

# Sentry (neprivaloma)
NEXT_PUBLIC_SENTRY_DSN=jūsų_sentry_dsn
SENTRY_ORG=jūsų_organizacija
SENTRY_PROJECT=jūsų_projektas

# PostHog (neprivaloma)
NEXT_PUBLIC_POSTHOG_KEY=jūsų_posthog_raktas
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
```

## 🗄️ Duomenų bazės valdymas

<details>
<summary>Duomenų bazės valdymo komandos</summary>

```bash
# Patikrinti Supabase ryšį
npm run supabase:test

# Patikrinti administravimo paskyrų būseną
npx tsx scripts/check-admins.ts

# Patikrinti prisijungimo duomenis
npx tsx scripts/verify-login.ts
```

</details>

## 📧 El. pašto eilės valdymas

<details>
<summary>El. pašto sistemos komandos</summary>

```bash
# Apdoroti el. pašto eilę (rankiniu būdu)
npm run email:process

# Testuoti PGMQ el. pašto funkcionalumą
npm run email:test-pgmq
```

</details>

## 🧪 Testavimas

<details>
<summary>Testavimo komandos</summary>

```bash
# Paleisti visus testus
npm run test

# Paleisti testus stebėjimo režimu
npm run test:watch

# Paleisti lintinimą
npm run lint
```

</details>

## 🚀 Diegimas / Deployment

### Coolify Deployment

Projektas yra optimizuotas diegimui naudojant Coolify platformą:

```bash
# Patikrinti vystymosi aplinką
npm run dev:verify

# Generuoti aplinkos kintamuosius diegimui
npm run deploy:env

# Patikrinti diegimo sėkmę (reikia veikiančios aplikacijos)
npm run deploy:verify
```

Detalūs diegimo nurodymai: [docs/COOLIFY_DEPLOYMENT.md](./docs/COOLIFY_DEPLOYMENT.md)

### Docker Compose

Projektas palaiko Docker Compose diegimą:

```bash
# Sukurti .env failą su reikiamais kintamaisiais
cp .env.example .env

# Paleisti su Docker Compose
docker-compose up -d
```

## 📄 Projektų failai

<div align="center">

| Failas | Aprašymas |
|--------|-----------|
| [CLAUDE.md](./CLAUDE.md) | Projekto gairės AI asistentui |
| [docs/README.md](./docs/README.md) | Dokumentacijos apžvalga |
| [docs/COOLIFY_DEPLOYMENT.md](./docs/COOLIFY_DEPLOYMENT.md) | Coolify diegimo gidas |
| [docs/JEST_TESTING_GUIDE.md](./docs/JEST_TESTING_GUIDE.md) | Testavimo gidas |
| [docs/LANGUAGE_GUIDELINES.md](./docs/LANGUAGE_GUIDELINES.md) | Kalbos naudojimo gairės |
| [docs/MCP_SETUP.md](./docs/MCP_SETUP.md) | MCP konfigūracijos instrukcijos |
| [SECURITY.md](./SECURITY.md) | Saugumo dokumentacija |

</div>

## 📝 Licencija

Šis projektas yra licencijuotas pagal MIT licenciją.

## Form Handling with React 19

This project implements the latest React 19 and Next.js 15 form handling patterns:

1. **Server Actions**: Asynchronous functions that execute on the server
   ```typescript
   'use server'
   export async function submitContactForm(prevState: any, formData: FormData) {
     // Process form data on the server
   }
   ```

2. **useActionState Hook**: For form state management
   ```typescript
   const [state, formAction] = useActionState(submitContactForm, initialState);
   ```

3. **useFormStatus Hook**: For handling loading states
   ```typescript
   // In a client component
   function SubmitButton() {
     const { pending } = useFormStatus();
     return (
       <button disabled={pending}>
         {pending ? 'Submitting...' : 'Submit'}
       </button>
     );
   }
   ```

## Street-House-Flat Hierarchy for Announcements

The system supports a hierarchical structure for residents and addresses:

- Streets contain houses
- Houses contain flats
- Flats are assigned to users

The announcements system allows administrators to send targeted messages to:

1. All users
2. Users on specific streets
3. Users in specific houses  
4. Users in specific flats
5. Specific users

When creating or editing an announcement, admins can:
- Filter by street, house, or flat
- Search for addresses using the search field
- See properly formatted addresses with street names, house numbers, and flat numbers
- Navigate through hierarchical tabs (Streets → Houses → Flats → Users)

The system excludes admin and editor users from the recipients list when sending announcements to regular users.

### Database Structure

The database implements the following schema:

```
streets
  ↓
houses
  ↓ 
flats
  ↓
users
```

The streets table was added to support this hierarchical filtering.
