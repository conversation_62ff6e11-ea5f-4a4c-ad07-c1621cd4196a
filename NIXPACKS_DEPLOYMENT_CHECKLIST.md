# Nixpacks Deployment Checklist for DNSB Vakarai

## Pre-Deployment Verification

### 1. Local Testing
- [ ] Run `npm run build` locally to ensure build succeeds
- [ ] Test with Node.js 20: `nvm use 20 && npm run build`
- [ ] Verify `.next/standalone/server.js` is created after build
- [ ] Run `cd .next/standalone && node server.js` to test standalone server

### 2. Environment Variables
Ensure all required environment variables are set in Coolify:

#### Core Application
- [ ] `NODE_ENV=production`
- [ ] `NEXT_TELEMETRY_DISABLED=1`
- [ ] `PORT=3000`
- [ ] `HOSTNAME=0.0.0.0`

#### Supabase Configuration
- [ ] `NEXT_PUBLIC_SUPABASE_URL` (your Supabase URL)
- [ ] `NEXT_PUBLIC_SUPABASE_ANON_KEY` (your anon key)
- [ ] `SUPABASE_SERVICE_ROLE_KEY` (your service role key)

#### Security
- [ ] `CSRF_SECRET` (generate new for production)
- [ ] `CRON_API_KEY` (generate new for production)

#### Analytics (Optional)
- [ ] `NEXT_PUBLIC_POSTHOG_KEY`
- [ ] `NEXT_PUBLIC_POSTHOG_HOST`

#### Features
- [ ] `ENABLE_REGISTRATION=true`
- [ ] `ENABLE_PASSWORD_RESET=true`

### 3. Nixpacks Configuration
- [ ] `nixpacks.toml` is committed to repository
- [ ] Configuration uses Node.js 20 (for stability)
- [ ] Standalone output mode is configured

## Deployment Steps

### 1. Prepare Code
```bash
# Ensure you're on the correct branch
git checkout main

# Run lint and tests
npm run lint
npm run test

# Commit the nixpacks.toml
git add nixpacks.toml
git commit -m "feat: Add Nixpacks configuration for deployment"
git push origin main
```

### 2. Configure Coolify/Dokploy

#### Build Pack Settings
- [ ] Build Pack: **Nixpacks**
- [ ] Base Directory: `/` (root)
- [ ] Install Command: *Leave empty (handled by nixpacks.toml)*
- [ ] Build Command: *Leave empty (handled by nixpacks.toml)*
- [ ] Start Command: *Leave empty (handled by nixpacks.toml)*
- [ ] Port: `3000`

#### Environment Variables
1. Go to Environment Variables section
2. Add all variables from the checklist above
3. Use the "Bulk Edit" feature with the template from `production-env-template.txt`

#### Advanced Settings
- [ ] Health Check Path: `/api/health`
- [ ] Health Check Interval: `30` seconds
- [ ] Resource Limits: Set based on your server capacity

### 3. Deploy
1. [ ] Click "Deploy" in Coolify
2. [ ] Monitor build logs for any errors
3. [ ] Wait for build to complete (typically 3-5 minutes)

## Post-Deployment Verification

### 1. Application Health
- [ ] Check health endpoint: `curl https://your-domain/api/health`
- [ ] Verify homepage loads
- [ ] Test authentication (login/logout)

### 2. Functionality Tests
- [ ] User login works (username: "31-7" format)
- [ ] Admin panel accessible for admin users
- [ ] Email preferences load correctly
- [ ] Announcements display properly
- [ ] Database queries execute successfully

### 3. Performance Checks
- [ ] Page load times are acceptable
- [ ] No memory leaks (monitor for 24h)
- [ ] CPU usage is normal
- [ ] Database connections are stable

### 4. Monitoring Setup
- [ ] PostHog events are being tracked
- [ ] Sentry errors are being reported (if configured)
- [ ] Application logs are accessible in Coolify

## Troubleshooting

### Build Failures

#### Node.js Version Issues
If you see "undefined variable 'nodejs_22'":
```bash
# Use the Node.js 20 configuration
cp nixpacks-alternatives.toml nixpacks.toml
git add nixpacks.toml
git commit -m "fix: Use Node.js 20 for Nixpacks compatibility"
git push
```

#### Environment Variable Issues
- Ensure all `NEXT_PUBLIC_` variables are set
- Check for typos in variable names
- Verify values don't contain special characters that need escaping

### Runtime Issues

#### Application Won't Start
1. Check Coolify logs for error messages
2. Verify port 3000 is configured correctly
3. Ensure standalone build completed successfully

#### Database Connection Errors
1. Verify Supabase URLs are correct
2. Check service role key is valid
3. Ensure network connectivity to Supabase

## Rollback Plan

If deployment fails:

1. **Keep Previous Deployment Running**
   - Don't delete the old deployment until new one is verified

2. **Quick Rollback**
   ```bash
   # Revert to previous commit
   git revert HEAD
   git push origin main
   # Redeploy
   ```

3. **Switch Back to Docker**
   - Change Build Pack back to "Docker Compose" in Coolify
   - Ensure `docker-compose.yml` and `Dockerfile` are still present

## Success Indicators

- [ ] Build completes without errors
- [ ] Application starts and responds to health checks
- [ ] All core functionality works as expected
- [ ] Performance metrics are within acceptable ranges
- [ ] No errors in application logs

## Next Steps After Successful Deployment

1. Monitor application for 24-48 hours
2. Set up automated backups if not already configured
3. Configure monitoring alerts
4. Document any custom configurations
5. Update team on new deployment process

## Additional Resources

- [Nixpacks Documentation](https://nixpacks.com/docs)
- [Coolify Nixpacks Guide](https://coolify.io/docs/knowledge-base/buildpacks/nixpacks)
- [Next.js Deployment Best Practices](https://nextjs.org/docs/deployment)
- Project-specific docs: `docs/NIXPACKS_TROUBLESHOOTING.md`