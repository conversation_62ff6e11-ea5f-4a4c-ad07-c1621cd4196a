{"permissions": {"allow": ["Bash(/Users/<USER>/Desktop/Dev/dnsb-vakarai/scripts/test-polls.sh:*)", "Bash(/Users/<USER>/Desktop/Dev/dnsb-vakarai/scripts/test-feedback.sh:*)", "mcp tools", "Bash(git add:*)", "Bash(npm run dev:*)", "Bash(npm install:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm run lint)", "mcp__supabase__execute_sql"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase", "postgres", "filesystem", "git", "Context7"]}