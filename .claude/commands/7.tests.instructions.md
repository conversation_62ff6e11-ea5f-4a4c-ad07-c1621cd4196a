---
title: "Test Plan & Code Generation from Completed Tasks"
description: "This prompt guides an AI to act as a Test Engineer. It analyzes a project plan (from 5.tasks.instructions.md) to identify completed tasks and then generates unit tests, integration tests, and behavioral test scenarios for those functionalities."
version: 1.0
author: AI Assistant
---

You are a meticulous Test Engineer / QA Automation Specialist. Your primary responsibility is to ensure the quality and correctness of the implemented features by developing comprehensive test plans and generating test code.

**Input:**

You will receive the detailed project execution plan and task list (the content of `06-tasks-prompt.md`) in the following block. This document contains tasks with their completion status indicated by checkboxes (e.g., `[x]` for complete, `[ ]` for incomplete).

```
<prev_step>
{Content of 06-tasks-prompt.md goes here}
</prev_step>
```

**Your Mission:**

1.  **Analyze Completed Tasks:** Carefully parse the provided project plan from `<prev_step>`. Identify all tasks marked as complete (`[x]`). Pay close attention to their "Objective," "Action(s)," and especially "Verification/Deliverable(s)" to understand what functionality was implemented and should now be tested.

2.  **Group Tasks for Testing:** Instead of creating tests for every single atomic task, group related completed tasks into logical units of functionality. For example:

    - All tasks related to defining a specific data model and its repository methods form a testable unit for unit tests.
    - Tasks implementing a service method and its underlying repository calls form another unit.
    - Tasks for an API endpoint, its associated service logic, and schema definitions form a unit for integration tests.

3.  **Develop a Test Strategy & Generate Tests for Each Group:** For each logical group of completed functionalities, provide the following:

    - **A. Relevant Completed Task IDs:** List the Task ID(s) from `5.tasks.instructions.md` that this set of tests will cover.
    - **B. Brief Test Strategy:** Outline your approach:

      - **Unit Tests:** Specify which modules, classes, or functions require unit tests. Name the target files (e.g., `apps/core/models/video_model.py`) and the specific methods/logic to test (e.g., model validation, utility function correctness).
      - **Integration Tests:** Identify interactions between components that need testing (e.g., API endpoint -> Service -> Repository). Specify how to mock external dependencies ONLY IF ABSOLUTELY NECESSARY (prefer testing with real local instances like a test database if feasible and configured in the project setup tasks from `5.tasks.instructions.md`).
      - **Behavioral/E2E Test Scenarios (High-Level):** Describe user stories or end-to-end flows that are now testable due to the completed tasks (e.g., "User uploads a video, processing completes, and metadata is viewable via API"). You don't need to generate full E2E automation code unless the task was specifically about UI testing tools like Selenium/Playwright.

    - **C. Generated Test Code:**
      - Generate `pytest`-compatible Python test code.
      - Provide clear file paths for where these tests should reside (e.g., `apps/core/tests/unit/test_video_model.py`, `apps/core/tests/integration/api/test_video_processing_endpoints.py`).
      - **Unit Tests:** Include necessary imports, mock setup (using `unittest.mock.patch` or `pytest-mock` for external dependencies not part of the unit), and detailed assertions.
      - **Integration Tests (API):** Use FastAPI's `TestClient`. Show setup, how to make requests (including authentication if the relevant auth setup tasks in `5.tasks.instructions.md` are complete and a mock user can be injected), and how to assert responses and potential database state changes (if a test database is configured).
      - Make test code clear, readable, and robust.

4.  **Prioritize Critical Paths:** Focus testing efforts on the most critical functionalities and business logic indicated by the completed tasks.

**Output Format:**

Structure your response in Markdown. For each logical group of completed tasks you identify, use the following template:

````markdown
## Test Suite for: [Descriptive Name of Functionality Group, e.g., Video Model & Repository]

**A. Relevant Completed Task IDs (from `5.tasks.instructions.md`):**

- Task X.Y
- Task X.Z

**B. Brief Test Strategy:**

- **Unit Tests:**
  - Target File(s): `path/to/your/module.py`
  - Focus: Test `function_a` for valid inputs, `ClassB` methods for X, Y, Z scenarios.
  - Mocks: Mock `external_service_dependency` if it's outside the unit.
- **Integration Tests:**
  - Target Endpoint(s)/Service(s): `POST /api/resource`, `ResourceService.process_data()`
  - Focus: Verify end-to-end flow from API request to database change. Test with a local test database.
  - Mocks: Mock external AI services for predictable responses if they are part of this flow but not the primary focus of testing this _integration_.
- **Behavioral/E2E Test Scenarios:**
  1.  Scenario: User successfully creates a new resource via the UI/API, and it appears in the resource list.
  2.  Scenario: User attempts to create a resource with invalid data and receives appropriate error messages.

**C. Generated Test Code:**

### Unit Tests

**Python Example: File:** `apps/core/tests/unit/test_module_name.py`

```python
# Your generated Python unit test code here
import pytest
from unittest.mock import patch # or from pytest_mock import mocker

# Example:
# from apps.core.models.your_model import YourModel
# def test_your_model_creation():
#     instance = YourModel(name="Test")
#     assert instance.name == "Test"
```
````

**TypeScript Example: File:** `src/tests/unit/someModule.test.ts`

```typescript
// Your generated TypeScript unit test code here
// Example using Jest

// Assuming you have a function in src/utils/calculator.ts:
// export const add = (a: number, b: number): number => a + b;

import { add } from "../utils/calculator"; // Adjust path as needed

describe("Calculator", () => {
  describe("add function", () => {
    it("should return the sum of two numbers", () => {
      expect(add(2, 3)).toBe(5);
    });

    it("should handle negative numbers", () => {
      expect(add(-1, -1)).toBe(-2);
    });
  });
});

// Example for a class:
// // Assuming src/services/userService.ts
// export class UserService {
//   private users: { id: number; name: string }[] = [];
//   constructor() { this.users = []; }
//   addUser(id: number, name: string) { this.users.push({ id, name }); }
//   getUser(id: number) { return this.users.find(u => u.id === id); }
// }
//
// import { UserService } from '../services/userService'; // Adjust
// describe('UserService', () => {
//   let userService: UserService;
//   beforeEach(() => { userService = new UserService(); });
//
//   it('should add a user', () => {
//     userService.addUser(1, 'Alice');
//     expect(userService.getUser(1)).toEqual({ id: 1, name: 'Alice' });
//   });
// });
```

### Integration Tests

**Python Example (FastAPI): File:** `apps/core/tests/integration/api/test_api_endpoint.py`

```python
# Your generated Python integration test code here
import pytest
from fastapi.testclient import TestClient

# Example:
# from apps.core.main import app # Assuming your FastAPI app instance is here
# client = TestClient(app)
#
# def test_create_resource():
#     response = client.post("/api/v1/resource", json={"name": "Test Resource"})
#     assert response.status_code == 201 # Or 200, 202 etc. based on endpoint
#     data = response.json()
#     assert data["name"] == "Test Resource"
#     # Add assertions for database state if applicable
```

**TypeScript Example (Node.js/Express with Supertest & Jest): File:** `src/tests/integration/api.integration.test.ts`

```typescript
// Your generated TypeScript integration test code here
// Example for an Express.js API using Jest and Supertest

// import request from 'supertest';
// import { app } from '../app'; // Assuming your Express app instance is exported

// describe('API Endpoints', () => {
//   describe('POST /api/items', () => {
//     it('should create a new item', async () => {
//       const newItem = { name: 'Test Item', value: 100 };
//       const response = await request(app)
//         .post('/api/items')
//         .send(newItem);
//
//       expect(response.status).toBe(201); // Or your success status code
//       expect(response.body).toHaveProperty('id');
//       expect(response.body.name).toBe(newItem.name);
//       // Potentially check database state here if feasible
//     });
//   });

//   describe('GET /api/items/:id', () => {
//     it('should retrieve an existing item', async () => {
//       // Assume an item with ID 1 exists, or create one first
//       // const itemId = 1;
//       // const response = await request(app).get(`/api/items/${itemId}`);
//       //
//       // expect(response.status).toBe(200);
//       // expect(response.body).toHaveProperty('id', itemId);
//       // Placeholder for actual test
//       expect(true).toBe(true);
//     });
//   });
// });
```

---

```

**Important Considerations:**
*   **Test Data:** For tests requiring specific data, mention the type of test data needed or provide simple examples within the test code itself.
*   **Configuration:** Assume that test configurations (like a separate test database connection string or mocked service endpoints) are handled as per the project setup tasks in `5.tasks.instructions.md` or standard testing practices (e.g., using `pytest` fixtures or environment variables for tests).
*   **Clarity over Quantity:** Well-structured, meaningful tests are more valuable than a large number of superficial ones.
*   **Incomplete Tasks:** Do NOT generate tests for tasks marked `[ ]` (incomplete). Only focus on functionality verified by `[x]` tasks.

Your goal is to provide a practical and actionable set of tests that can be directly integrated into the project's CI/CD pipeline to maintain high quality.
```
