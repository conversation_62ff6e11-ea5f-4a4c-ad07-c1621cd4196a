import { Calculator, PhoneCall, User, Zap } from 'lucide-react';
import styles from '@/app/avariniai-kontaktai/page-styles.module.css';

// Define the shape of a contact
export interface AssociationContact {
  id: string;
  role: string;
  name?: string;
  phone: string;
  icon: 'user' | 'calculator' | 'zap' | 'phone';
}

// Default contacts - these would ideally come from a database
const DEFAULT_CONTACTS: AssociationContact[] = [
  {
    id: '1',
    role: 'Pirmininkas',
    phone: '+370 61630230',
    icon: 'user',
  },
  {
    id: '2',
    role: 'Buhalterė',
    phone: '+370 61630989',
    icon: 'calculator',
  },
  {
    id: '3',
    role: 'Elektros paslaugos (UAB "Jubis")',
    phone: '+370 67719115',
    icon: 'zap',
  },
];

// Map of icon names to Lucide components
const ICON_MAP = {
  user: User,
  calculator: Calculator,
  zap: Zap,
  phone: PhoneCall,
};

interface AssociationContactsProps {
  title?: string;
  contacts?: AssociationContact[];
  showBorder?: boolean;
  className?: string;
}

export function AssociationContacts({
  title = 'Bendrijos kontaktai',
  contacts = DEFAULT_CONTACTS,
  showBorder = true,
  className = '',
}: AssociationContactsProps) {
  return (
    <div className={`${styles.associationSection} ${className}`}>
      {title && <h2 className={styles.associationTitle}>{title}</h2>}
      <div className={styles.associationContacts}>
        {contacts.map((contact) => {
          const Icon = ICON_MAP[contact.icon];
          return (
            <div key={contact.id} className={styles.contactPerson}>
              <Icon className="h-5 w-5 text-blue-600" />
              <span className={styles.personRole}>{contact.role}:</span>
              <a href={`tel:${contact.phone.replace(/\s+/g, '')}`} className={styles.personPhone}>
                {contact.phone}
              </a>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Export a smaller version that can be used in the homepage
export function HomepageContacts() {
  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
      <h3 className="text-lg font-semibold mb-3 text-blue-800 border-b border-blue-100 pb-2">
        Bendrijos kontaktai
      </h3>
      <div className="space-y-2">
        {DEFAULT_CONTACTS.map((contact) => {
          const Icon = ICON_MAP[contact.icon];
          return (
            <div key={contact.id} className="flex items-center justify-between py-1 border-b border-gray-100">
              <div className="flex items-center">
                <Icon className="h-4 w-4 mr-2 text-blue-600" />
                <span className="text-gray-700 font-medium">{contact.role}:</span>
              </div>
              <a href={`tel:${contact.phone.replace(/\s+/g, '')}`} className="text-blue-700 font-semibold">
                {contact.phone}
              </a>
            </div>
          );
        })}
      </div>
    </div>
  );
} 