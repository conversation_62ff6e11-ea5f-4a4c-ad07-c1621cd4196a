"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import * as z from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { Checkbox } from "@/components/ui/checkbox";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Plus, Trash, List, Users, Building, Home, MapPin } from "lucide-react";
import { cn } from "@/lib/utils";
import { 
  useStreets, 
  useHouses, 
  useFlats, 
  useUsers, 
  useCreatePoll,
  invalidateQueries 
} from "@/lib/tanstack/queries";

// --- Data Interfaces (Copied from AnnouncementForm) ---
interface Street {
  id: string;
  name: string;
  city: string;
  displayName?: string; // Example: "Gedimino pr. (Vilnius)"
}

interface House {
  id: string;
  name: string;
  address: string;
  streetId: string;
  streetName: string;
  displayName: string; // Example: "Gedimino pr. 1 (Vilnius)"
}

interface Flat {
  id: string;
  houseId: string;
  number: string;
  floor: number;
  houseName: string;
  streetId: string;
  streetName: string;
  displayName: string; // Example: "Gedimino pr. 1 - 5 (Vilnius)"
}

interface User {
  id: string;
  name: string;
  email: string;
  flatId: string | null;
  houseId: string | null;
  streetId: string | null;
  addressDisplay: string; // Example: "Gedimino pr. 1 - 5"
  displayName: string; // Example: "Vardenis Pavardenis (Gedimino pr. 1 - 5)"
}

// --- Poll Form Schema ---
const pollFormSchema = z.object({
  title: z.string().min(5, { message: "Pavadinimas turi būti bent 5 simbolių ilgio." }),
  description: z.string().min(10, { message: "Aprašymas turi būti bent 10 simbolių ilgio." }),
  audienceType: z.enum(["all", "specific"], {
    required_error: "Pasirinkite gavėjų tipą.",
  }),
  specificType: z.enum(["streets", "houses", "flats", "users"]).optional(),
  streetIds: z.array(z.string()).optional(), // For filtering UI
  houseIds: z.array(z.string()).optional(),
  flatIds: z.array(z.string()).optional(),
  userIds: z.array(z.string()).optional(),
  options: z.array(
    z.object({
      id: z.number().optional(), // Existing option ID for updates
      text: z.string().min(1, { message: "Pasirinkimas negali būti tuščias." }),
      deleted: z.boolean().optional(), // Mark for deletion on update
    })
  ).min(2, { message: "Reikia bent dviejų pasirinkimų." }),
  status: z.enum(["draft", "active"]).default("draft"), // Default to draft
  startDate: z.date().optional().nullable(),
  endDate: z.date().optional().nullable(),
  showResultsAfterVoting: z.boolean().default(true),
}).refine(data => {
    // Validate audience selection based on type
    if (data.audienceType === "specific") {
      if (!data.specificType) return false;
      if (data.specificType === "streets" && (!data.streetIds || data.streetIds.length === 0)) return false;
      if (data.specificType === "houses" && (!data.houseIds || data.houseIds.length === 0)) return false;
      if (data.specificType === "flats" && (!data.flatIds || data.flatIds.length === 0)) return false;
      if (data.specificType === "users" && (!data.userIds || data.userIds.length === 0)) return false;
    }
    return true;
  }, {
    message: "Privalote pasirinkti bent vieną elementą pagal pasirinktą auditorijos tipą.",
    path: ["specificType"], // Attach error to the specific type field
  }).refine(data => {
      // Validate dates: if end date exists, start date must too and be earlier
      if (data.endDate && (!data.startDate || data.startDate >= data.endDate)) {
          return false;
      }
      return true;
  }, {
      message: "Pabaigos data turi būti vėlesnė nei pradžios data.",
      path: ["endDate"],
  });

type PollFormValues = z.infer<typeof pollFormSchema>;

// --- Component Props ---
interface PollFormProps {
  existingPoll?: any; // Adjust type as needed based on actual data structure
  editMode?: boolean;
  userId?: number; // ID of the user creating/editing
}

// --- PollForm Component ---
export function PollForm({ existingPoll, editMode = false, userId }: PollFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);

  // Replace manual state with TanStack Query hooks
  const { data: streets = [], isLoading: streetsLoading } = useStreets();
  const { data: houses = [], isLoading: housesLoading } = useHouses();
  const { data: flats = [], isLoading: flatsLoading } = useFlats();
  const { data: users = [], isLoading: usersLoading } = useUsers({ excludeAdmins: true });
  const createPollMutation = useCreatePoll();
  
  const [selectedStreets, setSelectedStreets] = useState<string[]>([]); // Used for filtering houses/flats/users
  const [selectedHouses, setSelectedHouses] = useState<string[]>([]); // Used for filtering flats/users
  const [filteredHouses, setFilteredHouses] = useState<House[]>([]);
  const [filteredFlats, setFilteredFlats] = useState<Flat[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  
  // Compute loading state from all queries
  const loadingData = streetsLoading || housesLoading || flatsLoading || usersLoading;
  const [searchTerm, setSearchTerm] = useState("");

  // Initialize form
  const form = useForm<PollFormValues>({
    resolver: zodResolver(pollFormSchema),
    defaultValues: {
      title: "",
      description: "",
      audienceType: undefined, // No default - force user to select
      specificType: undefined,
      streetIds: [],
      houseIds: [],
      flatIds: [],
      userIds: [],
      options: [{ text: "" }, { text: "" }], // Start with two empty options
      status: "draft",
      startDate: null,
      endDate: null,
      showResultsAfterVoting: true,
    },
  });

  // Field array for poll options
  const { fields: optionFields, append: appendOption, remove: removeOption } = useFieldArray({
    control: form.control,
    name: "options",
  });

  // Load existing poll data when editing
  useEffect(() => {
    if (existingPoll && editMode) {
      console.log("Loading existing poll data:", existingPoll);
      form.reset({
        title: existingPoll.title || "",
        description: existingPoll.description || "",
        audienceType: existingPoll.audience_type || "all",
        streetIds: existingPoll.streetIds || [], // Populate from loaded data if available
        houseIds: existingPoll.houseIds || [],
        flatIds: existingPoll.flatIds || [],
        userIds: existingPoll.userIds || [],
        options: existingPoll.options?.map((opt: any) => ({ id: opt.id, text: opt.option_text })) || [{ text: "" }, { text: "" }],
        status: existingPoll.status || "draft",
        startDate: existingPoll.start_date ? new Date(existingPoll.start_date) : null,
        endDate: existingPoll.end_date ? new Date(existingPoll.end_date) : null,
        showResultsAfterVoting: existingPoll.show_results_after_voting !== undefined ? existingPoll.show_results_after_voting : true,
      });

      // Set initial state for audience filtering UI based on loaded IDs
      let initialStreets: string[] = [];
      let initialHouses: string[] = [];

      if (existingPoll.userIds?.length > 0) {
          initialHouses = users
              .filter(u => existingPoll.userIds.includes(u.id) && u.houseId)
              .map(u => u.houseId!);
          initialStreets = houses
              .filter(h => initialHouses.includes(h.id))
              .map(h => h.streetId);
      } else if (existingPoll.flatIds?.length > 0) {
          initialHouses = flats
              .filter(f => existingPoll.flatIds.includes(f.id))
              .map(f => f.houseId);
          initialStreets = houses
              .filter(h => initialHouses.includes(h.id))
              .map(h => h.streetId);
      } else if (existingPoll.houseIds?.length > 0) {
          initialHouses = existingPoll.houseIds;
          initialStreets = houses
              .filter(h => initialHouses.includes(h.id))
              .map(h => h.streetId);
      }

      setSelectedHouses([...new Set(initialHouses)]);
      setSelectedStreets([...new Set(initialStreets)]);

    }
  }, [existingPoll, editMode, form, houses, flats, users]); // Add users dependency

  // Data loading is now handled by TanStack Query hooks above

  // --- Filtering Effects (adapted for hierarchical filtering) ---
  useEffect(() => {
    // Filter houses based on selected streets OR all
    const housesToShow = selectedStreets.length > 0
    ? houses.filter(house => selectedStreets.includes(house.streetId))
    : houses;
    setFilteredHouses(housesToShow.filter(house => filterBySearchTerm(house, searchTerm)));
  }, [selectedStreets, houses, searchTerm]);

  useEffect(() => {
    // Filter flats based on selected houses OR selected streets OR all
    let flatsToShow = flats;
    if (selectedHouses.length > 0) {
    flatsToShow = flats.filter(flat => selectedHouses.includes(flat.houseId));
    } else if (selectedStreets.length > 0) {
    const streetsHouseIds = houses.filter(h => selectedStreets.includes(h.streetId)).map(h => h.id);
    flatsToShow = flats.filter(flat => streetsHouseIds.includes(flat.houseId));
    }
    setFilteredFlats(flatsToShow.filter(flat => filterBySearchTerm(flat, searchTerm)));
  }, [selectedHouses, selectedStreets, houses, flats, searchTerm]);

  useEffect(() => {
    // Filter users based on selected houses OR selected streets OR all
    let usersToShow = users;
    // Filter hierarchy: houses > streets
    if (selectedHouses.length > 0) {
        usersToShow = users.filter(user => user.houseId && selectedHouses.includes(user.houseId));
    } else if (selectedStreets.length > 0) {
        usersToShow = users.filter(user => user.streetId && selectedStreets.includes(user.streetId));
    }
    setFilteredUsers(usersToShow.filter(user => filterBySearchTermUser(user, searchTerm)));
  }, [selectedHouses, selectedStreets, users, searchTerm]); // Removed form.watch dependencies here as filtering is based on intermediate state

  // --- Selection Handlers (Mostly same, ensure form values are set correctly) ---
  const handleStreetSelection = (streetId: string, checked: boolean | string) => {
    const isChecked = !!checked;
    let newSelectedStreets;
    if (isChecked) {
      newSelectedStreets = [...selectedStreets, streetId];
    } else {
      newSelectedStreets = selectedStreets.filter(id => id !== streetId);
      // Deselect houses/flats/users under this street
      const housesToRemove = houses.filter(h => h.streetId === streetId).map(h => h.id);
      handleHouseDeselection(housesToRemove, false); // Don't update form yet
    }
    setSelectedStreets(newSelectedStreets);
    // Update form streetIds for streets audience type
    if (form.getValues("specificType") === 'streets') {
        form.setValue("streetIds", newSelectedStreets);
    }
  };

 const handleHouseSelection = (houseId: string, checked: boolean | string) => {
    const isChecked = !!checked;
    let newSelectedHouses;
    if (isChecked) {
      newSelectedHouses = [...selectedHouses, houseId];
      // Auto-select parent street if not already selected
      const house = houses.find(h => h.id === houseId);
      if (house && !selectedStreets.includes(house.streetId)) {
        setSelectedStreets([...selectedStreets, house.streetId]);
      }
    } else {
      newSelectedHouses = selectedHouses.filter(id => id !== houseId);
      // Deselect flats/users under this house
      handleHouseDeselection([houseId], false); // Don't update form yet
    }
    setSelectedHouses(newSelectedHouses);
    // Update form.houseIds regardless of audience type, as it's used for filtering flats/users
    form.setValue("houseIds", newSelectedHouses);
  };

  // Modified to prevent immediate form update if called internally
  const handleHouseDeselection = (houseIdsToRemove: string[], updateForm: boolean = true) => {
     const newSelectedHouses = selectedHouses.filter(id => !houseIdsToRemove.includes(id));
     setSelectedHouses(newSelectedHouses);
     if(updateForm) form.setValue("houseIds", newSelectedHouses);

     const flatIdsToRemove = flats.filter(f => houseIdsToRemove.includes(f.houseId)).map(f => f.id);
     const currentFlatIds = form.getValues("flatIds") || [];
     const newFlatIds = currentFlatIds.filter(id => !flatIdsToRemove.includes(id));
     if(updateForm) form.setValue("flatIds", newFlatIds);

     const userIdsToRemove = users.filter(u => u.houseId && houseIdsToRemove.includes(u.houseId)).map(u => u.id);
     const currentUserIds = form.getValues("userIds") || [];
     const newUserIds = currentUserIds.filter(id => !userIdsToRemove.includes(id));
     if(updateForm) form.setValue("userIds", newUserIds);
  }

  const handleFlatSelection = (flatId: string, checked: boolean | string) => {
    const isChecked = !!checked;
    const currentFlatIds = form.getValues("flatIds") || [];
    let newFlatIds;
    if (isChecked) {
      newFlatIds = [...currentFlatIds, flatId];
      // Auto-select parent house and street
      const flat = flats.find(f => f.id === flatId);
      if (flat) {
          if (!selectedHouses.includes(flat.houseId)) {
              setSelectedHouses([...selectedHouses, flat.houseId]);
              // Auto-select parent street as well
               const house = houses.find(h => h.id === flat.houseId);
               if (house && !selectedStreets.includes(house.streetId)) {
                   setSelectedStreets([...selectedStreets, house.streetId]);
               }
          }
      }
    } else {
      newFlatIds = currentFlatIds.filter(id => id !== flatId);
       // Deselect users under this flat
      const userIdsToRemove = users.filter(u => u.flatId === flatId).map(u => u.id);
      const currentUserIds = form.getValues("userIds") || [];
      const newUserIds = currentUserIds.filter(id => !userIdsToRemove.includes(id));
      form.setValue("userIds", newUserIds);
    }
    form.setValue("flatIds", newFlatIds);
  };

  const handleUserSelection = (userIdValue: string, checked: boolean | string) => {
     const isChecked = !!checked;
     const currentUserIds = form.getValues("userIds") || [];
     let newUserIds;
     if (isChecked) {
         newUserIds = [...currentUserIds, userIdValue];
         // Auto-select parent flat, house, street
         const user = users.find(u => u.id === userIdValue);
         if (user) {
             if (user.flatId && !(form.getValues("flatIds") || []).includes(user.flatId)) {
                 // Don't auto-select flat - user might select users across flats
                 // form.setValue("flatIds", [...(form.getValues("flatIds") || []), user.flatId]);
             }
             if (user.houseId && !selectedHouses.includes(user.houseId)) {
                  setSelectedHouses([...selectedHouses, user.houseId]);
             }
             if (user.streetId && !selectedStreets.includes(user.streetId)) {
                  setSelectedStreets([...selectedStreets, user.streetId]);
             }
         }
     } else {
         newUserIds = currentUserIds.filter(id => id !== userIdValue);
     }
     form.setValue("userIds", newUserIds);
  };

  // --- Search Filter Functions (Copied from AnnouncementForm) ---
  const filterBySearchTerm = (item: { displayName: string }, term: string) => {
    if (!term.trim()) return true;
    return item.displayName.toLowerCase().includes(term.toLowerCase());
  };
   const filterBySearchTermUser = (item: User, term: string): boolean => {
      if (!term.trim()) return true;
      const lowerTerm = term.toLowerCase();
      return (
         (item.displayName && item.displayName.toLowerCase().includes(lowerTerm)) ||
         (item.name && item.name.toLowerCase().includes(lowerTerm)) ||
         (item.email && item.email.toLowerCase().includes(lowerTerm)) ||
         (item.addressDisplay && item.addressDisplay.toLowerCase().includes(lowerTerm))
      );
   };

  // --- Form Submission Logic (Verify Payload) ---
  const onSubmit = async (values: PollFormValues) => {
    setIsLoading(true);
    setApiError(null);

    // Map the two-level structure to the API's expected format
    let finalAudienceType: string;
    let finalStreetIds: string[] | undefined = undefined;
    let finalHouseIds: string[] | undefined = undefined;
    let finalFlatIds: string[] | undefined = undefined;
    let finalUserIds: string[] | undefined = undefined;

    if (values.audienceType === 'all') {
        finalAudienceType = 'all';
    } else if (values.audienceType === 'specific' && values.specificType) {
        finalAudienceType = values.specificType;
        // Use the direct form field values for the final payload based on the specific type
        if (values.specificType === 'streets') {
            finalStreetIds = values.streetIds;
        } else if (values.specificType === 'houses') {
            finalHouseIds = values.houseIds;
        } else if (values.specificType === 'flats') {
            finalFlatIds = values.flatIds;
        } else if (values.specificType === 'users') {
            finalUserIds = values.userIds;
        }
    } else {
        // Fallback
        finalAudienceType = 'all';
    }

    const payload: any = {
      title: values.title,
      description: values.description,
      status: values.status,
      audienceType: finalAudienceType, // Send the mapped audience type
      options: values.options.map(opt => ({ 
          id: opt.id,
          text: opt.text,
          deleted: opt.deleted || false // Ensure deleted is always boolean
      })),
      startDate: values.startDate ? values.startDate.toISOString() : null,
      endDate: values.endDate ? values.endDate.toISOString() : null,
      showResultsAfterVoting: values.showResultsAfterVoting,
      // Explicitly set other ID arrays to undefined if not the target audience type
      streetIds: finalStreetIds,
      houseIds: finalHouseIds,
      flatIds: finalFlatIds,
      userIds: finalUserIds,
    };

    // Remove undefined keys to prevent sending them in the payload
    Object.keys(payload).forEach(key => payload[key] === undefined && delete payload[key]);

    // API call logic
    const url = editMode ? `/api/polls?id=${existingPoll.id}` : "/api/polls";
    const method = editMode ? "PATCH" : "POST";
    console.log(`Submitting poll ${editMode ? 'update' : 'creation'} to ${url}`, payload);
    try {
        const response = await fetch(url, {
            method: method,
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(payload),
          });
          if (!response.ok) {
            const errorData = await response.json().catch(() => ({ error: "Klaida apdorojant užklausą" }));
            console.error("API Error:", response.status, errorData);
            throw new Error(errorData.error || `HTTP klaida: ${response.status}`);
          }
          const result = await response.json();
         toast({
            title: editMode ? "Apklausa atnaujinta" : "Apklausa sukurta",
            description: `Apklausa "${values.title}" buvo sėkmingai ${editMode ? 'atnaujinta' : 'sukurta'}.`,
          });
          router.push("/dashboard/admin/polls");
          invalidateQueries.polls();
    } catch (error) {
        console.error("Form submission error:", error);
        setApiError(error instanceof Error ? error.message : "Įvyko nežinoma klaida.");
        toast({
          title: "Klaida",
          description: error instanceof Error ? error.message : "Nepavyko išsaugoti apklausos.",
          variant: "destructive",
        });
    } finally {
        setIsLoading(false);
    }
  };

  const audienceType = form.watch("audienceType");

  // Helper function to render the house list (Icon removed)
  const renderHouseList = (housesToList: House[]) => (
    <div className="max-h-60 overflow-y-auto space-y-2 rounded-md border border-slate-200 p-3 bg-white">
      {loadingData && <p className="text-slate-500 text-sm">Kraunami namai...</p>}
      {!loadingData && housesToList.length === 0 && 
         <p className="text-slate-500 text-sm">Namų nerasta pagal filtrus.</p>}
      
      {housesToList.map((house) => (
          <FormField
              key={`house-select-${house.id}`}
              control={form.control}
              name="houseIds" // Use 'houseIds' for the final form value
              render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md p-2 hover:bg-slate-100 transition-colors">
                      <FormControl>
                          <Checkbox
                              checked={field.value?.includes(house.id)}
                              onCheckedChange={(checked) => {
                                  const currentIds = field.value || [];
                                  const newIds = checked
                                      ? [...currentIds, house.id]
                                      : currentIds.filter(id => id !== house.id);
                                  field.onChange(newIds);
                                  handleHouseSelection(house.id, checked); // Update intermediate state
                              }}
                          />
                      </FormControl>
                      <FormLabel className="text-sm font-normal cursor-pointer">
                         {/* Icon Removed */} {house.displayName || `${house.name} (${house.address})`}
                      </FormLabel>
                  </FormItem>
              )}
          />
      ))}
    </div>
  );

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        {apiError && (
          <Alert variant="destructive">
            <AlertTitle>Klaida</AlertTitle>
            <AlertDescription>{apiError}</AlertDescription>
          </Alert>
        )}

        {/* Poll Details Section */}
        <Card className="border-slate-200 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-medium text-slate-800">Pagrindinė informacija</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pavadinimas</FormLabel>
                  <FormControl>
                    <Input placeholder="Apklausos pavadinimas" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Aprašymas</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Detalesnis apklausos aprašymas" rows={4} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Poll Options Section */}
        <Card className="border-slate-200 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-medium text-slate-800">Pasirinkimai</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {optionFields.map((field, index) => (
                 !field.deleted && (
                    <FormField
                        key={field.id}
                        control={form.control}
                        name={`options.${index}.text`}
                        render={({ field: optionField }) => (
                        <FormItem>
                            <FormLabel className="sr-only">Pasirinkimas {index + 1}</FormLabel>
                            <div className="flex items-center gap-2">
                            <FormControl>
                                <Input placeholder={`Pasirinkimas ${index + 1}`} {...optionField} />
                            </FormControl>
                            {/* Allow removing options only if more than 2 exist */} 
                            {optionFields.filter(f => !f.deleted).length > 2 && (
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="icon"
                                    onClick={() => {
                                        if (editMode && field.id) {
                                            // Mark existing option for deletion
                                            form.setValue(`options.${index}.deleted`, true);
                                            // Trigger re-render to hide
                                            form.trigger('options');
                                        } else {
                                            // Remove newly added option
                                            removeOption(index);
                                        }
                                    }}
                                    disabled={isLoading}
                                >
                                    <Trash className="h-4 w-4" />
                                    <span className="sr-only">Pašalinti pasirinkimą</span>
                                </Button>
                            )}
                            </div>
                            <FormMessage />
                        </FormItem>
                        )}
                    />
                )
            ))}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => appendOption({ text: "" })}
              disabled={isLoading}
              className="mt-2"
            >
              <Plus className="mr-2 h-4 w-4" />
              Pridėti pasirinkimą
            </Button>
          </CardContent>
        </Card>

         {/* Audience Selection Section */}
         <Card className="border-slate-200 shadow-sm">
            <CardHeader>
                <CardTitle className="text-lg font-medium text-slate-800">Auditorija</CardTitle>
                <FormDescription>
                    Pasirinkite kam skirta ši apklausa.
                </FormDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <FormField
                    control={form.control}
                    name="audienceType"
                    render={({ field }) => (
                        <FormItem>
                        <FormLabel>Gavėjų tipas</FormLabel>
                        <Select
                            onValueChange={(value) => {
                                field.onChange(value);
                                // Reset intermediate filter selections
                                setSelectedStreets([]);
                                setSelectedHouses([]);
                                // Reset form value selections for ALL types
                                form.setValue('streetIds', [], { shouldValidate: true });
                                form.setValue('houseIds', [], { shouldValidate: true });
                                form.setValue('flatIds', [], { shouldValidate: true });
                                form.setValue('userIds', [], { shouldValidate: true });
                                // Reset search
                                setSearchTerm("");
                                // Set default specificType when selecting "specific"
                                if (value === 'specific') {
                                    form.setValue('specificType', 'streets');
                                } else {
                                    form.setValue('specificType', undefined);
                                }
                            }}
                            value={field.value}
                            disabled={isLoading || loadingData}
                        >
                             <FormControl>
                                <SelectTrigger>
                                    <SelectValue placeholder="Pasirinkite gavėjų tipą" />
                                </SelectTrigger>
                             </FormControl>
                             <SelectContent>
                                 <SelectItem value="all">Visi vartotojai</SelectItem>
                                 <SelectItem value="specific">Pasirinkti konkrečius vartotojus</SelectItem>
                             </SelectContent>
                        </Select>
                        <FormMessage />
                        </FormItem>
                    )}
                />

                {/* Show tabs when "specific" is selected */}
                {audienceType === 'specific' && (
                    <div className="w-full">
                        <Tabs 
                            value={form.watch("specificType") || "streets"} 
                            onValueChange={(value) => {
                                form.setValue("specificType", value as any);
                                // Reset selections when changing tabs
                                setSelectedStreets([]);
                                setSelectedHouses([]);
                                form.setValue('streetIds', [], { shouldValidate: true });
                                form.setValue('houseIds', [], { shouldValidate: true });
                                form.setValue('flatIds', [], { shouldValidate: true });
                                form.setValue('userIds', [], { shouldValidate: true });
                                setSearchTerm("");
                            }}
                            className="w-full"
                        >
                            <TabsList className="grid w-full grid-cols-4 bg-slate-100">
                                <TabsTrigger value="streets" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
                                    <MapPin className="w-4 h-4 mr-2" />
                                    Pagal gatves
                                </TabsTrigger>
                                <TabsTrigger value="houses" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
                                    <Building className="w-4 h-4 mr-2" />
                                    Pagal namus
                                </TabsTrigger>
                                <TabsTrigger value="flats" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
                                    <Home className="w-4 h-4 mr-2" />
                                    Pagal butus
                                </TabsTrigger>
                                <TabsTrigger value="users" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
                                    <Users className="w-4 h-4 mr-2" />
                                    Individualiai
                                </TabsTrigger>
                            </TabsList>
                            
                            {/* Display selection count */}
                            {form.watch("specificType") === 'streets' && (
                                <div className="text-sm text-slate-600 mt-3 mb-2">Pasirinkta gatvių: {form.watch("streetIds")?.length || 0}</div>
                            )}
                            {form.watch("specificType") === 'houses' && (
                                <div className="text-sm text-slate-600 mt-3 mb-2">Pasirinkta namų: {form.watch("houseIds")?.length || 0}</div>
                            )}
                            {form.watch("specificType") === 'flats' && (
                                <div className="text-sm text-slate-600 mt-3 mb-2">Pasirinkta butų: {form.watch("flatIds")?.length || 0}</div>
                            )}
                            {form.watch("specificType") === 'users' && (
                                <div className="text-sm text-slate-600 mt-3 mb-2">Pasirinkta vartotojų: {form.watch("userIds")?.length || 0}</div>
                            )}
                        </Tabs>
                    </div>
                )}

                {/* --- Streets Selection UI --- */}
                {audienceType === 'specific' && form.watch("specificType") === 'streets' && (
                    <div className="w-full pt-4 border-t border-slate-100">
                        <p className="text-sm text-slate-600 mb-3">Pasirinkite gatves:</p>
                        <div className="max-h-60 overflow-y-auto space-y-1 rounded-md border border-slate-200 p-3 bg-white">
                            {loadingData ? <p>Kraunama...</p> :
                             streets.map((street) => (
                                 <FormItem key={`street-select-${street.id}`} className="flex flex-row items-center space-x-3 space-y-0 p-1.5 hover:bg-slate-50 rounded">
                                     <FormControl>
                                         <Checkbox
                                             checked={form.watch("streetIds")?.includes(street.id)}
                                             onCheckedChange={(checked) => {
                                                 const currentIds = form.getValues("streetIds") || [];
                                                 if (checked) {
                                                     form.setValue("streetIds", [...currentIds, street.id]);
                                                 } else {
                                                     form.setValue("streetIds", currentIds.filter(id => id !== street.id));
                                                 }
                                                 handleStreetSelection(street.id, checked);
                                             }}
                                         />
                                     </FormControl>
                                     <FormLabel className="text-sm font-normal cursor-pointer">
                                         {street.displayName || street.name}
                                     </FormLabel>
                                 </FormItem>
                             ))}
                        </div>
                    </div>
                )}

                {/* --- Houses Selection UI --- */}
                {audienceType === 'specific' && form.watch("specificType") === 'houses' && (
                    <div className="w-full pt-4 border-t border-slate-100">
                        <Input
                            placeholder="Ieškoti namų..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="mb-4"
                        />
                        {renderHouseList(houses.filter(h => filterBySearchTerm(h, searchTerm)))}
                    </div>
                )}

                {/* --- Flats Selection UI --- */}
                {audienceType === 'specific' && form.watch("specificType") === 'flats' && (
                     <div className="w-full pt-4 border-t border-slate-100">
                         <Input
                            placeholder="Ieškoti butų..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="mb-4"
                         />
                         <p className="text-sm text-slate-600 mb-3">Pasirinkite butus:</p>
                         <div className="max-h-60 overflow-y-auto space-y-2 rounded-md border border-slate-200 p-3 bg-white">
                           {loadingData && <p className="text-slate-500 text-sm">Kraunami butai...</p>}
                           {!loadingData && flats.length === 0 && <p className="text-slate-500 text-sm">Butų nerasta.</p>}
                           {flats
                               .filter(flat => filterBySearchTerm(flat, searchTerm))
                               .map((flat) => (
                                   <FormField
                                       key={`flat-select-${flat.id}`}
                                       control={form.control}
                                       name="flatIds"
                                       render={({ field }) => (
                                           <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md p-2 hover:bg-slate-100 transition-colors">
                                               <FormControl>
                                                   <Checkbox
                                                       checked={field.value?.includes(flat.id)}
                                                       onCheckedChange={(checked) => {
                                                           const currentIds = field.value || [];
                                                           const newIds = checked
                                                               ? [...currentIds, flat.id]
                                                               : currentIds.filter(id => id !== flat.id);
                                                           field.onChange(newIds);
                                                           handleFlatSelection(flat.id, checked);
                                                       }}
                                                   />
                                               </FormControl>
                                               <FormLabel className="text-sm font-normal cursor-pointer">
                                                   {flat.displayName || `${flat.houseName} - ${flat.number}`}
                                               </FormLabel>
                                           </FormItem>
                                       )}
                                   />
                               ))}
                         </div>
                     </div>
                 )}

                {/* --- Users Selection UI --- */}
                {audienceType === 'specific' && form.watch("specificType") === 'users' && (
                     <div className="w-full pt-4 border-t border-slate-100">
                         <Input
                            placeholder="Ieškoti gyventojų..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="mb-4"
                        />
                        <p className="text-sm text-slate-600 mb-3">Pasirinkite gyventojus:</p>
                        <div className="max-h-60 overflow-y-auto space-y-2 rounded-md border border-slate-200 p-3 bg-white">
                            {loadingData && <p className="text-slate-500 text-sm">Kraunami gyventojai...</p>}
                            {!loadingData && users.length === 0 && <p className="text-slate-500 text-sm">Gyventojų nerasta.</p>}
                            {users
                                .filter(user => filterBySearchTermUser(user, searchTerm))
                                .map((user) => (
                                    <FormField
                                        key={`user-select-${user.id}`}
                                        control={form.control}
                                        name="userIds"
                                        render={({ field }) => (
                                            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md p-2 hover:bg-slate-100 transition-colors">
                                                <FormControl>
                                                    <Checkbox
                                                        checked={field.value?.includes(user.id)}
                                                        onCheckedChange={(checked) => {
                                                            const currentIds = field.value || [];
                                                            const newIds = checked
                                                                ? [...currentIds, user.id]
                                                                : currentIds.filter(id => id !== user.id);
                                                            field.onChange(newIds);
                                                            handleUserSelection(user.id, checked);
                                                        }}
                                                    />
                                                </FormControl>
                                                <FormLabel className="text-sm font-normal cursor-pointer flex items-center gap-2">
                                                    <Users className="h-4 w-4 text-slate-500" /> 
                                                    {user.name}
                                                    {user.addressDisplay && (
                                                        <span className="text-slate-500 text-xs">
                                                            ({user.addressDisplay})
                                                        </span>
                                                    )}
                                                </FormLabel>
                                            </FormItem>
                                        )}
                                    />
                                ))}
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>

        {/* Poll Settings Section */}
        <Card className="border-slate-200 shadow-sm">
            <CardHeader>
                <CardTitle className="text-lg font-medium text-slate-800">Nustatymai</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                        <FormItem>
                        <FormLabel>Būsena</FormLabel>
                        <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            disabled={isLoading}
                        >
                            <FormControl>
                                <SelectTrigger>
                                    <SelectValue placeholder="Pasirinkite apklausos būseną" />
                                </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                                <SelectItem value="draft">Juodraštis</SelectItem>
                                <SelectItem value="active">Aktyvi</SelectItem>
                                {/* Closed status is typically set via separate action, not directly in form */}
                            </SelectContent>
                        </Select>
                        <FormDescription>
                            Juodraščiai nėra matomi gyventojams. Aktyvios apklausos iškart tampa matomos pasirinktai auditorijai.
                        </FormDescription>
                        <FormMessage />
                        </FormItem>
                    )}
                />
                 <FormField
                    control={form.control}
                    name="startDate"
                    render={({ field }) => (
                        <FormItem className="flex flex-col">
                            <FormLabel>Pradžios data (pasirinktinai)</FormLabel>
                            <FormControl>
                                <Input type="datetime-local" 
                                    value={field.value ? field.value.toISOString().slice(0, 16) : ''} 
                                    onChange={e => field.onChange(e.target.value ? new Date(e.target.value) : null)}
                                    disabled={isLoading} 
                                />
                            </FormControl>
                            <FormDescription>
                                Jei nurodyta, apklausa taps matoma tik nuo šios datos.
                            </FormDescription>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                 <FormField
                    control={form.control}
                    name="endDate"
                    render={({ field }) => (
                        <FormItem className="flex flex-col">
                            <FormLabel>Pabaigos data (pasirinktinai)</FormLabel>
                            <FormControl>
                                 <Input type="datetime-local" 
                                    value={field.value ? field.value.toISOString().slice(0, 16) : ''} 
                                    onChange={e => field.onChange(e.target.value ? new Date(e.target.value) : null)}
                                    disabled={isLoading} 
                                />
                            </FormControl>
                             <FormDescription>
                                Jei nurodyta, balsavimas automatiškai baigsis šią datą.
                            </FormDescription>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                 <FormField
                    control={form.control}
                    name="showResultsAfterVoting"
                    render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-4 bg-slate-50/50">
                            <FormControl>
                                <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={isLoading}
                                />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                                <FormLabel className="cursor-pointer">
                                Rodyti rezultatus po balsavimo
                                </FormLabel>
                                <FormDescription>
                                Ar gyventojai turėtų matyti rezultatus iškart po balsavimo (kol apklausa aktyvi)?
                                </FormDescription>
                            </div>
                        </FormItem>
                    )}
                />
            </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading || loadingData}>
            {isLoading ? "Saugoma..." : (editMode ? "Atnaujinti apklausą" : "Sukurti apklausą")}
          </Button>
        </div>
      </form>
    </Form>
  );
} 