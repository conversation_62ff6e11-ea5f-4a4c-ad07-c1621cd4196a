"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { RadioGroup, RadioItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ChevronDown, ChevronUp, Calendar, Clock, CheckCircle2, AlertCircle, User, BarChart3, Info, ShieldAlert } from "lucide-react";
import { toast } from "sonner";
import { formatDate } from "@/lib/utils";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { useSession } from "@/lib/hooks/use-session";
import { useOptimisticVote, usePollRealtime, usePollResponsesRealtime } from "@/lib/tanstack/queries";

interface PollOption {
  id: number;
  option_text: string;
  display_order: number;
}

interface PollResults {
  totalVotes: number;
  options: Array<{
    option_id: number;
    option_text: string;
    vote_count: number;
    percentage: number;
  }>;
  userExplanations?: Array<{
    id: number;
    userName: string;
    optionText: string;
    explanation: string;
    createdAt: string;
  }>;
}

interface PollResponse {
  id: number;
  option_id: number;
  explanation: string | null;
  created_at: string;
}

interface PollProps {
  id: number;
  title: string;
  description: string;
  status: "draft" | "active" | "closed";
  start_date: string | null;
  end_date: string | null;
  created_at: string;
  created_by_name: string;
  options: PollOption[];
  userResponse: PollResponse | null;
  hasVoted?: boolean;
  results: PollResults | null;
  show_results_after_voting?: boolean;
  onVote?: (pollId: number, optionId: number, explanation: string) => Promise<void>;
}

export function PollCard({ 
  id, 
  title, 
  description, 
  status, 
  start_date, 
  end_date, 
  created_at, 
  created_by_name, 
  options, 
  userResponse, 
  hasVoted: hasVotedProp,
  results,
  show_results_after_voting = true,
  onVote 
}: PollProps) {
  const { data: session } = useSession();
  
  // Set up real-time subscriptions for this poll
  usePollRealtime(id.toString());
  usePollResponsesRealtime(id.toString());
  
  // Use optimistic voting mutation
  const optimisticVote = useOptimisticVote();
  const [selectedOption, setSelectedOption] = useState<number | null>(
    userResponse ? userResponse.option_id : null
  );
  const [explanation, setExplanation] = useState<string>(
    userResponse?.explanation || ""
  );
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Prefer explicit hasVoted from props if available, otherwise derive from userResponse
  const hasVoted = hasVotedProp !== undefined ? hasVotedProp : !!userResponse;
  
  // Check if optimistic vote is in progress
  const isSubmitting = optimisticVote.isPending;
  const isAdmin = session?.user?.role === "super_admin" || session?.user?.role === "editor";
  const canVote = status === "active" && !hasVoted && !isAdmin;
  const isClosed = status === "closed";
  
  // Determine if we should show results based on poll status, user voting status and admin setting
  const showResults = isClosed || (hasVoted && show_results_after_voting) || isAdmin;
  
  const handleVote = async () => {
    if (!selectedOption) {
      toast.error("Pasirinkite variantą");
      return;
    }
    
    if (isAdmin) return;
    
    // Use optimistic voting for instant feedback
    optimisticVote.mutate({
      pollId: id.toString(),
      responses: [{
        pollId: id.toString(),
        questionId: '1', // Assuming single question for now
        optionId: selectedOption.toString(),
        explanation: explanation.trim() || undefined,
      }]
    });
    
    // Also call the onVote prop if provided (for backwards compatibility)
    if (onVote) {
      try {
        await onVote(id, selectedOption, explanation);
      } catch (error) {
        console.error("Callback vote error:", error);
        // The optimistic mutation will handle the error display
      }
    }
  };
  
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  // Get stat color based on the poll status
  const getStatusColor = () => {
    switch (status) {
      case "active": return "bg-gradient-to-r from-green-500 to-emerald-600";
      case "closed": return "bg-gradient-to-r from-slate-500 to-slate-600";
      default: return "bg-gradient-to-r from-amber-500 to-amber-600";
    }
  };
  
  return (
    <Card 
      className={cn(
        "border border-slate-200 shadow-sm overflow-hidden",
        isClosed ? "bg-slate-50" : "bg-white",
        isExpanded ? "mb-4" : ""
      )}
      id={`poll-${id}`}
    >
      {/* Status indicator */}
      <div className={`h-1.5 w-full ${getStatusColor()}`}></div>
      
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <CardTitle className="text-xl font-bold text-slate-800">{title}</CardTitle>
          {status === "active" && (
            <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200 border border-green-200">
              <div className="flex items-center gap-1">
                <span className="h-1.5 w-1.5 rounded-full bg-green-500 animate-pulse"></span>
                Aktyvi
              </div>
            </Badge>
          )}
          {status === "closed" && (
            <Badge variant="secondary" className="bg-slate-100 text-slate-700 border border-slate-200">
              <div className="flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                Uždaryta
              </div>
            </Badge>
          )}
        </div>
        <CardDescription className="flex items-center text-sm text-slate-600 mt-1.5">
          <User className="h-3.5 w-3.5 mr-1.5 text-slate-400" />
          Paskelbė <span className="font-medium mx-1">{created_by_name}</span> • {formatDate(created_at)}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-4">
          <div className="bg-slate-50 rounded-lg p-4">
            <p className={cn("text-slate-700 leading-relaxed", isExpanded ? "" : "line-clamp-2")}>{description}</p>
            
            {description.length > 100 && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={toggleExpand}
                className="flex items-center mt-2 p-0 h-auto text-indigo-600 hover:text-indigo-700 hover:bg-transparent"
              >
                {isExpanded ? (
                  <>
                    <ChevronUp className="h-4 w-4 mr-1.5" />
                    Rodyti mažiau
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-4 w-4 mr-1.5" />
                    Skaityti daugiau
                  </>
                )}
              </Button>
            )}
          </div>
          
          {(start_date || end_date) && (
            <div className="flex flex-wrap gap-4 text-sm">
              {start_date && (
                <div className="flex items-center text-slate-600">
                  <Calendar className="h-4 w-4 mr-2 text-indigo-500" />
                  <span>Pradėta: <span className="font-medium">{formatDate(start_date)}</span></span>
                </div>
              )}
              {end_date && (
                <div className="flex items-center text-slate-600">
                  <Clock className="h-4 w-4 mr-2 text-amber-500" />
                  <span>Baigiasi: <span className="font-medium">{formatDate(end_date)}</span></span>
                </div>
              )}
            </div>
          )}
          
          {/* Poll Options - Only show for regular users */}
          {canVote ? (
            <div className="mt-4">
              <h3 className="font-medium text-sm mb-3">Jūsų pasirinkimas:</h3>
              <RadioGroup value={selectedOption?.toString()} onValueChange={(value) => setSelectedOption(parseInt(value))}>
                <div className="space-y-3">
                  {options.map((option) => (
                    <div key={option.id} className="flex items-start space-x-2">
                      <RadioItem value={option.id.toString()} id={`option-${option.id}`} />
                      <Label htmlFor={`option-${option.id}`} className="cursor-pointer font-normal">{option.option_text}</Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
              
              <div className="mt-4">
                <Label htmlFor="explanation" className="text-sm mb-1 block">Komentaras (neprivaloma):</Label>
                <Textarea 
                  id="explanation" 
                  value={explanation}
                  onChange={(e) => setExplanation(e.target.value)}
                  placeholder="Kodėl pasirinkote šį variantą?"
                  className="resize-none"
                />
              </div>
              
              <Button 
                onClick={handleVote} 
                className="mt-4 w-full sm:w-auto" 
                disabled={!selectedOption || isSubmitting}
              >
                {isSubmitting ? "Balsuojama..." : "Balsuoti"}
              </Button>
            </div>
          ) : isAdmin && status === "active" ? (
            <div className="flex items-center gap-2 p-3 bg-amber-50 border border-amber-200 rounded-md text-amber-800 mt-4">
              <ShieldAlert className="h-5 w-5 text-amber-600 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium">Administratoriai negali balsuoti apklausose</p>
                <p className="text-xs">Apklausos skirtos tik reguliariems sistemos naudotojams</p>
              </div>
            </div>
          ) : null}
          
          {/* Poll Results */}
          {showResults && results && (
            <div className="mt-6 bg-white border border-slate-200 rounded-xl shadow-sm overflow-hidden">
              {/* Results Header */}
              <div className="flex items-center justify-between bg-gradient-to-r from-indigo-50 to-slate-50 px-5 py-3 border-b border-slate-200">
                <div className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-indigo-600" />
                  <h3 className="font-semibold text-slate-800 text-lg">Rezultatai</h3>
                </div>
                <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200 px-3 py-1 font-medium">
                  {results?.totalVotes || 0} {results?.totalVotes === 1 ? 'balsas' : 'balsai'}
                </Badge>
              </div>
              
              {/* Results Content */}
              <div className="p-5 space-y-5">
                {results?.options.map((option) => {
                  const isSelected = option.option_id === userResponse?.option_id;
                  return (
                    <div 
                      key={option.option_id} 
                      className={cn(
                        "space-y-2.5 p-3 rounded-lg transition-all duration-200",
                        isSelected 
                          ? "bg-indigo-50/70 border border-indigo-100" 
                          : "hover:bg-slate-50"
                      )}
                    >
                      <div className="flex justify-between items-center">
                        <div className={cn(
                          "flex items-center",
                          isSelected ? "font-medium text-indigo-800" : "text-slate-700"
                        )}>
                          {isSelected && (
                            <div className="bg-green-100 p-1 rounded-full mr-2 flex-shrink-0">
                              <CheckCircle2 className="h-4 w-4 text-green-600" />
                            </div>
                          )}
                          <span className="text-base">{option.option_text}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Badge variant={isSelected ? "default" : "outline"} className={cn(
                            "rounded-full px-3 py-0.5 font-medium text-sm",
                            isSelected 
                              ? "bg-indigo-100 text-indigo-700 border-indigo-200" 
                              : "bg-slate-50 text-slate-700"
                          )}>
                            {option.percentage}%
                          </Badge>
                          <span className={cn(
                            "text-sm",
                            isSelected ? "text-indigo-600 font-medium" : "text-slate-500"
                          )}>
                            ({option.vote_count})
                          </span>
                        </div>
                      </div>
                      <div className="relative pt-1">
                        <Progress 
                          value={option.percentage} 
                          className={cn(
                            "h-3 rounded-full", 
                            isSelected 
                              ? "bg-indigo-100" 
                              : "bg-slate-100"
                          )} 
                          indicatorClassName={cn(
                            "rounded-full transition-all duration-500 ease-in-out",
                            isSelected 
                              ? "bg-gradient-to-r from-indigo-500 to-indigo-600" 
                              : option.percentage > 0 
                                ? "bg-gradient-to-r from-slate-400 to-slate-500" 
                                : "bg-slate-200"
                          )}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
              
              {/* Message about results changing until poll is over */}
              {status === "active" && (
                <div className="mx-5 mb-5 flex items-start gap-3 p-4 bg-amber-50 border border-amber-100 rounded-lg text-sm">
                  <Info className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                  <p className="text-amber-800">
                    Apklausos rezultatai gali keistis, kol ji nėra uždaryta. Galutiniai rezultatai bus matomi, kai apklausa baigsis {end_date ? formatDate(end_date) : ""}.
                  </p>
                </div>
              )}
              
              {/* User's own explanation */}
              {userResponse?.explanation && (
                <div className="mx-5 mb-5 p-4 bg-indigo-50 rounded-lg border border-indigo-100">
                  <div className="flex items-center mb-2">
                    <div className="bg-indigo-100 p-1 rounded-full mr-2">
                      <User className="h-4 w-4 text-indigo-600" />
                    </div>
                    <p className="text-sm font-medium text-indigo-700">Jūsų paaiškinimas:</p>
                  </div>
                  <p className="text-sm text-indigo-600 pl-7">{userResponse.explanation}</p>
                </div>
              )}
              
              {/* Admin-only: All user explanations section */}
              {isAdmin && results.userExplanations && results.userExplanations.length > 0 && (
                <div className="mx-5 mb-5">
                  <div className="border-t border-slate-200 my-4"></div>
                  <div className="flex items-center mb-3">
                    <ShieldAlert className="h-5 w-5 text-purple-600 mr-2" />
                    <h4 className="font-semibold text-slate-800">Naudotojų paaiškinimai</h4>
                  </div>
                  <div className="space-y-3 max-h-[300px] overflow-y-auto pr-2">
                    {results.userExplanations.map((exp) => (
                      <div key={exp.id} className="bg-white border border-slate-200 rounded-lg p-3 hover:border-indigo-200 transition-colors">
                        <div className="flex justify-between items-center mb-2">
                          <div className="flex items-center gap-2">
                            <div className="bg-purple-100 text-purple-700 rounded-full w-7 h-7 flex items-center justify-center font-medium text-sm">
                              {exp.userName.charAt(0)}
                            </div>
                            <span className="font-medium text-sm text-slate-700">{exp.userName}</span>
                          </div>
                          <Badge variant="outline" className="bg-slate-50 text-slate-600 text-xs">
                            {exp.optionText}
                          </Badge>
                        </div>
                        <p className="text-sm text-slate-600 bg-slate-50 p-2 rounded border border-slate-100">
                          {exp.explanation}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
          
          {/* Message when results are hidden until poll closes */}
          {hasVoted && !showResults && (
            <div className="flex items-start gap-2 p-4 bg-indigo-50 border border-indigo-100 rounded-lg text-sm mt-4">
              <Info className="h-4 w-4 text-indigo-700 mt-0.5 flex-shrink-0" />
              <div className="text-indigo-800">
                <p className="font-medium mb-0.5">Ačiū už jūsų balsą!</p>
                <p>Rezultatai bus matomi tik užbaigus apklausą{end_date ? ` (${formatDate(end_date)})` : ""}. Šis nustatymas padeda užtikrinti, kad ankstesni balsai nedarytų įtakos vėliau balsuojantiems.</p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
      
      {canVote && (
        <CardFooter className="pt-0">
          <Button 
            onClick={handleVote} 
            disabled={!selectedOption || isSubmitting}
            className="w-full font-semibold py-6 bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 transition-all"
          >
            {isSubmitting ? (
              <>
                <div className="w-5 h-5 rounded-full border-2 border-white border-t-transparent animate-spin mr-2"></div>
                Siunčiama...
              </>
            ) : (
              <>Balsuoti</>
            )}
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}

export function PollCardSkeleton() {
  return (
    <Card className="w-full border-0 shadow-md overflow-hidden">
      <div className="h-1.5 w-full bg-gradient-to-r from-slate-200 to-slate-300"></div>
      <CardHeader>
        <Skeleton className="h-7 w-3/4 mb-2" />
        <Skeleton className="h-4 w-1/2" />
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-slate-50 rounded-lg p-4">
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-2/3" />
        </div>
        
        <div className="flex gap-4">
          <Skeleton className="h-4 w-28" />
          <Skeleton className="h-4 w-28" />
        </div>
        
        <div className="pt-2 space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="p-3 border border-slate-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <Skeleton className="h-4 w-4 rounded-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter>
        <Skeleton className="h-12 w-full rounded-md" />
      </CardFooter>
    </Card>
  );
} 