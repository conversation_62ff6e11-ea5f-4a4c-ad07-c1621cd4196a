'use client';

/**
 * Client component for critical styles that can safely use styled-jsx
 */
export function CriticalStyles() {
  return (
    <>
      <style jsx global>{`
        body {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background-color: #f9fafb !important;
          color: #111827 !important;
        }
        
        .card {
          background-color: white !important;
          border-radius: 0.5rem !important;
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
          padding: 1.5rem !important;
          margin-bottom: 1rem !important;
        }
      `}</style>
    </>
  );
} 