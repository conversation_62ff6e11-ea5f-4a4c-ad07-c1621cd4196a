'use client';

import React from 'react';
import { useQueryErrorResetBoundary } from '@tanstack/react-query';
import { AlertTriangle, RefreshCw, Wifi, WifiOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface QueryErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
  isOnline: boolean;
}

/**
 * Enhanced error boundary with TanStack Query integration
 * Provides intelligent error handling and recovery options
 */
export class QueryErrorBoundary extends React.Component<QueryErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null;

  constructor(props: QueryErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Query Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Call custom error handler
    this.props.onError?.(error, errorInfo);

    // Auto-retry after 10 seconds for network errors
    if (this.isNetworkError(error)) {
      this.resetTimeoutId = window.setTimeout(() => {
        this.resetErrorBoundary();
      }, 10000);
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  isNetworkError = (error: Error): boolean => {
    return error.message.includes('fetch') || 
           error.message.includes('network') ||
           error.message.includes('Failed to');
  };

  resetErrorBoundary = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
      this.resetTimeoutId = null;
    }
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      
      return (
        <FallbackComponent
          error={this.state.error!}
          resetErrorBoundary={this.resetErrorBoundary}
          isOnline={navigator.onLine}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * Default error fallback component
 */
function DefaultErrorFallback({ error, resetErrorBoundary, isOnline }: ErrorFallbackProps) {
  const isNetworkError = error.message.includes('fetch') || 
                        error.message.includes('network') ||
                        error.message.includes('Failed to');

  const isAuthError = error.message.includes('401') || 
                     error.message.includes('403') ||
                     error.message.includes('Unauthorized');

  return (
    <div className="min-h-[400px] flex items-center justify-center p-6">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            {isNetworkError ? (
              isOnline ? <Wifi className="h-6 w-6 text-red-600" /> : <WifiOff className="h-6 w-6 text-red-600" />
            ) : (
              <AlertTriangle className="h-6 w-6 text-red-600" />
            )}
          </div>
          <CardTitle className="text-lg">
            {isNetworkError ? 'Ryšio klaida' : 
             isAuthError ? 'Autentifikacijos klaida' : 
             'Įvyko klaida'}
          </CardTitle>
          <CardDescription>
            {isNetworkError ? 
              'Nepavyko prisijungti prie serverio. Patikrinkite interneto ryšį.' :
              isAuthError ?
              'Jūsų sesija pasibaigė. Prašome prisijungti iš naujo.' :
              'Kažkas nepavyko. Bandykite atnaujinti puslapį.'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!isOnline && (
            <Alert>
              <WifiOff className="h-4 w-4" />
              <AlertDescription>
                Jūs esate atsijungę nuo interneto. Kai tik atsijungsite, duomenys bus automatiškai atnaujinti.
              </AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col gap-2">
            <Button 
              onClick={resetErrorBoundary}
              className="w-full"
              disabled={!isOnline && isNetworkError}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Bandyti dar kartą
            </Button>

            {isAuthError && (
              <Button 
                variant="outline" 
                onClick={() => window.location.href = '/auth/login'}
                className="w-full"
              >
                Prisijungti iš naujo
              </Button>
            )}

            <Button 
              variant="ghost" 
              onClick={() => window.location.reload()}
              className="w-full"
            >
              Atnaujinti puslapį
            </Button>
          </div>

          {process.env.NODE_ENV === 'development' && (
            <details className="mt-4">
              <summary className="cursor-pointer text-sm text-gray-500">
                Klaidos detalės (tik plėtojimui)
              </summary>
              <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
                {error.stack}
              </pre>
            </details>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Hook for using query error reset in components
 */
export function useQueryErrorHandler() {
  const { reset } = useQueryErrorResetBoundary();
  
  const handleError = React.useCallback((error: Error) => {
    console.error('Query error:', error);
    
    // Reset query error boundary
    reset();
    
    // Show user-friendly error message
    if (error.message.includes('401') || error.message.includes('403')) {
      // Auth error - redirect to login
      window.location.href = '/auth/login';
    } else if (error.message.includes('network') || error.message.includes('fetch')) {
      // Network error - will be handled by offline support
      console.log('Network error detected, offline support will handle this');
    }
  }, [reset]);

  return { handleError, reset };
}

/**
 * Wrapper component that provides query error boundary
 */
interface WithQueryErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
}

export function WithQueryErrorBoundary({ children, fallback }: WithQueryErrorBoundaryProps) {
  return (
    <QueryErrorBoundary fallback={fallback}>
      {children}
    </QueryErrorBoundary>
  );
}

/**
 * Specific error fallback for data fetching components
 */
export function DataErrorFallback({ error, resetErrorBoundary, isOnline }: ErrorFallbackProps) {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <AlertTriangle className="h-8 w-8 text-red-500 mb-4" />
      <h3 className="text-lg font-semibold mb-2">Nepavyko užkrauti duomenų</h3>
      <p className="text-gray-600 mb-4 max-w-md">
        Įvyko klaida kraunant duomenis. Bandykite atnaujinti arba grįžkite vėliau.
      </p>
      <Button onClick={resetErrorBoundary} disabled={!isOnline}>
        <RefreshCw className="h-4 w-4 mr-2" />
        Bandyti dar kartą
      </Button>
    </div>
  );
}

/**
 * Compact error fallback for smaller components
 */
export function CompactErrorFallback({ error, resetErrorBoundary }: ErrorFallbackProps) {
  return (
    <div className="flex items-center justify-center p-4 bg-red-50 rounded-lg border border-red-200">
      <div className="text-center">
        <AlertTriangle className="h-5 w-5 text-red-500 mx-auto mb-2" />
        <p className="text-sm text-red-700 mb-2">Klaida kraunant duomenis</p>
        <Button size="sm" variant="outline" onClick={resetErrorBoundary}>
          <RefreshCw className="h-3 w-3 mr-1" />
          Bandyti dar kartą
        </Button>
      </div>
    </div>
  );
}