"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Plus, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { TagManager } from "./tag-manager";

interface TagData {
  id: number;
  name: string;
  color: string;
  category: string;
  created_at: string;
  updated_at?: string;
}

interface TagSelectorProps {
  selectedTags?: TagData[];
  onTagsChange?: (tags: TagData[]) => void;
  placeholder?: string;
  // Support both direct component usage and dialog mode
  isOpen?: boolean;
  onClose?: () => void;
  onTagsSelected?: (tags: TagData[]) => void;
  selectedTagIds?: number[];
}

// Color class mapping (same as in tag-manager.tsx)
const colorClassMap: Record<string, { bg: string, text: string, border: string, hover: string }> = {
  slate: { bg: 'bg-slate-100', text: 'text-slate-800', border: 'border-slate-200', hover: 'hover:bg-slate-200' },
  gray: { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200', hover: 'hover:bg-gray-200' },
  red: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200', hover: 'hover:bg-red-200' },
  orange: { bg: 'bg-orange-100', text: 'text-orange-800', border: 'border-orange-200', hover: 'hover:bg-orange-200' },
  amber: { bg: 'bg-amber-100', text: 'text-amber-800', border: 'border-amber-200', hover: 'hover:bg-amber-200' },
  yellow: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200', hover: 'hover:bg-yellow-200' },
  lime: { bg: 'bg-lime-100', text: 'text-lime-800', border: 'border-lime-200', hover: 'hover:bg-lime-200' },
  green: { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200', hover: 'hover:bg-green-200' },
  emerald: { bg: 'bg-emerald-100', text: 'text-emerald-800', border: 'border-emerald-200', hover: 'hover:bg-emerald-200' },
  teal: { bg: 'bg-teal-100', text: 'text-teal-800', border: 'border-teal-200', hover: 'hover:bg-teal-200' },
  cyan: { bg: 'bg-cyan-100', text: 'text-cyan-800', border: 'border-cyan-200', hover: 'hover:bg-cyan-200' },
  blue: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200', hover: 'hover:bg-blue-200' },
  indigo: { bg: 'bg-indigo-100', text: 'text-indigo-800', border: 'border-indigo-200', hover: 'hover:bg-indigo-200' },
  violet: { bg: 'bg-violet-100', text: 'text-violet-800', border: 'border-violet-200', hover: 'hover:bg-violet-200' },
  purple: { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-200', hover: 'hover:bg-purple-200' },
  fuchsia: { bg: 'bg-fuchsia-100', text: 'text-fuchsia-800', border: 'border-fuchsia-200', hover: 'hover:bg-fuchsia-200' },
  pink: { bg: 'bg-pink-100', text: 'text-pink-800', border: 'border-pink-200', hover: 'hover:bg-pink-200' },
  rose: { bg: 'bg-rose-100', text: 'text-rose-800', border: 'border-rose-200', hover: 'hover:bg-rose-200' }
};

// Function to get color classes for a tag
const getTagColorClasses = (color: string) => {
  return colorClassMap[color] || colorClassMap.gray; // Default to gray if color not found
};

export function TagSelector({ 
  selectedTags = [], 
  onTagsChange, 
  placeholder = "Įveskite naujos žymos pavadinimą",
  isOpen,
  onClose,
  onTagsSelected,
  selectedTagIds = []
}: TagSelectorProps) {
  // For inline mode (normal usage)
  const [isTagManagerOpen, setIsTagManagerOpen] = useState(false);
  const [localSelectedTags, setLocalSelectedTags] = useState<TagData[]>(selectedTags);
  
  // Fix infinite update loop by comparing array values instead of blindly setting state
  useEffect(() => {
    // Only update if the arrays are actually different by comparing IDs
    const currentIds = localSelectedTags.map(tag => tag.id).sort().join(',');
    const newIds = selectedTags.map(tag => tag.id).sort().join(',');
    
    if (currentIds !== newIds) {
      setLocalSelectedTags(selectedTags);
    }
  }, [selectedTags, localSelectedTags]);
  
  // Handle dialog mode (from announcement form)
  useEffect(() => {
    if (isOpen !== undefined) {
      setIsTagManagerOpen(isOpen);
    }
  }, [isOpen]);
  
  const handleTagsSelected = (tags: TagData[]) => {
    setLocalSelectedTags(tags);
    
    // Handle both callback types
    if (onTagsChange) {
      onTagsChange(tags);
    }
    
    if (onTagsSelected) {
      onTagsSelected(tags);
    }
  };
  
  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      setIsTagManagerOpen(false);
    }
  };
  
  const removeTag = (tagId: number) => {
    const updatedTags = localSelectedTags.filter(tag => tag.id !== tagId);
    setLocalSelectedTags(updatedTags);
    if (onTagsChange) {
      onTagsChange(updatedTags);
    }
  };
  
  const openTagManager = () => {
    setIsTagManagerOpen(true);
  };
  
  // If used in dialog mode only (from announcement form), only render the tag manager
  if (isOpen !== undefined && !onTagsChange) {
    return (
      <TagManager
        isOpen={isTagManagerOpen}
        onClose={handleClose}
        onTagsSelected={handleTagsSelected}
        selectedTagIds={selectedTagIds}
      />
    );
  }
  
  // Normal inline mode
  return (
    <div className="space-y-4">
      <div>
        <div className="flex flex-col space-y-2">
          <div className="flex space-x-2">
            <Input
              placeholder={placeholder}
              readOnly
              onClick={openTagManager}
              className="cursor-pointer flex-1"
              value=""
            />
            <Button
              type="button"
              onClick={openTagManager}
              className="whitespace-nowrap"
            >
              <Plus className="h-4 w-4 mr-1" />
              Sukurti
            </Button>
          </div>
          
          {localSelectedTags.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-2">
              {localSelectedTags.map((tag) => {
                const colorClasses = getTagColorClasses(tag.color);
                return (
                  <Badge
                    key={tag.id}
                    className={cn(
                      "flex items-center px-2 py-1",
                      colorClasses.bg,
                      colorClasses.text,
                      colorClasses.border
                    )}
                  >
                    {tag.name}
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 ml-1 p-0 hover:bg-transparent"
                      onClick={() => removeTag(tag.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                );
              })}
            </div>
          )}
        </div>
      </div>
      
      <TagManager
        isOpen={isTagManagerOpen}
        onClose={handleClose}
        onTagsSelected={handleTagsSelected}
        selectedTagIds={localSelectedTags.map(tag => tag.id)}
      />
    </div>
  );
}
