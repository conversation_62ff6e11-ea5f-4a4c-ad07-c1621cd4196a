"use client";

import { useState, useEffect } from "react";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter 
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { CheckCircle2, Plus, Tag as TagIcon, Trash2, X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface TagData {
  id: number;
  name: string;
  color: string;
  category: string;
  created_at: string;
  updated_at?: string;
}

interface TagManagerProps {
  isOpen: boolean;
  onClose: () => void;
  onTagsSelected?: (tags: TagData[]) => void;
  selectedTagIds?: number[];
}

const colorOptions = [
  { value: "slate", label: "Tam<PERSON>i pilka" },
  { value: "gray", label: "<PERSON>lk<PERSON>" },
  { value: "red", label: "<PERSON><PERSON><PERSON>" },
  { value: "orange", label: "Oranžinė" },
  { value: "amber", label: "Gintarinė" },
  { value: "yellow", label: "Geltona" },
  { value: "lime", label: "Citrininė" },
  { value: "green", label: "Žalia" },
  { value: "emerald", label: "Smaragdo" },
  { value: "teal", label: "Teal" },
  { value: "cyan", label: "Žydra" },
  { value: "blue", label: "Mėlyna" },
  { value: "indigo", label: "Indigo" },
  { value: "violet", label: "Violetinė" },
  { value: "purple", label: "Purpurinė" },
  { value: "fuchsia", label: "Fuksija" },
  { value: "pink", label: "Rožinė" },
  { value: "rose", label: "Rožių" }
];

const categoryOptions = [
  { value: "general", label: "Bendri" },
  { value: "utilities", label: "Komunaliniai" },
  { value: "maintenance", label: "Priežiūra" },
  { value: "community", label: "Bendruomenė" },
  { value: "environment", label: "Aplinka" },
  { value: "financial", label: "Finansai" },
  { value: "security", label: "Saugumas" },
  { value: "events", label: "Renginiai" }
];

// Color class mapping
const colorClassMap: Record<string, { bg: string, text: string, border: string, hover: string }> = {
  slate: { bg: 'bg-slate-100', text: 'text-slate-800', border: 'border-slate-200', hover: 'hover:bg-slate-200' },
  gray: { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200', hover: 'hover:bg-gray-200' },
  red: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200', hover: 'hover:bg-red-200' },
  orange: { bg: 'bg-orange-100', text: 'text-orange-800', border: 'border-orange-200', hover: 'hover:bg-orange-200' },
  amber: { bg: 'bg-amber-100', text: 'text-amber-800', border: 'border-amber-200', hover: 'hover:bg-amber-200' },
  yellow: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200', hover: 'hover:bg-yellow-200' },
  lime: { bg: 'bg-lime-100', text: 'text-lime-800', border: 'border-lime-200', hover: 'hover:bg-lime-200' },
  green: { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200', hover: 'hover:bg-green-200' },
  emerald: { bg: 'bg-emerald-100', text: 'text-emerald-800', border: 'border-emerald-200', hover: 'hover:bg-emerald-200' },
  teal: { bg: 'bg-teal-100', text: 'text-teal-800', border: 'border-teal-200', hover: 'hover:bg-teal-200' },
  cyan: { bg: 'bg-cyan-100', text: 'text-cyan-800', border: 'border-cyan-200', hover: 'hover:bg-cyan-200' },
  blue: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200', hover: 'hover:bg-blue-200' },
  indigo: { bg: 'bg-indigo-100', text: 'text-indigo-800', border: 'border-indigo-200', hover: 'hover:bg-indigo-200' },
  violet: { bg: 'bg-violet-100', text: 'text-violet-800', border: 'border-violet-200', hover: 'hover:bg-violet-200' },
  purple: { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-200', hover: 'hover:bg-purple-200' },
  fuchsia: { bg: 'bg-fuchsia-100', text: 'text-fuchsia-800', border: 'border-fuchsia-200', hover: 'hover:bg-fuchsia-200' },
  pink: { bg: 'bg-pink-100', text: 'text-pink-800', border: 'border-pink-200', hover: 'hover:bg-pink-200' },
  rose: { bg: 'bg-rose-100', text: 'text-rose-800', border: 'border-rose-200', hover: 'hover:bg-rose-200' }
};

// Function to get color classes for a tag
const getTagColorClasses = (color: string) => {
  return colorClassMap[color] || colorClassMap.gray; // Default to gray if color not found
};

export function TagManager({ isOpen, onClose, onTagsSelected, selectedTagIds = [] }: TagManagerProps) {
  const [tags, setTags] = useState<TagData[]>([]);
  const [newTagName, setNewTagName] = useState("");
  const [newTagColor, setNewTagColor] = useState("blue");
  const [newTagCategory, setNewTagCategory] = useState("general");
  const [loading, setLoading] = useState(false);
  const [localSelectedTagIds, setLocalSelectedTagIds] = useState<number[]>(selectedTagIds);
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  
  // Fetch tags when dialog opens
  useEffect(() => {
    if (isOpen) {
      fetchTags();
      setLocalSelectedTagIds(selectedTagIds);
    }
  }, [isOpen, selectedTagIds]);
  
  const fetchTags = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/tags");
      
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setTags(data);
    } catch (error) {
      console.error("Failed to fetch tags:", error);
      toast.error("Nepavyko gauti žymų");
    } finally {
      setLoading(false);
    }
  };
  
  const createTag = async () => {
    if (!newTagName.trim()) {
      toast.error("Žymos pavadinimas yra privalomas");
      return;
    }
    
    try {
      setLoading(true);
      const response = await fetch("/api/tags", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          name: newTagName.trim(),
          color: newTagColor,
          category: newTagCategory
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error ${response.status}: ${response.statusText}`);
      }
      
      const newTag = await response.json();
      setTags(prev => [...prev, newTag]);
      setNewTagName("");
      toast.success("Žyma sukurta sėkmingai");
    } catch (error) {
      console.error("Failed to create tag:", error);
      toast.error(error instanceof Error ? error.message : "Nepavyko sukurti žymos");
    } finally {
      setLoading(false);
    }
  };
  
  const deleteTag = async (tagId: number) => {
    if (!confirm("Ar tikrai norite ištrinti šią žymą?")) {
      return;
    }
    
    try {
      setLoading(true);
      const response = await fetch(`/api/tags?id=${tagId}`, {
        method: "DELETE"
      });
      
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      
      setTags(prev => prev.filter(tag => tag.id !== tagId));
      setLocalSelectedTagIds(prev => prev.filter(id => id !== tagId));
      toast.success("Žyma ištrinta sėkmingai");
    } catch (error) {
      console.error("Failed to delete tag:", error);
      toast.error("Nepavyko ištrinti žymos");
    } finally {
      setLoading(false);
    }
  };
  
  const toggleTagSelection = (tagId: number) => {
    setLocalSelectedTagIds(prev => 
      prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };
  
  const getCategoryLabel = (categoryValue: string) => {
    const category = categoryOptions.find(cat => cat.value === categoryValue);
    return category ? category.label : categoryValue;
  };
  
  const handleSave = () => {
    if (onTagsSelected) {
      const selectedTags = tags.filter(tag => localSelectedTagIds.includes(tag.id));
      onTagsSelected(selectedTags);
    }
    onClose();
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] flex flex-col overflow-hidden">
        <DialogHeader>
          <DialogTitle>Žymų valdymas</DialogTitle>
          <DialogDescription>
            Sukurkite naujas žymas arba pasirinkite esamas pranešimams.
          </DialogDescription>
        </DialogHeader>
        
        {/* Create new tag section */}
        <div className="bg-slate-50 border rounded-lg p-4 mb-4">
          <h3 className="text-sm font-medium mb-3">Sukurti naują žymą</h3>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="newTagName" className="text-xs mb-1.5 block">Žymos pavadinimas</Label>
              <Input
                id="newTagName"
                value={newTagName}
                onChange={(e) => setNewTagName(e.target.value)}
                placeholder="Įveskite naujos žymos pavadinimą"
              />
            </div>
            
            <div className="w-full sm:w-36">
              <Label htmlFor="newTagColor" className="text-xs mb-1.5 block">Spalva</Label>
              <Select
                value={newTagColor}
                onValueChange={setNewTagColor}
              >
                <SelectTrigger id="newTagColor">
                  <SelectValue placeholder="Spalva" />
                </SelectTrigger>
                <SelectContent>
                  {colorOptions.map((color) => (
                    <SelectItem key={color.value} value={color.value}>
                      <div className="flex items-center gap-2">
                        <div className={cn("w-3 h-3 rounded-full", colorClassMap[color.value].bg)} />
                        <span>{color.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="w-full sm:w-36">
              <Label htmlFor="newTagCategory" className="text-xs mb-1.5 block">Kategorija</Label>
              <Select
                value={newTagCategory}
                onValueChange={setNewTagCategory}
              >
                <SelectTrigger id="newTagCategory">
                  <SelectValue placeholder="Kategorija" />
                </SelectTrigger>
                <SelectContent>
                  {categoryOptions.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-end">
              <Button 
                onClick={createTag} 
                disabled={loading || !newTagName.trim()}
                className="w-full sm:w-auto"
              >
                <Plus className="h-4 w-4 mr-1" />
                Sukurti
              </Button>
            </div>
          </div>
        </div>
        
        {/* Filter section */}
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-2">
            <h3 className="text-sm font-medium">Visos žymos</h3>
            {!loading && (
              <Badge variant="outline" className="bg-slate-100 text-slate-700">
                {tags.length}
              </Badge>
            )}
          </div>
          
          <Select
            value={categoryFilter}
            onValueChange={setCategoryFilter}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filtruoti pagal kategoriją" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Visos kategorijos</SelectItem>
              {categoryOptions.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex-1 overflow-y-auto">
          {loading && tags.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2"></div>
              Kraunamos žymos...
            </div>
          ) : tags.length > 0 ? (
            <>
              {/* Category sections */}
              {categoryFilter === "all" ? (
                // Group by category when showing all
                Object.entries(
                  tags.reduce((acc, tag) => {
                    const category = tag.category;
                    if (!acc[category]) acc[category] = [];
                    acc[category].push(tag);
                    return acc;
                  }, {} as Record<string, TagData[]>)
                ).map(([category, categoryTags]) => (
                  <div key={category} className="mb-6 last:mb-0">
                    <h3 className="text-sm font-medium mb-2 text-slate-700">{getCategoryLabel(category)}</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                      {categoryTags.map((tag) => {
                        const colorClasses = getTagColorClasses(tag.color);
                        return (
                          <div 
                            key={tag.id}
                            className={cn(
                              "p-3 border rounded-md flex items-center justify-between transition-all cursor-pointer",
                              localSelectedTagIds.includes(tag.id) 
                                ? `${colorClasses.bg} border-${tag.color}-300` 
                                : 'bg-white border-slate-200 hover:border-slate-300 hover:shadow-sm'
                            )}
                            onClick={() => toggleTagSelection(tag.id)}
                            role="button"
                            tabIndex={0}
                            aria-pressed={localSelectedTagIds.includes(tag.id)}
                          >
                            <div className="flex items-center gap-2">
                              <Badge className={cn("px-2 py-1", colorClasses.bg, colorClasses.text, colorClasses.border)}>
                                <TagIcon className="h-3 w-3 mr-1" />
                                {tag.name}
                              </Badge>
                            </div>
                            
                            <div className="flex items-center gap-1">
                              <div className={cn(
                                "h-5 w-5 rounded-full border-2 flex items-center justify-center",
                                localSelectedTagIds.includes(tag.id)
                                  ? "border-primary bg-primary text-white"
                                  : "border-muted-foreground"
                              )}>
                                {localSelectedTagIds.includes(tag.id) && (
                                  <CheckCircle2 className="h-3 w-3" />
                                )}
                              </div>
                              
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-destructive ml-1"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  deleteTag(tag.id);
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))
              ) : (
                // Show filtered tags without category grouping
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                  {tags
                    .filter(tag => tag.category === categoryFilter)
                    .map((tag) => {
                      const colorClasses = getTagColorClasses(tag.color);
                      return (
                        <div 
                          key={tag.id}
                          className={cn(
                            "p-3 border rounded-md flex items-center justify-between transition-all cursor-pointer",
                            localSelectedTagIds.includes(tag.id) 
                              ? `${colorClasses.bg} border-${tag.color}-300` 
                              : 'bg-white border-slate-200 hover:border-slate-300 hover:shadow-sm'
                          )}
                          onClick={() => toggleTagSelection(tag.id)}
                          role="button"
                          tabIndex={0}
                          aria-pressed={localSelectedTagIds.includes(tag.id)}
                        >
                          <div className="flex items-center gap-2">
                            <Badge className={cn("px-2 py-1", colorClasses.bg, colorClasses.text, colorClasses.border)}>
                              <TagIcon className="h-3 w-3 mr-1" />
                              {tag.name}
                            </Badge>
                          </div>
                          
                          <div className="flex items-center gap-1">
                            <div className={cn(
                              "h-5 w-5 rounded-full border-2 flex items-center justify-center",
                              localSelectedTagIds.includes(tag.id)
                                ? "border-primary bg-primary text-white"
                                : "border-muted-foreground"
                            )}>
                              {localSelectedTagIds.includes(tag.id) && (
                                <CheckCircle2 className="h-3 w-3" />
                              )}
                            </div>
                            
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-destructive ml-1"
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteTag(tag.id);
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      );
                    })}
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-8 bg-slate-50 rounded-lg">
              <TagIcon className="h-8 w-8 text-slate-400 mx-auto mb-2" />
              <p className="text-slate-600">Nėra sukurtų žymų. Sukurkite pirmąją.</p>
            </div>
          )}
        </div>
        
        <DialogFooter className="mt-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 pt-4 border-t">
          <div className="flex flex-wrap gap-1.5">
            {localSelectedTagIds.length > 0 && (
              <>
                <span className="text-sm text-slate-700 font-medium">
                  Pasirinktos žymos:
                </span>
                <div className="flex flex-wrap gap-1.5">
                  {tags
                    .filter(tag => localSelectedTagIds.includes(tag.id))
                    .map(tag => {
                      const colorClasses = getTagColorClasses(tag.color);
                      return (
                        <Badge 
                          key={tag.id} 
                          className={cn(
                            "px-2 py-1 flex items-center", 
                            colorClasses.bg, 
                            colorClasses.text, 
                            colorClasses.border
                          )}
                        >
                          {tag.name}
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 ml-1 p-0 hover:bg-transparent"
                            onClick={() => toggleTagSelection(tag.id)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      );
                    })}
                </div>
              </>
            )}
            {localSelectedTagIds.length === 0 && (
              <span className="text-sm text-slate-500">
                Nepasirinkta jokių žymų
              </span>
            )}
          </div>
          <div className="flex gap-2 w-full sm:w-auto">
            <Button 
              variant="outline" 
              onClick={onClose} 
              className="flex-1 sm:flex-initial"
            >
              Atšaukti
            </Button>
            <Button 
              onClick={handleSave} 
              disabled={loading}
              className="flex-1 sm:flex-initial"
            >
              {localSelectedTagIds.length > 0 
                ? `Išsaugoti (${localSelectedTagIds.length})` 
                : "Išsaugoti"}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 