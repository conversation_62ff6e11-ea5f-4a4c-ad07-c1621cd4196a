"use client";

import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Phone } from "lucide-react";
import Link from "next/link";
import { usePhoneNumber, formatPhoneNumber } from "@/lib/hooks/use-phone-number";

interface EmergencyContactProps {
  phoneType: string;
  label: string;
  description?: string;
  fallback?: string;
}

export function EmergencyContactItem({ 
  phoneType, 
  label,
  description,
  fallback = "112" 
}: EmergencyContactProps) {
  const { phoneNumber, isLoading } = usePhoneNumber(phoneType, fallback);
  
  // Format phone number for display
  const formattedPhoneNumber = formatPhoneNumber(phoneNumber);
  
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 p-4 border rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow">
      <div className="flex-1">
        <h3 className="font-semibold">{label}</h3>
        {description && <p className="text-sm text-muted-foreground">{description}</p>}
      </div>

      <Link href={`tel:${phoneNumber}`} className="flex items-center gap-2 text-indigo-600 hover:text-indigo-800 font-bold text-lg whitespace-nowrap">
        <Phone className="h-4 w-4 flex-shrink-0" />
        <span>{isLoading ? "..." : formattedPhoneNumber}</span>
      </Link>
    </div>
  );
}

export function EmergencyContactsDisplay() {
  return (
    <Card className="shadow-sm">
      <CardHeader className="bg-gradient-to-r from-indigo-50 to-white border-b">
        <CardTitle className="text-lg font-semibold text-indigo-900">Avariniai kontaktai</CardTitle>
        <CardDescription>Svarbūs kontaktai avarijos atveju - išsaugokite šiuos numerius savo telefone.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        <div className="text-lg font-medium text-slate-700 mb-3">SVARBIAUSI KONTAKTAI</div>
        <div className="space-y-3">
          <EmergencyContactItem 
            phoneType="emergency" 
            label="Bendrasis pagalbos telefonas" 
            description="Policija, greitoji, gaisrinė"
            fallback="112"
          />
          
          <EmergencyContactItem
            phoneType="emergency_service"
            label="Šildymas, vanduo, kanalizacija - Avarinė 24/7"
            description='UAB "Skaidrola"'
            fallback="+370 70055007"
          />
        </div>

        <div className="text-lg font-medium text-slate-700 mt-6 mb-3">KITI AVARINIAI KONTAKTAI</div>
        <div className="space-y-3">
          <EmergencyContactItem
            phoneType="electrician"
            label="Elektros avarinė tarnyba"
            description='UAB "Jubis"'
            fallback="+370 67719115"
          />

          <EmergencyContactItem
            phoneType="elevator"
            label="Liftų avarinė tarnyba"
            description='UAB "Schindler-liftas"'
            fallback="+370 ********"
          />
        </div>

        <div className="text-lg font-medium text-slate-700 mt-6 mb-3">BENDRIJOS KONTAKTAI</div>
        <div className="space-y-3">
          <EmergencyContactItem
            phoneType="chairman"
            label="Pirmininkas"
            fallback="+370 ********"
          />

          <EmergencyContactItem
            phoneType="accountant"
            label="Buhalterė"
            fallback="+370 ********"
          />

          <EmergencyContactItem
            phoneType="heating_water"
            label="Šildymas, vandens priežiūra"
            description='UAB "SOBO sistemos"'
            fallback="+370 ********"
          />
        </div>
      </CardContent>
    </Card>
  );
} 