"use client";

import { useRef, useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "@/lib/tanstack/query-client";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Icons } from "@/components/ui/icons";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Form validation schema
const formSchema = z.object({
  number: z.string().min(1, {
    message: "Namo numeris yra privalomas la<PERSON>",
  }),
  streetId: z.string().refine((val) => val === "none" || val.length > 0, {
    message: "Gatvė turi būti pasirinkta arba nustatyta į 'Nėra'",
  }),
});

type FormValues = z.infer<typeof formSchema>;

interface HouseFormProps {
  house?: {
    id: string;
    number: string;
    streetId?: string;
  };
}

interface Street {
  id: string;
  name: string;
  city: string;
}

export function HouseForm({ house }: HouseFormProps = {}) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const formRef = useRef<HTMLFormElement>(null);
  const isEditing = !!house;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [streets, setStreets] = useState<Street[]>([]);
  const [isLoadingStreets, setIsLoadingStreets] = useState(true);

  // Debug the incoming house data
  console.log("House form data:", { 
    house, 
    streetId: house?.streetId, 
    isEditing 
  });

  // Set default values based on whether we're editing or creating
  const defaultValues: Partial<FormValues> = {
    number: house?.number || "",
    streetId: house?.streetId === undefined ? "none" : house.streetId,
  };

  console.log("Form default values:", defaultValues);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
    mode: "onChange",
  });

  // Debug form values when they change
  useEffect(() => {
    const subscription = form.watch((value) => {
      console.log("Form values changed:", value);
    });
    
    return () => subscription.unsubscribe();
  }, [form, form.watch]);

  // Fetch streets for the dropdown
  useEffect(() => {
    const fetchStreets = async () => {
      try {
        const response = await fetch("/api/streets");
        if (!response.ok) {
          throw new Error("Failed to fetch streets");
        }
        const data = await response.json();
        setStreets(data.streets || data);
      } catch (error) {
        console.error("Error fetching streets:", error);
        toast.error("Nepavyko gauti gatvių sąrašo");
      } finally {
        setIsLoadingStreets(false);
      }
    };

    fetchStreets();
  }, []);

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    
    try {
      // Determine if we're updating or creating
      const url = isEditing ? `/api/houses/${house?.id}` : "/api/houses";
      const method = isEditing ? "PUT" : "POST";
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "An error occurred");
      }
      
      toast.success(
        isEditing 
          ? "Namo duomenys atnaujinti" 
          : "Naujas namas sukurtas"
      );
      
      // Invalidate and refetch the houses data
      queryClient.invalidateQueries({ queryKey: queryKeys.housing.houses() });
      queryClient.invalidateQueries({ queryKey: queryKeys.housing.flats() });
      
      router.push("/dashboard/admin/houses");
      router.refresh();
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error(error instanceof Error ? error.message : "Įvyko klaida");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form ref={formRef} onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="streetId"
            render={({ field }) => {
              console.log("Field value in render:", field.value);
              return (
                <FormItem>
                  <FormLabel>Gatvė</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Pasirinkite gatvę" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem key="none" value="none">Nėra</SelectItem>
                      {isLoadingStreets ? (
                        <SelectItem key="loading" value="loading" disabled>
                          Kraunama...
                        </SelectItem>
                      ) : (
                        streets.map((street) => (
                          <SelectItem key={street.id} value={street.id}>
                            {street.name}, {street.city}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Pasirinkite gatvę, kurioje yra namas
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
          
          <FormField
            control={form.control}
            name="number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Namo numeris*</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="5" 
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  Įveskite namo numerį, pvz., "5" arba "5A"
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="flex justify-end">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                {isEditing ? "Atnaujinama..." : "Kuriama..."}
              </>
            ) : (
              <>
                {isEditing ? "Atnaujinti namą" : "Sukurti namą"}
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
} 