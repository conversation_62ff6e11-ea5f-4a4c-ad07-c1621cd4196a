"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { useQueryClient } from "@tanstack/react-query"
import { queryKeys } from "@/lib/tanstack/query-client"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"
import { Loader2 } from "lucide-react"

const flatFormSchema = z.object({
  number: z.string().min(1, "Buto numeris privalomas"),
  floor: z.string().optional(),
  houseId: z.string().min(1, "Namas privalomas"),
})

type FlatFormValues = z.infer<typeof flatFormSchema>

interface House {
  id: string
  name: string
  displayName: string
}

interface FlatFormProps {
  flatId?: string
  initialData?: FlatFormValues
  houses: House[]
}

export function FlatForm({ flatId, initialData, houses }: FlatFormProps) {
  const router = useRouter()
  const queryClient = useQueryClient()
  const [isLoading, setIsLoading] = useState(false)
  
  const form = useForm<FlatFormValues>({
    resolver: zodResolver(flatFormSchema),
    defaultValues: initialData || {
      number: "",
      floor: "",
      houseId: "",
    },
  })
  
  async function onSubmit(data: FlatFormValues) {
    setIsLoading(true)
    
    try {
      const url = flatId 
        ? `/api/flats/${flatId}` 
        : '/api/flats'
      
      const method = flatId ? 'PATCH' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          number: data.number,
          floor: data.floor || null,
          house_id: parseInt(data.houseId),
        }),
      })
      
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save flat')
      }
      
      toast({
        title: "Sėkmė!",
        description: flatId ? "Butas atnaujintas" : "Butas sukurtas",
      })
      
      // Invalidate and refetch the flats data
      queryClient.invalidateQueries({ queryKey: queryKeys.housing.flats() })
      
      // Redirect back to the flats list
      router.push('/dashboard/admin/houses?tab=flats')
      router.refresh()
    } catch (error) {
      console.error('Error saving flat:', error)
      toast({
        title: "Klaida",
        description: error instanceof Error ? error.message : "Nepavyko išsaugoti buto",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="houseId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Namas</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Pasirinkite namą" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {houses.map((house) => (
                    <SelectItem key={house.id} value={house.id}>
                      {house.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>
                Pasirinkite namą, kuriame yra šis butas
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Buto numeris</FormLabel>
              <FormControl>
                <Input placeholder="pvz., 1" {...field} />
              </FormControl>
              <FormDescription>
                Įveskite buto numerį
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="floor"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Aukštas (neprivaloma)</FormLabel>
              <FormControl>
                <Input placeholder="pvz., 1" {...field} />
              </FormControl>
              <FormDescription>
                Įveskite aukštą, kuriame yra butas
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="flex gap-4">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saugoma...
              </>
            ) : (
              flatId ? "Atnaujinti" : "Sukurti"
            )}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/dashboard/admin/houses?tab=flats')}
            disabled={isLoading}
          >
            Atšaukti
          </Button>
        </div>
      </form>
    </Form>
  )
}