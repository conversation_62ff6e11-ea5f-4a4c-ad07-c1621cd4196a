"use client";

import { useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "@/lib/tanstack/query-client";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Icons } from "@/components/ui/icons";
import { Plus, Save } from "lucide-react";

// Form validation schema
const formSchema = z.object({
  name: z.string().min(1, {
    message: "Gatvės pavadinimas yra privalomas laukas",
  }),
  city: z.string().min(1, {
    message: "Miestas yra privalomas laukas",
  }),
});

type FormValues = z.infer<typeof formSchema>;

interface StreetFormProps {
  street?: {
    id: string;
    name: string;
    city: string;
  };
}

export function StreetForm({ street }: StreetFormProps = {}) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const formRef = useRef<HTMLFormElement>(null);
  const isEditing = !!street;
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Set default values based on whether we're editing or creating
  const defaultValues: Partial<FormValues> = {
    name: street?.name || "",
    city: street?.city || "Vilnius",
  };

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    
    try {
      // Determine if we're updating or creating
      const url = isEditing ? `/api/streets/${street?.id}` : "/api/streets";
      const method = isEditing ? "PUT" : "POST";
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "An error occurred");
      }
      
      toast.success(
        isEditing 
          ? "Gatvės duomenys atnaujinti" 
          : "Nauja gatvė sukurta"
      );
      
      // Invalidate and refetch the streets data
      queryClient.invalidateQueries({ queryKey: queryKeys.housing.streets() });
      queryClient.invalidateQueries({ queryKey: queryKeys.housing.houses() });
      
      router.push("/dashboard/admin/houses");
      router.refresh();
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error(error instanceof Error ? error.message : "Įvyko klaida");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form ref={formRef} onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Gatvės pavadinimas*</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="Vilniaus g." 
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  Gatvės pavadinimas, įskaitant "g." dalį
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="city"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Miestas*</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="Vilnius" 
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  Miestas, kuriame yra gatvė
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="flex justify-end">
          <Button 
            type="submit" 
            disabled={isSubmitting} 
            className="flex items-center gap-2"
          >
            {isSubmitting ? (
              <>
                <Icons.spinner className="h-5 w-5 animate-spin" />
                <span>{isEditing ? "Atnaujinama..." : "Kuriama..."}</span>
              </>
            ) : (
              <>
                {isEditing ? (
                  <>
                    <Save className="h-5 w-5" />
                    <span>Išsaugoti pakeitimus</span>
                  </>
                ) : (
                  <>
                    <Plus className="h-5 w-5" />
                    <span>Sukurti naują gatvę</span>
                  </>
                )}
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
} 