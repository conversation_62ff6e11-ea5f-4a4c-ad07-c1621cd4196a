"use client";

import React, { useState, useEffect, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Setting } from "@/lib/settings";
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  Send,
  Wifi,
  <PERSON>if<PERSON>O<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Mail,
  Eye,
  <PERSON>Off
} from "lucide-react";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioItem } from "@/components/ui/radio-group";
import { useRouter } from "next/navigation";

interface SmtpSettingsProps {
  settings: Setting[];
  environmentInfo: {
    nodeEnv: string;
    isDevelopment: boolean;
    isProduction: boolean;
  };
}

// Form schema for SMTP settings
const smtpSettingsSchema = z.object({
  smtp_host: z.string().min(1, { message: "SMTP serverio adresas yra privalomas" }),
  smtp_port: z.string().refine((val) => !isNaN(Number(val)) && Number(val) > 0 && Number(val) <= 65535, {
    message: "Portas turi būti tarp 1 ir 65535",
  }),
  smtp_user: z.string().min(1, { message: "SMTP naudotojo vardas yra privalomas" }),
  smtp_password: z.string().optional(),
  smtp_from: z.string().email({ message: "Neteisingas el. pašto formatas" }),
  smtp_reply_to: z.string().email({ message: "Neteisingas el. pašto formatas" }).optional(),
  smtp_connection_type: z.enum(["none", "tls", "ssl"]).default("tls"),
  smtp_enabled: z.boolean(),
  test_recipient: z.string().email({ message: "Neteisingas el. pašto formatas" }).optional(),
});

// NEW: Schema specifically for the test recipient field
const testRecipientSchema = z.object({
  test_recipient: z.string()
    .min(1, { message: "Testinio gavėjo adresas yra privalomas" })
    .email({ message: "Neteisingas el. pašto formatas" }),
});

// Form schema for test email settings
const testEmailSchema = z.object({
  test_email_config: z.string().optional(),
  test_email_enabled: z.boolean(),
  test_email_addresses: z.string(),
  test_email_domains: z.string(),
  test_email_street_patterns: z.string(),
  email_dev_mode: z.enum(["redirect", "skip"]).default("redirect"),
  email_dev_recipient: z.string().email({ message: "Neteisingas el. pašto formatas" }),
});

export function SmtpSettings({ settings, environmentInfo }: SmtpSettingsProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isSubmittingTestConfig, setIsSubmittingTestConfig] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [testSuccess, setTestSuccess] = useState(false);
  const [testResult, setTestResult] = useState<{
    success?: boolean;
    connection?: boolean;
    sent?: boolean;
    error?: string;
  } | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();

  // Convert settings array to object for form - memoize to prevent infinite re-renders
  const settingsObj = useMemo(() => {
    const obj: Record<string, string | boolean> = {};

    // First pass: collect all settings
    settings.forEach(s => {
      // Convert boolean string values to actual booleans
      if (s.key === 'smtp_enabled') {
        obj[s.key] = s.value === 'true';
      } else {
        obj[s.key] = s.value;
      }
    });

    // Handle connection type: prefer new smtp_connection_type over old smtp_secure
    if (!obj.smtp_connection_type && obj.smtp_secure) {
      // Only convert old smtp_secure if new connection type doesn't exist
      obj.smtp_connection_type = obj.smtp_secure === 'true' ? 'ssl' : 'tls';
    } else if (!obj.smtp_connection_type) {
      // Default to TLS if neither exists
      obj.smtp_connection_type = 'tls';
    }

    return obj;
  }, [settings]);

  // Debug settings to ensure we're getting values from database
  console.log("Settings from database:", {
    email_dev_recipient: settings.find(s => s.key === 'email_dev_recipient')?.value,
    email_dev_mode: settings.find(s => s.key === 'email_dev_mode')?.value,
    smtp_connection_type: settings.find(s => s.key === 'smtp_connection_type')?.value,
    smtp_secure: settings.find(s => s.key === 'smtp_secure')?.value,
    settingsObj_connection_type: settingsObj.smtp_connection_type,
  });

  // Get test email config - memoize to prevent infinite re-renders
  const testEmailConfig = useMemo(() =>
    settings.find(s => s.key === 'test_email_config')?.value || '{}',
    [settings]
  );

  const parsedTestConfig = useMemo(() => {
    let config: {
      enabled: boolean;
      addresses: string[];
      domains: string[];
      streetPatterns: string[];
    } = { enabled: false, addresses: [], domains: [], streetPatterns: [] };

    try {
      config = JSON.parse(testEmailConfig);
    } catch (e) {
      console.error("Error parsing test email config", e);
    }

    return config;
  }, [testEmailConfig]);

  // Get development mode settings from database - memoize to prevent infinite re-renders
  const devModeSettings = useMemo(() =>
    settings.find(s => s.key === 'email_dev_mode')?.value || 'redirect',
    [settings]
  );

  const devRecipientSettings = useMemo(() =>
    settings.find(s => s.key === 'email_dev_recipient')?.value || '',
    [settings]
  );
  
  // Set up form with default values for MAIN settings
  const form = useForm<z.infer<typeof smtpSettingsSchema>>({
    resolver: zodResolver(smtpSettingsSchema),
    defaultValues: {
      smtp_host: (settingsObj.smtp_host as string) || "",
      smtp_port: (settingsObj.smtp_port as string) || "587",
      smtp_user: (settingsObj.smtp_user as string) || "",
      smtp_password: "",
      smtp_from: (settingsObj.smtp_from as string) || "",
      smtp_reply_to: (settingsObj.smtp_reply_to as string) || "",
      smtp_connection_type: (settingsObj.smtp_connection_type as "none" | "tls" | "ssl") || "tls",
      smtp_enabled: (settingsObj.smtp_enabled as boolean) || false,
    },
  });

  // Update form when settings change (fixes issue with values not loading)
  useEffect(() => {
    form.reset({
      smtp_host: (settingsObj.smtp_host as string) || "",
      smtp_port: (settingsObj.smtp_port as string) || "587",
      smtp_user: (settingsObj.smtp_user as string) || "",
      smtp_password: "", // Always keep password field empty for security
      smtp_from: (settingsObj.smtp_from as string) || "",
      smtp_reply_to: (settingsObj.smtp_reply_to as string) || "",
      smtp_connection_type: (settingsObj.smtp_connection_type as "none" | "tls" | "ssl") || "tls",
      smtp_enabled: (settingsObj.smtp_enabled as boolean) || false,
    });
  }, [settingsObj, form]);

  // NEW: Set up form specifically for the test recipient field
  const testRecipientForm = useForm<z.infer<typeof testRecipientSchema>>({
    resolver: zodResolver(testRecipientSchema),
    defaultValues: {
      test_recipient: "", // Start empty
    },
    mode: "onChange", // Validate on change for better UX
  });
  
  // Set up test email form
  const testEmailForm = useForm<z.infer<typeof testEmailSchema>>({
    resolver: zodResolver(testEmailSchema),
    defaultValues: {
      test_email_config: testEmailConfig,
      test_email_enabled: parsedTestConfig?.enabled || false,
      test_email_addresses: Array.isArray(parsedTestConfig?.addresses) 
        ? parsedTestConfig.addresses.join('\n') 
        : '',
      test_email_domains: Array.isArray(parsedTestConfig?.domains) 
        ? parsedTestConfig.domains.join('\n') 
        : '',
      test_email_street_patterns: Array.isArray(parsedTestConfig?.streetPatterns) 
        ? parsedTestConfig.streetPatterns.join('\n') 
        : '',
      email_dev_mode: (devModeSettings === 'skip' ? 'skip' : 'redirect') as 'redirect' | 'skip',
      email_dev_recipient: devRecipientSettings || '',
    },
    mode: "onChange",
  });

  // Update test email form when settings change
  useEffect(() => {
    testEmailForm.reset({
      test_email_config: testEmailConfig,
      test_email_enabled: parsedTestConfig?.enabled || false,
      test_email_addresses: Array.isArray(parsedTestConfig?.addresses)
        ? parsedTestConfig.addresses.join('\n')
        : '',
      test_email_domains: Array.isArray(parsedTestConfig?.domains)
        ? parsedTestConfig.domains.join('\n')
        : '',
      test_email_street_patterns: Array.isArray(parsedTestConfig?.streetPatterns)
        ? parsedTestConfig.streetPatterns.join('\n')
        : '',
      email_dev_mode: (devModeSettings === 'skip' ? 'skip' : 'redirect') as 'redirect' | 'skip',
      email_dev_recipient: devRecipientSettings || '',
    });
  }, [testEmailForm, testEmailConfig, parsedTestConfig, devModeSettings, devRecipientSettings]);
  
  // Submit handler for MAIN settings (remains the same, but validation won't include test_recipient)
  const onSubmit = async (data: z.infer<typeof smtpSettingsSchema>) => {
    console.log("onSubmit triggered for SMTP settings");
    setIsSubmitting(true);
    setSaveSuccess(false);
    
    try {
      // Validate form data again just before submission (optional, but good practice)
      smtpSettingsSchema.parse(data);
      console.log("Client-side validation passed.");
      
      // Prepare settings for submission (exclude test_recipient)
      const { test_recipient, ...smtpSettings } = data;
      
      // Log all values we're trying to save for debugging
      console.log("SMTP form values being processed:", {
        smtp_host: smtpSettings.smtp_host,
        smtp_port: smtpSettings.smtp_port,
        smtp_user: smtpSettings.smtp_user,
        smtp_password: smtpSettings.smtp_password ? "[password provided]" : "[password empty]",
        smtp_from: smtpSettings.smtp_from,
        smtp_reply_to: smtpSettings.smtp_reply_to || "",
        smtp_connection_type: smtpSettings.smtp_connection_type,
        smtp_enabled: smtpSettings.smtp_enabled
      });
      
      // Make sure undefined values are converted to empty strings and booleans to strings
      // Special handling for password: only include if it's not empty
      const settingsToUpdate = Object.entries(smtpSettings)
        .filter(([key, value]) => {
          // Skip empty password field - don't update it if it's empty
          if (key === 'smtp_password' && (!value || value === '')) {
            return false;
          }
          return true;
        })
        .map(([key, value]) => ({
          key,
          value: value === undefined ? "" :
                 typeof value === 'boolean' ? value.toString() :
                 value === null ? "" : String(value),
        }));
      
      console.log("Sending SMTP settings to API:", settingsToUpdate);
      
      // Submit to API
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ settings: settingsToUpdate }),
      });
      
      console.log(`API Response Status: ${response.status}`);
      
      // Get the response text/data
      const responseText = await response.text();
      let responseData;
      
      // Try to parse as JSON if possible
      try {
        responseData = JSON.parse(responseText);
        console.log("API Response data:", responseData);
      } catch (e) {
        console.warn("API Response was not valid JSON:", responseText);
        // If response is not OK and not JSON, use the text as error
        if (!response.ok) {
          throw new Error(`Server returned ${response.status}: ${responseText || 'No response body'}`);
        }
        // If response IS ok but not JSON (unexpected but possible)
        responseData = { success: true, message: "Settings updated (non-JSON response)" }; 
      }
      
      if (!response.ok || !responseData.success) {
        const errorMessage = responseData?.error || responseData?.message || `Server returned ${response.status}`;
        console.error("API returned an error:", errorMessage, "Full response:", responseData);
        throw new Error(errorMessage);
      }
      
      // Manually update our local state to match what we just saved
      // This might not be strictly necessary if router.refresh() works reliably,
      // but can help prevent UI flicker.
      const savedSettings = [...settings];
      for (const setting of settingsToUpdate) {
        // Don't update password in local state display
        if (setting.key === 'smtp_password' && setting.value !== '') continue; 
        const existingIndex = savedSettings.findIndex(s => s.key === setting.key);
        if (existingIndex >= 0) {
          savedSettings[existingIndex].value = setting.value;
        } else {
          savedSettings.push({ key: setting.key, value: setting.value });
        }
      }
      
      toast.success(responseData.message || "SMTP nustatymai sėkmingai atnaujinti");
      
      // Show success icon
      setSaveSuccess(true);
      
      // Reset form with the submitted data (keeping password field empty)
      // Important to reset with the *data* that was submitted
      // to ensure UI matches the state before refresh
      const resetData = {
        ...data, // Use the data that was successfully submitted
        smtp_password: "", // Always clear password field after submit
      };

      console.log("Resetting form with data:", resetData);
      form.reset(resetData);
      
      // Refresh the page data
      console.log("Attempting router.refresh()...");
      router.refresh();
      console.log("router.refresh() called.");
      
      // Reset success state after a delay
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
      
    } catch (error) {
      console.error("Error during SMTP settings submission:", error);
      // Ensure toast error is always shown
      toast.error(
        error instanceof Error ? error.message : "Įvyko nežinoma klaida atnaujinant nustatymus",
        { description: error instanceof Error ? error.stack : undefined }
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Submit test email config handler
  const onSubmitTestEmailConfig = async (data: z.infer<typeof testEmailSchema>) => {
    console.log("onSubmit triggered for Test Email settings");
    setIsSubmittingTestConfig(true);
    
    try {
      // Validate data
      testEmailSchema.parse(data);
      console.log("Test email form values being processed:", data);
      
      // Parse string values to arrays
      const addresses = data.test_email_addresses
        .split('\n')
        .map(line => line.trim())
        .filter(Boolean);
        
      const domains = data.test_email_domains
        .split('\n')
        .map(line => line.trim())
        .filter(Boolean);
        
      const streetPatterns = data.test_email_street_patterns
        .split('\n')
        .map(line => line.trim())
        .filter(Boolean);
      
      // Create test email config
      const testConfig = {
        enabled: data.test_email_enabled,
        addresses,
        domains,
        streetPatterns,
      };
      
      // Define settings to update
      const settingsToUpdate = [
        {
          key: 'test_email_config',
          value: JSON.stringify(testConfig),
          description: 'Configuration for test emails in development environment'
        },
        {
          key: 'email_dev_mode',
          value: data.email_dev_mode,
          description: 'How to handle non-test emails in development (redirect/skip)'
        },
        {
          key: 'email_dev_recipient',
          value: data.email_dev_recipient || "", // Ensure empty string if not provided
          description: 'Recipient for redirected emails in development'
        }
      ];
      
      console.log("Sending Test Email settings to API:", settingsToUpdate);
      
      // Submit to API
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          settings: settingsToUpdate
        }),
      });
      
      console.log(`Test Email API Response Status: ${response.status}`);
      
      // Get the response text/data
      const responseText = await response.text();
      let responseData;
      
      try {
        responseData = JSON.parse(responseText);
        console.log("Test Email API Response data:", responseData);
      } catch (e) {
        console.warn("Test Email API Response was not valid JSON:", responseText);
        if (!response.ok) {
          throw new Error(`Server returned ${response.status}: ${responseText || 'No response body'}`);
        }
        responseData = { success: true, message: "Test email settings updated (non-JSON response)" }; 
      }
      
      if (!response.ok || !responseData.success) {
        const errorMessage = responseData?.error || responseData?.message || `Server returned ${response.status}`;
        console.error("Test Email API returned an error:", errorMessage, "Full response:", responseData);
        throw new Error(errorMessage);
      }
      
      // Update local state (similar to main form)
      const savedSettings = [...settings];
      for (const setting of settingsToUpdate) {
        const existingIndex = savedSettings.findIndex(s => s.key === setting.key);
        if (existingIndex >= 0) {
          savedSettings[existingIndex].value = setting.value;
        } else {
          savedSettings.push({ key: setting.key, value: setting.value, description: setting.description });
        }
      }
      
      toast.success(responseData.message || "Testinių el. paštų nustatymai sėkmingai atnaujinti");
      
      // Force form values update to match what we saved
      testEmailForm.reset({
        ...data,
        test_email_config: JSON.stringify(testConfig),
      });
      
      // Refresh the page to ensure all settings are updated
      console.log("Attempting Test Email router.refresh()...");
      router.refresh();
      console.log("Test Email router.refresh() called.");
      
    } catch (error) {
      console.error("Error during Test Email settings submission:", error);
      toast.error(
        error instanceof Error ? error.message : "Įvyko nežinoma klaida atnaujinant testinių el. paštų nustatymus",
        { description: error instanceof Error ? error.stack : undefined }
      );
    } finally {
      setIsSubmittingTestConfig(false);
    }
  };

  // UPDATE: Test SMTP connection to use BOTH form states
  const testSmtpConnection = async () => {
    setIsTesting(true);
    setTestResult(null);
    setTestSuccess(false);

    // Validate the test recipient field first
    const recipientValid = await testRecipientForm.trigger("test_recipient");
    if (!recipientValid) {
      setIsTesting(false);
      toast.error("Prašome įvesti teisingą testinio gavėjo el. pašto adresą.");
      return; // Stop if recipient is invalid
    }
    
    try {
      // Get main SMTP settings (host, port, user, from, secure) from the 'form' instance
      const mainSmtpData = form.getValues(); 
      // Get the test recipient from the 'testRecipientForm' instance
      const testRecipientData = testRecipientForm.getValues();

      // Combine the necessary data, ensuring password logic is handled
      const testPayload = {
        smtp_host: mainSmtpData.smtp_host,
        smtp_port: mainSmtpData.smtp_port,
        smtp_user: mainSmtpData.smtp_user,
        smtp_password: mainSmtpData.smtp_password, // Pass potentially empty password
        smtp_from: mainSmtpData.smtp_from,
        smtp_reply_to: mainSmtpData.smtp_reply_to,
        smtp_connection_type: mainSmtpData.smtp_connection_type,
        test_recipient: testRecipientData.test_recipient, // Use recipient from its own form
      };
      
      console.log("Sending test with combined data:", {
        ...testPayload,
        smtp_password: testPayload.smtp_password ? "[REDACTED]" : "",
      });
      
      // Send test data to API
      const response = await fetch('/api/admin/settings/test-smtp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testPayload), // Send the combined payload
      });
      
      const result = await response.json();
      setTestResult(result);
      
      if (result.success) {
        toast.success("SMTP testas sėkmingas! Laiškas išsiųstas.");
        setTestSuccess(true);
        setTimeout(() => {
          setTestSuccess(false);
        }, 3000);
      } else {
        if (result.connection === false) {
          toast.error(`Nepavyko prisijungti prie SMTP serverio: ${result.error}`);
        } else {
          toast.error(`Prisijungimas pavyko, bet nepavyko išsiųsti laiško: ${result.error}`);
        }
      }
    } catch (error) {
      console.error("Test error:", error);
      toast.error(error instanceof Error ? error.message : "Įvyko klaida testuojant SMTP");
    } finally {
      setIsTesting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">SMTP nustatymai</CardTitle>
        <CardDescription>
          Nustatykite el. pašto serverį, kuris bus naudojamas sistemos pranešimams siųsti
        </CardDescription>
      </CardHeader>
      
      <Tabs defaultValue="settings">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="settings">Nustatymai</TabsTrigger>
          <TabsTrigger value="testing">Testavimas</TabsTrigger>
          <TabsTrigger value="test-emails">Testiniai el. paštai</TabsTrigger>
        </TabsList>
      
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <TabsContent value="settings">
              <CardContent className="space-y-4 pt-4">
                <FormField
                  control={form.control}
                  name="smtp_host"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SMTP Serveris</FormLabel>
                      <FormControl>
                        <Input id={field.name} placeholder="smtp.example.com" {...field} />
                      </FormControl>
                      <FormDescription>
                        Jūsų SMTP serverio adresas
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="smtp_port"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SMTP Portas</FormLabel>
                      <FormControl>
                        <Input id={field.name} placeholder="587" {...field} />
                      </FormControl>
                      <FormDescription>
                        Paprastai 587 (TLS) arba 465 (SSL)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="smtp_user"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SMTP Naudotojo vardas</FormLabel>
                      <FormControl>
                        <Input id={field.name} placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormDescription>
                        Prisijungimo vardas prie SMTP serverio
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="smtp_password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SMTP Slaptažodis</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            id={field.name}
                            type={showPassword ? "text" : "password"}
                            placeholder={settingsObj.smtp_password ? "••••••••" : "Įveskite slaptažodį"}
                            {...field}
                            className="pr-10"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </FormControl>
                      <FormDescription>
                        Palikite tuščią, jei nenorite keisti esamo slaptažodžio
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="smtp_from"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Siuntėjo adresas</FormLabel>
                      <FormControl>
                        <Input id={field.name} placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormDescription>
                        El. pašto adresas, kuris bus rodomas gavėjams
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="smtp_reply_to"
                  render={({ field }) => (
                    <FormItem className="mb-6">
                      <FormLabel className="text-base font-semibold">Atsakymų adresas</FormLabel>
                      <FormControl>
                        <Input 
                          id={field.name}
                          type="email"
                          placeholder="Įveskite el. pašto adresą" 
                          value={field.value || ""}
                          onChange={(e) => {
                            console.log("Reply-to changed:", e.target.value);
                            field.onChange(e.target.value);
                          }}
                          onBlur={field.onBlur}
                          className="border-indigo-200 focus:border-indigo-400"
                        />
                      </FormControl>
                      <FormDescription className="mt-1">
                        El. pašto adresas, į kurį bus nukreipti atsakymai į pranešimus ir laiškus.
                      </FormDescription>
                      <div className="mt-1 text-xs bg-indigo-50 p-2 rounded border border-indigo-100">
                        <span className="font-medium">Svarbu:</span> Jei nenurodysite atsakymų adreso, į laiškus atsakę
                        gavėjai matys pranešimą, kad atsakymas negali būti pristatytas.
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="smtp_connection_type"
                  render={({ field }) => (
                    <FormItem className="mb-6">
                      <FormLabel className="text-base font-semibold">Ryšio tipas</FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger className="border-indigo-200 focus:border-indigo-400">
                            <SelectValue placeholder="Pasirinkite ryšio tipą" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">Nesaugus (port 25)</SelectItem>
                            <SelectItem value="tls">TLS/STARTTLS (port 587)</SelectItem>
                            <SelectItem value="ssl">SSL (port 465)</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription className="space-y-1">
                        TLS/STARTTLS (587): Dažniausiai naudojamas, saugus ryšys su STARTTLS.{" "}
                        SSL (465): Tiesioginis SSL ryšys.{" "}
                        Nesaugus (25): Tik vidiniams serveriams, nereikalauja šifravimo.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="smtp_enabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base mb-0">Įjungti siuntimą</FormLabel>
                        <FormDescription>
                          Leisti siųsti el. laiškus
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          id={field.name}
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                {environmentInfo.isDevelopment && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Vystymo režimas</AlertTitle>
                    <AlertDescription>
                      Sistema veikia vystymo režimu. El. laiškai bus nukreipti į nurodytą testavimo adresą arba nebus siunčiami visai.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
              <CardFooter>
                <Button 
                  type="submit" 
                  className="ml-auto" 
                  disabled={isSubmitting || saveSuccess}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saugoma...
                    </>
                  ) : saveSuccess ? (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Išsaugota!
                    </>
                  ) : "Išsaugoti nustatymus"}
                </Button>
              </CardFooter>
            </TabsContent>
          </form>
        </Form>
        
        {/* Test Connection Tab */}
        <TabsContent value="testing">
          <Form {...testRecipientForm}>
            <form onSubmit={(e) => { e.preventDefault(); testSmtpConnection(); }}>
              <CardContent className="space-y-4 pt-4">
                <FormField
                  control={testRecipientForm.control}
                  name="test_recipient"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Testinio laiško gavėjas</FormLabel>
                      <FormControl>
                        <Input id={field.name} placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormDescription>
                        El. pašto adresas, kuriam bus siunčiamas testinis laiškas
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {testResult && (
                  <div className="my-4">
                    <h3 className="text-lg font-semibold mb-2">Testo rezultatai</h3>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <span className="font-medium mr-2">Prisijungimas:</span>
                        {testResult.connection === undefined ? (
                          <Badge variant="outline" className="bg-gray-100">Netestuota</Badge>
                        ) : testResult.connection ? (
                          <Badge className="bg-green-100 text-green-800 flex items-center">
                            <CheckCircle className="h-3.5 w-3.5 mr-1" />
                            Sėkminga
                          </Badge>
                        ) : (
                          <Badge variant="destructive" className="flex items-center">
                            <XCircle className="h-3.5 w-3.5 mr-1" />
                            Nepavyko
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center">
                        <span className="font-medium mr-2">Siuntimas:</span>
                        {testResult.sent === undefined ? (
                          <Badge variant="outline" className="bg-gray-100">Netestuota</Badge>
                        ) : testResult.sent ? (
                          <Badge className="bg-green-100 text-green-800 flex items-center">
                            <CheckCircle className="h-3.5 w-3.5 mr-1" />
                            Sėkminga
                          </Badge>
                        ) : (
                          <Badge variant="destructive" className="flex items-center">
                            <XCircle className="h-3.5 w-3.5 mr-1" />
                            Nepavyko
                          </Badge>
                        )}
                      </div>
                      
                      {testResult.error && (
                        <div className="mt-2">
                          <span className="font-medium">Klaida:</span>
                          <pre className="mt-1 text-xs bg-red-50 text-red-800 p-2 rounded whitespace-pre-wrap">
                            {testResult.error}
                          </pre>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button 
                  type="submit" 
                  className="ml-auto" 
                  disabled={isTesting || testSuccess}
                >
                  {isTesting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Testuojama...
                    </>
                  ) : testSuccess ? (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Testas sėkmingas!
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Siųsti testinį laišką
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Form>
        </TabsContent>
        
        {/* Test Email Configuration Tab */}
        <TabsContent value="test-emails">
          <Form {...testEmailForm}>
            <form onSubmit={testEmailForm.handleSubmit(onSubmitTestEmailConfig)}>
              <CardContent className="space-y-4 pt-4">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Prioritetų eiliškumas</AlertTitle>
                  <AlertDescription>
                    Sistema išsiųs laišką tik į testinius adresatus tokiu prioritetų eiliškumu:
                    1) Tikslūs el. pašto adresai (aukščiausias prioritetas),
                    2) El. pašto domenai (visi adresai su nurodytu domenu),
                    3) Naudotojai su pažymėta "test_user" vėliavėle,
                    4) Gatvės pavadinimo atitikimas (žemiausias prioritetas).
                    Sistema sustoja ties pirmuoju atitikimu ir nebetikrina kitų sąlygų.
                  </AlertDescription>
                </Alert>
                
                <Alert>
                  <UserCheck className="h-4 w-4" />
                  <AlertTitle>Testinių el. paštų konfigūracija</AlertTitle>
                  <AlertDescription>
                    Nustatykite, kurie vartotojai gali gauti el. laiškus vystymo aplinkoje. 
                    Tai naudinga testuojant funkcionalumą su testiniais vartotojais.
                  </AlertDescription>
                </Alert>
                
                <FormField
                  control={testEmailForm.control}
                  name="test_email_enabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base mb-0">Įgalinti testinius el. paštus</FormLabel>
                        <FormDescription>
                          Leisti tam tikriems vartotojams gauti el. laiškus vystymo aplinkoje
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          id={field.name}
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={testEmailForm.control}
                  name="test_email_addresses"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Testiniai el. pašto adresai</FormLabel>
                      <FormControl>
                        <Textarea 
                          id={field.name}
                          placeholder="<EMAIL>&#10;<EMAIL>" 
                          {...field} 
                          rows={3}
                        />
                      </FormControl>
                      <FormDescription>
                        Įveskite po vieną el. pašto adresą eilutėje. Šie adresai gaus laiškus vystymo aplinkoje.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={testEmailForm.control}
                  name="test_email_domains"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Testinės el. pašto domenos</FormLabel>
                      <FormControl>
                        <Textarea 
                          id={field.name}
                          placeholder="example-test.com&#10;test.example.org" 
                          {...field} 
                          rows={3}
                        />
                      </FormControl>
                      <FormDescription>
                        Įveskite po vieną el. pašto domeną eilutėje (be @). Visi adresai su šiomis domenomis gaus laiškus vystymo aplinkoje.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={testEmailForm.control}
                  name="test_email_street_patterns"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Gatvių pavadinimai</FormLabel>
                      <FormControl>
                        <Textarea 
                          id={field.name}
                          placeholder="Debreceno" 
                          {...field} 
                          rows={3}
                        />
                      </FormControl>
                      <FormDescription>
                        Įveskite po vieną gatvės pavadinimą eilutėje. Vartotojai, kurių adrese yra šie pavadinimai, gaus laiškus vystymo aplinkoje.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={testEmailForm.control}
                  name="email_dev_mode"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel>Laiškų elgsena vystyme</FormLabel>
                      <FormDescription>
                        Kaip sistema turėtų tvarkyti visus laiškus, kurie NEATITINKA jokių testinių kriterijų (nėra sąraše, neturi testinio domeno, nėra testinėje gatvėje ir t.t.)
                      </FormDescription>
                      <FormControl>
                        <RadioGroup
                          id={field.name}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="flex flex-col space-y-1"
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioItem value="redirect" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              Nukreipti į testinį el. paštą (saugu: realūs vartotojai negaus laiškų)
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioItem value="skip" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              Visiškai praleisti siuntimą (jokie laiškai nebus siunčiami)
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={testEmailForm.control}
                  name="email_dev_recipient"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Testinis gavėjas</FormLabel>
                      <FormControl>
                        <Input 
                          id={field.name}
                          type="email"
                          placeholder="Įveskite el. pašto adresą" 
                          value={field.value}
                          onChange={(e) => {
                            console.log("Input changed:", e.target.value);
                            field.onChange(e.target.value);
                          }}
                          onBlur={field.onBlur}
                        />
                      </FormControl>
                      <FormDescription>
                        El. paštas, į kurį bus nukreipti neTestiniai laiškai vystymo aplinkoje (kai nukreipimo režimas įjungtas)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {environmentInfo.isDevelopment ? (
                  <Alert>
                    <Mail className="h-4 w-4" />
                    <AlertTitle>Siuntimo logika vystymo aplinkoje</AlertTitle>
                    <AlertDescription>
                      • Testiniai vartotojai gaus laiškus savo tikrais el. pašto adresais.
                      • Kiti vartotojai bus nukreipti į {devRecipientSettings || "nenustatyta"}.
                      • Vartotojai tampa testiniais, jei: jų el. paštas yra sąraše arba jų el. pašto domenas yra sąraše arba jų gatvė turi vieną iš nurodytų pavadinimų arba jų vartotojo profilyje pažymėta "Testinis vartotojas" vėliavėlė.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Produkcijos aplinka</AlertTitle>
                    <AlertDescription>
                      Šie nustatymai neturi įtakos produkcijos aplinkoje. Produkcijoje visi el. laiškai bus siunčiami įprastai.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
              <CardFooter>
                <Button 
                  type="submit" 
                  className="ml-auto" 
                  disabled={isSubmittingTestConfig}
                >
                  {isSubmittingTestConfig ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saugoma...
                    </>
                  ) : (
                    "Išsaugoti testinių el. paštų nustatymus"
                  )}
                </Button>
              </CardFooter>
              
              {environmentInfo.isDevelopment && (
                <CardContent className="pt-0">
                  <div className="rounded-md bg-muted p-4 mt-4">
                    <div className="flex">
                      <AlertCircle className="h-5 w-5 text-primary" />
                      <div className="ml-3">
                        <h3 className="text-sm font-medium">
                          Sistema veikia vystymo režimu
                        </h3>
                        <div className="text-sm mt-2">
                          <p className="mb-2">
                            <strong>Kaip veikia el. pašto siuntimas vystymo aplinkoje:</strong>
                          </p>
                          <ul className="list-disc space-y-1 ml-5">
                            <li>Laiškai BUS siunčiami adresatams, kurie atitinka testinių gavėjų kriterijus (yra sąraše, turi testinį domeną, pažymėti kaip test_user, ar yra testinėje gatvėje)</li>
                            <li>Laiškai, skirti bet kuriam kitam adresatui (kuris neatitinka jokių testinių kriterijų), bus nukreipti į testinį el. paštą arba visai nesiunčiami - pagal pasirinktą nustatymą</li>
                            <li>Ši funkcija skirta užtikrinti, kad testavimo metu netyčia nenusiųstumėte laiškų realiems vartotojams</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              )}
            </form>
          </Form>
        </TabsContent>
      </Tabs>
    </Card>
  );
} 