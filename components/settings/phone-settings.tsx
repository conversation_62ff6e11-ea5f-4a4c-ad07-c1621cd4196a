"use client";

import React, { useState, useEffect, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { toast } from "sonner";
import { Setting } from "@/lib/settings";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { InfoIcon } from "lucide-react";

interface PhoneSettingsProps {
  settings: Setting[];
}

// Map of phone keys to human-readable labels and descriptions
const phoneLabels: Record<string, { label: string; description: string }> = {
  phone_emergency: { 
    label: "<PERSON>rasis pagalbos telefonas", 
    description: "Policija, greitoji, gaisrinė" 
  },
  phone_emergency_service: { 
    label: "Avarinės tarnybos 24/7", 
    description: "UAB \"Skaidirola\"" 
  },
  phone_heating_water: { 
    label: "Šildymo, vandens priežiūra", 
    description: "UAB \"SOBO\" sistemos" 
  },
  phone_elevator: { 
    label: "Liftų avarinė tarnyba", 
    description: "UAB \"Schindler-Liftas\"" 
  },
  phone_intercom: { 
    label: "Telefonspynių priežiūra", 
    description: "UAB \"Digitalas\"" 
  },
  phone_chairman: { 
    label: "Pirmininkas", 
    description: "DNSB Vakarai pirmininkas" 
  },
  phone_accountant: { 
    label: "Buhalterė", 
    description: "DNSB Vakarai buhalterė" 
  },
  phone_electrician: { 
    label: "Elektrikas", 
    description: "DNSB Vakarai elektrikas" 
  },
};

// Define form schema
const createPhoneSettingsSchema = () => {
  const schema: Record<string, any> = {};
  
  Object.keys(phoneLabels).forEach(key => {
    schema[key] = z.string().regex(/^(\+\d{1,3})?\d{3,15}$|^\d{3}\s\d{6}$/, {
      message: "Įveskite teisingą telefono numerį (pvz., +*********** arba 846 366577)",
    });
  });
  
  return z.object(schema);
};

const phoneSettingsSchema = createPhoneSettingsSchema();

export function PhoneSettings({ settings }: PhoneSettingsProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Convert settings array to object for form - memoize to prevent infinite re-renders
  const settingsObj = useMemo(() => {
    const obj: Record<string, string> = {};
    settings.forEach(s => {
      obj[s.key] = s.value;
    });
    return obj;
  }, [settings]);

  // Create default values - memoize to prevent infinite re-renders
  const defaultValues = useMemo(() => {
    const values: Record<string, string> = {};
    Object.keys(phoneLabels).forEach(key => {
      values[key] = settingsObj[key] || "";
    });
    return values;
  }, [settingsObj]);

  // Set up form with default values
  const form = useForm<z.infer<typeof phoneSettingsSchema>>({
    resolver: zodResolver(phoneSettingsSchema),
    defaultValues,
  });

  // Update form when settings change (fixes issue with values not loading)
  useEffect(() => {
    const updatedValues: Record<string, string> = {};
    Object.keys(phoneLabels).forEach(key => {
      updatedValues[key] = settingsObj[key] || "";
    });
    form.reset(updatedValues);
  }, [settingsObj, form]);
  
  // Submit handler
  const onSubmit = async (data: z.infer<typeof phoneSettingsSchema>) => {
    setIsSubmitting(true);
    
    try {
      // Prepare settings for submission
      const settingsToUpdate = Object.entries(data).map(([key, value]) => ({
        key,
        value,
      }));
      
      // Submit to API
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ settings: settingsToUpdate }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Nepavyko atnaujinti telefonų");
      }
      
      toast.success("Telefono numeriai sėkmingai atnaujinti");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Įvyko klaida atnaujinant telefonus");
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Card className="shadow-sm">
      <CardHeader className="bg-gradient-to-r from-indigo-50 to-white border-b">
        <CardTitle className="text-lg font-semibold text-indigo-900">Telefono numeriai</CardTitle>
        <CardDescription>Svarbūs kontaktiniai numeriai, naudojami visoje sistemoje</CardDescription>
      </CardHeader>
      <CardContent className="pt-6">
        <Alert className="mb-6 bg-indigo-50 border-indigo-200">
          <InfoIcon className="h-4 w-4 text-indigo-800" />
          <AlertDescription className="text-indigo-700">
            Šie telefono numeriai rodomi visoje sistemoje – pranešimuose, kontaktų puslapiuose ir 
            informaciniuose puoselapiuose. Atnaujinus numerius, pakeitimai bus pritaikyti visur automatiškai.
          </AlertDescription>
        </Alert>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Object.entries(phoneLabels).map(([key, { label, description }]) => (
                <FormField
                  key={key}
                  control={form.control}
                  name={key as any}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{label}</FormLabel>
                      <FormControl>
                        <Input placeholder="+***********" {...field} />
                      </FormControl>
                      <FormDescription>{description}</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ))}
            </div>
            
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="bg-indigo-600 hover:bg-indigo-700"
            >
              {isSubmitting ? "Išsaugoma..." : "Išsaugoti numerius"}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
} 