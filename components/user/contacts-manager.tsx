"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Trash2, Edit, Plus } from "lucide-react";
import { toast } from "sonner";

// Type for emergency contact
type EmergencyContact = {
  id: number;
  name: string;
  surname: string;
  email: string;
  phone: string;
  relationship: string;
  receiveNotifications: boolean;
  isEmergencyContact: boolean;
  customRelationship?: string;
};

// Empty contact template
const emptyContact: Omit<EmergencyContact, "id"> = {
  name: "",
  surname: "",
  email: "",
  phone: "",
  relationship: "",
  receiveNotifications: false,
  isEmergencyContact: true,
  customRelationship: "",
};

// Relationship type mapping
const relationshipTypes = {
  apartment_owner: "Buto savininkas",
  apartment_tenant: "Buto nuomininkas",
  son: "Sūnus",
  daughter: "Dukra",
  spouse: "Sutuoktinis(-ė)",
  parent: "Tėvas/Mama",
  sibling: "Brolis/Sesuo",
  friend: "Draugas",
  neighbor: "Kaimynas",
  other: "Kita"
};

// Component properties
interface ContactsManagerProps {
  userId: number;
}

export default function ContactsManager({ userId }: ContactsManagerProps) {
  const [contacts, setContacts] = useState<EmergencyContact[]>([]);
  const [loading, setLoading] = useState(true);
  const [showDialog, setShowDialog] = useState(false);
  const [currentContact, setCurrentContact] = useState<Omit<EmergencyContact, "id">>(emptyContact);
  const [editingId, setEditingId] = useState<number | null>(null);

  // Load contacts on component mount
  useEffect(() => {
    loadContacts();
  }, []);

  // Load contacts from API
  const loadContacts = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/users/${userId}/contacts`);
      
      if (!response.ok) {
        console.error("Error response:", await response.text());
        throw new Error(`Failed to load contacts: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Make sure we have an array of contacts
      if (!Array.isArray(data)) {
        console.error("Invalid response format:", data);
        setContacts([]);
        return;
      }
      
      // Map response data to match our component's expected structure
      const mappedContacts = data.map(contact => ({
        id: contact.id,
        name: contact.name || "",
        surname: contact.surname || "",
        email: contact.email || "",
        phone: contact.phone || "",
        relationship: contact.relationship || "",
        receiveNotifications: contact.receive_notifications || false,
        isEmergencyContact: contact.is_emergency_contact || true,
        customRelationship: contact.custom_relationship || "",
      }));
      
      setContacts(mappedContacts);
    } catch (error) {
      console.error("Error loading contacts:", error);
      toast.error("Nepavyko užkrauti kontaktų");
      setContacts([]);
    } finally {
      setLoading(false);
    }
  };

  // Save contact
  const saveContact = async () => {
    try {
      // Validate form
      if (!currentContact.name || !currentContact.phone) {
        toast.error("Vardas ir telefono numeris yra privalomi");
        return;
      }

      // Validate that customRelationship is provided when 'other' is selected
      if (currentContact.relationship === 'other' && !currentContact.customRelationship) {
        toast.error("Pasirinkus 'Kita', būtina nurodyti konkretų ryšį");
        return;
      }

      const url = editingId 
        ? `/api/users/${userId}/contacts/${editingId}` 
        : `/api/users/${userId}/contacts`;
      
      const method = editingId ? "PUT" : "POST";
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(currentContact),
      });

      if (!response.ok) {
        throw new Error("Failed to save contact");
      }

      toast.success(editingId ? "Kontaktas atnaujintas" : "Kontaktas pridėtas");
      setShowDialog(false);
      resetForm();
      loadContacts();
    } catch (error) {
      console.error("Error saving contact:", error);
      toast.error("Nepavyko išsaugoti kontakto");
    }
  };

  // Delete contact
  const deleteContact = async (id: number) => {
    if (!confirm("Ar tikrai norite ištrinti šį kontaktą?")) {
      return;
    }

    try {
      const response = await fetch(`/api/users/${userId}/contacts/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete contact");
      }

      toast.success("Kontaktas ištrintas");
      loadContacts();
    } catch (error) {
      console.error("Error deleting contact:", error);
      toast.error("Nepavyko ištrinti kontakto");
    }
  };

  // Edit contact
  const editContact = (contact: EmergencyContact) => {
    setCurrentContact({
      name: contact.name,
      surname: contact.surname,
      email: contact.email,
      phone: contact.phone,
      relationship: contact.relationship,
      receiveNotifications: contact.receiveNotifications,
      isEmergencyContact: contact.isEmergencyContact,
      customRelationship: contact.customRelationship,
    });
    setEditingId(contact.id);
    setShowDialog(true);
  };

  // Reset form
  const resetForm = () => {
    setCurrentContact(emptyContact);
    setEditingId(null);
  };

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCurrentContact(prev => ({ ...prev, [name]: value }));
  };

  // Handle relationship select change
  const handleRelationshipChange = (value: string) => {
    setCurrentContact(prev => ({ ...prev, relationship: value }));
  };

  // Handle custom relationship input change
  const handleCustomRelationshipChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setCurrentContact(prev => ({ ...prev, customRelationship: value }));
  };

  // Handle checkbox change for receive notifications
  const handleReceiveNotificationsChange = (checked: boolean) => {
    setCurrentContact(prev => ({ ...prev, receiveNotifications: checked }));
  };

  // Handle checkbox change for emergency contact
  const handleEmergencyContactChange = (checked: boolean) => {
    setCurrentContact(prev => ({ ...prev, isEmergencyContact: checked }));
  };

  // Add new contact
  const addNewContact = () => {
    resetForm();
    setShowDialog(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-medium">Jūsų kontaktai</h2>
        <Button onClick={addNewContact}>
          <Plus className="mr-2 h-4 w-4" />
          Pridėti kontaktą
        </Button>
      </div>

      {loading ? (
        <p>Kraunami kontaktai...</p>
      ) : contacts.length === 0 ? (
        <Card className="p-6">
          <p className="text-center text-muted-foreground">
            Neturite pridėtų kontaktų. Pridėkite bent vieną kontaktą, kuris būtų informuotas
            svarbių pranešimų atveju arba nelaimės atveju.
          </p>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2">
          {contacts.map((contact) => (
            <Card key={contact.id} className="p-4">
              <div className="flex justify-between">
                <div>
                  <h3 className="font-medium">{contact.name} {contact.surname}</h3>
                  <p className="text-sm text-muted-foreground">
                    {contact.relationship === 'other' && contact.customRelationship 
                      ? contact.customRelationship 
                      : relationshipTypes[contact.relationship as keyof typeof relationshipTypes] || contact.relationship}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <Button variant="ghost" size="icon" onClick={() => editContact(contact)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" onClick={() => deleteContact(contact.id)}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="mt-2">
                <p className="text-sm">{contact.phone}</p>
                {contact.email && <p className="text-sm">{contact.email}</p>}
              </div>
              <div className="mt-2 text-xs space-y-1">
                {contact.receiveNotifications && (
                  <div className="text-muted-foreground">
                    Gauna pranešimus
                  </div>
                )}
                {contact.isEmergencyContact && (
                  <div className="text-muted-foreground">
                    Ekstremalių situacijų kontaktas
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>
      )}

      <Dialog open={showDialog} onOpenChange={(open) => {
        if (!open) resetForm();
        setShowDialog(open);
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingId ? "Redaguoti kontaktą" : "Pridėti naują kontaktą"}
            </DialogTitle>
            <DialogDescription>
              Įveskite kontaktinio asmens informaciją. Kontaktai gali būti naudojami siųsti
              pranešimus susijusius su jūsų butu arba informuoti nelaimės atveju.
              Vardas ir telefono numeris yra privalomi.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Vardas *</Label>
                <Input
                  id="name"
                  name="name"
                  value={currentContact.name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="surname">Pavardė</Label>
                <Input
                  id="surname"
                  name="surname"
                  value={currentContact.surname}
                  onChange={handleChange}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="phone">Telefono numeris *</Label>
              <Input
                id="phone"
                name="phone"
                value={currentContact.phone}
                onChange={handleChange}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">El. paštas</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={currentContact.email}
                onChange={handleChange}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="relationship">Ryšys su kontaktu</Label>
              <Select
                value={currentContact.relationship}
                onValueChange={handleRelationshipChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pasirinkite ryšį su kontaktu" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(relationshipTypes).map(([value, label]) => (
                    <SelectItem key={value} value={value}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {currentContact.relationship === 'other' && (
              <div className="space-y-2">
                <Label htmlFor="customRelationship">Nurodykite konkretų ryšį su butu/savininku *</Label>
                <Input
                  id="customRelationship"
                  name="customRelationship"
                  value={currentContact.customRelationship || ''}
                  onChange={handleCustomRelationshipChange}
                  placeholder="Pvz.: Įgaliotas asmuo, Globėjas, Giminaitis, ir t.t."
                  required
                />
                <p className="text-sm text-muted-foreground">
                  Nurodykite, koks tiksliai ryšys sieja šį asmenį su butu ar jo savininku. 
                  Tai padės mums geriau suprasti kontakto svarbą avarinėse situacijose.
                </p>
              </div>
            )}
            
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="receiveNotifications"
                checked={currentContact.receiveNotifications}
                onCheckedChange={handleReceiveNotificationsChange}
              />
              <label 
                htmlFor="receiveNotifications" 
                className="text-sm font-medium leading-none"
              >
                Siųsti pranešimus šiam kontaktui
              </label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox 
                id="isEmergencyContact"
                checked={currentContact.isEmergencyContact}
                onCheckedChange={handleEmergencyContactChange}
              />
              <label 
                htmlFor="isEmergencyContact" 
                className="text-sm font-medium leading-none"
              >
                Ekstremalių situacijų kontaktas
              </label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDialog(false)}>
              Atšaukti
            </Button>
            <Button onClick={saveContact}>
              {editingId ? "Atnaujinti" : "Pridėti"} kontaktą
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 