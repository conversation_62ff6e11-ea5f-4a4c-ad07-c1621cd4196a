"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";

// Define props for the component
interface ProfileFormProps {
  userId: number;
  isNewUser?: boolean;
  initialData?: UserProfile;
}

// Define user profile data structure
interface UserProfile {
  name: string;
  email: string;
  phone: string;
  username: string;
  street?: string;
  houseNumber?: string;
  flatNumber?: string;
  flatFloor?: number | null;
  isProfileUpdated?: boolean;
}

export default function ProfileForm({ userId, isNewUser = false, initialData }: ProfileFormProps) {
  const router = useRouter();
  
  // State for form data, loading state, and form submission
  const [profile, setProfile] = useState<UserProfile>(initialData || {
    name: "",
    email: "",
    phone: "",
    username: "",
    street: "",
    houseNumber: "",
    flatNumber: "",
    flatFloor: null,
  });
  const [loading, setLoading] = useState(!isNewUser && !initialData);
  const [submitting, setSubmitting] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [flatInfo, setFlatInfo] = useState<any>(null);
  const [updatingFloor, setUpdatingFloor] = useState(false);
  
  // Check if user is admin
  useEffect(() => {
    const checkAdmin = async () => {
      try {
        const res = await fetch('/api/auth/check');
        const data = await res.json();
        setIsAdmin(data.role === 'super_admin');
      } catch (error) {
        console.error("Failed to check admin status:", error);
      }
    };
    
    checkAdmin();
  }, []);
  
  // Debug function for admins
  const checkUsername = async () => {
    try {
      const res = await fetch(`/api/users/check-username?userId=${userId}`);
      const data = await res.json();
      setDebugInfo(data);
    } catch (error) {
      console.error("Failed to check username:", error);
      setDebugInfo({ error: "Failed to check username" });
    }
  };
  
  // New debug function for detailed user info
  const getDetailedUserInfo = async () => {
    try {
      const res = await fetch(`/api/users/debug?userId=${userId}`);
      const data = await res.json();
      setDebugInfo(data);
    } catch (error) {
      console.error("Failed to get detailed user info:", error);
      setDebugInfo({ error: "Failed to get detailed user info" });
    }
  };
  
  // Load flat information
  const loadFlatInfo = async () => {
    try {
      const res = await fetch(`/api/users/${userId}/flat`);
      const data = await res.json();
      setFlatInfo(data.flat);
      
      // Update profile state with flat floor if available
      if (data.flat?.floor !== undefined) {
        setProfile(prev => ({ ...prev, flatFloor: data.flat.floor }));
      }
    } catch (error) {
      console.error("Failed to load flat info:", error);
    }
  };
  
  // Update flat floor
  const updateFloor = async (newFloor: number | null) => {
    try {
      setUpdatingFloor(true);
      const res = await fetch(`/api/users/${userId}/flat`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ floor: newFloor }),
      });
      
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || "Failed to update floor");
      }
      
      const data = await res.json();
      setFlatInfo(data.flat);
      setProfile(prev => ({ ...prev, flatFloor: data.flat.floor }));
      toast.success("Aukštas sėkmingai atnaujintas");
    } catch (error) {
      console.error("Failed to update floor:", error);
      toast.error(error instanceof Error ? error.message : "Nepavyko atnaujinti aukšto");
    } finally {
      setUpdatingFloor(false);
    }
  };
  
  // Load existing profile data if not a new user and no initialData provided
  useEffect(() => {
    if (!isNewUser && !initialData) {
      loadProfile();
      loadFlatInfo();
    }
  }, [isNewUser, userId, initialData]);
  
  // Load profile data from API
  const loadProfile = async () => {
    // Skip loading if we already have initialData
    if (initialData) {
      setProfile(initialData);
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      console.log(`Loading profile for user ID: ${userId}`);
      
      const response = await fetch(`/api/users/${userId}/profile`);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Failed to load profile. Status: ${response.status}, Error: ${errorText}`);
        throw new Error(`Failed to load profile: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log("Profile data received from API:", JSON.stringify(data, null, 2));
      
      // Check if username is missing
      if (!data.username) {
        console.warn("WARNING: Username is missing in the profile data from API");
      }
      
      // Create a new profile object with fallbacks for all fields
      // Be sure to match the exact field names returned by the API
      const updatedProfile = {
        name: data.name || "",
        email: data.email || "",
        phone: data.phone || "",
        username: data.username || "",
        // These field names must match what the API returns
        street: data.street || "",
        houseNumber: data.houseNumber || "", // camelCase from API
        flatNumber: data.flatNumber || "",   // camelCase from API
      };
      
      console.log("Setting profile state to:", JSON.stringify(updatedProfile, null, 2));
      setProfile(updatedProfile);
      
      // Log the profile state after setting it
      console.log("Profile state after update:", updatedProfile);
    } catch (error) {
      console.error("Error loading profile:", error);
      toast.error("Nepavyko užkrauti profilio informacijos");
    } finally {
      setLoading(false);
    }
  };
  
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfile(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!profile.name) {
      toast.error("Vardas ir pavardė yra privalomi");
      return;
    }

    // Validate email
    if (!profile.email || profile.email.trim() === '') {
      toast.error("El. pašto adresas yra privalomas");
      return;
    }

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(profile.email)) {
      toast.error("Neteisingas el. pašto adreso formatas");
      return;
    }
    
    try {
      setSubmitting(true);
      
      // Track changes by comparing with initial data
      const changes: string[] = [];
      console.log("Initial data:", initialData);
      console.log("Current profile:", profile);
      
      if (initialData) {
        if (profile.name !== initialData.name) {
          changes.push(`Vardas ir pavardė: ${initialData.name} → ${profile.name}`);
        }
        if (profile.email !== initialData.email) {
          changes.push(`El. paštas: ${initialData.email} → ${profile.email}`);
        }
        if (profile.phone !== initialData.phone) {
          const oldPhone = initialData.phone || "nenurodyta";
          const newPhone = profile.phone || "nenurodyta";
          changes.push(`Telefono numeris: ${oldPhone} → ${newPhone}`);
        }
      }
      
      console.log("Detected changes:", changes);
      
      // Transform data for API - convert from camelCase to snake_case where needed
      const submitData = {
        name: profile.name,
        email: profile.email,
        phone: profile.phone || "",
        username: profile.username,
        // These fields will be ignored by the API but included for completeness
        street: profile.street,
        house_number: profile.houseNumber,
        flat_number: profile.flatNumber,
        // Always mark the profile as updated when submitting
        is_profile_updated: true,
      };
      
      // Log the data we're about to send for debugging
      console.log("Submitting profile data:", JSON.stringify(submitData, null, 2));
      
      const response = await fetch(`/api/users/${userId}/profile`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Error response from API:", errorData);
        throw new Error(errorData.error || "Failed to update profile");
      }
      
      const updatedData = await response.json();
      console.log("Profile updated successfully:", updatedData);
      
      // Show toast with fixed settings and no page refresh
      if (changes.length > 0) {
        // Use the simpler toast API for consistency
        const formattedChanges = changes.map(change => `• ${change}`).join('\n');
        toast.success(`Profilis sėkmingai atnaujintas\n${formattedChanges}`, {
          duration: 6000,
        });
      } else {
        toast.success("Profilis sėkmingai atnaujintas", {
          duration: 6000,
        });
      }
      
      // Wait longer to ensure toast is visible
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // If this was a new user, redirect to the dashboard after toast is shown
      if (isNewUser) {
        setTimeout(() => {
          router.push("/dashboard");
        }, 1000);
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error(error instanceof Error ? error.message : "Nepavyko atnaujinti profilio", {
        duration: 6000,
      });
    } finally {
      setSubmitting(false);
    }
  };
  
  // Debug info display
  const renderDebugInfo = () => {
    if (!isAdmin || !debugInfo) return null;
    
    return (
      <div className="mt-6 p-4 border rounded-md bg-gray-50">
        <h3 className="text-sm font-semibold mb-2">Debug Information</h3>
        <pre className="text-xs overflow-auto max-h-40 p-2 bg-gray-100 rounded">
          {JSON.stringify(debugInfo, null, 2)}
        </pre>
        <div className="mt-2 space-y-2">
          <Button 
            type="button" 
            variant="outline" 
            size="sm" 
            onClick={() => setDebugInfo(profile)}
          >
            Show Current Profile State
          </Button>
          <Button 
            type="button" 
            variant="outline" 
            size="sm" 
            onClick={checkUsername}
            className="ml-2"
          >
            Check Username
          </Button>
          <Button 
            type="button" 
            variant="outline" 
            size="sm" 
            onClick={getDetailedUserInfo}
            className="ml-2"
          >
            Get Detailed User Info
          </Button>
        </div>
      </div>
    );
  };
  
  // Render loading state
  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p>Kraunama profilio informacija...</p>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {isNewUser ? "Užpildykite profilio informaciją" : "Redaguoti profilį"}
        </CardTitle>
        <CardDescription>
          {isNewUser
            ? "Prašome užpildyti savo kontaktinę informaciją, kad galėtume su jumis susisiekti"
            : "Atnaujinkite savo kontaktinę informaciją"}
        </CardDescription>
      </CardHeader>
      
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {/* Debug info for admins */}
          {isAdmin && (
            <div className="p-2 bg-blue-50 border border-blue-200 rounded-md mb-4">
              <p className="text-sm text-blue-800">
                <strong>Admin Debug:</strong> User ID: {userId}, 
                New User: {isNewUser ? 'Yes' : 'No'}, 
                Loading: {loading ? 'Yes' : 'No'},
                initialData: {initialData ? 'Yes' : 'No'}
              </p>
              {initialData && (
                <div className="mt-2">
                  <details>
                    <summary className="text-xs cursor-pointer">View Initial Data</summary>
                    <pre className="text-xs mt-1 p-1 bg-blue-100 rounded overflow-auto max-h-40">
                      {JSON.stringify(initialData, null, 2)}
                    </pre>
                  </details>
                </div>
              )}
              {profile && (
                <div className="mt-2">
                  <details>
                    <summary className="text-xs cursor-pointer">View Current Profile State</summary>
                    <pre className="text-xs mt-1 p-1 bg-blue-100 rounded overflow-auto max-h-40">
                      {JSON.stringify(profile, null, 2)}
                    </pre>
                  </details>
                </div>
              )}
            </div>
          )}
          <div className="space-y-2">
            <Label htmlFor="username">Prisijungimo vardas</Label>
            <Input
              id="username"
              name="username"
              value={profile.username || ""}
              readOnly
              disabled
              className="bg-muted"
            />
            {!profile.username && (
              <div className="mt-1 p-2 bg-amber-50 border border-amber-200 rounded-md">
                <p className="text-amber-800 text-sm">
                  <strong>Dėmesio:</strong> Prisijungimo vardas nenustatytas. 
                  Prašome susisiekti su administratoriumi.
                </p>
              </div>
            )}
            <p className="text-sm text-muted-foreground">
              Prisijungimo vardo pakeisti negalima
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="name">Vardas ir pavardė *</Label>
            <Input
              id="name"
              name="name"
              value={profile.name || ""}
              onChange={handleChange}
              placeholder="Vardas Pavardė"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="email">El. paštas *</Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={profile.email || ""}
              onChange={handleChange}
              placeholder="<EMAIL>"
              required
            />
            <p className="text-sm text-muted-foreground">
              El. pašto adresas yra privalomas pranešimams gauti
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="phone">Telefono numeris</Label>
            <Input
              id="phone"
              name="phone"
              value={profile.phone || ""}
              onChange={handleChange}
              placeholder="+370 600 00000"
            />
          </div>
          
          {/* Address Information Section */}
          <div className="mt-6 border-t pt-4">
            <h3 className="text-lg font-medium mb-4">Adreso informacija</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="street">Gatvė</Label>
                <Input
                  id="street"
                  name="street"
                  value={profile.street || ""}
                  readOnly
                  disabled
                  className="bg-muted"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="houseNumber">Namo numeris</Label>
                <Input
                  id="houseNumber"
                  name="houseNumber"
                  value={profile.houseNumber || ""}
                  readOnly
                  disabled
                  className="bg-muted"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="flatNumber">Buto numeris</Label>
                <Input
                  id="flatNumber"
                  name="flatNumber"
                  value={profile.flatNumber || ""}
                  readOnly
                  disabled
                  className="bg-muted"
                />
              </div>
            </div>
              <p className="text-sm text-muted-foreground mt-2">
              Adreso informacijos (gatvė, namas, butas) pakeisti negalima. Jei pastebėjote klaidą, susisiekite su administratoriumi.
              </p>
            {/* Floor Information - Editable */}
            <div className="mt-4">
              <div className="space-y-2">
                <Label htmlFor="flatFloor">Aukštas</Label>
                <div className="flex gap-2 items-center">
                  <Input
                    id="flatFloor"
                    type="number"
                    min="0"
                    max="50"
                    value={profile.flatFloor || ""}
                    onChange={(e) => {
                      const value = e.target.value;
                      setProfile(prev => ({ 
                        ...prev, 
                        flatFloor: value ? parseInt(value) : null 
                      }));
                    }}
                    placeholder="Pvz.: 3"
                    className="max-w-24"
                  />
                  <Button
                    type="button"
                    onClick={() => updateFloor(profile.flatFloor)}
                    disabled={updatingFloor}
                    size="sm"
                  >
                    {updatingFloor ? "Saugoma..." : "Išsaugoti"}
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Aukšto informacija, kuriame yra jūsų butas. Galite atnaujinti bet kuriuo metu.
                </p>
              </div>
            </div>
            

          </div>
          
          <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-md">
            <h3 className="text-sm font-medium text-amber-800">Svarbu:</h3>
            <p className="text-sm text-amber-700 mt-1">
              Prašome užpildyti savo kontaktinę informaciją. Ji bus naudojama tik svarbiais pranešimams apie namo priežiūrą ir avariją atveju.
            </p>
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-end gap-2">
          <Button 
            type="submit" 
            disabled={submitting} 
            className="min-w-[120px]"
          >
            {submitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saugoma...
              </>
            ) : isNewUser ? "Atnaujinti profilį" : "Išsaugoti pakeitimus"}
          </Button>
        </CardFooter>
      </form>
      
      {renderDebugInfo()}
    </Card>
  );
} 