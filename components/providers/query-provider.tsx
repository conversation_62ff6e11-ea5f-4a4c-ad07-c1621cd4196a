'use client';

import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useEffect, useState } from 'react';
import { useRealtimeSubscriptions } from '@/lib/tanstack/real-time';
import { 
  createAdvancedQueryClient, 
  setupOfflineSupport, 
  setupQueryPersistence,
  OfflineActionQueue 
} from '@/lib/tanstack/advanced-config';

interface QueryProviderProps {
  children: React.ReactNode;
}

function RealtimeProvider({ children }: { children: React.ReactNode }) {
  // Set up global real-time subscriptions
  useRealtimeSubscriptions();
  
  return <>{children}</>;
}

// Create advanced query client with persistence
const advancedQueryClient = createAdvancedQueryClient();
let offlineQueue: OfflineActionQueue;

function AdvancedFeaturesProvider({ children }: { children: React.ReactNode }) {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Set up offline support
    setupOfflineSupport(advancedQueryClient);
    
    // Set up query persistence
    setupQueryPersistence(advancedQueryClient);
    
    // Initialize offline action queue
    offlineQueue = new OfflineActionQueue(advancedQueryClient);
    
    // Make offline queue globally available
    if (typeof window !== 'undefined') {
      (window as any).__offlineQueue = offlineQueue;
    }

    setIsInitialized(true);
  }, []);

  if (!isInitialized) {
    // Show loading skeleton while initializing
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Kraunama...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

export function QueryProvider({ children }: QueryProviderProps) {
  return (
    <QueryClientProvider client={advancedQueryClient}>
      <AdvancedFeaturesProvider>
        <RealtimeProvider>
          {children}
        </RealtimeProvider>
      </AdvancedFeaturesProvider>
      {/* Enhanced devtools for production debugging */}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools
          initialIsOpen={false}
          position="bottom"
        />
      )}
    </QueryClientProvider>
  );
}

// Export the offline queue for use in mutations
export { offlineQueue };