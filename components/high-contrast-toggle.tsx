"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import { Eye, EyeOff, Contrast } from "lucide-react"
import { Switch } from "@/components/ui/switch"

interface HighContrastToggleProps {
  className?: string
}

export function HighContrastToggle({ className }: HighContrastToggleProps) {
  const [isHighContrast, setIsHighContrast] = useState(false)
  const [mounted, setMounted] = useState(false)

  // Only run on client
  useEffect(() => {
    setMounted(true)
    // Get saved contrast preference from local storage
    const savedContrast = localStorage.getItem("high-contrast-mode")
    if (savedContrast === "true") {
      setIsHighContrast(true)
      applyHighContrast(true)
    }
  }, [])

  const applyHighContrast = (enabled: boolean) => {
    if (enabled) {
      document.documentElement.classList.add("high-contrast")
    } else {
      document.documentElement.classList.remove("high-contrast")
    }
  }

  const toggleHighContrast = () => {
    const newState = !isHighContrast
    setIsHighContrast(newState)
    localStorage.setItem("high-contrast-mode", newState.toString())
    applyHighContrast(newState)
  }

  if (!mounted) return null

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "rounded-full focus-visible:ring-2 focus-visible:ring-indigo-500",
            className
          )}
          aria-label="Kontrastas"
        >
          <Contrast className="h-5 w-5" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="p-2 w-64">
        <div className="pb-2 pt-1 px-2">
          <p className="text-sm font-semibold">Kontrasto režimas</p>
          <p className="text-xs text-muted-foreground">
            Padidinkite kontrastą geresniam skaitomumui
          </p>
        </div>

        <DropdownMenuItem
          className="flex items-center justify-between py-2.5 gap-3 cursor-pointer"
          onClick={toggleHighContrast}
        >
          <div className="flex items-center gap-3">
            <div className={cn(
              "h-8 w-8 rounded-full flex items-center justify-center",
              isHighContrast ? "bg-indigo-100 text-indigo-600" : "bg-slate-100"
            )}>
              {isHighContrast ? (
                <Eye className="h-4 w-4" />
              ) : (
                <EyeOff className="h-4 w-4" />
              )}
            </div>
            
            <div className="space-y-0.5">
              <div className="font-medium text-sm">Didelis kontrastas</div>
              <div className="text-xs text-slate-500">
                {isHighContrast ? "Įjungta" : "Išjungta"}
              </div>
            </div>
          </div>

          <Switch
            checked={isHighContrast}
            onCheckedChange={toggleHighContrast}
            className="data-[state=checked]:bg-indigo-600"
          />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
} 