import { Phone, AlertCircle, Shield, Headphones, Building, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";

export function EmergencyContactsCard() {
  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
        <div className="flex flex-col md:flex-row md:items-center gap-5">
          <div className="bg-red-100 rounded-full p-3 flex-shrink-0 self-start md:self-center">
            <AlertCircle className="h-6 w-6 text-red-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-slate-900">Avariniai kontaktai</h2>
            <p className="text-base text-slate-600 mt-2">
              Svarbūs kontaktai avarijos atveju - išsaugokite šiuos numerius savo telefone.
            </p>
          </div>
        </div>
      </div>

      {/* Contact Cards - Primary Emergency */}
      <div>
        <h3 className="text-base font-medium text-slate-500 uppercase tracking-wider mb-3 px-1">Svarbiausi kontaktai</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Bendrasis pagalbos telefonas 24/7 */}
          <div className="bg-white rounded-xl overflow-hidden border border-red-200 shadow-sm">
            <div className="bg-red-50 px-4 py-3 border-b border-red-100">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <h3 className="font-medium text-lg text-red-900">Bendrasis pagalbos telefonas</h3>
              </div>
            </div>
            <div className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-3xl font-bold text-red-600">112</p>
                  <p className="text-base text-slate-500 mt-1">Policija, greitoji, gaisrinė</p>
                  <p className="text-sm text-slate-500 mt-2">Visiems kritiniams skubiems atvejams</p>
                </div>
                <Button variant="outline" size="sm" className="rounded-full bg-red-50 border-red-200 hover:bg-red-100 text-red-600" asChild>
                  <a href="tel:112" className="flex items-center gap-1.5">
                    <Phone className="h-3.5 w-3.5" /> Skambinti
                  </a>
                </Button>
              </div>
            </div>
          </div>

          {/* Avarinės tarnybos 24/7 */}
          <div className="bg-white rounded-xl overflow-hidden border border-indigo-200 shadow-sm">
            <div className="bg-indigo-50 px-4 py-3 border-b border-indigo-100">
              <div className="flex items-center gap-3">
                <Headphones className="h-5 w-5 text-indigo-600" />
                <h3 className="font-medium text-lg text-indigo-900">Avarinės tarnybos 24/7</h3>
              </div>
            </div>
            <div className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-2xl font-bold text-slate-900">846 366577</p>
                  <p className="text-base text-slate-500 mt-1">UAB "Skaidrola"</p>
                  <div className="flex items-center text-sm text-indigo-600 mt-2">
                    <Clock className="h-3.5 w-3.5 mr-1" /> Visą parą
                  </div>
                  <p className="text-sm text-slate-500 mt-2">Kanalizacija, vandentiekis, elektra, šildymas</p>
                </div>
                <Button variant="outline" size="sm" className="rounded-full bg-indigo-50 border-indigo-200 hover:bg-indigo-100 text-indigo-600 mt-1" asChild>
                  <a href="tel:846366577">
                    <Phone className="h-3.5 w-3.5" />
                  </a>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Cards - Other Services */}
      <div>
        <h3 className="text-base font-medium text-slate-500 uppercase tracking-wider mb-3 px-1">Kiti avariniai kontaktai</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Šildymo, vandens ir nuotekų priežiūra */}
          <div className="bg-white rounded-xl overflow-hidden border border-slate-200 shadow-sm">
            <div className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                <h3 className="text-base font-medium text-slate-700">Šildymo, vandens priežiūra</h3>
              </div>
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-xl font-semibold text-slate-900">846 342508</p>
                  <p className="text-sm text-slate-500 mt-1">UAB "SOBO" sistemos</p>
                  <p className="text-sm text-slate-500 mt-2">Šildymo ir vandens sistemų priežiūra darbo valandomis</p>
                </div>
                <Button variant="ghost" size="icon" className="h-9 w-9 rounded-full text-slate-400 hover:text-orange-500 hover:bg-orange-50" asChild>
                  <a href="tel:846342508">
                    <Phone className="h-4 w-4" />
                  </a>
                </Button>
              </div>
            </div>
          </div>
          
          {/* Liftų avarinė tarnyba */}
          <div className="bg-white rounded-xl overflow-hidden border border-slate-200 shadow-sm">
            <div className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <div className="h-2 w-2 rounded-full bg-red-500"></div>
                <h3 className="text-base font-medium text-slate-700">Liftų avarinė tarnyba</h3>
              </div>
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-xl font-semibold text-slate-900">+370 62072137</p>
                  <p className="text-sm text-slate-500 mt-1">UAB "Schindler-Liftas"</p>
                  <p className="text-sm text-slate-500 mt-2">Įstrigus lifte ar pastebėjus gedimus</p>
                </div>
                <Button variant="ghost" size="icon" className="h-9 w-9 rounded-full text-slate-400 hover:text-red-500 hover:bg-red-50" asChild>
                  <a href="tel:+37062072137">
                    <Phone className="h-4 w-4" />
                  </a>
                </Button>
              </div>
            </div>
          </div>
          
          {/* Telefonspynių priežiūra */}
          <div className="bg-white rounded-xl overflow-hidden border border-slate-200 shadow-sm">
            <div className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <div className="h-2 w-2 rounded-full bg-cyan-500"></div>
                <h3 className="text-base font-medium text-slate-700">Telefonspynių priežiūra</h3>
              </div>
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-xl font-semibold text-slate-900">846 493426</p>
                  <p className="text-sm text-slate-500 mt-1">UAB "Digitalas"</p>
                  <p className="text-sm text-slate-500 mt-2">Durų spynų gedimams ir magnetinių raktų gamybai</p>
                </div>
                <Button variant="ghost" size="icon" className="h-9 w-9 rounded-full text-slate-400 hover:text-cyan-500 hover:bg-cyan-50" asChild>
                  <a href="tel:846493426">
                    <Phone className="h-4 w-4" />
                  </a>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bendrijos kontaktai */}
      <div>
        <h3 className="text-base font-medium text-slate-500 uppercase tracking-wider mb-3 px-1">Bendrijos kontaktai</h3>
        <div className="bg-white rounded-xl overflow-hidden border border-slate-200 shadow-sm p-4">
          <div className="flex items-center gap-3 mb-4">
            <Building className="h-4 w-4 text-blue-600" />
            <h3 className="font-medium text-lg text-slate-900">DNSB Vakarai</h3>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="flex items-center gap-3 px-3 py-2 rounded-lg bg-slate-50">
              <div>
                <p className="text-sm text-slate-500">Pirmininkas</p>
                <p className="text-base font-medium text-slate-900">+370 616 30230</p>
              </div>
              <Button variant="ghost" size="icon" className="h-8 w-8 ml-auto rounded-full text-slate-400 hover:text-blue-500 hover:bg-blue-50" asChild>
                <a href="tel:+37061630230">
                  <Phone className="h-3.5 w-3.5" />
                </a>
              </Button>
            </div>
            
            <div className="flex items-center gap-3 px-3 py-2 rounded-lg bg-slate-50">
              <div>
                <p className="text-sm text-slate-500">Buhalterė</p>
                <p className="text-base font-medium text-slate-900">+370 61630989</p>
              </div>
              <Button variant="ghost" size="icon" className="h-8 w-8 ml-auto rounded-full text-slate-400 hover:text-blue-500 hover:bg-blue-50" asChild>
                <a href="tel:+37061630989">
                  <Phone className="h-3.5 w-3.5" />
                </a>
              </Button>
            </div>
            
            <div className="flex items-center gap-3 px-3 py-2 rounded-lg bg-slate-50">
              <div>
                <p className="text-sm text-slate-500">Elektrikas</p>
                <p className="text-base font-medium text-slate-900">+370 61631232</p>
              </div>
              <Button variant="ghost" size="icon" className="h-8 w-8 ml-auto rounded-full text-slate-400 hover:text-blue-500 hover:bg-blue-50" asChild>
                <a href="tel:+37061631232">
                  <Phone className="h-3.5 w-3.5" />
                </a>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Footer note */}
      <p className="text-sm text-slate-500 text-center px-1">
        Šie kontaktai skirti tik avarinėms situacijoms. Dėl kitų klausimų prašome susisiekti su bendrijos administracija.
      </p>
    </div>
  );
}