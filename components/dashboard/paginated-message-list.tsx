"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Clock, Mail, User, Star, CalendarIcon } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Button } from "@/components/ui/button";
import { formatDistanceToNow } from "date-fns";
import { lt } from "date-fns/locale";
import { translateCategory } from "@/lib/utils";

// Helper function to format date
function formatDate(date: string | Date) {
  return new Date(date).toLocaleDateString('lt-LT', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

// Badge component for status
function StatusBadge({ status }: { status: string }) {
  let colorClass = "bg-gray-500";
  
  switch(status) {
    case 'NEW':
      colorClass = "bg-blue-500";
      break;
    case 'IN_PROGRESS':
      colorClass = "bg-yellow-500";
      break;
    case 'RESOLVED':
      colorClass = "bg-green-500";
      break;
    case 'CLOSED':
      colorClass = "bg-gray-500";
      break;
  }
  
  const statusLabels: Record<string, string> = {
    NEW: 'Naujas',
    IN_PROGRESS: 'Nagrinėjamas',
    RESOLVED: 'Išspręstas',
    CLOSED: 'Uždarytas'
  };
  
  return (
    <Badge className={colorClass}>
      {statusLabels[status] || status}
    </Badge>
  );
}

interface PaginatedContactMessagesProps {
  messages: any[];
  emptyMessage?: string;
}

export function PaginatedContactMessages({ 
  messages,
  emptyMessage = "Nėra gautų pranešimų" 
}: PaginatedContactMessagesProps) {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [paginatedMessages, setPaginatedMessages] = useState<any[]>([]);
  
  useEffect(() => {
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    setPaginatedMessages(messages.slice(start, end));
  }, [messages, currentPage, pageSize]);
  
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page when changing page size
  };
  
  if (messages.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-muted-foreground">
          {emptyMessage}
        </CardContent>
      </Card>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="grid gap-6">
        {paginatedMessages.map((message: any) => (
          <Link href={`/dashboard/admin/messages/contact/${message.id}`} key={message.id}>
            <Card className="cursor-pointer transition-all hover:shadow-md">
              <CardHeader className="flex flex-row justify-between items-start pb-2">
                <div>
                  <CardTitle className="text-xl flex items-center gap-2">
                    {message.subject}
                    <StatusBadge status={message.status} />
                  </CardTitle>
                  <CardDescription>
                    {translateCategory(message.category)}
                  </CardDescription>
                </div>
                <div className="text-sm text-muted-foreground flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {formatDate(message.created_at)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-sm mb-2 line-clamp-2">
                  {message.message}
                </div>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  {message.is_anonymous ? (
                    // Display for anonymous users
                    <div className="flex items-center gap-1 text-amber-600">
                      <User className="h-4 w-4" />
                      <span>Anoniminis</span>
                    </div>
                  ) : (
                    // Display for non-anonymous users
                    <>
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {message.name}
                      </div>
                      <div className="flex items-center gap-1">
                        <Mail className="h-4 w-4" />
                        {message.email}
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
      
      {messages.length > pageSize && (
        <div className="mt-6">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
                  className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
              
              {/* Show page numbers */}
              {Array.from({ length: Math.min(5, Math.ceil(messages.length / pageSize)) }, (_, i) => (
                <PaginationItem key={i + 1}>
                  <PaginationLink
                    isActive={currentPage === i + 1}
                    onClick={() => handlePageChange(i + 1)}
                    className="cursor-pointer"
                  >
                    {i + 1}
                  </PaginationLink>
                </PaginationItem>
              ))}
              
              {/* Show ellipsis if there are more pages */}
              {Math.ceil(messages.length / pageSize) > 5 && (
                <PaginationItem>
                  <PaginationEllipsis />
                </PaginationItem>
              )}
              
              <PaginationItem>
                <PaginationNext 
                  onClick={() => currentPage < Math.ceil(messages.length / pageSize) && handlePageChange(currentPage + 1)}
                  className={currentPage >= Math.ceil(messages.length / pageSize) ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
          
          <div className="mt-4 flex justify-center items-center gap-2 text-sm text-muted-foreground">
            <span>Įrašai per puslapį:</span>
            <select 
              value={pageSize} 
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
              className="border rounded px-2 py-1"
            >
              {[10, 25, 50, 100].map((size) => (
                <option key={size} value={size}>{size}</option>
              ))}
            </select>
          </div>
        </div>
      )}
    </div>
  );
}

interface PaginatedFeedbackMessagesProps {
  messages: any[];
  emptyMessage?: string;
}

export function PaginatedFeedbackMessages({ 
  messages,
  emptyMessage = "Nėra gautų atsiliepimų" 
}: PaginatedFeedbackMessagesProps) {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [paginatedMessages, setPaginatedMessages] = useState<any[]>([]);
  
  useEffect(() => {
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    setPaginatedMessages(messages.slice(start, end));
  }, [messages, currentPage, pageSize]);
  
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page when changing page size
  };
  
  if (messages.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-muted-foreground">
          {emptyMessage}
        </CardContent>
      </Card>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="grid gap-6">
        {paginatedMessages.map((feedback: any) => (
          <Link href={`/dashboard/admin/messages/feedback/${feedback.id}`} key={feedback.id}>
            <Card className="cursor-pointer transition-all hover:shadow-md">
              <CardHeader className="flex flex-row justify-between items-start pb-2">
                <div>
                  <CardTitle className="text-xl flex items-center gap-2">
                    {feedback.title}
                    <StatusBadge status={feedback.status} />
                  </CardTitle>
                  <div className="flex items-center gap-2 text-sm text-slate-500 leading-relaxed mt-1">
                    {translateCategory(feedback.category)}
                    <span className="flex items-center ml-2 font-medium">
                      {feedback.rating}/5
                      <Star className="h-4 w-4 ml-1 fill-yellow-400 text-yellow-400" />
                    </span>
                  </div>
                </div>
                <div className="text-sm text-muted-foreground flex items-center gap-1">
                  <CalendarIcon className="h-4 w-4" />
                  {formatDate(feedback.created_at)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-sm mb-2 line-clamp-2">
                  {feedback.content || feedback.feedback}
                </div>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  {feedback.is_anonymous ? (
                    // Display for anonymous users
                    <div className="flex items-center gap-1 text-amber-600">
                      <User className="h-4 w-4" />
                      <span>Anoniminis</span>
                    </div>
                  ) : (
                    // Display for non-anonymous users
                    <>
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {feedback.name}
                      </div>
                      {feedback.allow_contact && (
                        <div className="flex items-center gap-1">
                          <Mail className="h-4 w-4" />
                          Leidžia susisiekti
                        </div>
                      )}
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
      
      {messages.length > pageSize && (
        <div className="mt-6">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
                  className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
              
              {/* Show page numbers */}
              {Array.from({ length: Math.min(5, Math.ceil(messages.length / pageSize)) }, (_, i) => (
                <PaginationItem key={i + 1}>
                  <PaginationLink
                    isActive={currentPage === i + 1}
                    onClick={() => handlePageChange(i + 1)}
                    className="cursor-pointer"
                  >
                    {i + 1}
                  </PaginationLink>
                </PaginationItem>
              ))}
              
              {/* Show ellipsis if there are more pages */}
              {Math.ceil(messages.length / pageSize) > 5 && (
                <PaginationItem>
                  <PaginationEllipsis />
                </PaginationItem>
              )}
              
              <PaginationItem>
                <PaginationNext 
                  onClick={() => currentPage < Math.ceil(messages.length / pageSize) && handlePageChange(currentPage + 1)}
                  className={currentPage >= Math.ceil(messages.length / pageSize) ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
          
          <div className="mt-4 flex justify-center items-center gap-2 text-sm text-muted-foreground">
            <span>Įrašai per puslapį:</span>
            <select 
              value={pageSize} 
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
              className="border rounded px-2 py-1"
            >
              {[10, 25, 50, 100].map((size) => (
                <option key={size} value={size}>{size}</option>
              ))}
            </select>
          </div>
        </div>
      )}
    </div>
  );
} 