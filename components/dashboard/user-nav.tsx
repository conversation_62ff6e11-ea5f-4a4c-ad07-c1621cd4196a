"use client"

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useRouter } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { 
  User, 
  LogOut, 
  Settings, 
  Bell, 
  Home, 
  Shield, 
  UserCog,
  ChevronDown,
  BadgeHelp,
  Terminal
} from "lucide-react"
import { cn } from "@/lib/utils"
import Link from "next/link"

interface UserNavProps {
  user: {
    name?: string;
    email?: string;
    role: string;
    username?: string;
  }
}

export function UserNav({ user }: UserNavProps) {
  const router = useRouter();
  const supabase = createClient();
  
  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/auth/login');
    router.refresh();
  }
  
  const getInitials = (name?: string) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase();
  }
  
  const getRoleDisplay = (role: string) => {
    switch (role) {
      case 'developer':
        return { 
          title: 'Programuotojas',
          color: 'bg-white text-purple-800 border-purple-200',
          iconColor: 'text-purple-600',
          icon: Terminal
        };
      case 'super_admin':
        return { 
          title: 'Super Administratorius',
          color: 'bg-white text-red-800 border-red-200',
          iconColor: 'text-red-600',
          icon: Shield
        };
      case 'editor':
        return { 
          title: 'Redaktorius',
          color: 'bg-white text-blue-800 border-blue-200',
          iconColor: 'text-blue-600',
          icon: UserCog
        };
      case 'user':
        return { 
          title: 'Vartotojas',
          color: 'bg-white text-emerald-800 border-emerald-200',
          iconColor: 'text-emerald-600',
          icon: User
        };
      default:
        return { 
          title: 'Nežinomas',
          color: 'bg-white text-slate-800 border-slate-200',
          iconColor: 'text-slate-600',
          icon: BadgeHelp
        };
    }
  }
  
  const roleInfo = getRoleDisplay(user.role);
  const RoleIcon = roleInfo.icon;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          className="relative h-10 rounded-full px-2 text-sm lg:pl-3 lg:pr-2 group"
        >
          <Avatar className="h-7 w-7 mr-0 lg:mr-2 border border-slate-200 shadow-sm group-hover:border-indigo-300 transition-colors">
            <AvatarImage src="/avatar.png" alt={user.name || ''} />
            <AvatarFallback className="bg-indigo-600 text-white text-xs">
              {getInitials(user.name)}
            </AvatarFallback>
          </Avatar>
          <span className="hidden lg:inline font-medium text-slate-900 max-w-[150px] truncate group-hover:text-indigo-700 transition-colors">
            {user.name || user.username || user.email?.split('@')[0] || 'Vartotojas'}
          </span>
          <ChevronDown className="hidden lg:inline h-4 w-4 ml-1 text-slate-500 group-hover:text-indigo-700 transition-colors" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64 p-2 bg-white" align="end" forceMount>
        <div className="flex items-center justify-start gap-3 p-3 mb-1 bg-white rounded-md border border-slate-200">
          <Avatar className="h-10 w-10 border border-slate-200 shadow-sm">
            <AvatarImage src="/avatar.png" alt={user.name || ''} />
            <AvatarFallback className="bg-indigo-600 text-white">
              {getInitials(user.name)}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col space-y-0.5">
            <p className="text-sm font-semibold leading-none text-slate-900">
              {user.name || 'Vartotojas'}
            </p>
            <p className="text-xs leading-none text-slate-600 truncate max-w-[180px]">
              {user.email}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2 px-2 py-2">
          <div className={cn(
            "flex items-center gap-1.5 text-xs py-1 px-2 rounded-full",
            roleInfo.color,
            "border shadow-sm"
          )}>
            <RoleIcon className={cn("h-3 w-3", roleInfo.iconColor)} />
            <span className="font-medium">{roleInfo.title}</span>
          </div>
          {user.username && (
            <div className="text-xs text-slate-600 ml-auto bg-white py-1 px-2 rounded-full border border-slate-200 shadow-sm">
              ID: <span className="font-medium">{user.username}</span>
            </div>
          )}
        </div>
        
        <DropdownMenuSeparator className="my-1.5" />
        
        <DropdownMenuGroup className="space-y-0.5">
          <DropdownMenuItem asChild className="cursor-pointer py-2">
            <Link href="/dashboard" className="flex items-center gap-2.5 w-full">
              <Home className="h-4 w-4 text-indigo-500" />
              <span>Valdymo skydelis</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild className="cursor-pointer py-2">
            <Link href="/dashboard/user/profile" className="flex items-center gap-2.5 w-full">
              <User className="h-4 w-4 text-indigo-500" />
              <span>Profilis</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild className="cursor-pointer py-2">
            <Link href="/dashboard/notifications" className="flex items-center gap-2.5 w-full">
              <Bell className="h-4 w-4 text-indigo-500" />
              <span>Pranešimai</span>
            </Link>
          </DropdownMenuItem>
          
          {(user.role === 'super_admin' || user.role === 'editor') && (
            <DropdownMenuItem asChild className="cursor-pointer py-2">
              <Link href="/dashboard/admin/settings" className="flex items-center gap-2.5 w-full">
                <Settings className="h-4 w-4 text-indigo-500" />
                <span>Sistemos nustatymai</span>
              </Link>
            </DropdownMenuItem>
          )}
        </DropdownMenuGroup>
        
        <DropdownMenuSeparator className="my-1.5" />
        
        <DropdownMenuItem 
          onClick={handleSignOut}
          className="cursor-pointer text-red-600 hover:text-white hover:bg-red-600 focus:text-white focus:bg-red-600 py-2"
        >
          <LogOut className="h-4 w-4 mr-2.5" />
          <span>Atsijungti</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}