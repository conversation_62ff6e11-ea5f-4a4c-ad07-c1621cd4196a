"use client";

import { DashboardNav } from "@/components/dashboard/nav";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import type { User } from "@supabase/supabase-js";
import React, { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { ChevronLeft, ChevronRight, ArrowLeft } from "lucide-react";

// TODO: Move relevant useState, useEffect, resize/nav logic here from old layout
interface SidebarClientWrapperProps {
  user: any; // Accept any user object from Supabase
}

export function SidebarClientWrapper({ user }: SidebarClientWrapperProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true); // Initial state
  const [isMobile, setIsMobile] = useState(false);
  const pathname = usePathname();

  // Effect for mobile detection and responsive sidebar behavior
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 1024;
      setIsMobile(mobile);
      // Set sidebar state based on mobile detection
      setSidebarOpen(!mobile);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []); // Empty dependency array - only run once

  // Effect for closing sidebar on mobile navigation only (when pathname changes)
  useEffect(() => {
    // Only close sidebar if we're on mobile and it's currently open
    if (window.innerWidth < 1024) {
      setSidebarOpen(false);
    }
  }, [pathname]); // Only depend on pathname changes

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <TooltipProvider delayDuration={0}>
      {/* Mobile sidebar overlay */}
      {sidebarOpen && isMobile && (
        <div 
          className="fixed inset-0 z-30 bg-slate-900/50 lg:hidden backdrop-blur-sm" 
          onClick={() => setSidebarOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <aside 
        id="sidebar-menu"
        className={cn(
          "fixed inset-y-0 left-0 z-40 h-screen pt-16 bg-white border-r border-slate-200 shadow-sm overflow-hidden",
          "transition-all duration-300 ease-in-out flex flex-col",
          sidebarOpen 
            ? isMobile 
              ? "w-[280px]" 
              : "w-[280px] lg:relative lg:z-10" 
            : "w-0 lg:w-[80px] lg:relative lg:z-10"
        )}
        role="navigation"
        aria-label="Pagrindinis navigacijos meniu"
      >
        {/* Mobile close button */}
        {isMobile && sidebarOpen && (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(false)}
            className="absolute top-4 right-4 z-20 text-slate-600 hover:bg-slate-100 hover:text-red-600 border border-slate-200 shadow-sm"
            aria-label="Uždaryti meniu"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
        )}
        
        {/* Desktop toggle button */}
        <div className="absolute top-3 right-3 hidden lg:flex items-center z-20">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleSidebar}
                className="h-7 w-7 text-slate-500 hover:text-indigo-600 bg-white hover:bg-white border border-slate-200 shadow-sm"
                aria-label={sidebarOpen ? "Sutraukti meniu" : "Išplėsti meniu"}
                aria-expanded={sidebarOpen}
                aria-controls="sidebar-menu"
              >
                {sidebarOpen ? (
                  <ChevronLeft className="h-4 w-4 transition-transform" />
                ) : (
                  <ChevronRight className="h-4 w-4 transition-transform" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              {sidebarOpen ? "Sutraukti meniu" : "Išplėsti meniu"}
            </TooltipContent>
          </Tooltip>
        </div>

        <ScrollArea className="flex-1 h-[calc(100vh-4rem-3rem)]">
          <DashboardNav pathname={pathname} collapsed={!sidebarOpen} user={user} />
        </ScrollArea>
      </aside>
    </TooltipProvider>
  );
} 