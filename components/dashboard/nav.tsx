"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useMemo } from "react"

import { cn } from "@/lib/utils"
import {
  Home,
  Settings,
  AlertCircle,
  Calendar,
  Users,
  FilePenLine,
  Mail,
  Phone,
  MessageSquare,
  Star,
  Inbox,
  FileText,
  UserCircle,
  Code,
  PhoneCall,
  ChevronRight,
  Bell,
  ArchiveIcon
} from "lucide-react"
import { 
  Sidebar, 
  SidebarHeader, 
  SidebarNav, 
  SidebarNavItem,
  SidebarFooter
} from "@/components/ui/sidebar"

interface NavProps {
  pathname: string;
  collapsed?: boolean;
  user?: any; // User from parent component
}

// Descriptions for tooltips
const routeDescriptions = {
  dashboard: "Valdymo skydelio pagrindinis puslapis",
  announcements: "Peržiūrėkite visus pranešimus jums ir namo gyventojams",
  polls: "Balsuokite apklausose ir peržiūrėkite rezultatus",
  adminPolls: "Administruokite apklausas ir balsavimo rezultatus",
  contacts: "Bendrijos ir avariniai kontaktiniai duomenys",
  emergencyContacts: "Avariniai kontaktai nelaimės atveju",
  contact: "Užduokite klausimą arba praneškite problemą administracijai",
  feedback: "Palikite atsiliepimą apie bendrijos darbą ar pasiūlykite patobulinimų",
  profile: "Tvarkykite savo profilio informaciją ir slaptažodį",
  createAnnouncement: "Sukurkite naują pranešimą gyventojams",
  houses: "Administruokite namų ir butų informaciją",
  users: "Vartotojų paskyros ir jų valdymas",
  importUsers: "Importuoti naujus vartotojus iš Excel failo",
  messages: "Peržiūrėti gyventojų išsiųstus pranešimus",
  settings: "Sistemos nustatymai ir konfigūracijos",
  emailQueue: "El. pašto eilės valdymas ir stebėjimas",
  archivedEmails: "Peržiūrėti archyvuotus laiškus"
};


export function DashboardNav({ pathname, collapsed = false, user: propsUser }: NavProps) {
  // Always prioritize the user prop passed from server component
  const user = propsUser;
  
  // Show loading state only if no user prop is provided
  if (!user) {
    console.warn('Navigation: No user data available');
    return (
      <div className="w-full h-full flex items-center justify-center p-4">
        <p className="text-sm text-gray-500">Nepavyko užkrauti navigacijos</p>
      </div>
    );
  }
  
  const isAdmin = user.role === "developer" || user.role === "super_admin" || user.role === "editor";
  const isSuperAdmin = user.role === "super_admin" || user.role === "developer";
  const isRegularUser = user.role === "user";

  // Memoize routes to prevent recreation on every render
  const commonRoutes = useMemo(() => [
    {
      label: "Pagrindinis",
      href: "/dashboard",
      icon: Home,
      active: pathname === "/dashboard",
      description: routeDescriptions.dashboard
    },
    {
      label: "Pranešimai",
      href: "/dashboard/announcements",
      icon: AlertCircle,
      active: pathname.startsWith("/dashboard/announcements") && 
             pathname !== "/dashboard/announcements/create",
      description: routeDescriptions.announcements
    },
    {
      label: "Apklausos",
      href: "/dashboard/polls",
      icon: FileText,
      active: pathname.startsWith("/dashboard/polls"),
      description: routeDescriptions.polls
    },
    {
      label: "Bendrijos kontaktai",
      href: "/dashboard/contacts",
      icon: PhoneCall,
      active: pathname.startsWith("/dashboard/contacts"),
      description: routeDescriptions.contacts
    },
    {
      label: "Profilis",
      href: "/dashboard/user/profile",
      icon: UserCircle,
      active: pathname === "/dashboard/user/profile",
      description: routeDescriptions.profile
    },
  ], [pathname]);
  
  // Routes only for regular users, not for admins
  const userOnlyRoutes = useMemo(() => [
    {
      label: "Buto kontaktai",
      href: "/dashboard/user/contacts",
      icon: Phone,
      active: pathname === "/dashboard/user/contacts",
      description: routeDescriptions.contacts
    },
    {
      label: "Susisiekite",
      href: "/dashboard/contact",
      icon: MessageSquare,
      active: pathname === "/dashboard/contact",
      description: routeDescriptions.contact
    },
    {
      label: "Atsiliepimas",
      href: "/dashboard/feedback",
      icon: Star,
      active: pathname === "/dashboard/feedback",
      description: routeDescriptions.feedback
    },
  ], [pathname]);
  
  const adminRoutes = useMemo(() => [
    {
      href: "/dashboard/announcements/create",
      label: "Naujas pranešimas",
      icon: FilePenLine,
      active: pathname === "/dashboard/announcements/create",
      showFor: isAdmin,
      description: routeDescriptions.createAnnouncement
    },
    {
      href: "/dashboard/admin/houses",
      label: "Namai ir butai",
      icon: Home,
      active: pathname.startsWith("/dashboard/admin/houses"),
      showFor: isAdmin,
      description: routeDescriptions.houses
    },
    {
      href: "/dashboard/admin/users",
      label: "Vartotojai",
      icon: Users,
      active: pathname.startsWith("/dashboard/admin/users"),
      showFor: isAdmin,
      description: routeDescriptions.users
    },
    {
      href: "/dashboard/admin/polls",
      label: "Apklausų valdymas",
      icon: FileText,
      active: pathname.startsWith("/dashboard/admin/polls"),
      showFor: isAdmin,
      description: routeDescriptions.adminPolls
    },
    {
      href: "/dashboard/admin/users/import",
      label: "Importuoti vartotojus",
      icon: FileText,
      active: pathname === "/dashboard/admin/users/import",
      showFor: isSuperAdmin,
      description: routeDescriptions.importUsers
    },
    {
      href: "/dashboard/admin/messages",
      label: "Gauti pranešimai",
      icon: Inbox,
      active: pathname.startsWith("/dashboard/admin/messages"),
      showFor: isAdmin,
      description: routeDescriptions.messages
    },
    {
      href: "/dashboard/admin/related-records",
      label: "El. pašto eilė",
      icon: Mail,
      active: pathname.startsWith("/dashboard/admin/related-records"),
      showFor: isAdmin,
      description: routeDescriptions.emailQueue
    },
    {
      href: "/dashboard/admin/settings",
      label: "Nustatymai",
      icon: Settings,
      active: pathname.startsWith("/dashboard/admin/settings"),
      showFor: isAdmin,
      description: routeDescriptions.settings
    },
  ].filter(route => route.showFor), [pathname, isAdmin, isSuperAdmin]);

  return (
    <Sidebar collapsed={collapsed} className="w-full h-full flex-1">
      {collapsed ? (
        <SidebarHeader>
          <p className="text-xs font-semibold text-slate-500 text-center">
            Meniu
          </p>
        </SidebarHeader>
      ) : (
        <SidebarHeader
          title="Pagrindinis meniu"
          description="Navigacija"
        />
      )}
      
      <SidebarNav>
        {commonRoutes.map((item) => (
          <SidebarNavItem
            key={item.href}
            href={item.href}
            icon={item.icon}
            label={item.label}
            description={item.description}
            active={item.active}
          />
        ))}
      </SidebarNav>
      
      {isRegularUser && userOnlyRoutes.length > 0 && (
        <>
          {collapsed ? (
            <div className="my-3 border-t border-slate-200 mx-2" />
          ) : (
            <SidebarHeader
              title="Vartotojo įrankiai"
              description="Jūsų ir jūsų buto informacija"
              className="pt-4 mt-4 border-t border-slate-200"
            />
          )}
          
          <SidebarNav>
            {userOnlyRoutes.map((item) => (
              <SidebarNavItem
                key={item.href}
                href={item.href}
                icon={item.icon}
                label={item.label}
                description={item.description}
                active={item.active}
              />
            ))}
          </SidebarNav>
        </>
      )}
      
      {isAdmin && adminRoutes.length > 0 && (
        <>
          {collapsed ? (
            <>
              <div className="my-3 border-t border-slate-200 mx-2" />
              <SidebarHeader>
                <p className="text-xs font-semibold text-slate-500 text-center">
                  Admin
                </p>
              </SidebarHeader>
            </>
          ) : (
            <SidebarHeader
              title="Administravimas"
              description="Sistemos valdymo įrankiai"
              className="pt-4 mt-4 border-t border-slate-200"
            />
          )}
          
          <SidebarNav>
            {adminRoutes.map((item) => (
              <SidebarNavItem
                key={item.href}
                href={item.href}
                icon={item.icon}
                label={item.label}
                description={item.description}
                active={item.active}
              />
            ))}
          </SidebarNav>
        </>
      )}
      
      <SidebarFooter>
        <Link
          href="https://dnsb-vakarai.lt"
          target="_blank"
          rel="noopener noreferrer"
          className={cn(
            "flex items-center w-full opacity-70 hover:opacity-100 transition-opacity text-slate-600 hover:text-indigo-700",
            collapsed ? "justify-center" : "justify-start"
          )}
        >
          <span className={cn(
            "text-xs font-medium",
            collapsed && "hidden"
          )}>
            © DNSB Vakarai, {new Date().getFullYear()}
          </span>
        </Link>
      </SidebarFooter>
    </Sidebar>
  )
}