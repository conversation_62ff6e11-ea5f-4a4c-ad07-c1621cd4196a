"use client";

import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { useMemo } from "react";
import { 
  CheckCircle2, 
  Clock, 
  MailQuestion, 
  TimerOff, 
  ClipboardList,
  Star
} from "lucide-react";

interface MessageStatsProps {
  messages: any[];
  type: 'contact' | 'feedback';
}

export function MessageStats({ messages, type }: MessageStatsProps) {
  const stats = useMemo(() => {
    const newCount = messages.filter(m => m.status === "NEW").length;
    const inProgressCount = messages.filter(m => m.status === "IN_PROGRESS").length;
    const resolvedCount = messages.filter(m => m.status === "RESOLVED").length;
    const closedCount = messages.filter(m => m.status === "CLOSED").length;
    const totalCount = messages.length;
    
    return {
      new: newCount,
      inProgress: inProgressCount,
      resolved: resolvedCount,
      closed: closedCount,
      total: totalCount
    };
  }, [messages]);
  
  const title = type === 'contact' ? 'Pranešimų statistika' : 'Atsiliepimų statistika';
  const icon = type === 'contact' ? <ClipboardList className="h-5 w-5" /> : <Star className="h-5 w-5" />;
  
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-xl flex items-center gap-2">
          {icon}
          {title}
        </CardTitle>
        <CardDescription>
          Iš viso: {stats.total}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          <div className="flex flex-col items-center p-3 bg-blue-50 rounded-md">
            <MailQuestion className="h-6 w-6 text-blue-600 mb-1" />
            <div className="text-2xl font-bold text-blue-600">{stats.new}</div>
            <div className="text-xs text-center text-blue-600">Nauji</div>
          </div>
          <div className="flex flex-col items-center p-3 bg-yellow-50 rounded-md">
            <Clock className="h-6 w-6 text-yellow-600 mb-1" />
            <div className="text-2xl font-bold text-yellow-600">{stats.inProgress}</div>
            <div className="text-xs text-center text-yellow-600">Nagrinėjami</div>
          </div>
          <div className="flex flex-col items-center p-3 bg-green-50 rounded-md">
            <CheckCircle2 className="h-6 w-6 text-green-600 mb-1" />
            <div className="text-2xl font-bold text-green-600">{stats.resolved}</div>
            <div className="text-xs text-center text-green-600">Išspręsti</div>
          </div>
          <div className="flex flex-col items-center p-3 bg-gray-50 rounded-md">
            <TimerOff className="h-6 w-6 text-gray-600 mb-1" />
            <div className="text-2xl font-bold text-gray-600">{stats.closed}</div>
            <div className="text-xs text-center text-gray-600">Uždaryti</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 