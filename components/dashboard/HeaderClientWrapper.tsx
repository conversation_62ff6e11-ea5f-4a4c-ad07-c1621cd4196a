"use client";

import Link from "next/link";
import { User<PERSON>av } from "@/components/dashboard/user-nav";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>u, X } from "lucide-react";
import { cn } from "@/lib/utils";
import type { User } from "@supabase/supabase-js"; // Import the User type
import React from "react"; // Import React for useState

// TODO: Move relevant useState, useEffect, toggleSidebar logic here from old layout
interface HeaderClientWrapperProps {
  user: any; // Accept any user object from Supabase
}

export function HeaderClientWrapper({ user }: HeaderClientWrapperProps) {
  const [sidebarOpen, setSidebarOpen] = React.useState(false); // Placeholder state
  const [isMobile, setIsMobile] = React.useState(false); // Placeholder state

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen); // Placeholder function

  // Placeholder implementation - Needs state/logic from old layout
  return (
    <header className="sticky top-0 z-40 border-b border-slate-200 bg-white shadow-sm backdrop-blur supports-[backdrop-filter]:bg-white/90" role="banner">
      <div className="flex h-16 items-center justify-between px-4 md:px-6">
        <div className="flex items-center gap-3">
          {/* Mobile sidebar toggle */}
          <Button 
            variant="ghost" 
            size="icon" 
            className={cn(
              "lg:hidden hover:bg-indigo-50",
              sidebarOpen ? "text-red-600 hover:text-red-700" : "text-indigo-700"
            )}
            onClick={toggleSidebar} // This needs proper state management
            aria-label={sidebarOpen ? "Uždaryti meniu" : "Atidaryti meniu"}
            aria-expanded={sidebarOpen}
            aria-controls="sidebar-menu"
          >
            {sidebarOpen ? (
              <X className="h-5 w-5" />
            ) : (
              <Menu className="h-5 w-5" />
            )}
          </Button>
          
          <Link href="/" className="flex items-center space-x-2 group" aria-label="Grįžti į pagrindinį puslapį">
            <div className="bg-gradient-to-br from-indigo-600 to-indigo-700 text-white rounded-md p-1.5 flex items-center justify-center w-8 h-8 shadow-sm group-hover:shadow-md group-hover:shadow-indigo-500/10 transition-shadow">
              <span className="font-bold text-sm">DV</span>
            </div>
            <span className="font-bold text-indigo-900 hidden sm:inline-block group-hover:text-indigo-700 transition-colors">
              DNSB Vakarai
            </span>
          </Link>
        </div>
        
        <div className="flex items-center">
          {user && <UserNav user={user} />} {/* Pass user to UserNav */}
        </div>
      </div>
    </header>
  );
} 