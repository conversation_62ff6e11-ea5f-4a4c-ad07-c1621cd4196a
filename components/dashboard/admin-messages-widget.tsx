"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardFooter, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { MessageSquare, Star, Clock, User, AlertCircle } from "lucide-react";

type ContactMessage = {
  id: string;
  subject: string;
  name: string;
  email: string;
  status: string;
  created_at: string;
  is_anonymous?: boolean;
};

type FeedbackMessage = {
  id: string;
  title: string;
  name: string;
  rating: number;
  status: string;
  created_at: string;
  is_anonymous?: boolean;
};

function StatusBadge({ status }: { status: string }) {
  const statusConfig: Record<string, { label: string; variant: "outline" | "secondary" | "destructive" | "default" }> = {
    NEW: { label: "Naujas", variant: "default" },
    IN_PROGRESS: { label: "Nagrinėja<PERSON>", variant: "secondary" },
    RESOLVED: { label: "Išspręstas", variant: "outline" },
    CLOSED: { label: "Uždarytas", variant: "destructive" },
  };

  const config = statusConfig[status] || { label: status, variant: "outline" };

  return (
    <Badge variant={config.variant}>{config.label}</Badge>
  );
}

function formatDate(date: string) {
  return new Date(date).toLocaleDateString("lt-LT", {
    year: "numeric",
    month: "numeric",
    day: "numeric",
  });
}

export function AdminMessagesWidget() {
  const router = useRouter();
  const [contactMessages, setContactMessages] = useState<ContactMessage[]>([]);
  const [feedbackMessages, setFeedbackMessages] = useState<FeedbackMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    async function fetchMessages() {
      setIsLoading(true);
      setError(null);
      
      try {
        // Fetch contact messages
        const contactResponse = await fetch("/api/admin/messages/recent?type=contact&limit=5", {
          cache: "no-store", // Prevent caching issues
        });
        
        if (!contactResponse.ok) {
          const errorData = await contactResponse.json().catch(() => ({}));
          throw new Error(errorData.error || "Nepavyko gauti pranešimų");
        }
        
        const contactData = await contactResponse.json();
        
        // Fetch feedback
        const feedbackResponse = await fetch("/api/admin/messages/recent?type=feedback&limit=5", {
          cache: "no-store", // Prevent caching issues
        });
        
        if (!feedbackResponse.ok) {
          const errorData = await feedbackResponse.json().catch(() => ({}));
          throw new Error(errorData.error || "Nepavyko gauti atsiliepimų");
        }
        
        const feedbackData = await feedbackResponse.json();
        
        setContactMessages(contactData);
        setFeedbackMessages(feedbackData);
        setRetryCount(0); // Reset retry count on success
      } catch (err) {
        setError(err instanceof Error ? err.message : "Įvyko klaida");
        console.error("Error fetching messages:", err);
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchMessages();
  }, [retryCount]); // Add retryCount as a dependency

  const hasNewMessages = contactMessages.some(msg => msg.status === "NEW") || 
                         feedbackMessages.some(msg => msg.status === "NEW");
  
  // Count new messages
  const newContactCount = contactMessages.filter(msg => msg.status === "NEW").length;
  const newFeedbackCount = feedbackMessages.filter(msg => msg.status === "NEW").length;
  
  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
  };
                     
  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center gap-2">
              Naujausi pranešimai
              {hasNewMessages && 
                <Badge variant="destructive" className="rounded-full h-5 w-5 p-0 flex items-center justify-center">
                  {newContactCount + newFeedbackCount}
                </Badge>
              }
            </CardTitle>
            <CardDescription>
              Peržiūrėkite naujausius pranešimus iš gyventojų
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {isLoading ? (
          <div className="py-6 text-center text-muted-foreground">
            Kraunama...
          </div>
        ) : error ? (
          <div className="py-6 text-center flex flex-col items-center gap-3">
            <div className="text-destructive flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              <p>{error}</p>
            </div>
            <Button variant="outline" size="sm" onClick={handleRetry} className="mt-2">
              Bandyti dar kartą
            </Button>
          </div>
        ) : (
          <Tabs defaultValue="contact" className="w-full">
            <TabsList className="mb-4 grid grid-cols-2">
              <TabsTrigger value="contact" className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Pranešimai
                {newContactCount > 0 && (
                  <Badge variant="secondary" className="ml-1 bg-blue-500 text-white h-5 min-w-5 px-1.5 rounded-full text-xs">
                    {newContactCount}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="feedback" className="flex items-center gap-2">
                <Star className="h-4 w-4" />
                Atsiliepimai
                {newFeedbackCount > 0 && (
                  <Badge variant="secondary" className="ml-1 bg-blue-500 text-white h-5 min-w-5 px-1.5 rounded-full text-xs">
                    {newFeedbackCount}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="contact">
              {contactMessages.length === 0 ? (
                <div className="py-6 text-center text-muted-foreground">
                  Nėra naujų pranešimų
                </div>
              ) : (
                <div className="space-y-3">
                  {contactMessages.map((message) => (
                    <div 
                      key={message.id}
                      className="p-3 border rounded-md flex justify-between items-start hover:bg-muted/50 cursor-pointer"
                      onClick={() => router.push(`/dashboard/admin/messages/contact/${message.id}`)}
                    >
                      <div>
                        <div className="font-medium flex items-center gap-2">
                          {message.subject}
                          <StatusBadge status={message.status} />
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center gap-2 mt-1">
                          <User className="h-3 w-3" />
                          {message.is_anonymous ? (
                            <span className="text-amber-600">Anoniminis</span>
                          ) : (
                            message.name
                          )}
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatDate(message.created_at)}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="feedback">
              {feedbackMessages.length === 0 ? (
                <div className="py-6 text-center text-muted-foreground">
                  Nėra naujų atsiliepimų
                </div>
              ) : (
                <div className="space-y-3">
                  {feedbackMessages.map((feedback) => (
                    <div 
                      key={feedback.id}
                      className="p-3 border rounded-md flex justify-between items-start hover:bg-muted/50 cursor-pointer"
                      onClick={() => router.push(`/dashboard/admin/messages/feedback/${feedback.id}`)}
                    >
                      <div>
                        <div className="font-medium flex items-center gap-2">
                          {feedback.title}
                          <StatusBadge status={feedback.status} />
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center gap-2 mt-1">
                          <User className="h-3 w-3" />
                          {feedback.is_anonymous ? (
                            <span className="text-amber-600">Anoniminis</span>
                          ) : (
                            feedback.name
                          )}
                          <div className="flex items-center">
                            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                            <span className="ml-1">{feedback.rating}/5</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatDate(feedback.created_at)}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
      
      <CardFooter>
        <Button asChild variant="outline" className="w-full">
          <Link href="/dashboard/admin/messages">
            Peržiūrėti visus pranešimus
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
} 