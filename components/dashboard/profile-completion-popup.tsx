"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { InfoIcon, Mail, Phone, AlertTriangle, Shield, UserX, Home, Users, Vote, Bell } from "lucide-react";
import { toast } from "sonner";

interface ProfileCompletionPopupProps {
  user: {
    id: string;
    email?: string;
    name?: string;
    role: string;
    isProfileUpdated?: boolean;
    gdprConsentGiven?: boolean;
    phone?: string;
  };
}

export function ProfileCompletionPopup({ user }: ProfileCompletionPopupProps) {
  const [open, setOpen] = useState(false);
  const [agreementAccepted, setAgreementAccepted] = useState(false);
  const [isDeactivating, setIsDeactivating] = useState(false);
  const [isSavingConsent, setIsSavingConsent] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Only show for regular users (not editors or admins)
    const isRegularUser = user.role === "user";
    if (!isRegularUser) return;
    
    // Check GDPR consent and profile completion status
    const needsGdprConsent = !user.gdprConsentGiven;
    const isProfileIncomplete = !user.isProfileUpdated || !user.email;
    const hasGdprConsent = user.gdprConsentGiven;
    
    // Show popup if:
    // 1. No GDPR consent (priority: show GDPR consent dialog)
    // 2. Has GDPR consent but profile incomplete (show profile completion dialog)
    if (needsGdprConsent || (hasGdprConsent && isProfileIncomplete)) {
      setOpen(true);
    }
  }, [user]);

  const handleCompleteProfile = async () => {
    // If user already has GDPR consent, no need to check agreement
    if (!user.gdprConsentGiven && !agreementAccepted) {
      toast.error("Turite sutikti su duomenų naudojimu, kad galėtumėte tęsti");
      return;
    }
    
    // If user doesn't have GDPR consent yet, save it
    if (!user.gdprConsentGiven && agreementAccepted) {
      try {
        setIsSavingConsent(true);
        const response = await fetch(`/api/users/${user.id}/gdpr-consent`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            consentGiven: true
          }),
        });

        if (!response.ok) {
          throw new Error("Failed to save GDPR consent");
        }

        toast.success("Sutikimas išsaugotas sėkmingai");
      } catch (error) {
        console.error("Error saving GDPR consent:", error);
        toast.error("Nepavyko išsaugoti sutikimo. Prašome bandyti vėliau.");
        return;
      } finally {
        setIsSavingConsent(false);
      }
    }
    
    setOpen(false);
    router.push("/dashboard/user/profile");
  };

  const handleDeclineAndDeactivate = async () => {
    if (isDeactivating) return;
    
    setIsDeactivating(true);
    
    try {
      const response = await fetch(`/api/users/${user.id}/deactivate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reason: "GDPR consent declined"
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to deactivate account");
      }

      // Redirect to account disabled page
      router.push("/account-disabled");
    } catch (error) {
      console.error("Error deactivating account:", error);
      toast.error("Nepavyko išjungti paskyros. Prašome bandyti vėliau.");
    } finally {
      setIsDeactivating(false);
    }
  };

  // Prevent closing the dialog - user must make a choice
  const handleCloseAttempt = () => {
    // Don't allow closing
    return;
  };

  // Determine dialog mode
  const hasGdprConsent = user.gdprConsentGiven;
  const needsGdprConsent = !hasGdprConsent;
  const isProfileMode = hasGdprConsent && (!user.isProfileUpdated || !user.email);

  return (
    <Dialog open={open} onOpenChange={handleCloseAttempt}>
      <DialogContent 
        className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto"
        hideCloseButton={true}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isProfileMode ? (
              <>
                <Home className="h-5 w-5 text-green-500" />
                Užpildykite profilio informaciją
              </>
            ) : (
              <>
                <Shield className="h-5 w-5 text-blue-500" />
                Duomenų naudojimo sutikimas
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {isProfileMode 
              ? "Prašome užpildyti trūkstamą profilio informaciją, kad galėtumėte naudotis platforma."
              : "Prašome sutikti su duomenų naudojimu, kad galėtumėte naudotis platforma."
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {isProfileMode ? (
            /* Profile Completion Mode - User already agreed to GDPR */
            <>
              {/* Welcome Message */}
              <div className="rounded-lg border p-4 bg-green-50">
                <div className="flex items-start gap-4">
                  <Users className="h-5 w-5 mt-0.5 text-green-600" />
                  <div className="space-y-2">
                    <h4 className="font-medium text-green-900">Sveiki atvykę į DNSB "Vakarai"!</h4>
                    <div className="text-sm text-green-800 space-y-1">
                      <div className="flex items-center gap-2">
                        <Bell className="h-3 w-3" />
                        <span>Gauti svarbius bendruomenės pranešimus</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Vote className="h-3 w-3" />
                        <span>Dalyvauti balsavimuose ir sprendimų priėmime</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Mail className="h-3 w-3" />
                        <span>Susisiekti su administracija</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* GDPR Already Agreed */}
              <div className="rounded-lg border p-4 bg-blue-50">
                <div className="flex items-start gap-4">
                  <Shield className="h-5 w-5 mt-0.5 text-blue-600" />
                  <div className="space-y-2">
                    <h4 className="font-medium text-blue-900">Duomenų naudojimo sutikimas</h4>
                    <div className="text-sm text-blue-800">
                      <p>✅ Jūs jau sutikote su duomenų naudojimu bendruomenės komunikacijai.</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Missing Information */}
              {(!user.email || !user.phone) && (
                <div className="rounded-lg border p-4 bg-amber-50">
                  <div className="flex items-start gap-4">
                    <AlertTriangle className="h-5 w-5 mt-0.5 text-amber-600" />
                    <div className="space-y-2">
                      <h4 className="font-medium text-amber-900">Trūkstama informacija</h4>
                      <ul className="space-y-2">
                        {!user.email && (
                          <li className="flex items-center gap-2 text-sm text-amber-800">
                            <Mail className="h-4 w-4 text-red-500" />
                            <span>El. pašto adresas (privalomas pranešimams)</span>
                          </li>
                        )}
                        {!user.phone && (
                          <li className="flex items-center gap-2 text-sm text-amber-800">
                            <Phone className="h-4 w-4 text-red-500" />
                            <span>Telefono numeris</span>
                          </li>
                        )}
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            /* GDPR Consent Mode - User needs to give consent */
            <>
              {/* Platform Features */}
              <div className="rounded-lg border p-4 bg-green-50">
                <div className="flex items-start gap-4">
                  <Users className="h-5 w-5 mt-0.5 text-green-600" />
                  <div className="space-y-2">
                    <h4 className="font-medium text-green-900">Ką galite daryti platformoje</h4>
                    <div className="text-sm text-green-800 space-y-1">
                      <div className="flex items-center gap-2">
                        <Bell className="h-3 w-3" />
                        <span>Gauti svarbius bendruomenės pranešimus</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Vote className="h-3 w-3" />
                        <span>Dalyvauti balsavimuose ir sprendimų priėmime</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Mail className="h-3 w-3" />
                        <span>Susisiekti su administracija</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* GDPR Consent Information */}
              <div className="rounded-lg border p-4 bg-blue-50">
                <div className="flex items-start gap-4">
                  <Shield className="h-5 w-5 mt-0.5 text-blue-600" />
                  <div className="space-y-2">
                    <h4 className="font-medium text-blue-900">Duomenų naudojimo sutikimas</h4>
                    <div className="text-sm text-blue-800">
                      <p>Naudojami duomenys: vardas, el. paštas, telefonas, buto adresas.</p>
                      <p className="mt-1">Tikslas: bendruomenės komunikacija, pranešimai, balsavimai.</p>
                      <p className="mt-1 text-xs">Galite atšaukti sutikimą bet kuriuo metu.</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Rights Information */}
              <div className="rounded-lg border p-4">
                <div className="space-y-2">
                  <h4 className="font-medium">Jūsų teisės</h4>
                  <div className="text-sm text-muted-foreground">
                    <p>Galite atšaukti sutikimą, prašyti duomenų ištaisymo ar ištrynimo.</p>
                    <p className="mt-1">
                      <strong>Svarbu:</strong> Atšaukus sutikimą, paskyra bus išjungta.
                    </p>
                  </div>
                </div>
              </div>

              {/* Consent Checkbox */}
              <div className="flex items-start space-x-3 p-4 rounded-lg border border-blue-200 bg-blue-50">
                <Checkbox
                  id="gdpr-consent"
                  checked={agreementAccepted}
                  onCheckedChange={setAgreementAccepted}
                  className="mt-1"
                />
                <label 
                  htmlFor="gdpr-consent" 
                  className="text-sm font-medium cursor-pointer"
                >
                  Sutinku su duomenų naudojimu bendruomenės komunikacijai ir suprantu, 
                  kad galiu atšaukti sutikimą bet kuriuo metu.
                </label>
              </div>
            </>
          )}
        </div>

        <DialogFooter className="gap-2 sm:gap-0 flex-col sm:flex-row">
          {!isProfileMode && (
            <Button 
              variant="destructive" 
              onClick={handleDeclineAndDeactivate}
              disabled={isDeactivating}
              className="w-full sm:w-auto order-2 sm:order-1"
              size="sm"
            >
              {isDeactivating ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Išjungiama...
                </>
              ) : (
                <>
                  <UserX className="h-4 w-4 mr-2" />
                  Nesutinku
                </>
              )}
            </Button>
          )}
          <Button 
            onClick={handleCompleteProfile}
            disabled={(!isProfileMode && !agreementAccepted) || isSavingConsent}
            className="w-full sm:w-auto order-1 sm:order-2"
          >
            {isSavingConsent ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saugoma...
              </>
            ) : isProfileMode ? "Užpildyti profilį" : "Sutinku ir pradėti naudotis"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}