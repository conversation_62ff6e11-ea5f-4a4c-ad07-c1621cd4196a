import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import Link from "next/link";
import { formatDate } from "@/lib/utils";
import { CheckCircle, Clock, Calendar, Eye, AlertCircle, PlusCircle, Users, UserCheck, MessageSquare } from "lucide-react";

interface Poll {
  id: number;
  title: string;
  description: string;
  status: "draft" | "active" | "closed";
  start_date: string | null;
  end_date: string | null;
  created_at: string;
  created_by_name: string;
  hasExplanations: boolean;
}

interface PollStats {
  totalEligibleUsers: number;
  votedUsers: number;
  notVotedUsers: number;
  votingPercentage: number;
}

export function AdminPollsWidget() {
  const [polls, setPolls] = useState<Poll[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [pollToClose, setPollToClose] = useState<Poll | null>(null);
  const [isClosing, setIsClosing] = useState(false);
  const [pollStats, setPollStats] = useState<Record<number | 'overall', PollStats>>({} as Record<number | 'overall', PollStats>);

  useEffect(() => {
    fetchPolls();
  }, []);

  const fetchPolls = async () => {
    setIsLoading(true);
    try {
      const res = await fetch(`/api/polls`);
      if (!res.ok) throw new Error("Failed to fetch polls");
      const data = await res.json();
      setPolls(data);
      
      // Fetch voting stats for active polls
      const activePolls = data.filter((poll: Poll) => poll.status === 'active');
      if (activePolls.length > 0) {
        await fetchPollStats(activePolls);
      }
    } catch (error) {
      console.error("Error fetching polls:", error);
      toast.error("Failed to load polls");
    } finally {
      setIsLoading(false);
    }
  };
  
  const fetchPollStats = async (activePolls: Poll[]) => {
    try {
      // First get overall stats
      const overallStatsRes = await fetch('/api/polls/stats');
      if (overallStatsRes.ok) {
        const overallStats = await overallStatsRes.json();
        // Store the overall stats with a special key
        setPollStats(prev => ({
          ...prev,
          overall: overallStats
        }));
      }
      
      // Then get stats for each active poll
      const statsPromises = activePolls.map(poll => 
        fetch(`/api/polls/stats?id=${poll.id}`)
          .then(res => res.ok ? res.json() : null)
      );
      
      const results = await Promise.all(statsPromises);
      
      const newStats: Record<number, PollStats> = {};
      activePolls.forEach((poll, index) => {
        if (results[index]) {
          newStats[poll.id] = results[index];
        }
      });
      
      setPollStats(prev => ({
        ...prev,
        ...newStats
      }));
    } catch (error) {
      console.error("Error fetching poll stats:", error);
    }
  };

  const handleClosePoll = async () => {
    if (!pollToClose) return;

    setIsClosing(true);
    try {
      const response = await fetch(`/api/polls/close?id=${pollToClose.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          endDate: pollToClose.end_date || new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to close poll");
      }
      
      // Update the poll in the local state
      const updatedPolls = polls.map(p => 
        p.id === pollToClose.id ? { ...p, status: "closed" as const } : p
      );
      
      setPolls(updatedPolls);
      toast.success("Apklausa uždaryta sėkmingai");
    } catch (error) {
      console.error("Error closing poll:", error);
      toast.error(error instanceof Error ? error.message : "Nepavyko uždaryti apklausos");
    } finally {
      setIsClosing(false);
      setPollToClose(null);
    }
  };

  // Get active polls
  const activePolls = polls.filter(poll => poll.status === 'active');
  
  // Get polls without end date
  const pollsWithoutEndDate = polls.filter(poll => 
    poll.status === 'active' && !poll.end_date
  );
  
  // Get recently started polls (within last 7 days)
  const recentlyStartedPolls = polls.filter(poll => {
    if (poll.status !== 'active' || !poll.start_date) return false;
    const startDate = new Date(poll.start_date);
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    return startDate >= sevenDaysAgo;
  });

  if (isLoading) {
    return (
      <Card className="border border-slate-200 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            Apklausos
          </CardTitle>
          <CardDescription>Kraunama...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-16 animate-pulse bg-slate-100 rounded-lg"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="border border-slate-200 shadow-sm">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle>Apklausų valdymas</CardTitle>
            <Link href="/dashboard/admin/polls/new">
              <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
                <PlusCircle className="h-4 w-4 mr-1" />
                Nauja apklausa
              </Button>
            </Link>
          </div>
          <CardDescription>
            Aktyvios: {activePolls.length} | 
            Be pabaigos datos: {pollsWithoutEndDate.length} | 
            Naujos (7d): {recentlyStartedPolls.length}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Summary counts cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-green-50 border border-green-100 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-1">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <h3 className="font-medium text-green-800">Aktyvios</h3>
              </div>
              <p className="text-2xl font-bold text-green-700">{activePolls.length}</p>
            </div>
            
            <div className="bg-amber-50 border border-amber-100 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-1">
                <Clock className="h-5 w-5 text-amber-600" />
                <h3 className="font-medium text-amber-800">Be pabaigos datos</h3>
              </div>
              <p className="text-2xl font-bold text-amber-700">{pollsWithoutEndDate.length}</p>
            </div>
            
            <div className="bg-blue-50 border border-blue-100 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-1">
                <Calendar className="h-5 w-5 text-blue-600" />
                <h3 className="font-medium text-blue-800">Naujos (7d)</h3>
              </div>
              <p className="text-2xl font-bold text-blue-700">{recentlyStartedPolls.length}</p>
            </div>
            
            <div className="bg-indigo-50 border border-indigo-100 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-1">
                <Users className="h-5 w-5 text-indigo-600" />
                <h3 className="font-medium text-indigo-800">Balsavimas</h3>
              </div>
              {pollStats.overall ? (
                <div>
                  <p className="text-xl font-bold text-indigo-700">{pollStats.overall.votingPercentage}%</p>
                  <p className="text-xs text-indigo-600 mt-1">
                    {pollStats.overall.votedUsers} / {pollStats.overall.totalEligibleUsers} naudotojų
                  </p>
                </div>
              ) : (
                <div className="h-8 w-16 bg-indigo-100 animate-pulse rounded"></div>
              )}
            </div>
          </div>
          
          {/* Active polls list */}
          <h3 className="font-medium text-lg mb-3 flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Apklausų valdymas
          </h3>
          
          {activePolls.length === 0 ? (
            <div className="text-center py-8 bg-slate-50 rounded-lg">
              <AlertCircle className="h-8 w-8 text-slate-400 mx-auto mb-2" />
              <p className="text-slate-600">Šiuo metu nėra aktyvių apklausų</p>
            </div>
          ) : (
            <div className="space-y-3 mb-4">
              {activePolls.slice(0, 5).map(poll => (
                <div key={poll.id} className="flex justify-between items-center p-3 rounded-lg bg-white border border-slate-200 hover:border-slate-300 transition-all">
                  <div className="overflow-hidden flex-grow">
                    <h4 className="font-medium text-slate-800 truncate">{poll.title}</h4>
                    <div className="flex items-center gap-2 text-sm text-slate-600">
                      <span>
                        {poll.end_date 
                          ? `Baigiasi: ${formatDate(poll.end_date)}` 
                          : 'Be pabaigos datos'}
                      </span>
                      {!poll.end_date && (
                        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                          Reikia uždaryti rankiniu būdu
                        </Badge>
                      )}
                    </div>
                    {pollStats[poll.id] && (
                      <div className="mt-1 flex items-center gap-4 text-xs">
                        <div className="flex items-center gap-1 text-emerald-700">
                          <UserCheck className="h-3.5 w-3.5" />
                          <span>Balsavo: {pollStats[poll.id].votedUsers}</span>
                        </div>
                        <div className="flex items-center gap-1 text-indigo-700">
                          <Users className="h-3.5 w-3.5" />
                          <span>Nebalsavo: {pollStats[poll.id].notVotedUsers}</span>
                        </div>
                        <div className="flex items-center gap-1 text-slate-600">
                          <span>Aktyvumas: {pollStats[poll.id].votingPercentage}%</span>
                        </div>
                        {poll.hasExplanations && (
                          <div className="flex items-center gap-1 text-purple-600">
                            <MessageSquare className="h-3.5 w-3.5" />
                            <span>Yra komentarų</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex gap-2 ml-4">
                    <Button 
                      size="sm" 
                      variant="outline"
                      className="whitespace-nowrap"
                      onClick={() => setPollToClose(poll)}
                    >
                      Baigti
                    </Button>
                    <Link href={`/dashboard/admin/polls/${poll.id}`}>
                      <Button 
                        size="icon" 
                        variant="ghost"
                        className="h-8 w-8"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </div>
              ))}
              
              {activePolls.length > 5 && (
                <div className="text-center mt-4">
                  <Link href="/dashboard/admin/polls?tab=active">
                    <Button variant="link" className="text-indigo-600 hover:text-indigo-800">
                      Peržiūrėti visas aktyvias apklausas ({activePolls.length})
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          )}
          
          <div className="text-right mt-4">
            <Link href="/dashboard/admin/polls">
              <Button variant="outline">
                Apklausų valdymo skydelis
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
      
      <AlertDialog open={!!pollToClose} onOpenChange={(open) => {
        if (!open) setPollToClose(null);
      }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Ar tikrai norite baigti apklausą?</AlertDialogTitle>
            <AlertDialogDescription>
              Šis veiksmas negali būti atšauktas. Apklausa bus pažymėta kaip baigta ir naudotojai nebegalės balsuoti.
            </AlertDialogDescription>
            
            {/* Notes about poll end date - moved outside of AlertDialogDescription to avoid nesting div inside p */}
            {pollToClose && !pollToClose.end_date && (
              <div className="mt-3 p-2 bg-amber-50 border border-amber-200 rounded-md text-amber-600 text-sm">
                <b>Pastaba:</b> Ši apklausa neturi nustatytos pabaigos datos. Uždarius apklausą, dabartinė data ir laikas bus nustatyti kaip pabaigos data.
              </div>
            )}
            
            {pollToClose && pollToClose.end_date && (
              <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded-md text-blue-600 text-sm">
                <b>Pastaba:</b> Ši apklausa turi nustatytą pabaigos datą ({pollToClose.end_date ? formatDate(pollToClose.end_date) : ""}). Ar tikrai norite ją uždaryti anksčiau?
              </div>
            )}
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Atšaukti</AlertDialogCancel>
            <AlertDialogAction
              className="bg-amber-500 hover:bg-amber-600 focus:ring-amber-400"
              onClick={handleClosePoll}
              disabled={isClosing}
            >
              {isClosing ? "Baigiama..." : "Baigti apklausą"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 