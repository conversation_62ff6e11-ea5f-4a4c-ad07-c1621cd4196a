import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, Card<PERSON><PERSON>le, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Clock, VoteIcon, ChevronRight, BarChart3, Loader2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { useSession } from "@/lib/hooks/use-session";
import { toast } from "sonner";
import { usePolls } from "@/lib/tanstack/queries";

export function ActivePollsCard() {
  // Use real session for all environments
  const { data: session } = useSession();
  
  const [showAll, setShowAll] = useState(false);
  
  // Use TanStack Query for active polls
  const { 
    data: allPolls = [], 
    isLoading, 
    error,
    isError 
  } = usePolls('active');

  // Show error toast if query fails
  if (isError && error) {
    toast.error("Nepavyko gauti apklausų");
  }

  // Check if user is an admin or editor
  const isAdmin = session?.user?.role === "super_admin" || session?.user?.role === "editor";
  
  // Filter active polls (polls that are actually active and user can vote on)
  const activePolls = allPolls.filter((poll: any) => poll.status === 'active' && !poll.userResponse);
  const allPollsCount = allPolls.length;

  if (isLoading) {
    return (
      <Card className="border border-slate-200 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-amber-500" />
            {isAdmin ? "Apklausų valdymas" : "Aktyvios apklausos"}
          </CardTitle>
          <CardDescription className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            Kraunama...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 2 }).map((_, i) => (
              <div key={i} className="h-16 animate-pulse bg-slate-100 rounded-lg"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }
  
  if (activePolls.length === 0 && allPollsCount === 0) {
    return null; // Don't show the card if there are no polls at all
  }
  
  if (activePolls.length === 0) {
    // No active polls needing votes, but there are other polls
    return (
      <Card className="bg-white border border-slate-200 shadow-sm">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-lg">
                Šiuo metu nėra aktyvių apklausų, kuriose galėtumėte balsuoti
              </CardTitle>
              <CardDescription className="mt-1">
                Peržiūrėkite ankstesnes apklausas ir jų rezultatus
              </CardDescription>
            </div>
            <div className="hidden sm:block">
              <div className="h-16 w-16 bg-slate-100 rounded-full flex items-center justify-center">
                <BarChart3 className="h-8 w-8 text-slate-500" />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardFooter className="pt-0 pb-4">
          <Link href="/dashboard/polls">
            <Button 
              variant="outline" 
              className="text-slate-600 border-slate-300 hover:bg-slate-100"
            >
              Peržiūrėti visas apklausas
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </Link>
        </CardFooter>
      </Card>
    );
  }
  
  return (
    <Card className="bg-gradient-to-br from-amber-50 to-white border border-amber-200 shadow-sm">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <Badge variant="outline" className="bg-amber-100 text-amber-700 mb-2">
              <Clock className="h-3 w-3 mr-1" /> 
              {isAdmin ? "Apklausų valdymas" : "Aktyvios apklausos"}
            </Badge>
            <CardTitle className="text-lg">
              {activePolls.length === 0 
                ? "Nėra naujų apklausų" 
                : `${activePolls.length} ${activePolls.length === 1 ? 'apklausa' : 'apklausos'} laukia jūsų balso`
              }
            </CardTitle>
            <CardDescription className="text-amber-700 mt-1">
              Prisidėkite prie bendruomenės sprendimų - jūsų balsas svarbus
            </CardDescription>
          </div>
          <div className="hidden sm:block">
            <div className="h-16 w-16 bg-amber-100 rounded-full flex items-center justify-center">
              <VoteIcon className="h-8 w-8 text-amber-600" />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-4">
        <div className="space-y-3">
          {activePolls.slice(0, 3).map((poll: any) => (
            <div key={poll.id} className="flex justify-between items-center p-3 rounded-lg bg-white border border-amber-100 hover:border-amber-300 transition-all">
              <div className="flex-1 min-w-0 mr-3">
                <h3 className="font-medium text-slate-800">{poll.title || 'Apklausa'}</h3>
                <div className="text-sm text-slate-500 line-clamp-1">{poll.description || ''}</div>
              </div>
              <Link href={`/dashboard/polls#poll-${poll.id}`} className="flex-shrink-0">
                <Button 
                  size="sm"
                  className="bg-amber-600 hover:bg-amber-700 text-white"
                >
                  <VoteIcon className="h-3.5 w-3.5 mr-1.5" />
                  Balsuoti
                </Button>
              </Link>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="pt-0 pb-4">
        <Link href={activePolls.length > 3 ? "/dashboard/polls?tab=not-voted" : "/dashboard/polls"}>
          <Button 
            variant="link" 
            className="text-amber-600 hover:text-amber-800"
          >
            {activePolls.length > 3 
              ? "Peržiūrėti visas aktyvias apklausas" 
              : "Peržiūrėti visas apklausas"}
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
} 