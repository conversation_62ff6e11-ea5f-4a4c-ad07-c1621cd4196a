"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { BellRing, Calendar, ChevronDown, ChevronRight, ChevronUp, Clock, FileText, Info, Loader2, Paperclip } from "lucide-react";
import { Tag } from "@/components/ui/tag";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { useAnnouncements } from "@/lib/tanstack/queries";

// Enhanced announcement type with importance and tags
interface Announcement {
  id: string;
  title: string;
  content: string;
  importance: string;
  created_at: string;
  updated_at?: string;
  sent_at?: string;
  recipient_type: string;
  author_name: string;
  author_id: string;
  tags: AnnouncementTag[];
  has_attachments?: boolean;
}

interface AnnouncementTag {
  id: number;
  name: string;
  color: string;
  category: string;
}

// Color class mapping
const colorClassMap: Record<string, { bg: string, text: string, border: string, hover: string }> = {
  slate: { bg: 'bg-slate-100', text: 'text-slate-800', border: 'border-slate-200', hover: 'hover:bg-slate-200' },
  gray: { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200', hover: 'hover:bg-gray-200' },
  red: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200', hover: 'hover:bg-red-200' },
  orange: { bg: 'bg-orange-100', text: 'text-orange-800', border: 'border-orange-200', hover: 'hover:bg-orange-200' },
  amber: { bg: 'bg-amber-100', text: 'text-amber-800', border: 'border-amber-200', hover: 'hover:bg-amber-200' },
  yellow: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200', hover: 'hover:bg-yellow-200' },
  lime: { bg: 'bg-lime-100', text: 'text-lime-800', border: 'border-lime-200', hover: 'hover:bg-lime-200' },
  green: { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200', hover: 'hover:bg-green-200' },
  emerald: { bg: 'bg-emerald-100', text: 'text-emerald-800', border: 'border-emerald-200', hover: 'hover:bg-emerald-200' },
  teal: { bg: 'bg-teal-100', text: 'text-teal-800', border: 'border-teal-200', hover: 'hover:bg-teal-200' },
  cyan: { bg: 'bg-cyan-100', text: 'text-cyan-800', border: 'border-cyan-200', hover: 'hover:bg-cyan-200' },
  blue: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200', hover: 'hover:bg-blue-200' },
  indigo: { bg: 'bg-indigo-100', text: 'text-indigo-800', border: 'border-indigo-200', hover: 'hover:bg-indigo-200' },
  violet: { bg: 'bg-violet-100', text: 'text-violet-800', border: 'border-violet-200', hover: 'hover:bg-violet-200' },
  purple: { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-200', hover: 'hover:bg-purple-200' },
  fuchsia: { bg: 'bg-fuchsia-100', text: 'text-fuchsia-800', border: 'border-fuchsia-200', hover: 'hover:bg-fuchsia-200' },
  pink: { bg: 'bg-pink-100', text: 'text-pink-800', border: 'border-pink-200', hover: 'hover:bg-pink-200' },
  rose: { bg: 'bg-rose-100', text: 'text-rose-800', border: 'border-rose-200', hover: 'hover:bg-rose-200' }
};

// Function to get color classes for a tag
const getTagColorClasses = (color: string) => {
  return colorClassMap[color] || colorClassMap.gray; // Default to gray if color not found
};

export function LatestAnnouncementsCard() {
  const [expandedAnnouncementIds, setExpandedAnnouncementIds] = useState<string[]>([]);
  const [showAllAnnouncements, setShowAllAnnouncements] = useState(false);
  
  // Use TanStack Query for data fetching
  const { 
    data: announcements = [], 
    isLoading: loading, 
    error,
    isError 
  } = useAnnouncements();

  // Show error toast if query fails
  if (isError && error) {
    toast.error("Nepavyko gauti pranešimų");
  }
  
  // Function to toggle announcement expansion
  const toggleAnnouncement = (id: string) => {
    setExpandedAnnouncementIds(prev => 
      prev.includes(id) 
        ? prev.filter(announcementId => announcementId !== id)
        : [...prev, id]
    );
  };
  
  // Format date to a more readable format
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('lt-LT', options);
  };
  
  // Get importance configuration
  const getImportanceConfig = (importance: string) => {
    switch (importance) {
      case 'urgent':
        return {
          bgColor: 'bg-red-25 border-red-100',
          iconBg: 'bg-red-500',
          textColor: 'text-red-800',
          icon: <BellRing className="h-4 w-4" />,
          tagVariant: 'danger' as const
        };
      case 'important':
        return {
          bgColor: 'bg-amber-25 border-amber-100',
          iconBg: 'bg-amber-500',
          textColor: 'text-amber-800',
          icon: <Info className="h-4 w-4" />,
          tagVariant: 'important' as const
        };
      default:
        return {
          bgColor: 'bg-gray-25 border-gray-100',
          iconBg: 'bg-gray-500',
          textColor: 'text-gray-800',
          icon: <FileText className="h-4 w-4" />,
          tagVariant: 'secondary' as const
        };
    }
  };
  
  // Determine how many announcements to show
  const visibleAnnouncements = showAllAnnouncements 
    ? announcements 
    : announcements.slice(0, 3);

  return (
    <Card className="col-span-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-xl">Naujausi pranešimai</CardTitle>
          <CardDescription>
            Paskutiniai bendrijos pranešimai ir informacija
          </CardDescription>
        </div>
        <Button asChild variant="outline" className="h-8 gap-1">
          <Link href="/dashboard/announcements">
            Visi pranešimai
            <ChevronRight className="h-4 w-4" />
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center p-8 text-muted-foreground">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            Kraunami pranešimai...
          </div>
        ) : announcements.length > 0 ? (
          <div className="space-y-4">
            {visibleAnnouncements.map((announcement: Announcement) => {
              const importanceConfig = getImportanceConfig(announcement.importance);
              const isExpanded = expandedAnnouncementIds.includes(announcement.id);
              
              return (
                <Collapsible 
                  key={announcement.id}
                  open={isExpanded}
                  onOpenChange={() => toggleAnnouncement(announcement.id)}
                  className={`rounded-lg border-2 shadow-sm ${importanceConfig.bgColor}`}
                >
                  <div className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`text-white p-1 rounded-full ${importanceConfig.iconBg}`}>
                          {importanceConfig.icon}
                        </div>
                        <h3 className={`font-medium text-lg ${importanceConfig.textColor}`}>
                          {announcement.title}
                        </h3>
                        {announcement.has_attachments && (
                          <Paperclip className="h-4 w-4 text-gray-500" title="Turi priedų" />
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="h-4 w-4 mr-1" />
                          {formatDate(announcement.created_at)}
                        </div>
                        <CollapsibleTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            {isExpanded ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )}
                            <span className="sr-only">
                              {isExpanded ? "Suskleisti" : "Išskleisti"}
                            </span>
                          </Button>
                        </CollapsibleTrigger>
                      </div>
                    </div>
                    
                    <div className="mt-2 flex flex-wrap gap-2">
                      <Tag 
                        text={announcement.importance === 'urgent' ? 'Skubu' : 
                              announcement.importance === 'important' ? 'Svarbu' : 'Informacija'} 
                        variant={importanceConfig.tagVariant}
                      />
                      {announcement.tags?.map((tag: AnnouncementTag) => {
                        const colorClasses = getTagColorClasses(tag.color);
                        return (
                          <div 
                            key={tag.id} 
                            className={cn(
                              "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
                              colorClasses.bg,
                              colorClasses.text,
                              "border",
                              colorClasses.border
                            )}
                          >
                            {tag.name}
                          </div>
                        );
                      })}
                    </div>
                    
                    <CollapsibleContent>
                      <div className="mt-4 border-t pt-4 border-gray-200">
                        <div 
                          className="text-gray-700 prose-sm"
                          dangerouslySetInnerHTML={{ __html: announcement.content }}
                        />
                        <div className="mt-3">
                          <Button asChild variant="outline" size="sm">
                            <Link href={`/dashboard/announcements/${announcement.id}`}>
                              Skaityti daugiau
                              <ChevronRight className="h-4 w-4 ml-1" />
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </CollapsibleContent>
                    
                    {!isExpanded && (
                      <p className="mt-2 text-gray-700 line-clamp-2">
                        {announcement.content.replace(/<[^>]*>/g, '')}
                      </p>
                    )}
                  </div>
                </Collapsible>
              );
            })}
            
            {announcements.length > 3 && (
              <Button 
                variant="ghost" 
                className="w-full mt-2" 
                onClick={() => setShowAllAnnouncements(!showAllAnnouncements)}
              >
                {showAllAnnouncements ? (
                  <>Rodyti mažiau <ChevronUp className="ml-2 h-4 w-4" /></>
                ) : (
                  <>Rodyti visus {announcements.length} pranešimus <ChevronDown className="ml-2 h-4 w-4" /></>
                )}
              </Button>
            )}
          </div>
        ) : (
          <div className="text-center p-6 text-muted-foreground">
            Šiuo metu pranešimų nėra
          </div>
        )}
      </CardContent>
    </Card>
  );
} 