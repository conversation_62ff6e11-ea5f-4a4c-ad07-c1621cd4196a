"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import { Accessibility, Moon, Sun, Laptop, Type, ZoomIn, Eye, EyeOff, Contrast } from "lucide-react"
import { Switch } from "@/components/ui/switch"

interface AccessibilityMenuProps {
  className?: string
}

// Font size values in percentages
const fontSizes = [
  { id: "normal", name: "Normalus", size: 100, default: true },
  { id: "large", name: "<PERSON><PERSON><PERSON>", size: 112.5 },
  { id: "xlarge", name: "<PERSON><PERSON> didelis", size: 125 },
]

export function AccessibilityMenu({ className }: AccessibilityMenuProps) {
  const [theme, setTheme] = useState<'light' | 'dark' | 'system'>('system')
  const [currentFontSize, setCurrentFontSize] = useState(fontSizes[0])
  const [isHighContrast, setIsHighContrast] = useState(false)
  const [mounted, setMounted] = useState(false)
  
  // Only run on client
  useEffect(() => {
    setMounted(true)
    
    // Load saved preferences
    const savedTheme = localStorage.getItem("theme") as 'light' | 'dark' | 'system'
    const savedFontSize = localStorage.getItem("font-size")
    const savedContrast = localStorage.getItem("high-contrast-mode")
    
    if (savedTheme) {
      setTheme(savedTheme)
    }
    
    if (savedFontSize) {
      const fontSize = fontSizes.find(size => size.id === savedFontSize)
      if (fontSize) {
        setCurrentFontSize(fontSize)
        applyFontSize(fontSize.size)
      }
    }
    
    if (savedContrast === "true") {
      setIsHighContrast(true)
      applyHighContrast(true)
    }
  }, [])
  
  const applyFontSize = (size: number) => {
    document.documentElement.style.fontSize = `${size}%`
  }
  
  const applyHighContrast = (enabled: boolean) => {
    if (enabled) {
      document.documentElement.classList.add("high-contrast")
    } else {
      document.documentElement.classList.remove("high-contrast")
    }
  }
  
  const setThemePreference = (value: 'light' | 'dark' | 'system') => {
    const root = window.document.documentElement
    
    setTheme(value)
    localStorage.setItem("theme", value)
    
    root.classList.remove("light", "dark")
    
    if (value === "system") {
      const systemTheme = window.matchMedia("(prefers-color-scheme: dark)").matches
        ? "dark"
        : "light"
      root.classList.add(systemTheme)
    } else {
      root.classList.add(value)
    }
  }
  
  const handleFontSizeChange = (fontSize: typeof fontSizes[0]) => {
    setCurrentFontSize(fontSize)
    localStorage.setItem("font-size", fontSize.id)
    applyFontSize(fontSize.size)
  }
  
  const toggleHighContrast = () => {
    const newState = !isHighContrast
    setIsHighContrast(newState)
    localStorage.setItem("high-contrast-mode", newState.toString())
    applyHighContrast(newState)
  }
  
  // Show nothing until mounted to prevent hydration errors
  if (!mounted) return null
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "rounded-full focus-visible:ring-2 focus-visible:ring-indigo-500",
            className
          )}
          aria-label="Pritaikymo nustatymai"
        >
          <Accessibility className="h-5 w-5" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="p-2 w-64">
        <div className="pb-1 pt-1 px-2">
          <p className="text-sm font-semibold">Pritaikymo nustatymai</p>
          <p className="text-xs text-muted-foreground">
            Pritaikykite sistemos išvaizdą pagal savo poreikius
          </p>
        </div>
        
        <DropdownMenuSeparator className="my-1" />
        
        {/* Theme Options */}
        <DropdownMenuLabel className="px-2 py-1 text-xs font-medium">
          Išvaizda
        </DropdownMenuLabel>
        <DropdownMenuItem
          className={cn(
            "flex items-center py-2 px-2 gap-3 cursor-pointer",
            theme === "light" && "bg-slate-100 rounded-md"
          )}
          onClick={() => setThemePreference("light")}
        >
          <div className={cn(
            "h-8 w-8 rounded-full flex items-center justify-center",
            theme === "light" ? "bg-indigo-100 text-indigo-600" : "bg-slate-100"
          )}>
            <Sun className="h-4 w-4" />
          </div>
          <div>Šviesi tema</div>
        </DropdownMenuItem>
        <DropdownMenuItem
          className={cn(
            "flex items-center py-2 px-2 gap-3 cursor-pointer",
            theme === "dark" && "bg-slate-100 rounded-md"
          )}
          onClick={() => setThemePreference("dark")}
        >
          <div className={cn(
            "h-8 w-8 rounded-full flex items-center justify-center",
            theme === "dark" ? "bg-indigo-100 text-indigo-600" : "bg-slate-100"
          )}>
            <Moon className="h-4 w-4" />
          </div>
          <div>Tamsi tema</div>
        </DropdownMenuItem>
        <DropdownMenuItem
          className={cn(
            "flex items-center py-2 px-2 gap-3 cursor-pointer",
            theme === "system" && "bg-slate-100 rounded-md"
          )}
          onClick={() => setThemePreference("system")}
        >
          <div className={cn(
            "h-8 w-8 rounded-full flex items-center justify-center",
            theme === "system" ? "bg-indigo-100 text-indigo-600" : "bg-slate-100"
          )}>
            <Laptop className="h-4 w-4" />
          </div>
          <div>Sistemos tema</div>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator className="my-1" />
        
        {/* Font Size Options */}
        <DropdownMenuLabel className="px-2 py-1 text-xs font-medium">
          Teksto dydis
        </DropdownMenuLabel>
        {fontSizes.map((fontSize) => (
          <DropdownMenuItem
            key={fontSize.id}
            className={cn(
              "flex items-center py-2 px-2 gap-3 cursor-pointer",
              currentFontSize.id === fontSize.id && "bg-slate-100 rounded-md"
            )}
            onClick={() => handleFontSizeChange(fontSize)}
          >
            <div className={cn(
              "h-8 w-8 rounded-full flex items-center justify-center",
              currentFontSize.id === fontSize.id ? "bg-indigo-100 text-indigo-600" : "bg-slate-100"
            )}>
              {fontSize.id === "normal" ? (
                <Type className="h-4 w-4" />
              ) : fontSize.id === "large" ? (
                <ZoomIn className="h-4 w-4" />
              ) : (
                <ZoomIn className="h-5 w-5" />
              )}
            </div>
            
            <div className="space-y-0.5">
              <div className="font-medium text-sm">{fontSize.name}</div>
              <div 
                className="text-xs text-slate-500"
                style={{ fontSize: `${fontSize.size / 100}rem` }}
              >
                Pavyzdys
                {fontSize.default && <span className="ml-2 text-indigo-600">(numatytasis)</span>}
              </div>
            </div>
          </DropdownMenuItem>
        ))}
        
        <DropdownMenuSeparator className="my-1" />
        
        {/* High Contrast Mode */}
        <DropdownMenuLabel className="px-2 py-1 text-xs font-medium">
          Kontrasto režimas
        </DropdownMenuLabel>
        <DropdownMenuItem
          className="flex items-center justify-between py-2 px-2 gap-3 cursor-pointer"
          onClick={toggleHighContrast}
        >
          <div className="flex items-center gap-3">
            <div className={cn(
              "h-8 w-8 rounded-full flex items-center justify-center",
              isHighContrast ? "bg-indigo-100 text-indigo-600" : "bg-slate-100"
            )}>
              {isHighContrast ? (
                <Eye className="h-4 w-4" />
              ) : (
                <EyeOff className="h-4 w-4" />
              )}
            </div>
            
            <div className="space-y-0.5">
              <div className="font-medium text-sm">Didelis kontrastas</div>
              <div className="text-xs text-slate-500">
                {isHighContrast ? "Įjungta" : "Išjungta"}
              </div>
            </div>
          </div>

          <Switch
            checked={isHighContrast}
            onCheckedChange={toggleHighContrast}
            className="data-[state=checked]:bg-indigo-600"
          />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
} 