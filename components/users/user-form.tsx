"use client";

import { useRef, useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Icons } from "@/components/ui/icons";
import { clientFetch } from "@/lib/client/fetcher"; // Import clientFetch

// Form validation schema
const formSchema = z.object({
  username: z.string().min(3, {
    message: "Prisijungimo vardas turi būti bent 3 simbolių ilgio",
  }),
  name: z.string().min(2, {
    message: "Vardas turi būti bent 2 simbolių ilgio",
  }),
  email: z.string().email({
    message: "Neteisingas el. pašto formatas",
  }),
  role: z.enum(["developer", "super_admin", "editor", "user"], {
    required_error: "Pasirinkite vartotojo rolę",
  }),
  password: z.string().optional().superRefine((val, ctx) => {
    if (val && val.length > 0 && val.length < 8) { 
      ctx.addIssue({
        code: z.ZodIssueCode.too_small,
        minimum: 8,
        type: "string",
        inclusive: true,
        message: "Slaptažodis turi būti bent 8 simbolių ilgio",
      });
    }
  }),
  flat_id: z.string().optional(),
  phone: z.string().optional(),
  is_profile_updated: z.boolean().default(false),
  is_test_user: z.boolean().default(false),
});

type FormValues = z.infer<typeof formSchema>;

interface UserFormProps {
  user?: {
    id: string;
    username: string;
    name: string;
    email: string;
    role: string;
    flat_id?: string;
    street_id?: string;
    house_id?: string;
    street?: string;
    house_number?: string;
    flat_number?: string;
    phone?: string;
    is_profile_updated: boolean;
    is_test_user?: boolean;
  };
}

// Submit Button component with form status handling
function SubmitButton({ isEditing, isSubmitting }: { isEditing: boolean, isSubmitting: boolean }) {
  const isLoading = isSubmitting;
  
  return (
    <Button type="submit" disabled={isLoading}>
      {isLoading ? (
        <>
          <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
          {isEditing ? "Atnaujinama..." : "Kuriama..."}
        </>
      ) : (
        <>
          {isEditing ? "Atnaujinti vartotoją" : "Sukurti vartotoją"}
        </>
      )}
    </Button>
  );
}

export function UserForm({ user }: UserFormProps = {}) {
  const router = useRouter();
  const formRef = useRef<HTMLFormElement>(null);
  const isEditing = !!user;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedRole, setSelectedRole] = useState<string>(user?.role || "user");
  
  // State for hierarchical data
  const [streets, setStreets] = useState<any[]>([]);
  const [houses, setHouses] = useState<any[]>([]);
  const [flats, setFlats] = useState<any[]>([]);
  const [selectedStreetId, setSelectedStreetId] = useState<string | null>(user?.street_id || null);
  const [selectedHouseId, setSelectedHouseId] = useState<string | null>(user?.house_id || null);
  const [isLoading, setIsLoading] = useState(true);
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: user?.username || "",
      name: user?.name || "",
      email: user?.email || "",
      role: (user?.role as "developer" | "super_admin" | "editor" | "user") || "user",
      password: isEditing ? "" : "",
      flat_id: user?.flat_id || "",
      phone: user?.phone || "",
      is_profile_updated: !!user?.is_profile_updated,
      is_test_user: !!user?.is_test_user,
    },
  });
  
  // Fetch streets, houses, and flats data
  useEffect(() => {
    async function fetchData() {
      console.log("[UserForm useEffect] Starting data fetch with selectedStreetId:", selectedStreetId, "selectedHouseId:", selectedHouseId);
      setIsLoading(true);
      try {
        // Fetch streets
        const streetsResponse = await fetch("/api/streets");
        const streetsData = await streetsResponse.json();
        console.log("[UserForm useEffect] Fetched streets:", streetsData);
        setStreets(streetsData);
        
        // Fetch houses based on initial selectedStreetId
        if (selectedStreetId) {
          const housesResponse = await fetch(`/api/houses?streetId=${selectedStreetId}`);
          const housesData = await housesResponse.json();
          setHouses(housesData);
        } else {
          // Fetch all houses if no street initially selected (e.g., new user)
          const housesResponse = await fetch("/api/houses");
          const housesData = await housesResponse.json();
          setHouses(housesData);
        }
        
        // Fetch flats based on initial selectedHouseId
        if (selectedHouseId) {
          const flatsResponse = await fetch(`/api/flats?houseId=${selectedHouseId}`);
          const flatsData = await flatsResponse.json();
          setFlats(flatsData);
        } else {
           // Fetch all flats if no house initially selected (e.g., new user)
           // This might be too much data, consider fetching only if needed
           // const flatsResponse = await fetch("/api/flats");
           // const flatsData = await flatsResponse.json();
           // setFlats(flatsData);
           setFlats([]); // Start with empty flats if no house selected
        }

      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to load location data");
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchData();
  // Depend on the initially set IDs from props (or null)
  }, [selectedStreetId, selectedHouseId]); 
  
  // Handle street selection change
  const handleStreetChange = async (streetId: string) => {
    setSelectedStreetId(streetId === "none" ? null : streetId);
    setSelectedHouseId(null);
    form.setValue("flat_id", "");
    
    try {
      // Fetch houses for selected street
      const housesResponse = await fetch(`/api/houses?streetId=${streetId === "none" ? '' : streetId}`);
      const housesData = await housesResponse.json();
      setHouses(housesData);
      setFlats([]);
    } catch (error) {
      console.error("Error fetching houses:", error);
      toast.error("Failed to load houses");
    }
  };
  
  // Handle house selection change
  const handleHouseChange = async (houseId: string) => {
    setSelectedHouseId(houseId === "none" ? null : houseId);
    form.setValue("flat_id", "");
    
    try {
      // Fetch flats for selected house
      const flatsResponse = await fetch(`/api/flats?houseId=${houseId === "none" ? '' : houseId}`);
      const flatsData = await flatsResponse.json();
      setFlats(flatsData);
    } catch (error) {
      console.error("Error fetching flats:", error);
      toast.error("Failed to load flats");
    }
  };
  
  // Modified onSubmit function using clientFetch
  const onSubmit = async (data: FormValues) => {
    console.log("Form data to be submitted:", data);
    setIsSubmitting(true);
    
    const isEditing = !!user;
    const endpoint = isEditing 
      ? `/api/users/${user.id}` 
      : "/api/users";
    const method = isEditing ? "PUT" : "POST";
    
    const payload: Record<string, any> = { ...data };
    payload.is_profile_updated = data.is_profile_updated || false;
    payload.is_test_user = data.is_test_user || false;

    if (isEditing && (!payload.password || payload.password.trim() === '')) {
      delete payload.password;
    }

    console.log(`[UserForm onSubmit] Submitting payload to ${endpoint}:`, JSON.stringify(payload, null, 2));

    try {
      await clientFetch(endpoint, {
        method: method,
        body: JSON.stringify(payload),
      });
      
      toast.success(isEditing ? "Vartotojas atnaujintas sėkmingai" : "Vartotojas sukurtas sėkmingai");
      router.push("/dashboard/admin/users");
      router.refresh();
      
    } catch (error) {
      console.error("Error submitting user form:", error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : "Nepavyko išsaugoti vartotojo duomenų"
      );
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // ---> Add Log Here <---
  console.log("[UserForm Render] Received user prop:", user);
  console.log("[UserForm Render] Selected street ID:", selectedStreetId);
  console.log("[UserForm Render] Selected house ID:", selectedHouseId);
  console.log("[UserForm Render] Streets loaded:", streets.length, streets);
  console.log("[UserForm Render] Form default values:", form.formState.defaultValues);
  
  return (
    <Form {...form}>
      <form
        ref={formRef}
        className="space-y-6"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        {user && <input type="hidden" name="id" value={user.id} />}
        
        {/* Include checkbox value as hidden field to ensure it's always submitted with correct value */}
        <input 
          type="hidden" 
          name="is_profile_updated" 
          value={form.getValues("is_profile_updated") ? "true" : "false"} 
        />
        {/* Add hidden input for is_test_user to ensure it's always submitted */}
        <input 
          type="hidden" 
          name="is_test_user" 
          value={form.getValues("is_test_user") ? "true" : "false"} 
        />
        
        {/* Hidden input for role to ensure it's always submitted */}
        <input type="hidden" name="role" value={selectedRole} />
        
        {/* Show form-level errors if they exist */}
        {/*
        {state.error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <strong className="font-bold">Klaida! </strong>
            <span className="block sm:inline">{state.error}</span>
          </div>
        )}
        */}
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Prisijungimo vardas*</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="44-18 arba VardenisPavardenis" 
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  Unikalus prisijungimo vardas, pvz. namo numeris ir buto numeris - 44-18. Jeigu toks vartotojas jau egzistuoja, sukurkite unikalu vartotojo varda.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Vardas*</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="Jonas Jonaitis" 
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  Pilnas vardas ir pavardė
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>El. paštas*</FormLabel>
                <FormControl>
                  <Input 
                    type="email"
                    placeholder="<EMAIL>" 
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  El. pašto adresas bus naudojamas siuntimui
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{isEditing ? "Naujas slaptažodis" : "Slaptažodis*"}</FormLabel>
                <FormControl>
                  <Input 
                    type="password"
                    placeholder={isEditing ? "Palikite tuščią jei nekeičiate" : "Slaptažodis"} 
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  {isEditing 
                    ? "Jei paliekate tuščią, slaptažodis nebus pakeistas" 
                    : "Turi būti bent 8 simbolių ilgio"
                  }
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Rolė*</FormLabel>
                <Select 
                  onValueChange={(value) => {
                    field.onChange(value);
                    setSelectedRole(value);
                  }} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger name="role">
                      <SelectValue placeholder="Pasirinkite rolę" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="user">Vartotojas</SelectItem>
                    <SelectItem value="developer">Programuotojas</SelectItem>
                    <SelectItem value="super_admin">Super Administratorius</SelectItem>
                    <SelectItem value="editor">Redaktorius</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Vartotojo rolė sistemoje
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Telefono numeris</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="+37060012345" 
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  Telefono numeris kontaktams
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Hierarchical location selection */}
          <div className="md:col-span-2 space-y-4">
            <h3 className="text-lg font-medium">Gyvenamoji vieta</h3>
            
            {/* Street selection */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <FormLabel>Gatvė</FormLabel>
                <Select 
                  onValueChange={handleStreetChange}
                  value={selectedStreetId || ""}
                  disabled={isLoading}
                  key={`street-select-${streets.length}`}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pasirinkite gatvę" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">-- Pasirinkite gatvę --</SelectItem>
                    {streets.map((street) => (
                      <SelectItem key={street.id} value={street.id.toString()}>
                        {street.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* House selection */}
              <div>
                <FormLabel>Namas</FormLabel>
                <Select 
                  onValueChange={handleHouseChange}
                  value={selectedHouseId || ""}
                  disabled={isLoading}
                  key={`house-select-${houses.length}`}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pasirinkite namą" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">-- Pasirinkite namą --</SelectItem>
                    {houses.map((house) => (
                      <SelectItem key={house.id} value={house.id.toString()}>
                        {house.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* Flat selection */}
              <FormField
                control={form.control}
                name="flat_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Butas</FormLabel>
                    <Select 
                      onValueChange={(value) => field.onChange(value === "none" ? "" : value)}
                      value={field.value || ""}
                      disabled={isLoading || !selectedHouseId}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pasirinkite butą" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">-- Pasirinkite butą --</SelectItem>
                        {flats.map((flat) => (
                          <SelectItem key={flat.id} value={flat.id.toString()}>
                            {flat.number}{flat.floor ? ` (aukštas: ${flat.floor})` : ''}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Pasirinkite butą, kuriame gyvena vartotojas
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
          
          <FormField
            control={form.control}
            name="is_test_user"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    Test vartotojas
                  </FormLabel>
                  <FormDescription>
                    Pažymėkite, jei tai yra test vartotojas, kuris gali gauti laiškus ir dev aplinkoje
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="is_profile_updated"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 py-2">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={(checked) => {
                      field.onChange(checked);
                      // Update the hidden field value when checkbox changes
                      const hiddenInput = formRef.current?.querySelector('input[name="is_profile_updated"]');
                      if (hiddenInput) {
                        (hiddenInput as HTMLInputElement).value = checked ? "true" : "false";
                      }
                    }}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    Profilis atnaujintas (nebūtina įvesti el. paštą ar tel. nr.)
                  </FormLabel>
                  <FormDescription>
                    Pažymėkite šį laukelį, jei vartotojui nereikės atnaujinti profilio pirmo prisijungimo metu
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />
        </div>
        
        <div className="flex justify-end">
          <SubmitButton isEditing={isEditing} isSubmitting={isSubmitting} />
        </div>
      </form>
    </Form>
  );
} 