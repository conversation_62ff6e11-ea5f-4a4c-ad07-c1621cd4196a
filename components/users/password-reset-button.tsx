"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { KeyRound, RefreshCw } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { clientFetch } from "@/lib/client/fetcher";
import { useAnalytics } from "@/lib/hooks/useAnalytics";
import { trackError } from "@/lib/errorTracking";

interface PasswordResetButtonProps {
  userId: string;
  userName: string;
}

export default function PasswordResetButton({ userId, userName }: PasswordResetButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [paymentCode, setPaymentCode] = useState<string | null>(null);
  const router = useRouter();
  const { trackEvent } = useAnalytics();

  const handleResetPassword = async () => {
    if (!userId) return;
    
    setIsLoading(true);
    setPaymentCode(null);
    
    try {
      // Call the password reset API
      const result = await clientFetch(`/api/admin/users/${userId}/reset-password`, {
        method: "POST",
      });
      
      // If successful, show success message
      if (result.message) {
        toast({
          title: "Slaptažodis atstatytas",
          description: "Vartotojo slaptažodis sėkmingai atstatytas į mokėtojo kodą.",
        });
        
        // If we received the payment code, store it for display
        if (result.payment_code) {
          setPaymentCode(result.payment_code);
        }
        
        // Track the event
        trackEvent('user_password_reset', {
          userId,
          success: true,
        });
        
        // Don't close dialog if we have a payment code to show
        if (!result.payment_code) {
          setIsOpen(false);
        }
        
        // Refresh the page data
        router.refresh();
      }
    } catch (error) {
      console.error("Failed to reset password:", error);
      
      // Show error toast
      toast({
        title: "Klaida",
        description: error instanceof Error ? error.message : "Nepavyko atstatyti slaptažodžio",
        variant: "destructive",
      });
      
      // Track the error
      trackError('user_password_reset_failed', {
        userId,
        error: error instanceof Error ? error.message : String(error),
      });
      
      trackEvent('user_password_reset', {
        userId,
        success: false,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDialogClose = () => {
    setIsOpen(false);
    setPaymentCode(null);
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-1">
          <KeyRound className="h-4 w-4" />
          <span>Atstatyti slaptažodį</span>
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Slaptažodžio atstatymas</AlertDialogTitle>
          <AlertDialogDescription>
            {paymentCode ? (
              <div className="space-y-4">
                <p>
                  Vartotojo <strong>{userName}</strong> slaptažodis sėkmingai atstatytas į mokėtojo kodą.
                </p>
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-md text-blue-800 text-center">
                  <div className="text-xs uppercase font-semibold text-blue-600 mb-1">Mokėtojo kodas:</div>
                  <div className="text-xl font-mono font-bold">{paymentCode}</div>
                </div>
                <p className="text-amber-600 mt-2 text-sm">
                  <strong>Svarbu:</strong> Išsaugokite šį mokėtojo kodą, nes jis bus naudojamas prisijungimui.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <p>
                  Ar tikrai norite atstatyti vartotojo <strong>{userName}</strong> slaptažodį į pradinį mokėtojo kodą?
                </p>
                <p>
                  Atlikus šį veiksmą, vartotojas galės prisijungti naudodamas savo mokėtojo kodą kaip slaptažodį.
                  Jei vartotojas buvo pakeitęs slaptažodį, jo pasirinktas slaptažodis nebebus galiojantis.
                </p>
                <div className="bg-amber-50 p-3 rounded-md text-amber-800 text-sm">
                  <p>
                    <strong>Dėmesio:</strong> Šis veiksmas negali būti atšauktas. Vartotojui bus pranešta apie slaptažodžio pakeitimą.
                  </p>
                </div>
              </div>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          {paymentCode ? (
            <AlertDialogAction onClick={handleDialogClose}>Uždaryti</AlertDialogAction>
          ) : (
            <>
              <AlertDialogCancel disabled={isLoading}>Atšaukti</AlertDialogCancel>
              <AlertDialogAction 
                className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                onClick={handleResetPassword}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Vykdoma...
                  </>
                ) : (
                  "Atstatyti slaptažodį"
                )}
              </AlertDialogAction>
            </>
          )}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}