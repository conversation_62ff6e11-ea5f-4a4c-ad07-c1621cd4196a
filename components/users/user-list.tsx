"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { formatDate } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { userApi } from "@/lib/client/api";
import { clientFetch } from "@/lib/client/fetcher";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Edit, Trash2, User, Mail, Phone, Eye, MoreHorizontal, Check, X, Search } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CustomPagination } from "@/components/ui/custom-pagination";
import { Checkbox } from "@/components/ui/checkbox";

type User = {
  id: string;
  username: string;
  name: string;
  email: string;
  role: string;
  phone?: string;
  is_profile_updated: boolean;
  created_at: string;
  last_login?: string;
  house_name?: string;
  flat_number?: string;
  test_user?: boolean;
};

interface UserListProps {
  users: User[];
}

export function UserList({ users }: UserListProps) {
  const router = useRouter();
  const [userToDelete, setUserToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  
  // Multi-select state
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isMultipleDeleting, setIsMultipleDeleting] = useState(false);
  
  // Search state
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filteredUsers, setFilteredUsers] = useState<User[]>(users);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [paginatedUsers, setPaginatedUsers] = useState<User[]>([]);
  
  // Filter users based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredUsers(users);
    } else {
      const searchLower = searchTerm.toLowerCase();
      const filtered = users.filter(user => 
        user.name?.toLowerCase().includes(searchLower) ||
        user.username?.toLowerCase().includes(searchLower) ||
        user.email?.toLowerCase().includes(searchLower) ||
        user.house_name?.toLowerCase().includes(searchLower) ||
        user.flat_number?.toLowerCase().includes(searchLower) ||
        `${user.house_name} ${user.flat_number}`.toLowerCase().includes(searchLower)
      );
      setFilteredUsers(filtered);
    }
    // Reset to first page when search changes
    setCurrentPage(1);
  }, [users, searchTerm]);

  // Update paginatedUsers when filteredUsers, currentPage, or pageSize changes
  useEffect(() => {
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    setPaginatedUsers(filteredUsers.slice(start, end));
  }, [filteredUsers, currentPage, pageSize]);
  
  // Reset selected users when page changes
  useEffect(() => {
    setSelectedUsers([]);
  }, [currentPage, pageSize]);
  
  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page when changing page size
  };
  
  // Select all users on current page
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(paginatedUsers.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };
  
  // Toggle select single user
  const toggleSelectUser = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
    }
  };
  
  const handleDeleteUser = async () => {
    if (!userToDelete) return;
    
    setIsDeleting(true);
    
    try {
      // Use the userApi.delete method which properly handles CSRF tokens
      await userApi.delete(userToDelete);
      
      toast.success("Vartotojas ištrintas sėkmingai");
      router.refresh();
    } catch (error) {
      console.error("Klaida trinant vartotoją:", error);
      toast.error(error instanceof Error ? error.message : "Nepavyko ištrinti vartotojo");
    } finally {
      setIsDeleting(false);
      setUserToDelete(null);
    }
  };
  
  // Handle deleting multiple users
  const handleDeleteMultipleUsers = async () => {
    if (selectedUsers.length === 0) return;
    
    setIsMultipleDeleting(true);
    
    try {
      // Use clientFetch which handles CSRF tokens properly
      await clientFetch("/api/users/bulk-delete", {
        method: "DELETE",
        body: JSON.stringify({
          userIds: selectedUsers
        }),
      });
      
      toast.success(`${selectedUsers.length} vartotojai ištrinti sėkmingai`);
      setSelectedUsers([]);
      router.refresh();
    } catch (error) {
      console.error("Klaida trinant vartotojus:", error);
      toast.error(error instanceof Error ? error.message : "Nepavyko ištrinti pasirinktų vartotojų");
    } finally {
      setIsMultipleDeleting(false);
    }
  };
  
  const getRoleBadge = (role: string) => {
    switch (role) {
      case "developer":
        return (
          <Badge variant="destructive" className="bg-purple-600">
            Programuotojas
          </Badge>
        );
      case "super_admin":
        return (
          <Badge variant="destructive">
            Super Administratorius
          </Badge>
        );
      case "editor":
        return (
          <Badge variant="default">
            Redaktorius
          </Badge>
        );
      case "user":
      default:
        return (
          <Badge variant="outline">
            Vartotojas
          </Badge>
        );
    }
  };
  
  if (!users || users.length === 0) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        Vartotojų nerasta. Sukurkite naują vartotoją paspaudę mygtuką "Pridėti vartotoją".
      </div>
    );
  }
  
  return (
    <>
      {/* Search Input */}
      <div className="flex items-center space-x-2 mb-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Ieškoti vartotojų..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        {searchTerm && (
          <div className="text-sm text-muted-foreground">
            Rasta: {filteredUsers.length} iš {users.length} vartotojų
          </div>
        )}
      </div>

      {selectedUsers.length > 0 && (
        <div className="flex items-center justify-between mb-4">
          <div className="text-sm">
            Pasirinkta: <span className="font-medium">{selectedUsers.length}</span> vartotojų
          </div>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" size="sm">
                <Trash2 className="h-4 w-4 mr-2" />
                Ištrinti pasirinktus ({selectedUsers.length})
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Ar tikrai norite ištrinti pasirinktus vartotojus?</AlertDialogTitle>
                <AlertDialogDescription>
                  Ši operacija negrįžtama. {selectedUsers.length} vartotojų duomenys bus ištrinti visam laikui.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel disabled={isMultipleDeleting}>Atšaukti</AlertDialogCancel>
                <AlertDialogAction
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  onClick={handleDeleteMultipleUsers}
                  disabled={isMultipleDeleting}
                >
                  {isMultipleDeleting ? "Trinama..." : "Ištrinti"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      )}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]">
                <Checkbox 
                  checked={paginatedUsers.length > 0 && selectedUsers.length === paginatedUsers.length} 
                  onCheckedChange={handleSelectAll}
                  aria-label="Pasirinkti visus"
                />
              </TableHead>
              <TableHead>Prisijungimo vardas</TableHead>
              <TableHead>Vardas</TableHead>
              <TableHead>El. paštas</TableHead>
              <TableHead>Rolė</TableHead>
              <TableHead>Profilis</TableHead>
              <TableHead>Test User</TableHead>
              <TableHead className="text-right">Veiksmai</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                  {searchTerm ? 
                    `Pagal paieškos žodį "${searchTerm}" vartotojų nerasta.` : 
                    "Vartotojų nerasta."
                  }
                </TableCell>
              </TableRow>
            ) : (
              paginatedUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell className="w-[40px]">
                    <Checkbox 
                      checked={selectedUsers.includes(user.id)} 
                      onCheckedChange={(checked) => toggleSelectUser(user.id, !!checked)}
                      aria-label={`Pasirinkti vartotoją ${user.name}`}
                    />
                  </TableCell>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{getRoleBadge(user.role)}</TableCell>
                  <TableCell>
                    {user.is_profile_updated ? (
                      <Badge variant="default" className="bg-green-500 hover:bg-green-600">
                        <Check className="h-3 w-3 mr-1" /> Atnaujintas
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-amber-500 border-amber-500">
                        <X className="h-3 w-3 mr-1" /> Neatnaujintas
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {user.test_user ? (
                      <Badge variant="outline" className="text-blue-600 border-blue-600">
                        <Check className="h-3 w-3 mr-1" /> Testinis
                      </Badge>
                    ) : (
                      <span className="text-xs text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Meniu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Veiksmai</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => router.push(`/dashboard/admin/users/${user.id}`)}>
                          <Eye className="h-4 w-4 mr-2" />
                          Peržiūrėti
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => router.push(`/dashboard/admin/users/${user.id}/edit`)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Redaguoti
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="text-destructive focus:text-destructive"
                          onSelect={() => setUserToDelete(user.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Ištrinti
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      
      {/* Add pagination component */}
      <CustomPagination
        currentPage={currentPage}
        totalItems={filteredUsers.length}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        className="mt-6"
      />
      
      <AlertDialog open={!!userToDelete} onOpenChange={(open) => !open && setUserToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Ar tikrai norite ištrinti šį vartotoją?</AlertDialogTitle>
            <AlertDialogDescription>
              Ši operacija negrįžtama. Vartotojo duomenys bus ištrinti visam laikui.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Atšaukti</AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              onClick={handleDeleteUser}
              disabled={isDeleting}
            >
              {isDeleting ? "Trinama..." : "Ištrinti"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 