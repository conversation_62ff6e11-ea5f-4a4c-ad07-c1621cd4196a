"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Info, Upload, CheckCircle, FileSpreadsheet, RefreshCw, Download, ArrowRight } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";

// Required and optional fields for the database
const requiredFields = [
  { id: "street", label: "Gatvė" },
  { id: "houseNumber", label: "Namo numeris" },
  { id: "flatNumber", label: "Buto numeris" },
  { id: "payerCode", label: "Mokėtojo kodas" }
];

const optionalFields = [
  { id: "name", label: "Vardas" },
  { id: "surname", label: "Pavardė" },
  { id: "email", label: "El. paštas" },
  { id: "phone", label: "Telefonas" }
];

export default function UserImportForm() {
  const router = useRouter();
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [importStep, setImportStep] = useState<"upload" | "map" | "result">("upload");
  const [excelColumns, setExcelColumns] = useState<string[]>([]);
  const [columnMapping, setColumnMapping] = useState<Record<string, string>>({});
  const [importResult, setImportResult] = useState<{
    success: boolean;
    message: string;
    details?: {
      total: number;
      imported: number;
      errors: {row: number; message: string}[];
    };
  } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection and validation
  function handleFileChange(e: React.ChangeEvent<HTMLInputElement>) {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      
      // Validate file type (only allow .xlsx files)
      if (!selectedFile.name.endsWith('.xlsx')) {
        toast({
          title: "Netinkamas failo formatas",
          description: "Prašome įkelti tik Excel (.xlsx) failus.",
          variant: "destructive",
        });
        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }
      
      setFile(selectedFile);
      setImportResult(null);
    }
  }

  // Process the uploaded file to extract column headers
  async function handleContinueToMapping() {
    if (!file) {
      toast({
        title: "Failas nepasirinktas",
        description: "Prašome pasirinkti Excel failą.",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    
    try {
      // Create a FormData object to send the file for header inspection
      const formData = new FormData();
      formData.append('file', file);
      formData.append('getHeaders', 'true'); // Tell the API we just want headers
      
      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 20;
          if (newProgress >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return newProgress;
        });
      }, 300);
      
      // Send the file to get headers
      const response = await fetch('/api/users/import/inspect', {
        method: 'POST',
        body: formData,
      });
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      if (!response.ok) {
        const errorData = await response.json();
        toast({
          title: "Klaida nuskaitant failą",
          description: errorData.message || "Nepavyko nuskaityti failo stulpelių",
          variant: "destructive",
        });
        return;
      }
      
      const data = await response.json();
      
      // Initialize the column mapping
      if (data.headers && data.headers.length > 0) {
        setExcelColumns(data.headers);
        
        // Create initial mapping
        const initialMapping: Record<string, string> = {};
        
        // Try to map columns by name matching
        [...requiredFields, ...optionalFields].forEach(field => {
          // Look for an exact match first
          const exactMatch = data.headers.find((header: string) => 
            header.toLowerCase() === field.label.toLowerCase()
          );
          
          if (exactMatch) {
            initialMapping[field.id] = exactMatch;
          } else {
            // Look for a partial match
            const partialMatch = data.headers.find((header: string) => 
              header.toLowerCase().includes(field.label.toLowerCase()) || 
              field.label.toLowerCase().includes(header.toLowerCase())
            );
            
            if (partialMatch) {
              initialMapping[field.id] = partialMatch;
            }
          }
        });
        
        setColumnMapping(initialMapping);
        setImportStep("map");
      } else {
        toast({
          title: "Tuščias failas",
          description: "Faile nerasta stulpelių antraščių",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Header inspection error:', error);
      toast({
        title: "Klaida",
        description: error instanceof Error ? error.message : "Nepavyko nuskaityti Excel failo antraščių",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }
  
  // Update the column mapping when user selects a different column
  function handleMappingChange(fieldId: string, columnName: string) {
    setColumnMapping(prev => ({
      ...prev,
      [fieldId]: columnName === "none" ? "" : columnName
    }));
  }
  
  // Process the import with the mapped columns
  async function handleProcessImport() {
    // Check if all required fields are mapped
    const missingRequiredFields = requiredFields.filter(
      field => !columnMapping[field.id] || columnMapping[field.id] === "none"
    );
    
    if (missingRequiredFields.length > 0) {
      toast({
        title: "Trūksta privalomų laukų",
        description: `Prašome susieti šiuos privalomus laukus: ${missingRequiredFields.map(f => f.label).join(', ')}`,
        variant: "destructive",
      });
      return;
    }
    
    if (!file) {
      toast({
        title: "Failas nepasirinktas",
        description: "Prašome pasirinkti Excel failą.",
        variant: "destructive",
      });
      return;
    }
    
    setIsUploading(true);
    setUploadProgress(0);
    
    // Create a FormData object to send the file
    const formData = new FormData();
    formData.append('file', file);
    
    // Add column mapping to the request
    formData.append('columnMapping', JSON.stringify(columnMapping));
    
    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 10;
          if (newProgress >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return newProgress;
        });
      }, 300);
      
      // Send the file to the API endpoint
      const response = await fetch('/api/users/import', {
        method: 'POST',
        body: formData,
      });
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      const responseData = await response.json();
      
      // Set the import result regardless of success or failure
      setImportResult(responseData);
      setImportStep("result");
      
      if (responseData.success) {
        toast({
          title: "Importavimas sėkmingas",
          description: `Sėkmingai importuota ${responseData.details?.imported} vartotojų.`,
        });
      } else {
        toast({
          title: "Importavimas su klaidomis",
          description: responseData.message || "Importavimas nepavyko",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Import error:', error);
      toast({
        title: "Importavimo klaida",
        description: error instanceof Error ? error.message : "Įvyko nežinoma klaida importuojant vartotojus.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }

  function handleReset() {
    setFile(null);
    setImportResult(null);
    setExcelColumns([]);
    setColumnMapping({});
    setImportStep("upload");
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }

  async function handleDownloadTemplate() {
    try {
      const response = await fetch('/api/users/import/template', {
        method: 'GET',
      });
      
      if (!response.ok) {
        throw new Error('Failed to download template');
      }
      
      // Get the blob from the response
      const blob = await response.blob();
      
      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);
      
      // Create a temporary anchor element and trigger download
      const a = document.createElement('a');
      a.href = url;
      a.download = 'vartotoju_importavimo_sablonas.xlsx';
      document.body.appendChild(a);
      a.click();
      
      // Cleanup
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast({
        title: "Šablonas atsisiųstas",
        description: "Vartotojų importavimo šablonas sėkmingai atsisiųstas.",
      });
    } catch (error) {
      console.error('Template download error:', error);
      toast({
        title: "Atsisiuntimo klaida",
        description: "Nepavyko atsisiųsti šablono. Bandykite dar kartą.",
        variant: "destructive",
      });
    }
  }

  // Render the file upload step
  const renderUploadStep = () => (
    <>
      <div className="flex justify-end mb-2">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleDownloadTemplate}
        >
          <Download className="mr-2 h-4 w-4" />
          Atsisiųsti šabloną
        </Button>
      </div>

      <div className="border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center">
        <FileSpreadsheet className="h-10 w-10 text-muted-foreground mb-4" />
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          className="hidden"
          accept=".xlsx"
          disabled={isUploading}
        />
        <Button 
          type="button"
          variant="outline"
          size="lg"
          className="mb-2"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
        >
          <Upload className="mr-2 h-4 w-4" />
          Pasirinkti Excel failą
        </Button>
        {file && (
          <p className="text-sm text-center mt-2">
            Pasirinktas failas: <span className="font-medium">{file.name}</span> ({Math.round(file.size / 1024)} KB)
          </p>
        )}
      </div>
      
      {isUploading && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">Apdorojamas failas...</span>
            <span className="text-sm">{uploadProgress}%</span>
          </div>
          <Progress value={uploadProgress} className="h-2" />
        </div>
      )}
      
      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>Failo formatas</AlertTitle>
        <AlertDescription>
          <p className="text-sm mt-2">
            Excel failas turi turėti bent vieną eilutę su antraštėmis, pvz.:
          </p>
          <ul className="text-sm list-disc pl-5 mt-2 space-y-1">
            <li>Vardas</li>
            <li>Pavardė</li>
            <li>El. paštas</li>
            <li>Gatvė</li>
            <li>Namo numeris</li>
            <li>Butas</li>
            <li>Telefonas</li>
            <li>Mokėtojo kodas</li>
          </ul>
          <p className="text-sm mt-2">
            Tolimesniame žingsnyje galėsite susieti Excel stulpelius su atitinkamais duomenų bazės laukais.
          </p>
        </AlertDescription>
      </Alert>
      
      <div className="flex justify-end space-x-4 pt-4">
        <Button
          type="button"
          onClick={handleContinueToMapping}
          disabled={isUploading || !file}
        >
          {isUploading ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Apdorojama...
            </>
          ) : (
            <>
              Tęsti
              <ArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </>
  );

  // Render the column mapping step
  const renderMappingStep = () => (
    <>
      <div className="space-y-4">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Stulpelių susiejimas</AlertTitle>
          <AlertDescription>
            <p className="text-sm mt-2">
              Prašome susieti Excel failo stulpelius su reikiamais duomenų bazės laukais.
              Privalomi laukai pažymėti žvaigždute (*).
            </p>
          </AlertDescription>
        </Alert>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-3">Privalomi laukai</h3>
            <div className="grid gap-4 md:grid-cols-2">
              {requiredFields.map(field => (
                <div key={field.id} className="space-y-2">
                  <Label htmlFor={`field-${field.id}`}>
                    {field.label} <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={columnMapping[field.id] || "none"}
                    onValueChange={(value) => handleMappingChange(field.id, value)}
                  >
                    <SelectTrigger id={`field-${field.id}`}>
                      <SelectValue placeholder="Pasirinkite stulpelį" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">-- Pasirinkite stulpelį --</SelectItem>
                      {excelColumns.map(column => (
                        <SelectItem key={column} value={column}>
                          {column}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-3">Pasirinktiniai laukai</h3>
            <div className="grid gap-4 md:grid-cols-2">
              {optionalFields.map(field => (
                <div key={field.id} className="space-y-2">
                  <Label htmlFor={`field-${field.id}`}>{field.label}</Label>
                  <Select
                    value={columnMapping[field.id] || "none"}
                    onValueChange={(value) => handleMappingChange(field.id, value)}
                  >
                    <SelectTrigger id={`field-${field.id}`}>
                      <SelectValue placeholder="Pasirinkite stulpelį" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">-- Pasirinkite stulpelį --</SelectItem>
                      {excelColumns.map(column => (
                        <SelectItem key={column} value={column}>
                          {column}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      
      {isUploading && (
        <div className="space-y-2 mt-4">
          <div className="flex items-center justify-between">
            <span className="text-sm">Vykdomas importavimas...</span>
            <span className="text-sm">{uploadProgress}%</span>
          </div>
          <Progress value={uploadProgress} className="h-2" />
        </div>
      )}
      
      <div className="flex justify-between space-x-4 pt-6">
        <Button
          type="button"
          variant="outline"
          onClick={() => setImportStep("upload")}
          disabled={isUploading}
        >
          Atgal
        </Button>
        <Button
          type="button"
          onClick={handleProcessImport}
          disabled={isUploading}
        >
          {isUploading ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Importuojama...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Importuoti vartotojus
            </>
          )}
        </Button>
      </div>
    </>
  );

  // Render the results step
  const renderResultStep = () => (
    <>
      {importResult && (
        <Alert variant={importResult.success ? "default" : "destructive"}>
          {importResult.success ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <AlertCircle className="h-4 w-4" />
          )}
          <AlertTitle>
            {importResult.success 
              ? "Importavimas sėkmingas" 
              : "Importavimas su klaidomis"}
          </AlertTitle>
          <AlertDescription>
            <div className="mt-2">
              <p>{importResult.message}</p>
              {importResult.details && (
                <div className="mt-4">
                  <p className="text-sm">
                    Iš viso: {importResult.details.total} vartotojų<br />
                    Importuota: {importResult.details.imported} vartotojų
                  </p>
                  
                  {importResult.details.errors.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-medium mb-2">Klaidos:</p>
                      <ul className="text-sm space-y-1 list-disc pl-5">
                        {importResult.details.errors.map((error, index) => (
                          <li key={index}>
                            Eilutė {error.row}: {error.message}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          </AlertDescription>
        </Alert>
      )}
      
      <div className="flex justify-end space-x-4 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={handleReset}
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          Pradėti iš naujo
        </Button>
      </div>
    </>
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Vartotojų importas iš Excel</CardTitle>
        <CardDescription>
          Įkelkite Excel (.xlsx) failą su vartotojų duomenimis ir susieskite stulpelius su duomenų bazės laukais.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6">
          <div className="flex justify-between mb-2">
            <div className="flex items-center space-x-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${importStep === "upload" ? "bg-primary text-primary-foreground" : "bg-muted"}`}>
                1
              </div>
              <div className={`text-sm ${importStep === "upload" ? "font-medium" : "text-muted-foreground"}`}>
                Failo pasirinkimas
              </div>
            </div>
            <div className="border-t border-gray-200 flex-1 mx-4 mt-4"></div>
            <div className="flex items-center space-x-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${importStep === "map" ? "bg-primary text-primary-foreground" : "bg-muted"}`}>
                2
              </div>
              <div className={`text-sm ${importStep === "map" ? "font-medium" : "text-muted-foreground"}`}>
                Stulpelių susiejimas
              </div>
            </div>
            <div className="border-t border-gray-200 flex-1 mx-4 mt-4"></div>
            <div className="flex items-center space-x-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${importStep === "result" ? "bg-primary text-primary-foreground" : "bg-muted"}`}>
                3
              </div>
              <div className={`text-sm ${importStep === "result" ? "font-medium" : "text-muted-foreground"}`}>
                Rezultatai
              </div>
            </div>
          </div>
        </div>

        <form className="space-y-6">
          {importStep === "upload" && renderUploadStep()}
          {importStep === "map" && renderMappingStep()}
          {importStep === "result" && renderResultStep()}
        </form>
      </CardContent>
    </Card>
  );
} 