"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { 
  Download, 
  File, 
  Image, 
  FileText, 
  Paperclip
} from 'lucide-react';

interface Attachment {
  id: string;
  file_name: string;
  file_size: number;
  file_type: string;
  is_image: boolean;
  created_at: string;
}

interface AttachmentListProps {
  announcementId: string;
  showTitle?: boolean;
}

export function AttachmentList({ announcementId, showTitle = true }: AttachmentListProps) {
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAttachments();
  }, [announcementId]);

  const fetchAttachments = async () => {
    try {
      const response = await fetch(`/api/announcements/attachments?announcementId=${announcementId}`);
      
      if (!response.ok) {
        throw new Error('Nepavyko gauti priedų');
      }

      const data = await response.json();
      
      if (data.success) {
        setAttachments(data.attachments);
      }

    } catch (error) {
      console.error('Error fetching attachments:', error);
      toast({
        title: "Klaida",
        description: "Nepavyko gauti priedų",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string, isImage: boolean) => {
    if (isImage) return <Image className="h-4 w-4 text-blue-500" />;
    if (fileType.includes('pdf')) return <FileText className="h-4 w-4 text-red-500" />;
    return <File className="h-4 w-4 text-gray-500" />;
  };

  const handleDownload = async (attachment: Attachment) => {
    try {
      const response = await fetch(`/api/announcements/attachments/${attachment.id}`);
      
      if (!response.ok) {
        throw new Error('Nepavyko gauti failo');
      }

      const data = await response.json();
      
      if (data.success && data.attachment.download_url) {
        // Open download URL in new tab
        window.open(data.attachment.download_url, '_blank');
      }

    } catch (error) {
      console.error('Download error:', error);
      toast({
        title: "Klaida",
        description: "Nepavyko atsisiųsti failo",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="space-y-2">
        {showTitle && (
          <div className="flex items-center space-x-2">
            <Paperclip className="h-4 w-4" />
            <h3 className="text-sm font-medium">Priedai</h3>
          </div>
        )}
        <p className="text-sm text-gray-500">Kraunami priedai...</p>
      </div>
    );
  }

  if (attachments.length === 0) {
    return null;
  }

  return (
    <div className="space-y-3">
      {showTitle && (
        <div className="flex items-center space-x-2">
          <Paperclip className="h-4 w-4" />
          <h3 className="text-sm font-medium">Priedai ({attachments.length})</h3>
        </div>
      )}
      
      <div className="space-y-2">
        {attachments.map((attachment) => (
          <Card key={attachment.id} className="border border-gray-200">
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  {getFileIcon(attachment.file_type, attachment.is_image)}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate" title={attachment.file_name}>
                      {attachment.file_name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(attachment.file_size)}
                      {attachment.is_image && ' • Paveikslėlis'}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDownload(attachment)}
                  className="flex-shrink-0"
                >
                  <Download className="h-4 w-4" />
                  <span className="sr-only">Atsisiųsti {attachment.file_name}</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
