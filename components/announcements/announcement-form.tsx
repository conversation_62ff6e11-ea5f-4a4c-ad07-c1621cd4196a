"use client";

import { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { CountdownAlert } from "@/components/announcements/countdown-alert";
import { Checkbox } from "@/components/ui/checkbox";
import {
  useStreets,
  useHouses,
  useFlats,
  useUsers,
  useTags,
  useCreateAnnouncement,
  invalidateQueries
} from "@/lib/tanstack/queries";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Tag as TagIcon, Plus, MapPin, Building, Home, Users } from "lucide-react";
import { TagSelector } from "@/components/tags/tag-selector";
import { AttachmentUpload } from "@/components/announcements/attachment-upload";

// Interface for tag data
interface TagData {
  id: number;
  name: string;
  color: string;
  category: string;
  created_at: string;
  updated_at?: string;
}

// Form validation schema
const formSchema = z.object({
  title: z.string().min(5, {
    message: "Pavadinimas turi būti bent 5 simbolių ilgio.",
  }),
  content: z.string().min(10, {
    message: "Turinys turi būti bent 10 simbolių ilgio.",
  }),
  importance: z.enum(["normal", "important", "urgent"], {
    required_error: "Pasirinkite svarbumą.",
  }),
  audienceType: z.enum(["all", "specific"], {
    required_error: "Pasirinkite gavėjų tipą.",
  }),
  specificType: z.enum(["streets", "houses", "flats", "users"]).optional(),
  streetIds: z.array(z.string()).optional(),
  houseIds: z.array(z.string()).optional(),
  flatIds: z.array(z.string()).optional(),
  userIds: z.array(z.string()).optional(),
  isDraft: z.boolean().default(false),
  tagIds: z.array(z.number()).default([]),
  sendEmails: z.boolean().default(true),
});

type AnnouncementFormValues = z.infer<typeof formSchema>;

interface Street {
  id: string;
  name: string;
  city: string;
  displayName?: string;
}

interface House {
  id: string;
  name: string;
  address: string;
  streetId: string;
  streetName: string;
  displayName: string;
}

interface Flat {
  id: string;
  houseId: string;
  number: string;
  floor: number;
  houseName: string;
  streetId: string;
  streetName: string;
  displayName: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  flatId: string | null;
  houseId: string | null;
  streetId: string | null;
  addressDisplay: string;
  displayName: string;
}

interface AnnouncementFormProps {
  existingAnnouncement?: any;
  editMode?: boolean;
  userId?: number;
}

export function AnnouncementForm({ existingAnnouncement, editMode = false, userId }: AnnouncementFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showCountdown, setShowCountdown] = useState(false);
  const [announcementData, setAnnouncementData] = useState<AnnouncementFormValues | null>(null);
  // Replace manual state with TanStack Query hooks
  const { data: streets = [], isLoading: streetsLoading } = useStreets();
  const { data: houses = [], isLoading: housesLoading } = useHouses();
  const { data: flats = [], isLoading: flatsLoading } = useFlats();
  const { data: users = [], isLoading: usersLoading } = useUsers({ excludeAdmins: true });
  const { data: tags = [], isLoading: tagsLoading } = useTags();
  const createAnnouncementMutation = useCreateAnnouncement();
  
  const [selectedStreets, setSelectedStreets] = useState<string[]>([]);
  const [selectedHouses, setSelectedHouses] = useState<string[]>([]);
  const [filteredHouses, setFilteredHouses] = useState<House[]>([]);
  const [filteredFlats, setFilteredFlats] = useState<Flat[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  
  // Compute loading state from all queries
  const loadingData = streetsLoading || housesLoading || flatsLoading || usersLoading || tagsLoading;
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTags, setSelectedTags] = useState<TagData[]>([]);
  const [isTagManagerOpen, setIsTagManagerOpen] = useState(false);
  const [attachments, setAttachments] = useState<any[]>([]);

  // Initialize form with default values
  const form = useForm<AnnouncementFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      content: "",
      importance: "normal",
      audienceType: undefined, // No default - force user to select
      specificType: undefined,
      streetIds: [],
      houseIds: [],
      flatIds: [],
      userIds: [],
      isDraft: false,
      tagIds: [],
      sendEmails: true,
    },
  });

  // Load existing announcement data when editing
  useEffect(() => {
    if (existingAnnouncement && editMode) {
      console.log("Loading existing announcement data:", existingAnnouncement);
      
      // Reset form with existing announcement data
      form.reset({
        title: existingAnnouncement.title || "",
        content: existingAnnouncement.content || "",
        importance: existingAnnouncement.importance || "normal",
        audienceType: existingAnnouncement.recipient_type === "all" ? "all" : "specific",
        specificType: existingAnnouncement.recipient_type === "all" ? undefined : existingAnnouncement.recipient_type,
        streetIds: existingAnnouncement.street_ids || [],
        houseIds: existingAnnouncement.house_ids || [],
        flatIds: existingAnnouncement.flat_ids || [],
        userIds: existingAnnouncement.user_ids || [],
        isDraft: !!existingAnnouncement.is_draft,
        tagIds: existingAnnouncement.tags?.map((tag: any) => tag.id) || [],
        sendEmails: true,
      });
      
      // Update selected streets and houses
      if (existingAnnouncement.street_ids?.length > 0) {
        setSelectedStreets(existingAnnouncement.street_ids);
      }
      
      if (existingAnnouncement.house_ids?.length > 0) {
        setSelectedHouses(existingAnnouncement.house_ids);
      }
      
      // Load tags if available
      if (existingAnnouncement.tags?.length > 0) {
        setSelectedTags(existingAnnouncement.tags);
      }
    }
  }, [existingAnnouncement, editMode, form]);

  // Data loading is now handled by TanStack Query hooks above

  // Update filtered houses when streets are selected
  useEffect(() => {
    if (selectedStreets.length > 0) {
      const filtered = houses.filter(house => selectedStreets.includes(house.streetId));
      setFilteredHouses(filtered);
    } else {
      setFilteredHouses(houses);
    }
  }, [selectedStreets, houses]);

  // Update filtered flats when houses are selected
  useEffect(() => {
    if (selectedHouses.length > 0) {
      const filtered = flats.filter(flat => selectedHouses.includes(flat.houseId));
      setFilteredFlats(filtered);
    } else if (selectedStreets.length > 0) {
      // If streets are selected but no houses, show all flats for the selected streets
      const streetsHouseIds = houses
        .filter(house => selectedStreets.includes(house.streetId))
        .map(house => house.id);
      const filtered = flats.filter(flat => streetsHouseIds.includes(flat.houseId));
      setFilteredFlats(filtered);
    } else {
      setFilteredFlats(flats);
    }
  }, [selectedHouses, selectedStreets, houses, flats]);

  // Update filtered users based on selection
  useEffect(() => {
    let filtered = [...users];

    // Apply search filter if search term exists
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(user =>
        (user.name && user.name.toLowerCase().includes(searchLower)) ||
        (user.email && user.email.toLowerCase().includes(searchLower)) ||
        (user.addressDisplay && user.addressDisplay.toLowerCase().includes(searchLower))
      );
    }

    // If streets are selected, filter users by street
    if (selectedStreets.length > 0) {
      filtered = filtered.filter(user =>
        user.streetId && selectedStreets.includes(user.streetId)
      );
    }

    // If houses are selected, filter users by house
    if (selectedHouses.length > 0) {
      filtered = filtered.filter(user =>
        user.houseId && selectedHouses.includes(user.houseId)
      );
    }

    // If flats are selected via form, don't need to filter here as the user selection will happen separately

    setFilteredUsers(filtered);
  }, [selectedStreets, selectedHouses, users, searchTerm]);

  // Use useCallback to prevent unnecessary re-renders
  const formValues = form.watch();

  // Memoize form values to prevent infinite loops
  const audienceType = useMemo(() => formValues.audienceType, [formValues.audienceType]);
  const specificType = useMemo(() => formValues.specificType, [formValues.specificType]);
  const streetIds = useMemo(() => formValues.streetIds || [], [formValues.streetIds]);
  const houseIds = useMemo(() => formValues.houseIds || [], [formValues.houseIds]);
  const flatIds = useMemo(() => formValues.flatIds || [], [formValues.flatIds]);
  const userIds = useMemo(() => formValues.userIds || [], [formValues.userIds]);

  // Use ref to track previous values to prevent infinite loops
  const prevAudienceType = useRef(audienceType);
  const prevSpecificType = useRef(specificType);

  // Handle audience type changes and reset selections
  useEffect(() => {
    // Only run if audienceType actually changed
    if (prevAudienceType.current !== audienceType) {
      prevAudienceType.current = audienceType;

      if (audienceType === 'specific') {
        // Set default specificType when selecting "specific"
        if (!specificType) {
          form.setValue('specificType', 'streets', { shouldValidate: false });
        }
      } else if (audienceType === 'all') {
        // Reset all selections when switching to "all"
        setSelectedStreets([]);
        setSelectedHouses([]);
        form.setValue("streetIds", [], { shouldValidate: false });
        form.setValue("houseIds", [], { shouldValidate: false });
        form.setValue("flatIds", [], { shouldValidate: false });
        form.setValue("userIds", [], { shouldValidate: false });
        form.setValue('specificType', undefined, { shouldValidate: false });
      }
    }
  }, [audienceType, specificType, form]);

  // Handle street selection change
  const handleStreetSelection = (streetId: string, checked: boolean) => {
    let newSelectedStreets;
    if (checked) {
      newSelectedStreets = [...selectedStreets, streetId];
    } else {
      newSelectedStreets = selectedStreets.filter(id => id !== streetId);
      
      // Remove any houses from this street
      const housesToRemove = houses
        .filter(house => house.streetId === streetId)
        .map(house => house.id);
      
      const newSelectedHouses = selectedHouses.filter(id => !housesToRemove.includes(id));
      setSelectedHouses(newSelectedHouses);
      form.setValue("houseIds", newSelectedHouses);
      
      // Also remove any flats from houses in this street
      const flatIdsToRemove = flats
        .filter(flat => housesToRemove.includes(flat.houseId))
        .map(flat => flat.id);
      
      const currentFlatIds = form.getValues("flatIds") || [];
      form.setValue(
        "flatIds", 
        currentFlatIds.filter(id => !flatIdsToRemove.includes(id))
      );
    }
    
    setSelectedStreets(newSelectedStreets);
    form.setValue("streetIds", newSelectedStreets);
  };

  // Handle house selection change
  const handleHouseSelection = (houseId: string, checked: boolean) => {
    let newSelectedHouses;
    if (checked) {
      newSelectedHouses = [...selectedHouses, houseId];
      
      // Also select the street if not already selected
      const house = houses.find(h => h.id === houseId);
      if (house && !selectedStreets.includes(house.streetId)) {
        const newSelectedStreets = [...selectedStreets, house.streetId];
        setSelectedStreets(newSelectedStreets);
        form.setValue("streetIds", newSelectedStreets);
      }
    } else {
      newSelectedHouses = selectedHouses.filter(id => id !== houseId);
      
      // Also remove any flats from this house
      const flatIdsToRemove = flats
        .filter(flat => flat.houseId === houseId)
        .map(flat => flat.id);
      
      const currentFlatIds = form.getValues("flatIds") || [];
      form.setValue(
        "flatIds", 
        currentFlatIds.filter(id => !flatIdsToRemove.includes(id))
      );
    }
    
    setSelectedHouses(newSelectedHouses);
    form.setValue("houseIds", newSelectedHouses);
  };

  // Filter function for searching
  const filterBySearchTerm = (item: { displayName: string }, term: string) => {
    if (!term.trim()) return true;
    return item.displayName.toLowerCase().includes(term.toLowerCase());
  };

  // Add validation before sending
  function validateBeforeSending(values: AnnouncementFormValues): string | null {
    // Check required fields
    if (!values.title || values.title.length < 5) {
      return "Pranešimo pavadinimas yra privalomas ir turi būti bent 5 simbolių ilgio.";
    }
    
    if (!values.content || values.content.length < 10) {
      return "Pranešimo turinys yra privalomas ir turi būti bent 10 simbolių ilgio.";
    }
    
    if (!values.audienceType) {
      return "Būtina pasirinkti gavėjų tipą.";
    }
    
    // Check if recipient IDs are provided based on selected audience type
    if (values.audienceType === "specific") {
      if (!values.specificType) {
        return "Privalote pasirinkti konkretų auditorijos tipą.";
      }
      
      if (values.specificType === "streets" && (!values.streetIds || values.streetIds.length === 0)) {
        return "Privalote pasirinkti bent vieną gatvę.";
      }
      
      if (values.specificType === "houses" && (!values.houseIds || values.houseIds.length === 0)) {
        return "Privalote pasirinkti bent vieną namą.";
      }
      
      if (values.specificType === "flats" && (!values.flatIds || values.flatIds.length === 0)) {
        return "Privalote pasirinkti bent vieną butą.";
      }
      
      if (values.specificType === "users" && (!values.userIds || values.userIds.length === 0)) {
        return "Privalote pasirinkti bent vieną vartotoją.";
      }
    }
    
    return null;
  }

  // Handle saving announcement as draft
  async function handleSave(values: AnnouncementFormValues) {
    setIsLoading(true);
    
    try {
      // Determine if creating new or updating existing announcement
      const isUpdate = editMode && existingAnnouncement?.id;
      const url = isUpdate 
        ? `/api/announcements/${existingAnnouncement.id}` 
        : "/api/announcements";
      const method = isUpdate ? "PUT" : "POST";
      
      console.log(`${isUpdate ? 'Updating' : 'Creating'} announcement with method ${method} to ${url}`);
      
      // API call to save draft
      const response = await fetch(url, {
        method: method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...values,
          recipientType: values.audienceType === 'all' ? 'all' : (values.specificType || 'all'),
          isDraft: true,
          sendEmails: false
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Error response:", response.status, errorData);
        throw new Error(errorData.error || `Failed to save draft: ${response.status}`);
      }
      
      toast({
        title: "Pranešimas išsaugotas",
        description: "Pranešimas sėkmingai išsaugotas kaip juodraštis.",
      });
      
      router.push("/dashboard/announcements");
      invalidateQueries.announcements();
    } catch (error) {
      console.error("Error saving draft:", error);
      toast({
        title: "Klaida",
        description: error instanceof Error ? error.message : "Nepavyko išsaugoti pranešimo. Bandykite dar kartą.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }

  // Handle sending announcement immediately
  async function handleSend(values: AnnouncementFormValues) {
    // Validate before sending
    const validationError = validateBeforeSending(values);
    if (validationError) {
      console.error('[handleSend] Validation failed:', validationError);
      toast({
        title: "Klaida",
        description: validationError,
        variant: "destructive",
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      console.log("[handleSend] Sending announcement with values:", values);
      console.log("[handleSend] Selected tags:", selectedTags);
      
      // Determine if creating new or updating existing announcement
      const isUpdate = editMode && existingAnnouncement?.id;
      const url = isUpdate 
        ? `/api/announcements/${existingAnnouncement.id}` 
        : "/api/announcements";
      const method = isUpdate ? "PUT" : "POST";
      
      console.log(`[handleSend] ${isUpdate ? 'Updating' : 'Creating'} announcement with method ${method} to ${url}`);
      
      // Map the two-level structure to the API's expected format
      let finalRecipientType: string;
      if (values.audienceType === 'all') {
        finalRecipientType = 'all';
      } else if (values.audienceType === 'specific' && values.specificType) {
        finalRecipientType = values.specificType;
      } else {
        finalRecipientType = 'all';
      }

      const payload = {
        ...values,
        recipientType: finalRecipientType, // Use the mapped recipient type for API
        tagIds: values.tagIds?.length > 0 ? values.tagIds : selectedTags.map(tag => tag.id),
      };

      // Remove the UI-specific fields from payload
      delete payload.audienceType;
      delete payload.specificType;

      console.log(`[handleSend] Submitting userIds:`, JSON.stringify(payload.userIds));

      // API call to create/update announcement
      const response = await fetch(url, {
        method: method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("[handleSend] Error response:", response.status, errorData);
        throw new Error(errorData.error || `Failed to ${values.isDraft ? 'save' : 'send'} announcement: ${response.status}`);
      }
      
      const data = await response.json();
      console.log("[handleSend] Response data:", data);
      
      // If not a draft, we need to actually send it
      if (!values.isDraft && isUpdate === false) {
        // For a new announcement that is not a draft, we need to send it
        console.log("[handleSend] Sending new announcement with ID:", data.id);
        await sendAnnouncement(data.id, values.sendEmails);
      } else if (!values.isDraft && isUpdate === true) {
        // For an existing announcement that is switching from draft to published, send it
        console.log("[handleSend] Sending existing announcement with ID:", existingAnnouncement.id);
        await sendAnnouncement(existingAnnouncement.id, values.sendEmails);
      } else {
        // It's just a draft save
        toast({
          title: "Juodraštis išsaugotas",
          description: "Pranešimo juodraštis sėkmingai išsaugotas.",
        });
        
        router.push("/dashboard/announcements");
        invalidateQueries.announcements();
      }
    } catch (error) {
      console.error("[handleSend] Error processing announcement:", error);
      toast({
        title: "Klaida",
        description: error instanceof Error ? error.message : "Nepavyko apdoroti pranešimo. Bandykite dar kartą.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }

  // Function to send an announcement after it's created/saved
  async function sendAnnouncement(announcementId: string | number, sendEmails: boolean = true) {
    console.log(`[sendAnnouncement] Starting with ID: ${announcementId}, sendEmails: ${sendEmails}`);
    
    try {
      // Construct the URL and create the request
      const url = `/api/announcements/${announcementId}/send`;
      console.log(`[sendAnnouncement] Sending POST request to: ${url}`);
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          sendEmails
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error(`[sendAnnouncement] Error response: ${response.status}`, errorData);
        throw new Error(errorData.error || `Failed to send announcement: ${response.status}`);
      }
      
      const responseData = await response.json();
      console.log(`[sendAnnouncement] Success response:`, responseData);
      
      toast({
        title: "Pranešimas išsiųstas",
        description: "Pranešimas sėkmingai išsiųstas pasirinktiem gavėjams.",
      });
      
      router.push("/dashboard/announcements");
      invalidateQueries.announcements();
    } catch (error) {
      console.error("[sendAnnouncement] Error:", error);
      toast({
        title: "Klaida siunčiant",
        description: error instanceof Error ? error.message : "Nepavyko išsiųsti pranešimo. Bandykite dar kartą.",
        variant: "destructive",
      });
    }
  }

  // Handle starting the send process with countdown
  function handleStartSend(values: AnnouncementFormValues) {
    // Validate before starting countdown
    console.log('[handleStartSend] Running validateBeforeSending...');
    const validationError = validateBeforeSending(values);
    if (validationError) {
      console.log('[handleStartSend] validateBeforeSending failed:', validationError);
      toast({
        title: "Klaida",
        description: validationError,
        variant: "destructive",
      });
      return;
    }
    
    console.log('[handleStartSend] Validation passed. Setting state...');
    setAnnouncementData(values);
    setShowCountdown(true);
    console.log('[handleStartSend] State set, countdown should show.');
  }

  // Handle cancel sending
  function handleCancelSend() {
    setShowCountdown(false);
    setAnnouncementData(null);
    toast({
      title: "Siuntimas atšauktas",
      description: "Pranešimo siuntimas buvo atšauktas.",
    });
  }

  // Handle actual sending after countdown completes
  async function handleCompleteSend() {
    if (!announcementData) return;
    
    setIsLoading(true);
    setShowCountdown(false); // Hide countdown immediately
    
    let savedAnnouncementId: string | number | null = null;
    
    try {
      console.log("Completing send with values:", announcementData);
      
      // Determine if creating new or updating existing announcement
      const isUpdate = editMode && existingAnnouncement?.id;
      const url = isUpdate 
        ? `/api/announcements/${existingAnnouncement.id}` 
        : "/api/announcements";
      const method = isUpdate ? "PUT" : "POST";
      
      console.log(`${isUpdate ? 'Saving' : 'Creating'} announcement with method ${method} to ${url} before sending`);
      
      const payload = {
        ...announcementData,
        recipientType: announcementData.audienceType === 'all' ? 'all' : (announcementData.specificType || 'all'),
        tagIds: announcementData.tagIds || selectedTags.map(tag => tag.id),
        isDraft: false,
      };

      // Remove the UI-specific fields from payload
      delete payload.audienceType;
      delete payload.specificType;

      console.log(`[handleCompleteSend] Submitting userIds:`, JSON.stringify(payload.userIds));

      // Step 1: Save the announcement (POST/PUT) with isDraft: false
      const response = await fetch(url, {
        method: method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Error response during save:", response.status, errorData);
        throw new Error(errorData.error || `Failed to save announcement before sending: ${response.status}`);
      }
      
      const savedData = await response.json();
      savedAnnouncementId = savedData.id || existingAnnouncement?.id; // Get the ID of the saved announcement
      
      if (!savedAnnouncementId) {
        throw new Error("Could not determine announcement ID after saving.");
      }
      
      console.log(`Announcement ${savedAnnouncementId} saved successfully. Proceeding to send.`);

      // Step 2: Trigger the actual sending process using the saved ID
      await sendAnnouncement(savedAnnouncementId, announcementData.sendEmails);
      
      // Success is handled within sendAnnouncement (toast, redirect)
      
    } catch (error) {
      console.error("Error during complete send process:", error);
      toast({
        title: "Klaida siunčiant",
        description: error instanceof Error ? error.message : "Nepavyko išsiųsti pranešimo. Bandykite dar kartą.",
        variant: "destructive",
      });
      // No need to hide countdown here, already hidden
    } finally {
      setIsLoading(false);
      setAnnouncementData(null); // Clear temp data
    }
  }

  // Handle tag selection
  const handleTagsSelected = (tags: TagData[]) => {
    setSelectedTags(tags);
    form.setValue("tagIds", tags.map(tag => tag.id));
  };

  return (
    <>
      <Card className="shadow-sm border-0">
        <CardContent className="p-6">
          <Form {...form}>
            <form className="space-y-8">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pavadinimas</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Įveskite pranešimo pavadinimą"
                        className="focus-visible:ring-indigo-500"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="importance"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Svarbumas</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger className="focus:ring-indigo-500">
                          <SelectValue placeholder="Pasirinkite svarbumą" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="normal">Normalus</SelectItem>
                          <SelectItem value="important">Svarbus</SelectItem>
                          <SelectItem value="urgent">Skubus</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription className="text-slate-500">
                      Skubūs pranešimai bus pažymėti raudonai ir rodomi pirmi.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="tagIds"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Žymos</FormLabel>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {selectedTags.length > 0 ? (
                        selectedTags.map((tag) => (
                          <Badge 
                            key={tag.id} 
                            className={`bg-${tag.color}-100 text-${tag.color}-800 border border-${tag.color}-200 hover:bg-${tag.color}-200`}
                          >
                            <TagIcon className="h-3 w-3 mr-1" />
                            {tag.name}
                          </Badge>
                        ))
                      ) : (
                        <div className="text-sm text-slate-500">
                          Nepasirinkta jokių žymų
                        </div>
                      )}
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsTagManagerOpen(true)}
                      className="w-full sm:w-auto border-indigo-200 text-indigo-700 hover:bg-indigo-50"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      {selectedTags.length > 0 ? "Keisti žymas" : "Pridėti žymas"}
                    </Button>
                    <FormDescription className="text-slate-500">
                      Žymos padeda kategorizuoti pranešimus ir palengvina jų paiešką.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="audienceType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gavėjai</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger className="focus:ring-indigo-500">
                          <SelectValue placeholder="Pasirinkite gavėjų tipą" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Visi vartotojai</SelectItem>
                          <SelectItem value="specific">Pasirinkti konkrečius vartotojus</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription className="text-slate-500">
                      Pasirinkite kam bus siunčiamas pranešimas.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Show tabs when "specific" is selected */}
              {audienceType === 'specific' && (
                <div className="w-full">
                  <Tabs
                    value={specificType || "streets"}
                    onValueChange={(value) => {
                      form.setValue("specificType", value as any);
                      // Reset selections when changing tabs
                      setSelectedStreets([]);
                      setSelectedHouses([]);
                      form.setValue('streetIds', [], { shouldValidate: true });
                      form.setValue('houseIds', [], { shouldValidate: true });
                      form.setValue('flatIds', [], { shouldValidate: true });
                      form.setValue('userIds', [], { shouldValidate: true });
                      setSearchTerm("");
                    }}
                    className="w-full"
                  >
                    <TabsList className="grid w-full grid-cols-4 bg-slate-100">
                      <TabsTrigger value="streets" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
                        <MapPin className="w-4 h-4 mr-2" />
                        Pagal gatves
                      </TabsTrigger>
                      <TabsTrigger value="houses" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
                        <Building className="w-4 h-4 mr-2" />
                        Pagal namus
                      </TabsTrigger>
                      <TabsTrigger value="flats" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
                        <Home className="w-4 h-4 mr-2" />
                        Pagal butus
                      </TabsTrigger>
                      <TabsTrigger value="users" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
                        <Users className="w-4 h-4 mr-2" />
                        Individualiai
                      </TabsTrigger>
                    </TabsList>

                    {/* Display selection count */}
                    {specificType === 'streets' && (
                      <div className="text-sm text-slate-600 mt-3 mb-2">Pasirinkta gatvių: {streetIds?.length || 0}</div>
                    )}
                    {specificType === 'houses' && (
                      <div className="text-sm text-slate-600 mt-3 mb-2">Pasirinkta namų: {houseIds?.length || 0}</div>
                    )}
                    {specificType === 'flats' && (
                      <div className="text-sm text-slate-600 mt-3 mb-2">Pasirinkta butų: {flatIds?.length || 0}</div>
                    )}
                    {specificType === 'users' && (
                      <div className="text-sm text-slate-600 mt-3 mb-2">Pasirinkta vartotojų: {userIds?.length || 0}</div>
                    )}
                  </Tabs>
                </div>
              )}

              {/* Streets Selection UI */}
              {audienceType === 'specific' && specificType === 'streets' && (
                <div className="w-full pt-4 border-t border-slate-100">
                  <p className="text-sm text-slate-600 mb-3">Pasirinkite gatves:</p>
                  <div className="max-h-60 overflow-y-auto space-y-1 rounded-md border border-slate-200 p-3 bg-white">
                    {loadingData ? <p>Kraunama...</p> :
                     streets.map((street) => (
                       <div key={`street-select-${street.id}`} className="flex items-center space-x-3 p-1.5 hover:bg-slate-50 rounded">
                         <Checkbox
                           checked={streetIds?.includes(street.id)}
                           onCheckedChange={(checked) => {
                             const currentIds = form.getValues("streetIds") || [];
                             if (checked) {
                               form.setValue("streetIds", [...currentIds, street.id]);
                             } else {
                               form.setValue("streetIds", currentIds.filter(id => id !== street.id));
                             }
                             handleStreetSelection(street.id, checked);
                           }}
                         />
                         <label className="text-sm font-normal cursor-pointer">
                           {street.displayName || street.name}
                         </label>
                       </div>
                     ))}
                  </div>
                </div>
              )}

              {/* Houses Selection UI */}
              {audienceType === 'specific' && specificType === 'houses' && (
                <div className="w-full pt-4 border-t border-slate-100">
                  <Input
                    placeholder="Ieškoti namų..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="mb-4"
                  />
                  <p className="text-sm text-slate-600 mb-3">Pasirinkite namus:</p>
                  <div className="max-h-60 overflow-y-auto space-y-2 rounded-md border border-slate-200 p-3 bg-white">
                    {loadingData && <p className="text-slate-500 text-sm">Kraunami namai...</p>}
                    {!loadingData && houses.length === 0 && <p className="text-slate-500 text-sm">Namų nerasta.</p>}
                    {houses
                      .filter(house => filterBySearchTerm(house, searchTerm))
                      .map((house) => (
                        <div key={`house-select-${house.id}`} className="flex items-center space-x-3 p-2 hover:bg-slate-100 transition-colors rounded">
                          <Checkbox
                            checked={houseIds?.includes(house.id)}
                            onCheckedChange={(checked) => {
                              const currentIds = form.getValues("houseIds") || [];
                              if (checked) {
                                form.setValue("houseIds", [...currentIds, house.id]);
                              } else {
                                form.setValue("houseIds", currentIds.filter(id => id !== house.id));
                              }
                              handleHouseSelection(house.id, checked);
                            }}
                          />
                          <label className="text-sm font-normal cursor-pointer">
                            {house.displayName || `${house.name} (${house.address})`}
                          </label>
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {/* Flats Selection UI */}
              {audienceType === 'specific' && specificType === 'flats' && (
                <div className="w-full pt-4 border-t border-slate-100">
                  <Input
                    placeholder="Ieškoti butų..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="mb-4"
                  />
                  <p className="text-sm text-slate-600 mb-3">Pasirinkite butus:</p>
                  <div className="max-h-60 overflow-y-auto space-y-2 rounded-md border border-slate-200 p-3 bg-white">
                    {loadingData && <p className="text-slate-500 text-sm">Kraunami butai...</p>}
                    {!loadingData && flats.length === 0 && <p className="text-slate-500 text-sm">Butų nerasta.</p>}
                    {flats
                      .filter(flat => filterBySearchTerm(flat, searchTerm))
                      .map((flat) => (
                        <div key={`flat-select-${flat.id}`} className="flex items-center space-x-3 p-2 hover:bg-slate-100 transition-colors rounded">
                          <Checkbox
                            checked={flatIds?.includes(flat.id)}
                            onCheckedChange={(checked) => {
                              const currentIds = form.getValues("flatIds") || [];
                              if (checked) {
                                form.setValue("flatIds", [...currentIds, flat.id]);
                              } else {
                                form.setValue("flatIds", currentIds.filter(id => id !== flat.id));
                              }
                            }}
                          />
                          <label className="text-sm font-normal cursor-pointer">
                            {flat.displayName || `${flat.houseName} - ${flat.number}`}
                          </label>
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {/* Users Selection UI */}
              {audienceType === 'specific' && specificType === 'users' && (
                <div className="w-full pt-4 border-t border-slate-100">
                  <Input
                    placeholder="Ieškoti gyventojų..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="mb-4"
                  />
                  <p className="text-sm text-slate-600 mb-3">Pasirinkite gyventojus:</p>
                  <div className="max-h-60 overflow-y-auto space-y-2 rounded-md border border-slate-200 p-3 bg-white">
                    {loadingData && <p className="text-slate-500 text-sm">Kraunami gyventojai...</p>}
                    {!loadingData && users.length === 0 && <p className="text-slate-500 text-sm">Gyventojų nerasta.</p>}
                    {users
                      .filter(user => 
                        (user.name && user.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
                        (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
                        (user.addressDisplay && user.addressDisplay.toLowerCase().includes(searchTerm.toLowerCase()))
                      )
                      .map((user) => (
                        <div key={`user-select-${user.id}`} className="flex items-center space-x-3 p-2 hover:bg-slate-100 transition-colors rounded">
                          <Checkbox
                            checked={userIds?.includes(user.id.toString())}
                            onCheckedChange={(checked) => {
                              const currentIds = form.getValues("userIds") || [];
                              if (checked) {
                                form.setValue("userIds", [...currentIds, user.id.toString()]);
                              } else {
                                form.setValue("userIds", currentIds.filter(id => id !== user.id.toString()));
                              }
                            }}
                          />
                          <label className="text-sm font-normal cursor-pointer flex items-center gap-2">
                            <Users className="h-4 w-4 text-slate-500" />
                            {user.name}
                            {user.addressDisplay && (
                              <span className="text-slate-500 text-xs">
                                ({user.addressDisplay})
                              </span>
                            )}
                          </label>
                        </div>
                      ))}
                  </div>
                </div>
              )}
              
              <FormField
                control={form.control}
                name="sendEmails"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 my-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-slate-800">
                        Siųsti el. laiškus gavėjams
                      </FormLabel>
                      <FormDescription className="text-xs text-slate-500">
                        Išjunkite, jei norite, kad pranešimas būtų rodomas tik sistemoje be el. laiškų.
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Turinys</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Įveskite pranešimo turinį"
                        rows={8}
                        className="border-slate-200 focus-visible:ring-indigo-500"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <AttachmentUpload
                announcementId={existingAnnouncement?.id?.toString()}
                attachments={attachments}
                onAttachmentsChange={setAttachments}
                disabled={isLoading}
              />

              <div className="flex justify-end space-x-4 pt-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={isLoading}
                  className="border-slate-200 text-slate-700 hover:bg-slate-100 hover:text-slate-900"
                >
                  Atšaukti
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => {
                    const formValues = form.getValues();
                    handleSave(formValues);
                  }}
                  disabled={isLoading}
                  className="bg-slate-100 text-slate-800 hover:bg-slate-200"
                >
                  {isLoading ? "Saugoma..." : "Išsaugoti juodraštį"}
                </Button>
                <Button
                  type="button"
                  onClick={(e) => {
                    console.log('[Send Button] Clicked! Bypassing countdown for direct send');
                    // Directly call handleSend with isDraft explicitly set to false
                    // This bypasses the countdown completely for testing
                    const values = form.getValues();
                    handleSend({...values, isDraft: false});
                  }}
                  disabled={isLoading}
                  className="bg-indigo-600 hover:bg-indigo-700"
                >
                  {isLoading ? "Siunčiama..." : "Siųsti pranešimą"}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
      
      {showCountdown && announcementData && (
        <CountdownAlert
          onCancel={handleCancelSend}
          onTimeout={handleCompleteSend}
          title="Patvirtinkite pranešimo siuntimą"
          description="Pranešimas bus išsiųstas pasirinktiems gavėjams po kelių sekundžių."
          seconds={10}
        />
      )}
      
      <TagSelector
        isOpen={isTagManagerOpen}
        onClose={() => setIsTagManagerOpen(false)}
        onTagsSelected={handleTagsSelected}
        selectedTagIds={form.getValues("tagIds")}
      />
    </>
  );
}