"use client";

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { toast } from '@/components/ui/use-toast';
import { 
  Upload, 
  X, 
  File, 
  Image, 
  FileText, 
  Download,
  Trash2
} from 'lucide-react';

interface Attachment {
  id: string;
  file_name: string;
  file_size: number;
  file_type: string;
  is_image: boolean;
  created_at: string;
}

interface AttachmentUploadProps {
  announcementId?: string;
  attachments: Attachment[];
  onAttachmentsChange: (attachments: Attachment[]) => void;
  disabled?: boolean;
}

const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
const ALLOWED_TYPES = [
  'image/jpeg',
  'image/png', 
  'image/gif',
  'image/webp',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/plain',
  'text/csv'
];

export function AttachmentUpload({ 
  announcementId, 
  attachments, 
  onAttachmentsChange, 
  disabled = false 
}: AttachmentUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string, isImage: boolean) => {
    if (isImage) return <Image className="h-4 w-4" />;
    if (fileType.includes('pdf')) return <FileText className="h-4 w-4" />;
    return <File className="h-4 w-4" />;
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    if (!announcementId) {
      toast({
        title: "Klaida",
        description: "Pirmiausia išsaugokite pranešimą kaip juodraštį",
        variant: "destructive",
      });
      return;
    }

    const file = files[0];

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      toast({
        title: "Failas per didelis",
        description: "Maksimalus failo dydis: 50MB",
        variant: "destructive",
      });
      return;
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      toast({
        title: "Netinkamas failo tipas",
        description: "Leidžiami: paveikslėliai, PDF, Word, Excel, tekstiniai failai",
        variant: "destructive",
      });
      return;
    }

    await uploadFile(file);
  };

  const uploadFile = async (file: File) => {
    setUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('announcementId', announcementId!);

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 10;
          if (newProgress >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return newProgress;
        });
      }, 200);

      const response = await fetch('/api/announcements/attachments', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepavyko įkelti failo');
      }

      const data = await response.json();
      
      if (data.success) {
        onAttachmentsChange([...attachments, data.attachment]);
        toast({
          title: "Sėkmė",
          description: "Failas sėkmingai įkeltas",
        });
      }

    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Klaida",
        description: error instanceof Error ? error.message : "Nepavyko įkelti failo",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDownload = async (attachment: Attachment) => {
    try {
      const response = await fetch(`/api/announcements/attachments/${attachment.id}`);
      
      if (!response.ok) {
        throw new Error('Nepavyko gauti failo');
      }

      const data = await response.json();
      
      if (data.success && data.attachment.download_url) {
        // Open download URL in new tab
        window.open(data.attachment.download_url, '_blank');
      }

    } catch (error) {
      console.error('Download error:', error);
      toast({
        title: "Klaida",
        description: "Nepavyko atsisiųsti failo",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (attachment: Attachment) => {
    if (!confirm(`Ar tikrai norite ištrinti failą "${attachment.file_name}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/announcements/attachments/${attachment.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Nepavyko ištrinti failo');
      }

      onAttachmentsChange(attachments.filter(a => a.id !== attachment.id));
      toast({
        title: "Sėkmė",
        description: "Failas sėkmingai ištrintas",
      });

    } catch (error) {
      console.error('Delete error:', error);
      toast({
        title: "Klaida",
        description: "Nepavyko ištrinti failo",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Priedai</h3>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => fileInputRef.current?.click()}
          disabled={disabled || uploading || !announcementId}
        >
          <Upload className="h-4 w-4 mr-2" />
          Pridėti failą
        </Button>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        onChange={handleFileSelect}
        accept={ALLOWED_TYPES.join(',')}
        disabled={disabled || uploading}
      />

      {uploading && (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Įkeliamas failas...</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      {attachments.length > 0 && (
        <div className="space-y-2">
          {attachments.map((attachment) => (
            <Card key={attachment.id}>
              <CardContent className="p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getFileIcon(attachment.file_type, attachment.is_image)}
                    <div>
                      <p className="text-sm font-medium">{attachment.file_name}</p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(attachment.file_size)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDownload(attachment)}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    {!disabled && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(attachment)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {!announcementId && (
        <p className="text-xs text-gray-500">
          Išsaugokite pranešimą kaip juodraštį, kad galėtumėte pridėti priedus
        </p>
      )}
    </div>
  );
}
