"use client";

import * as React from "react";

interface CountdownAlertProps {
  seconds: number;
  title: string;
  description: string;
  onTimeout: () => void;
  onCancel: () => void;
}

export function CountdownAlert({ seconds, title, description, onTimeout, onCancel }: CountdownAlertProps) {
  return (
    <div className="p-4 border rounded shadow">
      <h3 className="font-bold text-lg mb-2">{title}</h3>
      <p className="mb-2">{description}</p>
      <p className="mb-4">Time left: {seconds} seconds</p>
      <div className="flex space-x-2">
        <button onClick={onTimeout} className="px-3 py-1 bg-red-500 text-white rounded">Timeout</button>
        <button onClick={onCancel} className="px-3 py-1 bg-gray-500 text-white rounded">Cancel</button>
      </div>
    </div>
  );
} 