import { PhoneCall, Thermometer, Droplet, ArrowUpCircle, Phone, Shield } from 'lucide-react';
import styles from '@/app/avariniai-kontaktai/page-styles.module.css';
import { AssociationContacts } from './association-contacts';

export function EmergencyContacts() {
  return (
    <div className={styles.pageContainer}>
      {/* Header */}
      <div className="mb-6 text-center">
        <h1 className={styles.pageTitle}>Avariniai kontaktai</h1>
        <p className={styles.pageSubtitle}>Svarbūs kontaktai avarijos atveju</p>
        <div className="w-20 h-1 bg-red-500 mx-auto mt-4 rounded-full"></div>
      </div>
      
      <div className={styles.mainLayout}>
        {/* Association Contacts Section (Moved to top) */}
        <AssociationContacts />
        
        {/* Emergency Services Grid */}
        <div className={styles.contactsGrid}>
          {/* Emergency Number - Important so placed first */}
          <div className={`${styles.contactCard} ${styles.service}`}>
            <div className="flex items-center mb-4">
              <div className={styles.contactIcon}>
                <Shield className="h-6 w-6 text-red-600" />
              </div>
              <h2 className={styles.contactName}>Bendrasis pagalbos telefonas 24/7</h2>
            </div>
            <div className={styles.emergencyNumber}>
              <a href="tel:112" className="flex items-center justify-center w-full">
                <PhoneCall className="h-6 w-6 mr-2" />
                112
              </a>
            </div>
            <p className={styles.contactDescription}>(policija, greitoji medicinos pagalba, gaisrinė)</p>
          </div>
          
          {/* Heating and Water */}
          <div className={`${styles.contactCard} ${styles.water}`}>
            <div className="flex items-center mb-4">
              <div className={styles.contactIcon}>
                <Thermometer className="h-6 w-6 text-blue-600" />
              </div>
              <h2 className={styles.contactName}>Šildymo, vandens ir nuotekų priežiūra</h2>
            </div>
            <div className={styles.contactRow}>
              <PhoneCall className="h-5 w-5 text-blue-700" />
              <span className={styles.contactLabel}>UAB "SOBO" sistemos:</span>
              <a href="tel:846342508" className={styles.contactPhone}>846 342508</a>
            </div>
          </div>
          
          {/* Emergency Services */}
          <div className={`${styles.contactCard} ${styles.service}`}>
            <div className="flex items-center mb-4">
              <div className={styles.contactIcon}>
                <Droplet className="h-6 w-6 text-indigo-600" />
              </div>
              <h2 className={styles.contactName}>Avarinės tarnybos paslaugos 24/7</h2>
            </div>
            <div className={styles.contactRow}>
              <PhoneCall className="h-5 w-5 text-indigo-700" />
              <span className={styles.contactLabel}>UAB "Skaidrola":</span>
              <a href="tel:846366577" className={styles.contactPhone}>846 366577</a>
            </div>
            <p className={styles.contactDescription}>(kanalizacija, vandentiekis, elektra, šildymas)</p>
          </div>
          
          {/* Elevator Service */}
          <div className={`${styles.contactCard} ${styles.lift}`}>
            <div className="flex items-center mb-4">
              <div className={styles.contactIcon}>
                <ArrowUpCircle className="h-6 w-6 text-emerald-600" />
              </div>
              <h2 className={styles.contactName}>Liftų avarinė tarnyba 24/7</h2>
            </div>
            <div className={styles.contactRow}>
              <PhoneCall className="h-5 w-5 text-emerald-700" />
              <span className={styles.contactLabel}>UAB "Schindler-Liftas":</span>
              <a href="tel:+37062072137" className={styles.contactPhone}>+370 62072137</a>
            </div>
          </div>
          
          {/* Telephone System */}
          <div className={styles.contactCard}>
            <div className="flex items-center mb-4">
              <div className={styles.contactIcon}>
                <Phone className="h-6 w-6 text-amber-600" />
              </div>
              <h2 className={styles.contactName}>Telefonspynių priežiūra</h2>
            </div>
            <div className={styles.contactRow}>
              <PhoneCall className="h-5 w-5 text-amber-700" />
              <span className={styles.contactLabel}>UAB "Digitalas":</span>
              <a href="tel:846493426" className={styles.contactPhone}>846 493426</a>
            </div>
            <p className={styles.contactDescription}>(magnetinių įėjimo raktų gamyba)</p>
          </div>
        </div>
      </div>
      
      {/* Footer Hint */}
      <div className={styles.footer}>
        <p>Išsisaugokite šiuos kontaktus savo telefone emergencijos atveju.</p>
      </div>
    </div>
  );
} 