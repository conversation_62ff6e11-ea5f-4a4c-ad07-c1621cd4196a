'use client';

import Link from "next/link";

export default function AuthError({ 
  error, 
  errorDigest 
}: { 
  error: Error | any; 
  errorDigest?: string;
}) {
  return (
    <div className="flex min-h-screen flex-col bg-slate-50 p-4">
      <div className="max-w-md mx-auto mt-20 p-6 bg-white rounded-lg shadow-lg">
        <h1 className="text-xl font-bold text-red-600 mb-4">Authentication Error</h1>
        <p className="mb-4">There was an error with authentication:</p>
        
        {errorDigest && errorDigest.includes('NEXT_REDIRECT') ? (
          <div className="bg-amber-100 p-3 rounded-md mb-4 text-amber-800">
            <p className="font-medium">Redirect Detected</p>
            <p className="text-sm mt-1">This error indicates a redirection is being attempted.</p>
            <p className="text-sm mt-1">Digest: {errorDigest}</p>
          </div>
        ) : null}
        
        <div className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-60">
          <p className="font-bold mb-2">Error Message:</p>
          <pre>{error instanceof Error ? error.message : JSON.stringify(error, null, 2)}</pre>
          
          {error instanceof Error && error.stack ? (
            <>
              <p className="font-bold mt-4 mb-2">Stack Trace:</p>
              <pre className="text-xs">{error.stack}</pre>
            </>
          ) : null}
        </div>
        
        <div className="mt-6 flex gap-4">
          <Link href="/auth/login" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            Go to Login
          </Link>
          <button 
            onClick={() => window.location.reload()} 
            className="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300"
          >
            Retry
          </button>
        </div>
      </div>
    </div>
  );
}