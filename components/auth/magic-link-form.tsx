"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Loader2, Mail, ArrowLeft, Info } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

const formSchema = z.object({
  email: z.string().email({
    message: "Įveskite galiojantį el. pašto adresą",
  }),
});

type MagicLinkFormValues = z.infer<typeof formSchema>;

interface MagicLinkFormProps {
  onBack?: () => void;
}

export function MagicLinkForm({ onBack }: MagicLinkFormProps) {
  const [isLoading, setIsLoading] = React.useState(false);
  const [isSuccess, setIsSuccess] = React.useState(false);
  const [error, setError] = React.useState("");

  const form = useForm<MagicLinkFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
    },
  });

  async function onSubmit(data: MagicLinkFormValues) {
    setIsLoading(true);
    setError("");
    setIsSuccess(false);

    try {
      const response = await fetch("/api/auth/magic-link", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        setIsSuccess(true);
        toast.success(result.message || "Prisijungimo nuoroda išsiųsta!");
        form.reset();
      } else {
        setError(result.error || "Nepavyko išsiųsti prisijungimo nuorodos");
        if (result.hint) {
          setError(error + ". " + result.hint);
        }
      }
    } catch (error) {
      console.error("Magic link error:", error);
      setError("Įvyko klaida. Bandykite vėliau.");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="space-y-6">
      {onBack && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={onBack}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Grįžti
        </Button>
      )}

      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">
          Prisijungimas el. paštu
        </h1>
        <p className="text-sm text-muted-foreground">
          Įveskite savo el. paštą ir mes atsiųsime prisijungimo nuorodą
        </p>
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Svarbu:</strong> Prisijungimas el. paštu veikia tik jei:
          <ul className="mt-2 ml-4 list-disc text-sm">
            <li>Esate užpildę savo profilį</li>
            <li>Nurodėte el. pašto adresą</li>
            <li>Sutikote su GDPR</li>
          </ul>
          <p className="mt-2 text-sm">
            Jei dar neaktyvavote šios funkcijos, prisijunkite su vartotojo vardu (pvz., 44-18).
          </p>
        </AlertDescription>
      </Alert>

      {isSuccess ? (
        <Alert className="bg-green-50 border-green-200">
          <Mail className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <strong>Nuoroda išsiųsta!</strong>
            <p className="mt-1">
              Patikrinkite savo el. paštą ir paspauskite prisijungimo nuorodą.
              Nuoroda galioja 15 minučių.
            </p>
          </AlertDescription>
        </Alert>
      ) : (
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">El. paštas</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              disabled={isLoading}
              {...form.register("email")}
            />
            {form.formState.errors.email && (
              <p className="text-sm text-red-500">
                {form.formState.errors.email.message}
              </p>
            )}
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button
            type="submit"
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Siunčiama...
              </>
            ) : (
              <>
                <Mail className="mr-2 h-4 w-4" />
                Siųsti prisijungimo nuorodą
              </>
            )}
          </Button>
        </form>
      )}
    </div>
  );
}