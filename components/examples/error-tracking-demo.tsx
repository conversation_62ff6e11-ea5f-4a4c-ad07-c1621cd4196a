'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { trackError } from '@/lib/errorTracking';

/**
 * A demo component that can be used to test different kinds of error tracking
 * 
 * This component contains buttons that trigger various types of errors
 * to demonstrate how the error tracking system works.
 */
export function ErrorTrackingDemo() {
  const [showDemo, setShowDemo] = useState(false);

  const triggerJsError = () => {
    // This will cause a TypeError
    const nullObject = null;
    nullObject.someProperty = true;
  };

  const triggerPromiseRejection = () => {
    // This creates an unhandled promise rejection
    new Promise((_, reject) => {
      reject(new Error('Demo Promise Rejection'));
    }).then(() => {
      console.log('This will not execute');
    });
  };

  const triggerApiError = () => {
    // This will cause a 404 API error
    fetch('/api/non-existent-endpoint')
      .then(response => response.json())
      .then(data => console.log(data));
  };

  const triggerNetworkError = () => {
    // This will cause a network error
    fetch('https://this-domain-definitely-does-not-exist-123456789.com/api')
      .then(response => response.json())
      .then(data => console.log(data));
  };

  const triggerManualError = () => {
    // This demonstrates manual error tracking
    trackError('manual_demo', {
      demoId: 'button_click',
      timestamp: new Date().toISOString(),
      user_action: 'clicked_test_button'
    });
    
    alert('Manually tracked error sent to PostHog!');
  };

  const triggerCaughtError = () => {
    try {
      // Cause an error but catch it
      const arr = [];
      console.log(arr[999].toString());
    } catch (error) {
      // Use trackError to log the caught error
      trackError('caught_error_demo', {
        error_message: error instanceof Error ? error.message : String(error),
        error_stack: error instanceof Error ? error.stack : undefined,
        source: 'demo_component'
      });
      
      alert('Caught error tracked in PostHog!');
    }
  };

  if (!showDemo) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-medium text-yellow-800 mb-2">Error Tracking Demo</h3>
        <p className="text-yellow-700 mb-4">
          This component contains buttons that will trigger various types of errors to demonstrate 
          the error tracking system. These errors are intentional and will be tracked in PostHog.
        </p>
        <Button 
          onClick={() => setShowDemo(true)}
          variant="outline"
          className="border-yellow-300 text-yellow-800 hover:bg-yellow-100"
        >
          Show Error Demo
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
      <h3 className="text-lg font-medium text-yellow-800 mb-2">Error Tracking Demo</h3>
      <p className="text-yellow-700 mb-4">
        Click the buttons below to trigger different types of errors and see how they are tracked in PostHog.
      </p>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
        <Button 
          onClick={triggerJsError}
          variant="outline"
          className="border-red-300 text-red-700 hover:bg-red-100"
        >
          Trigger JS Error
        </Button>
        
        <Button 
          onClick={triggerPromiseRejection}
          variant="outline"
          className="border-orange-300 text-orange-700 hover:bg-orange-100"
        >
          Trigger Promise Rejection
        </Button>
        
        <Button 
          onClick={triggerApiError}
          variant="outline"
          className="border-blue-300 text-blue-700 hover:bg-blue-100"
        >
          Trigger API Error
        </Button>
        
        <Button 
          onClick={triggerNetworkError}
          variant="outline"
          className="border-purple-300 text-purple-700 hover:bg-purple-100"
        >
          Trigger Network Error
        </Button>
        
        <Button 
          onClick={triggerManualError}
          variant="outline"
          className="border-green-300 text-green-700 hover:bg-green-100"
        >
          Trigger Manual Error
        </Button>
        
        <Button 
          onClick={triggerCaughtError}
          variant="outline"
          className="border-indigo-300 text-indigo-700 hover:bg-indigo-100"
        >
          Trigger Caught Error
        </Button>
      </div>
      
      <Button 
        onClick={() => setShowDemo(false)}
        variant="ghost"
        className="mt-4 text-yellow-700 hover:bg-yellow-100"
      >
        Hide Demo
      </Button>
      
      <div className="mt-4 text-sm text-yellow-600">
        Note: Some errors will cause console errors. This is expected and demonstrates that the error tracking is working.
      </div>
    </div>
  );
}