'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useAnalytics } from '@/lib/hooks/useAnalytics';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';

/**
 * A demonstration component that shows how to track various user flows
 * using the analytics hooks in the application.
 */
export function UserFlowTrackingDemo() {
  const [userId, setUserId] = useState('user-' + Math.floor(Math.random() * 1000));
  const [userIdentified, setUserIdentified] = useState(false);
  const [formStep, setFormStep] = useState(0);
  const [formData, setFormData] = useState({
    topic: '',
    comment: '',
    priority: false,
    notification: false
  });
  const [pageViews, setPageViews] = useState(0);
  
  const { 
    trackEvent, 
    trackImportantEvent, 
    trackPageView,
    trackInteraction,
    trackImportantInteraction,
    identifyUser,
    resetIdentity
  } = useAnalytics();
  
  // Track page view on component mount
  useEffect(() => {
    trackPageView('/examples/user-flow-tracking', {
      demo_page: true,
      demo_id: 'user-flow-tracking'
    });
    
    setPageViews(prev => prev + 1);
    
    // Record start of session
    trackEvent('demo_session_start', {
      timestamp: new Date().toISOString(),
      session_id: 'session-' + Math.random().toString(36).substring(2, 9)
    });
    
    // Cleanup/end session on unmount
    return () => {
      trackEvent('demo_session_end', {
        duration_seconds: Math.floor((Date.now() - performance.now()) / 1000),
        page_views: pageViews
      });
    };
  }, []);
  
  // Handle user identification
  const handleIdentifyUser = () => {
    identifyUser(userId, {
      demo_user: true,
      role: 'visitor',
      first_visit: new Date().toISOString()
    });
    
    trackEvent('demo_user_identified', {
      user_id: userId
    });
    
    setUserIdentified(true);
  };
  
  // Reset user identity
  const handleResetIdentity = () => {
    resetIdentity();
    setUserIdentified(false);
    
    trackEvent('demo_identity_reset', {
      previous_user_id: userId
    });
    
    setUserId('user-' + Math.floor(Math.random() * 1000));
  };
  
  // Simulate a content view like an announcement
  const handleViewContent = () => {
    // Track a content view (like announcement)
    trackImportantEvent('content_view', 'content-123', {
      content_type: 'announcement',
      content_title: 'Sample Announcement',
      source: 'demo_page'
    });
    
    // Also track specialized interaction
    trackInteraction('announcement', 'view', 'announcement-123', {
      title: 'Sample Announcement',
      category: 'General',
      is_urgent: false
    });
  };
  
  // Track a simulated vote
  const handleVote = () => {
    trackImportantInteraction('poll', 'vote', 'poll-456', {
      option_id: 'option-2',
      poll_title: 'Community Garden',
      question: 'Would you like a community garden?',
      timestamp: new Date().toISOString()
    });
  };
  
  // Multi-step form tracking
  const handleNextStep = () => {
    // Track completion of current step
    trackEvent('form_step_complete', {
      step: formStep,
      form_type: 'feedback',
      fields_completed: Object.values(formData).filter(Boolean).length
    });
    
    setFormStep(prev => prev + 1);
  };
  
  const handlePrevStep = () => {
    trackEvent('form_step_back', {
      from_step: formStep,
      to_step: formStep - 1
    });
    
    setFormStep(prev => Math.max(0, prev - 1));
  };
  
  const handleFormSubmit = (e) => {
    e.preventDefault();
    
    // Track form submission 
    trackImportantEvent('form_submit', 'feedback-form', {
      form_data: {
        has_topic: !!formData.topic,
        has_comment: !!formData.comment,
        priority_selected: formData.priority,
        notification_requested: formData.notification
      },
      total_steps: formStep + 1
    });
    
    // Reset form
    setFormData({
      topic: '',
      comment: '',
      priority: false,
      notification: false
    });
    setFormStep(0);
    
    alert('Form submitted! Check PostHog for tracking events.');
  };
  
  // Render form steps
  const renderFormStep = () => {
    switch (formStep) {
      case 0:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="topic">Topic</Label>
              <Input 
                id="topic" 
                value={formData.topic}
                onChange={(e) => {
                  setFormData({...formData, topic: e.target.value});
                  // Track field changes
                  trackEvent('form_field_change', {
                    field: 'topic',
                    has_value: !!e.target.value,
                    step: formStep
                  });
                }}
                placeholder="What's this feedback about?"
              />
            </div>
            <Button onClick={handleNextStep}>Next Step</Button>
          </div>
        );
      case 1:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="comment">Your Feedback</Label>
              <Textarea 
                id="comment" 
                value={formData.comment}
                onChange={(e) => {
                  setFormData({...formData, comment: e.target.value});
                  trackEvent('form_field_change', {
                    field: 'comment',
                    length: e.target.value.length,
                    step: formStep
                  });
                }}
                placeholder="Please provide your detailed feedback..."
              />
            </div>
            <div className="flex justify-between">
              <Button variant="outline" onClick={handlePrevStep}>Back</Button>
              <Button onClick={handleNextStep}>Next Step</Button>
            </div>
          </div>
        );
      case 2:
        return (
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="priority" 
                checked={formData.priority}
                onCheckedChange={(checked) => {
                  setFormData({...formData, priority: !!checked});
                  trackEvent('form_option_toggle', {
                    field: 'priority',
                    value: !!checked,
                    step: formStep
                  });
                }}
              />
              <Label htmlFor="priority">This feedback is high priority</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="notification" 
                checked={formData.notification}
                onCheckedChange={(checked) => {
                  setFormData({...formData, notification: !!checked});
                  trackEvent('form_option_toggle', {
                    field: 'notification',
                    value: !!checked,
                    step: formStep
                  });
                }}
              />
              <Label htmlFor="notification">Notify me about updates</Label>
            </div>
            <div className="flex justify-between">
              <Button variant="outline" onClick={handlePrevStep}>Back</Button>
              <Button onClick={handleFormSubmit}>Submit Feedback</Button>
            </div>
          </div>
        );
      default:
        return null;
    }
  };
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>User Identity Tracking</CardTitle>
          <CardDescription>
            Track and identify users across sessions using PostHog's identify feature
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="userId">User ID</Label>
            <Input 
              id="userId" 
              value={userId} 
              onChange={(e) => setUserId(e.target.value)}
              disabled={userIdentified}
            />
          </div>
          
          {!userIdentified ? (
            <Button onClick={handleIdentifyUser}>
              Identify User
            </Button>
          ) : (
            <div className="space-y-2">
              <div className="text-sm text-green-600 font-medium">
                User identified! This user's events will be tracked under ID: {userId}
              </div>
              <Button variant="outline" onClick={handleResetIdentity}>
                Reset Identity
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Content Interaction Tracking</CardTitle>
          <CardDescription>
            Track user interactions with content like announcements and polls
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Button 
              onClick={handleViewContent}
              variant="outline"
              className="h-auto py-4 flex flex-col items-center justify-center"
            >
              <span className="text-lg mb-2">View Announcement</span>
              <span className="text-xs text-gray-500">Tracks content view events</span>
            </Button>
            
            <Button 
              onClick={handleVote}
              variant="outline"
              className="h-auto py-4 flex flex-col items-center justify-center"
            >
              <span className="text-lg mb-2">Vote on Poll</span>
              <span className="text-xs text-gray-500">Tracks poll interaction events</span>
            </Button>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Multi-step Form Tracking</CardTitle>
          <CardDescription>
            Track user progress through a multi-step feedback form
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleFormSubmit} className="space-y-4">
            <div className="flex justify-between text-sm font-medium text-gray-500 mb-4">
              {['Topic', 'Feedback', 'Options'].map((step, i) => (
                <div 
                  key={i} 
                  className={`flex items-center ${i === formStep ? 'text-blue-600' : ''}`}
                >
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${i === formStep ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
                    {i + 1}
                  </div>
                  {step}
                </div>
              ))}
            </div>
            
            {renderFormStep()}
          </form>
        </CardContent>
        <CardFooter className="text-xs text-gray-500">
          This demo shows how to track progress through a multi-step process
        </CardFooter>
      </Card>
    </div>
  );
}