"use client"

import React from "react"
import { useState } from "react"
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
  RowSelectionState,
  getFilteredRowModel,
  SortingState,
  getSortedRowModel,
} from "@tanstack/react-table"
import { format, formatDistanceToNow } from "date-fns"
import { lt } from "date-fns/locale"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "./alert-dialog"
import { Checkbox } from "./checkbox"
import { Loader2, Trash, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsR<PERSON>, SendIcon, XIcon } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import EmailStatusBadge from "@/components/emails/email-status-badge"
import EmailQueueActions from "@/components/emails/email-queue-actions"
import SourceLinkCell from '@/components/emails/source-link-cell'

// Custom column type that allows string cell types
type CustomColumnDef<TData, TValue> = ColumnDef<TData, TValue> & {
  accessorKey?: string;
  cell?: string | ((props: any) => React.ReactNode);
}

// Type for bulk actions
interface BulkAction<TData> {
  label: string;
  icon: string;
  action: (selectedRows: TData[]) => Promise<boolean>;
  showIf?: (selectedRows: TData[]) => boolean;
}

interface DataTableProps<TData, TValue> {
  columns: CustomColumnDef<TData, TValue>[];
  data: TData[];
  pagination?: {
    pageIndex: number;
    pageSize: number;
  };
  pageCount: number;
  searchColumn?: string;
  searchPlaceholder?: string;
  onDelete?: (selectedRows: TData[]) => Promise<boolean>;
  bulkActions?: BulkAction<TData>[];
  getRowId?: (originalRow: TData, index: number, parent?: any) => string;
}

// Define formatters directly here
const ScheduledForFormat = ({ row }: { row: { original: { scheduled_for?: string | null } } }) => {
  const date = row.original.scheduled_for;
  if (!date) return <span className="text-muted-foreground">-</span>;
  return (
    <span title={format(new Date(date), 'yyyy-MM-dd HH:mm:ss')}>
      {formatDistanceToNow(new Date(date), { addSuffix: true, locale: lt })}
    </span>
  );
};

const CreatedAtFormat = ({ row }: { row: { original: { created_at: string } } }) => {
  const date = row.original.created_at;
  return (
    <span title={format(new Date(date), 'yyyy-MM-dd HH:mm:ss')}>
      {formatDistanceToNow(new Date(date), { addSuffix: true, locale: lt })}
    </span>
  );
};

export function DataTable<TData, TValue>({
  columns,
  data,
  pagination,
  pageCount,
  searchColumn = "",
  searchPlaceholder = "Ieškoti...",
  onDelete,
  bulkActions = [],
  getRowId,
}: DataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = React.useState({})
  const [globalFilter, setGlobalFilter] = React.useState('')
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false)
  const [isDeleting, setIsDeleting] = React.useState(false)
  const [isBulkActionInProgress, setIsBulkActionInProgress] = React.useState(false)
  const [paginationState, setPaginationState] = React.useState({
    pageIndex: pagination?.pageIndex || 0,
    pageSize: pagination?.pageSize || 10,
  })
  const [sorting, setSorting] = React.useState<SortingState>([])

  const cellComponents: Record<string, React.ComponentType<any>> = {
    "status-badge": EmailStatusBadge,
    "email-actions": EmailQueueActions,
    "scheduled-for-format": ScheduledForFormat,
    "created-at-format": CreatedAtFormat,
    "source-link": SourceLinkCell,
  };

  // Process columns to map string cell types to components
  const processedColumns = columns.map((col) => {
    if (typeof col.cell === 'string') {
      const CellComponent = cellComponents[col.cell];
      if (CellComponent) {
        // Pass the entire row data to the cell component
        // EXCEPT for email-actions, which expects the data under the 'email' prop
        if (col.cell === 'email-actions') {
          return {
            ...col,
            cell: ({ row }: { row: any }) => <CellComponent email={row.original} />,
          };
        } else {
          return {
            ...col,
            cell: ({ row }: { row: any }) => <CellComponent row={row} />,
          };
        }
      } else {
        console.warn(`Cell component not found for key: ${col.cell}. Rendering default value.`);
        const accessorKey = typeof col.accessorKey === 'string' ? col.accessorKey : undefined;
        return {
          ...col,
          cell: ({ row }: { row: any }) => accessorKey ? flexRender(row.getValue(accessorKey), {}) : null,
        };
      }
    } else if (col.cell) {
      return col;
    } else {
      const accessorKey = typeof col.accessorKey === 'string' ? col.accessorKey : undefined;
      return {
        ...col,
        cell: ({ row }: { row: any }) => accessorKey ? flexRender(row.getValue(accessorKey), {}) : null,
      };
    }
  });

  // Add selection column if onDelete or bulkActions are provided
  const shouldEnableRowSelection = onDelete || bulkActions.length > 0;
  
  const columnsWithSelection = shouldEnableRowSelection
    ? [
        {
          id: "select",
          header: ({ table }) => (
            <Checkbox
              checked={
                table.getIsAllPageRowsSelected() ||
                (table.getIsSomePageRowsSelected() && "indeterminate")
              }
              onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
              aria-label="Select all"
            />
          ),
          cell: ({ row }) => (
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={(value) => row.toggleSelected(!!value)}
              aria-label="Select row"
            />
          ),
          enableSorting: false,
          enableHiding: false,
        },
        ...processedColumns,
      ]
    : processedColumns;

  const table = useReactTable({
    data,
    columns: columnsWithSelection as ColumnDef<TData, any>[],
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onRowSelectionChange: setRowSelection,
    getRowId,
    manualPagination: pageCount !== undefined,
    pageCount: pageCount,
    state: {
      rowSelection,
      globalFilter,
      pagination: paginationState,
      sorting,
    },
    onGlobalFilterChange: setGlobalFilter,
    onPaginationChange: setPaginationState,
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
  })

  const handleDelete = async () => {
    if (!onDelete) return;

    try {
      setIsDeleting(true);
      // Get the selected rows data
      const selectedRows = table.getSelectedRowModel().rows.map(row => row.original);
      await onDelete(selectedRows);
      
      // Clear selection after deletion
      setRowSelection({});
    } catch (error) {
      console.error("Failed to delete selected items:", error);
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  // Get the selected rows
  const selectedRows = table.getSelectedRowModel().rows.map(row => row.original);
  
  // Filter bulk actions based on the showIf condition
  const visibleBulkActions = bulkActions.filter(action => 
    !action.showIf || action.showIf(selectedRows)
  );

  // Get icon component for bulk actions
  const getActionIcon = (iconName: string) => {
    switch (iconName) {
      case 'cancel':
        return <XIcon className="mr-2 h-4 w-4" />;
      case 'resend':
        return <SendIcon className="mr-2 h-4 w-4" />;
      case 'delete':
        return <Trash className="mr-2 h-4 w-4" />;
      default:
        return null;
    }
  };

  const hasSelectableRows = shouldEnableRowSelection;
  const hasSelectedRows = table.getSelectedRowModel().rows.length > 0;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between gap-4">
        {searchColumn && (
          <div className="flex-1 max-w-sm">
            <Input
              placeholder={searchPlaceholder}
              value={globalFilter ?? ""}
              onChange={(e) => setGlobalFilter(e.target.value)}
              className="max-w-sm"
            />
          </div>
        )}
        
        <div className="flex items-center gap-2 ml-auto">
          {hasSelectedRows && visibleBulkActions.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="secondary" 
                  size="sm" 
                  className="h-9"
                  disabled={isBulkActionInProgress}
                >
                  {isBulkActionInProgress ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Vykdoma...
                    </>
                  ) : (
                    <>
                      Veiksmai ({table.getSelectedRowModel().rows.length})
                    </>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[200px]">
                <DropdownMenuLabel className="text-xs text-muted-foreground">
                  Pažymėtų įrašų veiksmai
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                {visibleBulkActions.map((action, index) => (
                  <DropdownMenuItem 
                    key={index}
                    onClick={() => action.action(selectedRows)}
                    disabled={isBulkActionInProgress}
                  >
                    {getActionIcon(action.icon)}
                    <span>{action.label}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          
          {hasSelectedRows && onDelete && (
            <Button 
              variant="destructive" 
              onClick={() => setIsDeleteDialogOpen(true)}
              size="sm"
              className="h-9"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Trinama...
                </>
              ) : (
                <>
                  <Trash className="mr-2 h-4 w-4" />
                  Ištrinti ({table.getSelectedRowModel().rows.length})
                </>
              )}
            </Button>
          )}
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader className="bg-muted/50">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="hover:bg-muted/50">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead 
                      key={header.id} 
                      className="font-medium text-xs uppercase text-muted-foreground py-3 px-4"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className="hover:bg-muted/50"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell 
                      key={cell.id} 
                      className="py-3 px-4"
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell 
                  colSpan={columns.length + (hasSelectableRows ? 1 : 0)} 
                  className="h-24 text-center text-muted-foreground"
                >
                  Duomenų nerasta.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      <div className="flex flex-col sm:flex-row items-center justify-between space-y-3 sm:space-y-0 space-x-0 sm:space-x-2 py-2">
        <div className="flex items-center space-x-2">
          <p className="text-xs text-muted-foreground">
            Įrašai per puslapį:
          </p>
          <Select
            value={`${table.getState().pagination.pageSize}`}
            onValueChange={(value) => {
              table.setPageSize(Number(value))
            }}
          >
            <SelectTrigger className="h-9 w-[70px] text-xs">
              <SelectValue placeholder={table.getState().pagination.pageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {[5, 10, 20, 50, 100].map((pageSize) => (
                <SelectItem key={pageSize} value={`${pageSize}`} className="text-xs">
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="flex-1 text-xs text-muted-foreground">
            {hasSelectedRows && (
              <span>
                Pažymėta {table.getFilteredSelectedRowModel().rows.length} iš{" "}
                {table.getFilteredRowModel().rows.length} įrašų.
              </span>
            )}
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row items-center space-x-0 sm:space-x-6 space-y-2 sm:space-y-0">
          <div className="text-xs text-muted-foreground">
            Puslapis {table.getState().pagination.pageIndex + 1} iš{" "}
            {pageCount || table.getPageCount() || 1}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline" 
              className="h-9 w-9 p-0 text-xs"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Pirmas puslapis</span>
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline" 
              className="h-9 w-9 p-0 text-xs"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Ankstesnis puslapis</span>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-9 w-9 p-0 text-xs"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Kitas puslapis</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-9 w-9 p-0 text-xs"
              onClick={() => table.setPageIndex(pageCount ? pageCount - 1 : table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Paskutinis puslapis</span>
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Patvirtinkite ištrynimą</AlertDialogTitle>
            <AlertDialogDescription>
              Ar tikrai norite ištrinti {table.getSelectedRowModel().rows.length} pažymėtus įrašus? 
              Šio veiksmo negalėsite atšaukti.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Atšaukti</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} disabled={isDeleting}>
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Trinama...
                </>
              ) : (
                "Ištrinti"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
} 