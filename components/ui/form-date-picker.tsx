"use client"

import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import { lt } from "date-fns/locale"
import { useController, Control } from "react-hook-form"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { FormControl, FormItem, FormLabel, FormMessage } from "@/components/ui/form"

interface FormDatePickerProps {
  name: string
  control: Control<any>
  label?: string
  placeholder?: string
  disabled?: boolean | ((date: Date) => boolean)
  className?: string
}

export function FormDatePicker({
  name,
  control,
  label,
  placeholder = "Pasirinkite datą",
  disabled = false,
  className,
}: FormDatePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const { field } = useController({
    name,
    control,
  })

  // Make sure we're working with a proper Date object or undefined
  const dateValue = React.useMemo(() => {
    if (!field.value) return undefined
    return field.value instanceof Date ? field.value : new Date(field.value)
  }, [field.value])

  // Handle date selection
  const handleSelect = (date: Date | undefined) => {
    field.onChange(date)
    if (date) {
      setIsOpen(false)
      // Force re-render to ensure the popover closes and UI updates
      setTimeout(() => {
        field.onChange(date)
      }, 50)
    }
  }

  return (
    <FormItem className={className}>
      {label && <FormLabel>{label}</FormLabel>}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              className={cn(
                "w-full flex justify-start text-left font-normal h-10",
                !dateValue && "text-muted-foreground"
              )}
              disabled={typeof disabled === "boolean" ? disabled : false}
              type="button" // Important to prevent form submission
              onClick={() => setIsOpen(true)}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {dateValue ? (
                format(dateValue, "yyyy-MM-dd", { locale: lt })
              ) : (
                <span>{placeholder}</span>
              )}
            </Button>
          </FormControl>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 z-[100]" align="start">
          <div className="bg-white border rounded-md shadow-lg p-2">
            <Calendar
              mode="single"
              selected={dateValue}
              onSelect={handleSelect}
              initialFocus
              locale={lt}
              disabled={disabled}
            />
          </div>
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}

// For date and time selection with React Hook Form
interface FormDateTimePickerProps {
  dateName: string
  timeName: string
  control: Control<any>
  dateLabel?: string
  timeLabel?: string
  datePlaceholder?: string
  disabled?: boolean
  disabledDates?: (date: Date) => boolean
  className?: string
}

export function FormDateTimePicker({
  dateName,
  timeName,
  control,
  dateLabel = "Data",
  timeLabel = "Laikas",
  datePlaceholder = "Pasirinkite datą",
  disabled = false,
  disabledDates,
  className,
}: FormDateTimePickerProps) {
  const [isCalendarOpen, setIsCalendarOpen] = React.useState(false)
  
  // Get the date field controller
  const dateField = useController({
    name: dateName,
    control,
  })
  
  // Get the time field controller
  const timeField = useController({
    name: timeName,
    control,
  })

  // Make sure we're working with a proper Date object or undefined
  const dateValue = React.useMemo(() => {
    if (!dateField.field.value) return undefined
    return dateField.field.value instanceof Date ? 
      dateField.field.value : new Date(dateField.field.value)
  }, [dateField.field.value])

  // Handle date selection
  const handleDateSelect = (date: Date | undefined) => {
    dateField.field.onChange(date)
    if (date) {
      setIsCalendarOpen(false)
      // Force re-render to ensure the popover closes and UI updates
      setTimeout(() => {
        dateField.field.onChange(date)
      }, 50)
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      <FormItem>
        {dateLabel && <FormLabel>{dateLabel}</FormLabel>}
        <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
          <PopoverTrigger asChild>
            <FormControl>
              <Button
                variant="outline"
                className={cn(
                  "w-full flex justify-start text-left font-normal h-10",
                  !dateValue && "text-muted-foreground"
                )}
                disabled={disabled}
                type="button" // Important to prevent form submission
                onClick={() => setIsCalendarOpen(true)}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateValue ? (
                  format(dateValue, "yyyy-MM-dd", { locale: lt })
                ) : (
                  <span>{datePlaceholder}</span>
                )}
              </Button>
            </FormControl>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0 z-[100]" align="start">
            <div className="bg-white border rounded-md shadow-lg p-2">
              <Calendar
                mode="single"
                selected={dateValue}
                onSelect={handleDateSelect}
                initialFocus
                locale={lt}
                disabled={disabledDates}
              />
            </div>
          </PopoverContent>
        </Popover>
        <FormMessage />
      </FormItem>
      
      <FormItem>
        {timeLabel && <FormLabel>{timeLabel}</FormLabel>}
        <FormControl>
          <input
            type="time"
            className={cn(
              "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background",
              "file:border-0 file:bg-transparent file:text-sm file:font-medium",
              "placeholder:text-slate-400 focus-visible:outline-none",
              "focus:ring-2 focus:ring-indigo-200 focus:border-indigo-400 focus:outline-none",
              "disabled:cursor-not-allowed disabled:opacity-50",
            )}
            value={timeField.field.value || ""}
            onChange={(e) => timeField.field.onChange(e.target.value)}
            disabled={disabled}
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    </div>
  )
} 