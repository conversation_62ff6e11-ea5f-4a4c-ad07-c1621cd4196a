interface LogoImageProps {
  src?: string;
  alt: string;
  width: number;
  height: number;
  className?: string;
  priority?: boolean;
  variant?: 'auto' | 'light' | 'dark'; // auto chooses based on background
}

export function LogoImage({
  src,
  alt,
  width,
  height,
  className = "",
  priority = false,
  variant = 'auto'
}: LogoImageProps) {
  // Determine which logo to use based on variant
  let logoSrc = src;

  if (!src) {
    // Auto-select logo based on variant
    if (variant === 'auto') {
      // Check if we're on a dark background by looking at className
      const isDarkBackground = className.includes('bg-[#002855]') ||
                              className.includes('bg-gray-900') ||
                              className.includes('bg-black') ||
                              className.includes('bg-slate-900') ||
                              className.includes('bg-indigo-') ||
                              className.includes('bg-blue-');

      logoSrc = isDarkBackground
        ? '/images/logo/dnsb-vakarai-logo-white.svg'
        : '/images/logo/dnsb-vakarai-logo-dark.svg';
    } else if (variant === 'light') {
      logoSrc = '/images/logo/dnsb-vakarai-logo-white.svg';
    } else {
      logoSrc = '/images/logo/dnsb-vakarai-logo-dark.svg';
    }
  }

  // For SVG files, use regular img tag to avoid Next.js optimization issues
  // This prevents hydration mismatches and image optimization errors
  if (logoSrc?.endsWith('.svg')) {
    return (
      <img
        src={logoSrc}
        alt={alt}
        width={width}
        height={height}
        className={className}
        style={{
          display: 'block',
          height: 'auto'
        }}
      />
    );
  }

  // For non-SVG images, use regular img tag as well to avoid optimization issues
  return (
    <img
      src={logoSrc}
      alt={alt}
      width={width}
      height={height}
      className={className}
      style={{
        display: 'block',
        height: 'auto'
      }}
    />
  );
}
