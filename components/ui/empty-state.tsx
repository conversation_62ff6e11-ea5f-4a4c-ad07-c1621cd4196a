import React from "react";
import { InboxIcon } from "lucide-react";

interface EmptyStateProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  children?: React.ReactNode;
}

export function EmptyState({
  title,
  description,
  icon,
  children,
}: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center py-12 border rounded-lg bg-background">
      <div className="flex flex-col items-center justify-center text-center p-8 max-w-md">
        <div className="flex h-20 w-20 items-center justify-center rounded-full bg-muted mb-4">
          {icon || <InboxIcon className="h-10 w-10 text-muted-foreground" />}
        </div>
        <h3 className="text-xl font-semibold">{title}</h3>
        <p className="text-muted-foreground mt-2">{description}</p>
        {children && <div className="mt-6">{children}</div>}
      </div>
    </div>
  );
} 