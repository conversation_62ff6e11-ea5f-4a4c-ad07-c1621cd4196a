import React from 'react';
import { Edit, MessageSquare } from 'lucide-react';
import { AdminNote } from './admin-note';

interface AdminNotesSectionProps {
  title?: string;
  notes: string;
  icon?: React.ReactNode;
}

export function AdminNotesSection({ 
  title = "Administratoriaus pastabos", 
  notes,
  icon = <Edit className="h-5 w-5 text-blue-600" />
}: AdminNotesSectionProps) {
  // Split notes by double newlines to handle multiple notes
  const notesList = notes.split(/\n\n+/).filter(note => note.trim() !== '');
  
  return (
    <div className="bg-blue-50 border border-blue-100 rounded-lg shadow-sm">
      <div className="border-b border-blue-100 bg-blue-100/50 px-6 py-4 flex items-center gap-2">
        <div className="bg-blue-600 text-white p-1.5 rounded-full">
          {icon}
        </div>
        <h2 className="text-lg font-semibold text-blue-800">
          {title}
        </h2>
      </div>
      
      <div className="p-6 space-y-4">
        {notesList.length > 0 ? (
          notesList.map((note, index) => (
            <AdminNote key={index} content={note} />
          ))
        ) : (
          <div className="text-gray-500 italic p-4 bg-white rounded-md border border-gray-200 text-center">
            Nėra pastabų
          </div>
        )}
      </div>
    </div>
  );
} 