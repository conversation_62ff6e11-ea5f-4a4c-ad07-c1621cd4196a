import { ChevronRight, Home } from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface BreadcrumbItemProps {
  href: string;
  label: string;
  isLast?: boolean;
  icon?: React.ReactNode;
}

interface BreadcrumbProps {
  items: BreadcrumbItemProps[];
  className?: string;
}

export function Breadcrumb({ items, className }: BreadcrumbProps) {
  return (
    <nav aria-label="Breadcrumb" className={cn("mb-4", className)}>
      <ol className="flex flex-wrap items-center gap-1 text-sm">
        {/* Home item is always first */}
        <li>
          <Link 
            href="/dashboard" 
            className="inline-flex items-center text-sm font-medium text-slate-600 hover:text-indigo-700 transition-colors"
          >
            <Home className="mr-1.5 h-3.5 w-3.5 text-slate-500" />
            Pradžia
          </Link>
        </li>
        
        {/* Map through the provided items */}
        {items.map((item, index) => (
          <li key={item.href} className="inline-flex items-center">
            {/* Separator between items */}
            <ChevronRight
              className="mx-1.5 h-3.5 w-3.5 text-slate-400"
              aria-hidden="true"
            />
            
            {/* The item itself */}
            {item.isLast || index === items.length - 1 ? (
              // Last item (current page)
              <span
                className="inline-flex items-center text-sm font-semibold text-indigo-900"
                aria-current="page"
              >
                {item.icon && <span className="mr-1.5">{item.icon}</span>}
                {item.label}
              </span>
            ) : (
              // Non-last item (link)
              <Link
                href={item.href}
                className="inline-flex items-center text-sm font-medium text-slate-600 hover:text-indigo-700 transition-colors"
              >
                {item.icon && <span className="mr-1.5">{item.icon}</span>}
                {item.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
} 