import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { AlertTriangle } from "lucide-react";

export function AccessDenied() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] text-center p-6">
      <AlertTriangle className="w-16 h-16 text-red-500 mb-4" />
      <h1 className="text-2xl font-bold text-slate-800 mb-2">Prieiga uždrausta</h1>
      <p className="text-slate-600 mb-6">Neturite teisės peržiūr<PERSON>ti šio puslapio.</p>
      <Button asChild>
        <Link href="/dashboard">Grįžti į valdymo skydą</Link>
      </Button>
    </div>
  );
} 