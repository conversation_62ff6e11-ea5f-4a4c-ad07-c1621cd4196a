"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import Link from "next/link"
import { LucideIcon } from "lucide-react"
import { 
  Toolt<PERSON>, 
  TooltipContent, 
  TooltipTrigger 
} from "@/components/ui/tooltip"
import { buttonVariants } from "@/components/ui/button"

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string
  collapsed?: boolean
}

export function Sidebar({
  className,
  collapsed = false,
  children,
  ...props
}: SidebarProps) {
  return (
    <aside
      className={cn(
        "group flex flex-col gap-4 py-2 pb-14 data-[collapsed=true]:py-2 h-full relative",
        className
      )}
      data-collapsed={collapsed}
      {...props}
    >
      {children}
    </aside>
  )
}

interface SidebarHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string
  description?: string
}

export function SidebarHeader({
  className,
  title,
  description,
  children,
  ...props
}: SidebarHeaderProps) {
  return (
    <div
      className={cn(
        "flex h-auto items-center justify-between px-4 group-[[data-collapsed=true]]:justify-center",
        className
      )}
      {...props}
    >
      <div className="space-y-1 group-[[data-collapsed=true]]:hidden">
        {title && (
          <h2 className="text-md font-semibold tracking-tight text-indigo-900">
            {title}
          </h2>
        )}
        {description && (
          <p className="text-xs text-slate-500">
            {description}
          </p>
        )}
      </div>
      {children}
    </div>
  )
}

interface SidebarNavProps extends React.HTMLAttributes<HTMLDivElement> {}

export function SidebarNav({
  className,
  children,
  ...props
}: SidebarNavProps) {
  return (
    <div
      className={cn(
        "flex flex-col gap-1 px-2",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

interface SidebarNavItemProps extends React.HTMLAttributes<HTMLAnchorElement> {
  href: string
  icon: LucideIcon
  label: string
  description?: string
  active?: boolean
  tooltipSide?: "top" | "right" | "bottom" | "left"
}

export function SidebarNavItem({
  className,
  href,
  icon: Icon,
  label,
  description,
  active,
  tooltipSide = "right",
  ...props
}: SidebarNavItemProps) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Link
          href={href}
          className={cn(
            buttonVariants({ variant: "ghost", size: "default" }),
            "h-10 w-full justify-start gap-3 rounded-lg px-3 py-6 font-normal",
            active
              ? "bg-gradient-to-r from-indigo-100 to-indigo-50 text-indigo-900 hover:from-indigo-200 hover:to-indigo-100 border-r-4 border-indigo-600"
              : "text-slate-700 hover:bg-slate-100/80 hover:text-slate-900",
            "group-[[data-collapsed=true]]:justify-center group-[[data-collapsed=true]]:px-2",
            className
          )}
          {...props}
        >
          <Icon
            className={cn(
              "h-5 w-5 flex-shrink-0", 
              active ? "text-indigo-600" : "text-slate-500"
            )}
          />
          <span
            className={cn(
              "text-sm font-medium", 
              active ? "text-indigo-900" : "text-slate-700",
              "transition-opacity duration-200 group-[[data-collapsed=true]]:opacity-0 group-[[data-collapsed=true]]:w-0 group-[[data-collapsed=true]]:overflow-hidden"
            )}
          >
            {label}
          </span>
        </Link>
      </TooltipTrigger>
      <TooltipContent side={tooltipSide} sideOffset={15} align="start" className="hidden group-[[data-collapsed=true]]:block">
        <div className="flex flex-col">
          <span className="font-medium">{label}</span>
          {description && <span className="text-xs text-slate-300">{description}</span>}
        </div>
      </TooltipContent>
    </Tooltip>
  )
}

interface SidebarFooterProps extends React.HTMLAttributes<HTMLDivElement> {}

export function SidebarFooter({
  className,
  children,
  ...props
}: SidebarFooterProps) {
  return (
    <div
      className={cn(
        "absolute bottom-0 left-0 right-0 px-4 py-3 border-t border-slate-200 bg-white w-full",
        "group-[[data-collapsed=true]]:flex group-[[data-collapsed=true]]:justify-center group-[[data-collapsed=true]]:px-2",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
} 