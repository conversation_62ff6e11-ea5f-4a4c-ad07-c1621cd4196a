"use client"

import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon, Clock } from "lucide-react"
import { lt } from "date-fns/locale"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Input } from "@/components/ui/input"

interface DateTimePickerProps {
  date: Date | undefined
  setDate: (date: Date | undefined) => void
  time: string | undefined
  setTime: (time: string | undefined) => void
  className?: string
  disabled?: boolean
}

export function DateTimePicker({
  date,
  setDate,
  time,
  setTime,
  className,
  disabled = false,
}: DateTimePickerProps) {
  return (
    <div className="flex flex-col gap-4">
      {/* Date Picker */}
      <div className="space-y-2">
        <div className="flex items-center">
          <CalendarIcon className="mr-2 h-4 w-4 text-indigo-600" />
          <span className="text-sm font-medium">Data</span>
        </div>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal h-10",
                !date && "text-muted-foreground",
                className
              )}
              disabled={disabled}
            >
              {date ? (
                format(date, "yyyy-MM-dd", { locale: lt })
              ) : (
                <span>Pasirinkite datą</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0 z-50" align="start">
            <Calendar
              mode="single"
              selected={date}
              onSelect={setDate}
              initialFocus
              locale={lt}
              className="border rounded-md shadow-lg bg-white"
            />
          </PopoverContent>
        </Popover>
      </div>

      {/* Time Picker */}
      <div className="space-y-2">
        <div className="flex items-center">
          <Clock className="mr-2 h-4 w-4 text-indigo-600" />
          <span className="text-sm font-medium">Laikas</span>
        </div>
        <Input
          type="time"
          value={time || ""}
          onChange={(e) => setTime(e.target.value)}
          disabled={disabled}
          className="h-10 w-full"
        />
      </div>
    </div>
  )
}

// A simplified version that only displays a single date selection
export function SimpleDatePicker({
  date,
  setDate,
  className,
  disabled = false,
}: {
  date: Date | undefined
  setDate: (date: Date | undefined) => void
  className?: string
  disabled?: boolean
}) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal h-10",
            !date && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? (
            format(date, "yyyy-MM-dd", { locale: lt })
          ) : (
            <span>Pasirinkite datą</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0 z-50" align="start">
        <Calendar
          mode="single"
          selected={date}
          onSelect={setDate}
          initialFocus
          locale={lt}
          className="border rounded-md shadow-lg bg-white"
        />
      </PopoverContent>
    </Popover>
  )
} 