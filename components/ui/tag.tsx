import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import { HTMLAttributes } from "react";

const tagVariants = cva(
  "inline-flex items-center justify-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/80",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        success: "bg-green-100 text-green-800 border border-green-200",
        info: "bg-blue-100 text-blue-800 border border-blue-200",
        warning: "bg-yellow-100 text-yellow-800 border border-yellow-200",
        danger: "bg-red-100 text-red-800 border border-red-200",
        important: "bg-amber-100 text-amber-800 border border-amber-200",
        urgent: "bg-red-100 text-red-800 border border-red-200",
        outline: "text-foreground border border-input bg-background hover:bg-accent hover:text-accent-foreground",
      },
      size: {
        default: "h-5 text-xs",
        sm: "h-4 text-xs",
        lg: "h-6 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface TagProps
  extends HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof tagVariants> {
  text: string;
}

export function Tag({ className, variant, size, text, ...props }: TagProps) {
  return (
    <div
      className={cn(tagVariants({ variant, size }), className)}
      {...props}
    >
      {text}
    </div>
  );
} 