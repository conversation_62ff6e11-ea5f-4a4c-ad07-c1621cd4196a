import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default:
          "bg-gradient-to-br from-indigo-600 to-indigo-700 text-white hover:from-indigo-700 hover:to-indigo-800 hover:shadow-md hover:shadow-indigo-800/20 focus-visible:ring-indigo-500 shadow-sm border border-indigo-800/20",
        destructive:
          "bg-gradient-to-br from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 hover:shadow-md hover:shadow-red-800/20 focus-visible:ring-red-500 shadow-sm border border-red-800/20",
        outline:
          "border border-indigo-200 bg-transparent text-indigo-700 hover:bg-indigo-50 focus-visible:ring-indigo-500",
        secondary:
          "bg-gradient-to-br from-indigo-100 to-indigo-200 text-indigo-800 hover:from-indigo-200 hover:to-indigo-300 hover:shadow-sm hover:shadow-indigo-300/20 focus-visible:ring-indigo-400 shadow-sm border border-indigo-300/20",
        ghost: 
          "bg-transparent text-indigo-700 hover:bg-indigo-50 hover:text-indigo-800 focus-visible:ring-indigo-500",
        link: 
          "text-indigo-600 underline-offset-4 hover:underline hover:text-indigo-700 p-0 h-auto focus-visible:ring-indigo-500",
        success: 
          "bg-gradient-to-br from-emerald-600 to-emerald-700 text-white hover:from-emerald-700 hover:to-emerald-800 hover:shadow-md hover:shadow-emerald-800/20 focus-visible:ring-emerald-500 shadow-sm border border-emerald-800/20",
        warning: 
          "bg-gradient-to-br from-amber-500 to-amber-600 text-white hover:from-amber-600 hover:to-amber-700 hover:shadow-md hover:shadow-amber-700/20 focus-visible:ring-amber-500 shadow-sm border border-amber-700/20",
      },
      size: {
        default: "h-10 px-4 py-2 rounded-md text-sm",
        sm: "h-9 rounded-md px-3 text-xs",
        lg: "h-11 rounded-md px-6 text-base",
        xl: "h-12 rounded-lg px-8 text-base",
        icon: "h-10 w-10 rounded-md",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(
          buttonVariants({ variant, size, className }),
          "font-medium transition-all duration-200 active:scale-[0.98]",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }