import React from 'react';
import { formatAdminTimestamp } from '@/lib/utils';
import { Edit, Clock } from 'lucide-react';

interface AdminNoteProps {
  content: string;
}

export function AdminNote({ content }: AdminNoteProps) {
  // Check if the note starts with a timestamp pattern (YYYY-MM-DDThh:mm:ss.sssZ)
  const hasTimestamp = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/.test(content);
  
  if (!hasTimestamp) {
    // If no timestamp, just return the content as is
    return (
      <div className="p-4 border border-blue-100 rounded-md bg-white whitespace-pre-wrap">
        {content}
      </div>
    );
  }
  
  // Find where the timestamp ends (usually after the Z)
  const timestampEndIndex = content.indexOf('Z') + 1;
  
  // Extract the timestamp and the message
  const timestamp = content.substring(0, timestampEndIndex);
  const message = content.substring(timestampEndIndex).trim();
  
  // Format the timestamp nicely
  const formattedTimestamp = formatAdminTimestamp(timestamp);
  
  return (
    <div className="p-4 border border-blue-100 rounded-md bg-white">
      <div className="flex items-center gap-2 mb-2 text-sm text-blue-800">
        <Clock className="h-4 w-4 text-blue-600" />
        <span className="font-medium">{formattedTimestamp}</span>
      </div>
      {message.includes(':') ? (
        <div className="whitespace-pre-wrap">
          <span className="font-medium">{message.split(':')[0]}:</span> 
          <span>{message.split(':').slice(1).join(':').trim()}</span>
        </div>
      ) : (
        <div className="whitespace-pre-wrap">{message}</div>
      )}
    </div>
  );
} 