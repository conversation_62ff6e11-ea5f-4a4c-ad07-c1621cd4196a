'use client';

import { useFormStatus } from 'react-dom';
import { Button } from '@/components/ui/button';
import { Icons } from '@/components/ui/icons';

interface SubmitButtonProps {
  text?: string;
  loadingText?: string;
  className?: string;
}

export function SubmitButton({ 
  text = "Siųsti pranešim<PERSON>", 
  loadingText = "Siunčiama...",
  className = "w-full bg-[#002855] hover:bg-blue-800"
}: SubmitButtonProps) {
  const { pending } = useFormStatus();
  
  return (
    <Button 
      type="submit" 
      className={className}
      disabled={pending}
      aria-disabled={pending}
    >
      {pending ? (
        <>
          <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
          {loadingText}
        </>
      ) : text}
    </Button>
  );
} 