"use client"

import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import { lt } from "date-fns/locale"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DatePickerProps {
  date: Date | undefined
  setDate: (date: Date | undefined) => void
  className?: string
  placeholder?: string
  disabled?: boolean
}

export function DatePicker({
  date,
  setDate,
  className,
  placeholder = "Pasirinkite datą",
  disabled = false,
}: DatePickerProps) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal h-10",
            !date && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? (
            format(date, "yyyy-MM-dd", { locale: lt })
          ) : (
            <span>{placeholder}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={date}
          onSelect={setDate}
          initialFocus
          locale={lt}
        />
      </PopoverContent>
    </Popover>
  )
}

// DateTimeRangePicker component for selecting a date/time range
interface DateTimeRangePickerProps {
  startDate: Date | undefined
  setStartDate: (date: Date | undefined) => void
  endDate: Date | undefined
  setEndDate: (date: Date | undefined) => void
  className?: string
  disabled?: boolean
}

export function DateTimeRangePicker({
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  className,
  disabled = false,
}: DateTimeRangePickerProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {/* Start date picker */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Pradžios data</label>
        <DatePicker
          date={startDate}
          setDate={setStartDate}
          placeholder="Pasirinkite pradžios datą"
          disabled={disabled}
        />
      </div>
      
      {/* End date picker */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Pabaigos data</label>
        <DatePicker
          date={endDate}
          setDate={setEndDate}
          placeholder="Pasirinkite pabaigos datą"
          disabled={disabled}
        />
      </div>
    </div>
  )
} 