"use client";

import React, { useTransition, useState, useEffect } from "react";
import { useFormStatus } from "react-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useSession } from "@/lib/hooks/use-session";
import { toast } from "sonner";
import { useActionState } from "react";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Icons } from "@/components/ui/icons";
import { CheckCircle2, MessageSquarePlus } from "lucide-react";

const feedbackFormSchema = z.object({
  category: z.string({
    required_error: "Pasirinkite atsiliepimo kategoriją",
  }),
  title: z.string().min(5, {
    message: "Pavadinimas turi būti bent 5 simboliai",
  }).max(100, {
    message: "Pavadinimas negali būti ilgesnis nei 100 simbolių",
  }),
  feedback: z.string().min(20, {
    message: "Atsiliepimas turi būti bent 20 simbolių",
  }).max(1000, {
    message: "Atsiliepimas negali būti ilgesnis nei 1000 simbolių",
  }),
  rating: z.string({
    required_error: "Prašome įvertinti paslaugą",
  }),
  allowContact: z.boolean().default(false),
  isAnonymous: z.boolean().default(false),
});

type FeedbackFormValues = z.infer<typeof feedbackFormSchema>;

// Form action handler
async function handleFormAction(prevState: any, formData: FormData) {
  try {
    const response = await fetch("/api/messages/feedback", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(Object.fromEntries(formData.entries())),
    });

    if (!response.ok) {
      return { error: "Klaida siunčiant atsiliepimą" };
    }

    return { success: true };
  } catch (error) {
    console.error("Klaida siunčiant atsiliepimą:", error);
    return { 
      error: "Nepavyko išsiųsti atsiliepimo. Bandykite dar kartą vėliau." 
    };
  }
}

// Success message component that replaces the form
const SuccessMessage = ({ onReset }: { onReset: () => void }) => (
  <Card className="border border-green-200 bg-green-50">
    <CardHeader>
      <div className="flex items-center gap-2">
        <CheckCircle2 className="h-8 w-8 text-green-500" />
        <CardTitle className="text-green-800">Atsiliepimas išsiųstas</CardTitle>
      </div>
      <CardDescription className="text-green-700">
        Jūsų atsiliepimas sėkmingai išsiųstas administracijai
      </CardDescription>
    </CardHeader>
    <CardContent>
      <p className="text-green-700">
        Dėkojame už jūsų atsiliepimą ir pasiūlymus. Jūsų nuomonė yra labai svarbi mums, 
        nes ji padeda tobulinti mūsų teikiamas paslaugas.
      </p>
    </CardContent>
    <CardFooter>
      <Button 
        onClick={onReset} 
        variant="outline" 
        className="border-green-500 text-green-700 hover:bg-green-100"
      >
        <MessageSquarePlus className="mr-2 h-4 w-4" />
        Pateikti kitą atsiliepimą
      </Button>
    </CardFooter>
  </Card>
);

// Submit Button component that uses React 19's useFormStatus
function SubmitButton() {
  const { pending } = useFormStatus();
  
  return (
    <Button 
      type="submit" 
      className="w-full bg-[#002855] hover:bg-blue-800"
      disabled={pending}
      aria-disabled={pending}
    >
      {pending ? (
        <>
          <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
          Siunčiama...
        </>
      ) : "Pateikti atsiliepimą"}
    </Button>
  );
}

export default function FeedbackForm() {
  const { data: session } = useSession();
  const [isPending, startTransition] = useTransition();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  
  // Initialize the state with the server action - using React.useActionState instead of ReactDOM.useFormState
  const [state, formAction] = useActionState(handleFormAction, { error: null, success: false });

  const form = useForm<FeedbackFormValues>({
    resolver: zodResolver(feedbackFormSchema),
    defaultValues: {
      category: "",
      title: "",
      feedback: "",
      rating: "",
      allowContact: false,
      isAnonymous: false,
    },
  });

  // Handle form state changes
  useEffect(() => {
    if (state.error) {
      toast.error("Nepavyko išsiųsti atsiliepimo", {
        description: state.error,
      });
    } else if (state.success) {
      toast.success("Atsiliepimas sėkmingai išsiųstas");
      setShowSuccessMessage(true);
      form.reset();
    }
  }, [state, form]);

  // Reset the form and hide success message
  const handleReset = () => {
    setShowSuccessMessage(false);
    form.reset();
  };

  // Add a watcher for the isAnonymous field
  const isAnonymous = form.watch('isAnonymous');
  
  // When isAnonymous changes, update allowContact appropriately
  useEffect(() => {
    if (isAnonymous) {
      form.setValue('allowContact', false);
    }
  }, [isAnonymous, form]);

  // Use react-hook-form's handleSubmit for client-side validation before triggering server action
  const onSubmit = form.handleSubmit((data) => {
    const formData = new FormData();
    
    // Set all fields properly
    formData.set('category', data.category || "");
    formData.set('title', data.title);
    formData.set('feedback', data.feedback);
    formData.set('rating', data.rating || "");
    formData.set('allowContact', data.allowContact ? "true" : "false");
    formData.set('isAnonymous', data.isAnonymous ? "true" : "false");
    formData.set('email', session?.user?.email || "");
    formData.set('name', session?.user?.name || "");
    
    // Manually trigger the form action inside a transition
    startTransition(() => {
      formAction(formData);
    });
  });

  // Show success message if form was submitted successfully
  if (showSuccessMessage) {
    return <SuccessMessage onReset={handleReset} />;
  }

  return (
    <Card className="border border-gray-200 shadow-sm">
      <CardContent className="pt-6">
        <Form {...form}>
          <form onSubmit={onSubmit} className="space-y-6">
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">Kategorija</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    name="category"
                  >
                    <FormControl>
                      <SelectTrigger className="bg-white">
                        <SelectValue placeholder="Pasirinkite kategoriją" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="service">Bendruomenės paslaugos</SelectItem>
                      <SelectItem value="management">Administracijos darbas</SelectItem>
                      <SelectItem value="maintenance">Pastato priežiūra</SelectItem>
                      <SelectItem value="safety">Saugumas</SelectItem>
                      <SelectItem value="events">Renginiai</SelectItem>
                      <SelectItem value="other">Kita</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Pasirinkite temą, kuri geriausiai atitinka jūsų atsiliepimą
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="rating"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">Įvertinimas</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    name="rating"
                  >
                    <FormControl>
                      <SelectTrigger className="bg-white">
                        <SelectValue placeholder="Įvertinkite paslaugą" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="5">Puikiai (5)</SelectItem>
                      <SelectItem value="4">Labai gerai (4)</SelectItem>
                      <SelectItem value="3">Gerai (3)</SelectItem>
                      <SelectItem value="2">Patenkinamai (2)</SelectItem>
                      <SelectItem value="1">Blogai (1)</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Kaip vertinate paslaugos kokybę?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">Atsiliepimo pavadinimas</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Trumpas atsiliepimo pavadinimas" 
                      {...field} 
                      className="bg-white"
                      name="title"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="feedback"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">Jūsų atsiliepimas</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Išsamiai aprašykite savo atsiliepimą..."
                      className="h-32 min-h-[128px] resize-y bg-white"
                      {...field}
                      name="feedback"
                    />
                  </FormControl>
                  <FormDescription>
                    Aprašykite savo patirtį, įspūdžius ir pasiūlymus
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isAnonymous"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      name="isAnonymous"
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      Pateikti atsiliepimą anonimiškai
                    </FormLabel>
                    <FormDescription>
                      Jūsų vardas ir kontaktinė informacija nebus rodomi administracijai
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="allowContact"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      name="allowContact"
                      disabled={isAnonymous}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel className={isAnonymous ? "opacity-50" : ""}>
                      Sutinku, kad su manimi būtų susisiekta dėl atsiliepimo
                    </FormLabel>
                    <FormDescription className={isAnonymous ? "opacity-50" : ""}>
                      Jei reikalinga papildoma informacija, administratorius gali susisiekti su jumis
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <div className="bg-blue-50 p-4 rounded-md mb-6">
              <p className="text-sm text-blue-700">
                <strong>Pastaba:</strong> Jūsų atsiliepimai padeda mums gerinti teikiamas paslaugas.
                Dėkojame už jūsų nuomonę ir pasiūlymus.
              </p>
            </div>

            <Button 
              type="submit" 
              className="w-full bg-[#002855] hover:bg-blue-800"
              disabled={isPending}
            >
              {isPending ? (
                <>
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  Siunčiama...
                </>
              ) : "Pateikti atsiliepimą"}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
} 