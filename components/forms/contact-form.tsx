"use client";

import React, { useTransition, useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useSession } from "@/lib/hooks/use-session";
import { toast } from "sonner";
import { useActionState } from "react";

import { 
  Form as UIForm,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { SubmitButton } from "@/components/ui/submit-button";
import { submitContactForm } from "@/app/actions/contact";
import { CheckCircle2, MessageSquarePlus } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";

const contactFormSchema = z.object({
  category: z.string({
    required_error: "Pasirinkite pranešimo kategoriją",
  }),
  subject: z.string().min(5, {
    message: "Tema turi būti bent 5 simboliai",
  }).max(100, {
    message: "Tema negali būti ilgesnė nei 100 simbolių",
  }),
  message: z.string().min(20, {
    message: "Pranešimas turi būti bent 20 simboliai",
  }).max(1000, {
    message: "Pranešimas negali būti ilgesnis nei 1000 simbolių",
  }),
  phoneNumber: z.string().optional(),
  isAnonymous: z.boolean().default(false),
});

type ContactFormValues = z.infer<typeof contactFormSchema>;

// Success message component that replaces the form
const SuccessMessage = ({ onReset }: { onReset: () => void }) => (
  <Card className="border border-green-200 bg-green-50">
    <CardHeader>
      <div className="flex items-center gap-2">
        <CheckCircle2 className="h-8 w-8 text-green-500" />
        <CardTitle className="text-green-800">Pranešimas išsiųstas</CardTitle>
      </div>
      <CardDescription className="text-green-700">
        Jūsų pranešimas sėkmingai išsiųstas administracijai
      </CardDescription>
    </CardHeader>
    <CardContent>
      <p className="text-green-700">
        Dėkojame už jūsų pranešimą. Administracija peržiūrės jūsų pranešimą ir, jei reikės, susisieks su jumis.
        Skubiais atvejais rekomenduojame susisiekti tiesiogiai telefonu.
      </p>
    </CardContent>
    <CardFooter>
      <Button 
        onClick={onReset} 
        variant="outline" 
        className="border-green-500 text-green-700 hover:bg-green-100"
      >
        <MessageSquarePlus className="mr-2 h-4 w-4" />
        Siųsti kitą pranešimą
      </Button>
    </CardFooter>
  </Card>
);

export default function ContactForm() {
  const { data: session } = useSession();
  const [isPending, startTransition] = useTransition();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  
  // Initialize the state with the server action
  const [state, formAction] = useActionState(submitContactForm, {
    success: false,
    error: null
  });

  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      category: "",
      subject: "",
      message: "",
      phoneNumber: "",
      isAnonymous: false,
    },
  });

  // Handle form state changes
  useEffect(() => {
    if (state.error) {
      toast.error("Nepavyko išsiųsti pranešimo", {
        description: state.error,
      });
    } else if (state.success) {
      toast.success("Pranešimas sėkmingai išsiųstas");
      setShowSuccessMessage(true);
      form.reset();
    }
  }, [state, form]);

  // Reset the form and hide success message
  const handleReset = () => {
    setShowSuccessMessage(false);
    form.reset();
  };

  // Use react-hook-form's handleSubmit for client-side validation before triggering server action
  const onSubmit = form.handleSubmit((data) => {
    const formData = new FormData();
    // Always set the category value to ensure it is a string
    formData.set('category', data.category || "");
    
    // Set other fields properly
    formData.set('subject', data.subject);
    formData.set('message', data.message);
    formData.set('phoneNumber', data.phoneNumber || "");
    formData.set('isAnonymous', data.isAnonymous ? "true" : "false");
    
    // Manually trigger the form action inside a transition using the startTransition hook
    startTransition(() => {
      formAction(formData);
    });
  });

  // Show success message if form was submitted successfully
  if (showSuccessMessage) {
    return <SuccessMessage onReset={handleReset} />;
  }

  // Otherwise show the form
  return (
    <Card className="border border-gray-200 shadow-sm">
      <CardContent className="pt-6">
        <UIForm {...form}>
          <form onSubmit={onSubmit} className="space-y-6">
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Kategorija <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    name="category"
                  >
                    <FormControl>
                      <SelectTrigger className="bg-white">
                        <SelectValue placeholder="Pasirinkite kategoriją" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="general">Bendras klausimas</SelectItem>
                      <SelectItem value="technical">Techninis gedimas</SelectItem>
                      <SelectItem value="financial">Finansai ir mokėjimai</SelectItem>
                      <SelectItem value="noise">Triukšmas ir konfliktai</SelectItem>
                      <SelectItem value="suggestion">Pasiūlymas</SelectItem>
                      <SelectItem value="other">Kita</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Pasirinkite kategoriją, kuri geriausiai atitinka jūsų pranešimą
                  </FormDescription>
                  <FormMessage />
                  {state.errors?.category && (
                    <p className="text-sm font-medium text-red-500 mt-1">
                      Pasirinkite kategoriją
                    </p>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="subject"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Tema <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Trumpa pranešimo tema" 
                      {...field} 
                      className="bg-white"
                      name="subject"
                    />
                  </FormControl>
                  <FormMessage />
                  {state.errors?.subject && (
                    <p className="text-sm font-medium text-red-500 mt-1">
                      {state.errors.subject}
                    </p>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Pranešimas <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Išsamiai aprašykite savo pranešimą..."
                      className="h-32 min-h-[128px] resize-y bg-white"
                      {...field}
                      name="message"
                    />
                  </FormControl>
                  <FormMessage />
                  {state.errors?.message && (
                    <p className="text-sm font-medium text-red-500 mt-1">
                      {state.errors.message}
                    </p>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isAnonymous"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      name="isAnonymous"
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      Pateikti pranešimą anonimiškai
                    </FormLabel>
                    <FormDescription>
                      Jūsų vardas ir kontaktinė informacija nebus rodomi administracijai
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Kontaktinis telefono numeris <span className="text-gray-500">(nebūtinas)</span>
                  </FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="+370 600 00000" 
                      {...field} 
                      className="bg-white"
                      name="phoneNumber"
                    />
                  </FormControl>
                  <FormDescription>
                    Jei norite, kad su jumis susisiektume telefonu
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="bg-blue-50 p-4 rounded-md mb-6">
              <p className="text-sm text-blue-700">
                <strong>Pastaba:</strong> Jūsų pranešimas bus perduotas administracijai, 
                kuri susisieks su jumis artimiausiu metu. Skubiais atvejais rekomenduojame 
                susisiekti tiesiogiai telefonu.
              </p>
              <p className="text-sm text-red-500 mt-2">
                <span className="text-red-500">*</span> Privalomi laukai
              </p>
            </div>

            <SubmitButton />
          </form>
        </UIForm>
      </CardContent>
    </Card>
  );
} 