"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import { Type, ZoomIn, ZoomOut } from "lucide-react"

interface FontSizeSwitcherProps {
  className?: string
}

// Font size values in percentages
const fontSizes = [
  { id: "normal", name: "Normalus", size: 100, default: true },
  { id: "large", name: "<PERSON><PERSON><PERSON>", size: 112.5 },
  { id: "xlarge", name: "<PERSON><PERSON> dideli<PERSON>", size: 125 },
]

export function FontSizeSwitcher({ className }: FontSizeSwitcherProps) {
  const [currentFontSize, setCurrentFontSize] = useState(fontSizes[0])
  const [mounted, setMounted] = useState(false)
  
  // Only run on client
  useEffect(() => {
    setMounted(true)
    // Get saved font size from local storage
    const savedFontSize = localStorage.getItem("font-size")
    if (savedFontSize) {
      const fontSize = fontSizes.find(size => size.id === savedFontSize)
      if (fontSize) {
        setCurrentFontSize(fontSize)
        applyFontSize(fontSize.size)
      }
    }
  }, [])
  
  const applyFontSize = (size: number) => {
    // Apply font size to the html element
    document.documentElement.style.fontSize = `${size}%`
  }
  
  const handleFontSizeChange = (fontSize: typeof fontSizes[0]) => {
    setCurrentFontSize(fontSize)
    localStorage.setItem("font-size", fontSize.id)
    applyFontSize(fontSize.size)
  }
  
  if (!mounted) return null
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "rounded-full focus-visible:ring-2 focus-visible:ring-indigo-500",
            className
          )}
          aria-label="Keisti teksto dydį"
        >
          <Type className="h-5 w-5" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="p-2 w-64">
        <div className="pb-2 pt-1 px-2">
          <p className="text-sm font-semibold">Teksto dydis</p>
          <p className="text-xs text-muted-foreground">
            Pritaikykite teksto dydį geresniam skaitomumui
          </p>
        </div>
        
        {fontSizes.map((fontSize) => (
          <DropdownMenuItem
            key={fontSize.id}
            className={cn(
              "flex items-center py-2.5 gap-3 cursor-pointer rounded-md",
              currentFontSize.id === fontSize.id && "bg-slate-100"
            )}
            onClick={() => handleFontSizeChange(fontSize)}
          >
            <div className={cn(
              "h-8 w-8 rounded-full flex items-center justify-center",
              currentFontSize.id === fontSize.id ? "bg-indigo-100 text-indigo-600" : "bg-slate-100"
            )}>
              {fontSize.id === "normal" ? (
                <Type className="h-4 w-4" />
              ) : fontSize.id === "large" ? (
                <ZoomIn className="h-4 w-4" />
              ) : (
                <ZoomIn className="h-5 w-5" />
              )}
            </div>
            
            <div className="space-y-0.5">
              <div className="font-medium text-sm">{fontSize.name}</div>
              <div 
                className="text-xs text-slate-500"
                style={{ fontSize: `${fontSize.size / 100}rem` }}
              >
                Pavyzdys
                {fontSize.default && <span className="ml-2 text-indigo-600">(numatytasis)</span>}
              </div>
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
} 