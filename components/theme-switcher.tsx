"use client"

import { useEffect, useState } from "react"
import { Moon, Sun, Laptop } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useTheme } from "next-themes"
import { cn } from "@/lib/utils"

interface ThemeSwitcherProps {
  className?: string
  iconClassName?: string
  size?: "sm" | "md" | "lg"
  showText?: boolean
}

export function ThemeSwitcher({ 
  className, 
  iconClassName,
  size = "md",
  showText = false
}: ThemeSwitcherProps) {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  
  // Prevent hydration mismatch by only showing the theme switcher after mount
  useEffect(() => {
    setMounted(true)
  }, [])
  
  if (!mounted) {
    return null
  }
  
  const sizeClasses = {
    sm: "h-8 w-8",
    md: "h-9 w-9",
    lg: "h-10 w-10"
  }
  
  const iconSizes = {
    sm: 16,
    md: 18,
    lg: 20
  }
  
  const getCurrentThemeIcon = () => {
    switch (theme) {
      case "dark":
        return <Moon className={cn("transition-all", iconClassName)} size={iconSizes[size]} />
      case "light":
        return <Sun className={cn("transition-all", iconClassName)} size={iconSizes[size]} />
      default:
        return <Laptop className={cn("transition-all", iconClassName)} size={iconSizes[size]} />
    }
  }
  
  const getThemeLabel = (themeOption: string) => {
    switch (themeOption) {
      case "dark":
        return "Tamsus režimas"
      case "light":
        return "Šviesus režimas"
      default:
        return "Sistemos tema"
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "rounded-full focus-visible:ring-2 focus-visible:ring-indigo-500",
            sizeClasses[size],
            className
          )}
          aria-label="Pasirinkti temą"
        >
          <div className="flex items-center justify-center">
            {getCurrentThemeIcon()}
            {showText && (
              <span className="ml-2 text-sm font-medium">
                {getThemeLabel(theme || "system")}
              </span>
            )}
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-52 p-2 font-medium">
        <div className="pb-2 pt-1 px-2">
          <p className="text-sm font-semibold">Pasirinkite temą</p>
          <p className="text-xs text-muted-foreground">
            Pritaikykite svetainės išvaizdą sau
          </p>
        </div>
        <DropdownMenuItem 
          className={cn(
            "flex items-center py-2.5 gap-2 cursor-pointer rounded-md",
            theme === "light" && "bg-slate-100"
          )} 
          onClick={() => setTheme("light")}
        >
          <div className={cn(
            "h-8 w-8 rounded-full flex items-center justify-center", 
            theme === "light" ? "bg-indigo-100 text-indigo-600" : "bg-slate-100"
          )}>
            <Sun size={18} />
          </div>
          <div>
            <span className="block font-medium">Šviesus režimas</span>
            <span className="block text-xs text-slate-500 mt-0.5">
              Aukšto kontrasto vaizdas
            </span>
          </div>
        </DropdownMenuItem>
        <DropdownMenuItem 
          className={cn(
            "flex items-center py-2.5 gap-2 cursor-pointer rounded-md",
            theme === "dark" && "bg-slate-100"
          )} 
          onClick={() => setTheme("dark")}
        >
          <div className={cn(
            "h-8 w-8 rounded-full flex items-center justify-center", 
            theme === "dark" ? "bg-indigo-100 text-indigo-600" : "bg-slate-100"
          )}>
            <Moon size={18} />
          </div>
          <div>
            <span className="block font-medium">Tamsus režimas</span>
            <span className="block text-xs text-slate-500 mt-0.5">
              Žemesnio kontrasto vaizdas
            </span>
          </div>
        </DropdownMenuItem>
        <DropdownMenuItem 
          className={cn(
            "flex items-center py-2.5 gap-2 cursor-pointer rounded-md",
            theme === "system" && "bg-slate-100"
          )} 
          onClick={() => setTheme("system")}
        >
          <div className={cn(
            "h-8 w-8 rounded-full flex items-center justify-center", 
            theme === "system" ? "bg-indigo-100 text-indigo-600" : "bg-slate-100"
          )}>
            <Laptop size={18} />
          </div>
          <div>
            <span className="block font-medium">Sistemos tema</span>
            <span className="block text-xs text-slate-500 mt-0.5">
              Pritaikoma prie jūsų įrenginio
            </span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
} 