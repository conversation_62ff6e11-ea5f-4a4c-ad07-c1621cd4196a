'use client'

import { useEffect } from 'react'
import { initPostHog } from '@/lib/posthog'
import { setupErrorTracking } from '@/lib/errorTracking'

/**
 * Provider component that initializes analytics (PostHog)
 * and error tracking in client-side rendering.
 * 
 * @param {Object} props Component props
 * @param {React.ReactNode} props.children Child components to render
 * @returns {JSX.Element} Provider component that wraps children
 */
export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Initialize PostHog on the client side
    initPostHog()
    
    // Initialize error tracking once PostHog is ready
    setupErrorTracking()
  }, [])

  return <>{children}</>
} 