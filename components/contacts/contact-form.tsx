"use client";

import React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Contact, CONTACT_CATEGORIES } from "@/lib/contacts";

// Form schema with validation
const formSchema = z.object({
  label: z.string()
    .min(2, { message: "Pavadinimas turi būti bent 2 simbolių ilgio" })
    .max(100, { message: "Pavadinimas negali būti ilgesnis nei 100 simbolių" }),
  
  phone_number: z.string()
    .regex(/^(\+\d{1,3})?\d{3,15}$|^\d{3}\s\d{6}$/, {
      message: "Įveskite teisingą telefono numerį (pvz., +37061234567 arba **********)",
    }),
  
  description: z.string().optional(),
  company_name: z.string().optional(),
  
  category: z.enum(["emergency", "maintenance", "building", "custom"], {
    required_error: "Pasirinkite kategoriją",
  }),
  
  display_in_footer: z.boolean().default(false),
  display_in_contacts: z.boolean().default(true),
  display_in_emergency: z.boolean().default(false),
  
  icon: z.string().optional(),
});

interface ContactFormProps {
  initialData?: Contact;
  onSubmit: (data: z.infer<typeof formSchema>) => Promise<void>;
  isSubmitting?: boolean;
  mode: 'create' | 'edit';
}

export function ContactForm({
  initialData,
  onSubmit,
  isSubmitting = false,
  mode
}: ContactFormProps) {
  // Set up form with default values
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      label: initialData?.label || "",
      phone_number: initialData?.phone_number || "",
      description: initialData?.description || "",
      company_name: initialData?.company_name || "",
      category: (initialData?.category || "maintenance") as any,
      display_in_footer: initialData?.display_in_footer || false,
      display_in_contacts: initialData?.display_in_contacts || true,
      display_in_emergency: initialData?.display_in_emergency || false,
      icon: initialData?.icon || "",
    },
  });

  // Handle form submission
  const handleSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await onSubmit(values);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Klaida saugant kontaktą");
    }
  };

  return (
    <Card className="shadow-sm">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-indigo-900">
          {mode === 'create' ? 'Naujas kontaktas' : 'Redaguoti kontaktą'}
        </CardTitle>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-6">
                <FormField
                  control={form.control}
                  name="label"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pavadinimas</FormLabel>
                      <FormControl>
                        <Input placeholder="Pvz.: Avarinė tarnyba" {...field} />
                      </FormControl>
                      <FormDescription>
                        Kontakto pavadinimas, kuris bus rodomas vartotojams
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Telefono numeris</FormLabel>
                      <FormControl>
                        <Input placeholder="+37012345678 arba **********" {...field} />
                      </FormControl>
                      <FormDescription>
                        Telefono numeris (mobilusis arba fiksuoto ryšio)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="company_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Įmonės pavadinimas</FormLabel>
                      <FormControl>
                        <Input placeholder="Pvz.: UAB 'Įmonė'" {...field} />
                      </FormControl>
                      <FormDescription>
                        Įmonės pavadinimas (jei taikoma)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kategorija</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pasirinkite kategoriją" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.entries(CONTACT_CATEGORIES).map(([key, category]) => (
                            <SelectItem key={key} value={key}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Kategorija, pagal kurią bus grupuojamas kontaktas
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-6">
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Aprašymas</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Trumpas aprašymas apie kontakto paskirtį"
                          rows={3}
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-2 border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Rodymo nustatymai</h3>
                  <p className="text-sm text-muted-foreground mb-3">
                    Pasirinkite, kur šis kontaktas bus rodomas
                  </p>

                  <div className="space-y-3">
                    <FormField
                      control={form.control}
                      name="display_in_contacts"
                      render={({ field }) => (
                        <FormItem className="flex items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              Kontaktų puslapyje
                            </FormLabel>
                            <FormDescription>
                              Rodoma pagrindiniame kontaktų sąraše
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="display_in_footer"
                      render={({ field }) => (
                        <FormItem className="flex items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              Puslapio apačioje
                            </FormLabel>
                            <FormDescription>
                              Rodoma svetainės poraštėje
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="display_in_emergency"
                      render={({ field }) => (
                        <FormItem className="flex items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              Avarinių kontaktų skyriuje
                            </FormLabel>
                            <FormDescription>
                              Rodoma prioritetiniame avarinių kontaktų sąraše
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end gap-2 pt-2">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-indigo-600 hover:bg-indigo-700"
            >
              {isSubmitting
                ? "Įrašoma..."
                : mode === 'create'
                  ? "Sukurti kontaktą"
                  : "Išsaugoti pakeitimus"}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
} 