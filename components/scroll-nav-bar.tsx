'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { User } from 'lucide-react';

export function ScrollNavBar() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      // Show navbar when user scrolls down more than 100px
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      setIsVisible(scrollTop > 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200 shadow-lg transition-all duration-300 ${
        isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
      }`}
    >
      <div className="container mx-auto flex items-center justify-between py-3 px-4">
        {/* Small favicon/logo */}
        <div className="flex items-center gap-2">
          <img
            src="/favicon-96x96.png"
            alt="DNSB Vakarai"
            width={32}
            height={32}
            className="w-8 h-8"
          />
          <span className="text-lg font-bold text-[#002855] hidden sm:inline">
            DNSB Vakarai
          </span>
        </div>

        {/* Login button */}
        <Link href="/auth/login">
          <button className="inline-flex items-center justify-center whitespace-nowrap focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-[#002855] text-white hover:bg-[#001a3a] focus-visible:ring-[#002855] h-9 px-3 sm:px-4 rounded-md text-sm font-medium transition-all duration-200 active:scale-[0.98] gap-2">
            <User size={16} />
            <span className="hidden sm:inline">Prisijungti</span>
          </button>
        </Link>
      </div>
    </nav>
  );
}