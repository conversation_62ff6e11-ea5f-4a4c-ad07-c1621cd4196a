"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Loader2, Send } from "lucide-react";
import { toast } from "sonner";

export default function ProcessEmailsButton() {
  const [isProcessing, setIsProcessing] = useState(false);

  const processEmails = async () => {
    setIsProcessing(true);

    try {
      const response = await fetch('/api/admin/emails/process', {
        method: 'POST',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Nepavyko apdoroti el. pašto eilės");
      }

      const result = await response.json();

      if (result.success) {
        if (result.processed && result.processed > 0) {
          toast.success(`El. pašto eilė apdorota: išsiųsta ${result.processed} laiškų`);
        } else {
          toast.info("Nėra laiškų eilėje laukianči<PERSON> išsiuntimo");
        }
      } else {
        toast.error(result.error || "Įvyko klaida apdorojant el. pašto eil<PERSON>");
      }
    } catch (error) {
      console.error("Error processing email queue:", error);
      toast.error(error instanceof Error ? error.message : "Įvyko klaida apdorojant el. pašto eilę");
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Button 
      onClick={processEmails}
      disabled={isProcessing}
      className="min-w-[180px]"
    >
      {isProcessing ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Apdorojama...
        </>
      ) : (
        <>
          <Send className="mr-2 h-4 w-4" />
          Apdoroti eilę dabar
        </>
      )}
    </Button>
  );
} 