"use client";

import { DataTable } from "@/components/ui/data-table";
import { useState } from "react";
import { toast } from "sonner";

interface Email {
  id: number;
  type: string;
  recipient: string;
  subject: string;
  status: string;
  priority: number;
  attempts: number;
  scheduled_for: string | null;
  sent_at: string | null;
  created_at: string;
  entity_type: string | null;
  entity_id: number | null;
  error: string | null;
  emailData: {
    id: number;
    status: string;
    entity_type: string | null;
    entity_id: number | null;
    error_message: string | null;
  };
}

interface EmailTableClientProps {
  emails: Email[];
  columns: any[];
  pageIndex: number;
  pageSize: number;
  totalPages: number;
  stats: Record<string, number>;
}

export function EmailTableClient({
  emails,
  columns,
  pageIndex,
  pageSize,
  totalPages,
  stats,
}: EmailTableClientProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Handle bulk operations on selected emails
  const handleBulkAction = async (selectedRows: any[], action: string) => {
    try {
      const emailIds = selectedRows.map(row => row.id);
      const response = await fetch(`/api/admin/emails/bulk/${action}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ids: emailIds }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to ${action} selected emails`);
      }
      
      await refreshData();
      return true;
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error);
      return false;
    }
  };

  // Refresh data after actions
  const refreshData = async () => {
    setIsRefreshing(true);
    try {
      // Force router refresh
      await fetch('/api/revalidate?path=/dashboard/admin/emails', { method: 'POST' });
      window.location.reload();
    } catch (error) {
      console.error("Failed to refresh data:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <DataTable
      columns={columns}
      data={emails}
      pagination={{
        pageIndex,
        pageSize,
      }}
      pageCount={totalPages}
      searchColumn="subject"
      searchPlaceholder="Ieškoti pagal temą..."
      onDelete={async (selectedRows) => {
        try {
          const success = await handleBulkAction(selectedRows, 'cancel');
          if (success) {
            toast.success("Pasirinkti laiškai sėkmingai atšaukti");
            return true;
          } else {
            toast.error("Nepavyko atšaukti pasirinktų laiškų");
            return false;
          }
        } catch (error) {
          console.error("Error in onDelete:", error);
          toast.error("Įvyko klaida atšaukiant laiškus");
          return false;
        }
      }}
      getRowId={(row) => row.id.toString()}
      bulkActions={[
        {
          label: "Atšaukti laiškus",
          icon: "cancel",
          action: async (selectedRows) => {
            try {
              const success = await handleBulkAction(selectedRows, 'cancel');
              if (success) {
                toast.success("Pasirinkti laiškai sėkmingai atšaukti");
              } else {
                toast.error("Nepavyko atšaukti pasirinktų laiškų");
              }
              return success;
            } catch (error) {
              toast.error("Įvyko klaida atšaukiant laiškus");
              return false;
            }
          },
          showIf: (selectedRows) => {
            // Only show cancel action if at least one selected email is in pending status
            return selectedRows.some(row => row.status === 'pending');
          }
        },
        {
          label: "Bandyti siųsti iš naujo",
          icon: "resend",
          action: async (selectedRows) => {
            try {
              const success = await handleBulkAction(selectedRows, 'resend');
              if (success) {
                toast.success("Pasirinkti laiškai grąžinti į siuntimo eilę");
              } else {
                toast.error("Nepavyko pakartotinai siųsti pasirinktų laiškų");
              }
              return success;
            } catch (error) {
              toast.error("Įvyko klaida bandant pakartotinai siųsti laiškus");
              return false;
            }
          },
          showIf: (selectedRows) => {
            // Only show resend action if at least one selected email is in failed status
            return selectedRows.some(row => row.status === 'failed');
          }
        }
      ]}
    />
  );
} 