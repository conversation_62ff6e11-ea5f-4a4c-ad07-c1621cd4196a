"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { CheckCircle, Clock, XCircle, AlertCircle } from "lucide-react";

interface EmailStatusBadgeProps {
  status: string;
}

export default function EmailStatusBadge({ status }: EmailStatusBadgeProps) {
  // Map statuses to Lithuanian labels
  const statusLabels: Record<string, string> = {
    pending: "Laukiama",
    sent: "Iš<PERSON><PERSON>sta<PERSON>",
    failed: "Nepavyko",
    cancelled: "Atšauktas",
    dev_skipped: "Praleista (DEV)",
  };
  
  // Determine the appropriate styles and icon based on status
  let variant = "outline";
  let className = "";
  let Icon = Clock;
  
  switch (status) {
    case "pending":
      className = "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-50";
      Icon = Clock;
      break;
    case "sent":
      className = "bg-emerald-50 text-emerald-700 border-emerald-200 hover:bg-emerald-50";
      Icon = CheckCircle;
      break;
    case "failed":
      className = "bg-rose-50 text-rose-700 border-rose-200 hover:bg-rose-50";
      Icon = XCircle;
      break;
    case "cancelled":
      className = "bg-slate-50 text-slate-500 border-slate-200 hover:bg-slate-50";
      Icon = AlertCircle;
      break;
    case "dev_skipped":
      className = "bg-gray-50 text-gray-500 border-gray-200 hover:bg-gray-50";
      Icon = AlertCircle;
      break;
  }
  
  return (
    <Badge 
      variant="outline" 
      className={cn("flex items-center gap-1.5 py-1 px-2.5 font-normal rounded-md", className)}
    >
      <Icon className="h-3.5 w-3.5" />
      <span>{statusLabels[status] || status}</span>
    </Badge>
  );
} 