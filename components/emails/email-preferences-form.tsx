"use client";

import React from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
// Use API route instead of direct server-side function
// import { updateUserEmailPreferences } from "@/lib/email-queue";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useRouter } from "next/navigation";

type EmailPreference = {
  id: string;
  title: string;
  description: string;
  key: string;
  required?: boolean;
};

const emailPreferenceTypes: EmailPreference[] = [
  {
    id: "announcement",
    title: "Pranešimai",
    description: "Nauji pranešimai ir skelbimai iš DNSB administracijos",
    key: "announcement",
  },
  {
    id: "poll",
    title: "Apklausos",
    description: "Pranešimai apie naujas apklausa<PERSON>, kuriose galite daly<PERSON>",
    key: "poll",
  },
  {
    id: "contact_response",
    title: "Atsakymai į pranešimus",
    description: "Atsakymai į jūsų pateiktus pranešimus ar klausimus",
    key: "contact_response",
  },
  {
    id: "feedback_response",
    title: "Atsakymai į atsiliepimus",
    description: "Atsakymai į jūsų pateiktus atsiliepimus",
    key: "feedback_response",
  },
  {
    id: "password_reset",
    title: "Slaptažodžio keitimas",
    description: "Pranešimai susiję su slaptažodžio keitimu ar atkūrimu",
    key: "password_reset",
    required: true,
  },
];

interface EmailPreferencesFormProps {
  initialPreferences: Record<string, boolean>;
  userId: number;
}

export function EmailPreferencesForm({
  initialPreferences,
  userId,
}: EmailPreferencesFormProps) {
  const router = useRouter();
  const [preferences, setPreferences] = React.useState<Record<string, boolean>>(initialPreferences);
  const [isLoading, setIsLoading] = React.useState(false);

  const handleToggle = (key: string, value: boolean) => {
    // Don't allow toggling off required preferences
    const preference = emailPreferenceTypes.find((p) => p.key === key);
    if (preference?.required && !value) {
      toast.warning("Šio tipo pranešimai yra būtini ir negali būti išjungti");
      return;
    }

    setPreferences((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Use fetch API to call the API route instead of direct server function
      const response = await fetch('/api/email/preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(preferences),
      });
      
      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || "Klaida atnaujinant nustatymus");
      }

      toast.success("El. pašto nustatymai sėkmingai atnaujinti");
      router.refresh();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Klaida atnaujinant nustatymus");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Pranešimų tipai</CardTitle>
          <CardDescription>
            Pasirinkite, kokio tipo pranešimus norite gauti į savo el. paštą
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {emailPreferenceTypes.map((preference) => (
            <div key={preference.id} className="flex items-center justify-between py-2">
              <div className="space-y-0.5">
                <Label htmlFor={preference.id} className="text-base">
                  {preference.title}
                  {preference.required && (
                    <span className="ml-2 text-xs text-muted-foreground">(privalomas)</span>
                  )}
                </Label>
                <p className="text-sm text-muted-foreground">
                  {preference.description}
                </p>
              </div>
              <Switch
                id={preference.id}
                checked={preferences[preference.key] ?? true}
                onCheckedChange={(value) => handleToggle(preference.key, value)}
                disabled={preference.required || isLoading}
              />
            </div>
          ))}
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Įrašoma..." : "Išsaugoti nustatymus"}
        </Button>
      </div>
    </form>
  );
} 