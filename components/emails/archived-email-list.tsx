import { db } from "@/lib/db/supabase-adapter";
import Link from "next/link";
import { Card } from "@/components/ui/card";
import { EmptyState } from "@/components/ui/empty-state";
import { ArchiveIcon, Trash2 } from "lucide-react";
import { EmailTableClient } from "@/components/emails/email-table-client";
import { Button } from "@/components/ui/button";
// import { PurgeArchivedEmailsButton } from "@/components/emails/purge-archived-emails-button"; // We will create this later

interface ArchivedEmailListProps {
  searchParams: { page?: string; limit?: string };
}

export default async function ArchivedEmailList({ searchParams }: ArchivedEmailListProps) {
  // Pagination
  const page = searchParams?.page ? parseInt(searchParams.page, 10) : 1;
  const limit = searchParams?.limit ? parseInt(searchParams.limit, 10) : 10;
  const offset = (Math.max(1, page) - 1) * limit;

  // Fetch archived emails
  const emailsResult = await db.query(
    `SELECT *, announcement_id, poll_id 
     FROM email_queue 
     WHERE is_archived = TRUE 
     ORDER BY updated_at DESC -- Order by when they were archived (or updated)
     LIMIT $1 OFFSET $2`,
    [limit, offset]
  );

  // Get total count for pagination
  const countResult = await db.query(
    `SELECT COUNT(*) as total FROM email_queue WHERE is_archived = TRUE`
  );

  const total = parseInt(countResult.rows[0].total, 10);
  const totalPages = Math.ceil(total / limit);

  // Format the data for the table
  const emails = emailsResult.rows.map((email: any) => ({
    id: email.id,
    type: email.email_type,
    recipient: email.recipient,
    subject: email.subject,
    status: email.status, // Display the original status (sent/cancelled)
    priority: email.priority,
    attempts: email.attempts,
    scheduled_for: email.scheduled_for,
    sent_at: email.sent_at,
    created_at: email.created_at,
    updated_at: email.updated_at,
    entity_type: email.entity_type,
    entity_id: email.entity_id,
    announcement_id: email.announcement_id,
    poll_id: email.poll_id,
    error: email.error_message,
    emailData: {
      id: email.id,
      status: email.status,
      entity_type: email.entity_type,
      entity_id: email.entity_id,
      announcement_id: email.announcement_id,
      poll_id: email.poll_id,
      error_message: email.error_message
    }
  }));

  // Define columns for the archived table
  const columns = [
    { accessorKey: "id", header: "ID" },
    { accessorKey: "type", header: "Tipas", cell: "type-badge" },
    { accessorKey: "recipient", header: "Gavėjas" },
    { accessorKey: "subject", header: "Tema" },
    { accessorKey: "source", header: "Susijęs įrašas", cell: "source-link" },
    { accessorKey: "status", header: "Būsena (Archivavimo metu)", cell: "status-badge" },
    { accessorKey: "updated_at", header: "Archyvuota", cell: "created-at-format" },
    // No 'actions' column needed unless we add un-archive
  ];

  return (
    <div className="space-y-6">
      {/* Add Purge Button Here - Maybe pass as prop or add directly? */} 
      {/* <div className="text-right"> <PurgeArchivedEmailsButton /> </div> */}

      {emails.length === 0 ? (
        <EmptyState
          title="Archyvuotų laiškų nerasta"
          description="Nėra laiškų, pažymėtų kaip archyvuoti."
          icon={<ArchiveIcon className="h-10 w-10 text-muted-foreground" />}
        />
      ) : (
        <Card>
          <EmailTableClient 
            emails={emails}
            columns={columns}
            pageIndex={page - 1}
            pageSize={limit}
            totalPages={totalPages}
            stats={{}} 
          />
        </Card>
      )}
    </div>
  );
} 