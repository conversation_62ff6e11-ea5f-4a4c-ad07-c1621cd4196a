"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { 
  CalendarIcon, 
  MoreHorizontalIcon, 
  SendIcon, 
  XIcon, 
  FileTextIcon, 
  ExternalLinkIcon,
  ArchiveIcon,
  Loader2
} from "lucide-react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface Email {
  id: number;
  status: string;
  entity_type?: string;
  entity_id?: number;
  error_message?: string;
}

export default function EmailQueueActions({ email }: { email: Email }) {
  const router = useRouter();
  const [isViewingDetails, setIsViewingDetails] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [isArchiving, setIsArchiving] = useState(false);
  
  // Entity type translations
  const entityTypeLabels: Record<string, string> = {
    announcement: "pranešimą",
    poll: "apklausą",
    contact: "kontaktinę formą",
    feedback: "atsiliepimą"
  };
  
  // Function to handle cancelling an email
  const cancelEmail = async () => {
    try {
      setIsCancelling(true);
      const response = await fetch(`/api/admin/emails/${email.id}/cancel`, {
        method: "POST",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Nepavyko atšaukti laiško");
      }

      toast.success("Laiškas sėkmingai atšauktas");
      router.refresh();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Nepavyko atšaukti laiško");
    } finally {
      setIsCancelling(false);
    }
  };

  // Function to handle resending a failed email
  const resendEmail = async () => {
    try {
      setIsResending(true);
      const response = await fetch(`/api/admin/emails/${email.id}/resend`, {
        method: "POST",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Nepavyko pakartotinai išsiųsti laiško");
      }

      toast.success("Laiškas grąžintas į eilę");
      router.refresh();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Nepavyko pakartotinai išsiųsti laiško");
    } finally {
      setIsResending(false);
    }
  };

  // Function to handle archiving an email
  const archiveEmail = async () => {
    if (email.status !== 'sent' && email.status !== 'cancelled') return;
    
    try {
      setIsArchiving(true);
      const response = await fetch(`/api/admin/emails/${email.id}/archive`, {
        method: "PATCH",
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new Error(error.error || "Nepavyko archyvuoti laiško");
      }

      toast.success("Laiškas sėkmingai archyvuotas");
      router.refresh();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Nepavyko archyvuoti laiško");
    } finally {
      setIsArchiving(false);
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-9 w-9 p-0 rounded-md hover:bg-muted"
          >
            <MoreHorizontalIcon className="h-5 w-5" />
            <span className="sr-only">Veiksmai</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[220px]">
          <DropdownMenuLabel className="text-xs text-muted-foreground">Veiksmai</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          <DropdownMenuItem onClick={() => setIsViewingDetails(true)}>
            <FileTextIcon className="mr-2 h-4 w-4" />
            <span>Peržiūrėti detales</span>
          </DropdownMenuItem>
          
          {email.status === "pending" && (
            <DropdownMenuItem onClick={cancelEmail} className="text-destructive focus:text-destructive">
              <XIcon className="mr-2 h-4 w-4" />
              <span>Atšaukti laišką</span>
            </DropdownMenuItem>
          )}
          
          {email.status === "failed" && (
            <DropdownMenuItem onClick={resendEmail}>
              <SendIcon className="mr-2 h-4 w-4" />
              <span>Bandyti išsiųsti iš naujo</span>
            </DropdownMenuItem>
          )}
          
          {(email.status === "sent" || email.status === "cancelled") && (
            <DropdownMenuItem 
              onClick={archiveEmail}
              disabled={isArchiving}
              className="text-muted-foreground focus:text-foreground"
            >
              {isArchiving ? (
                 <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                 <ArchiveIcon className="mr-2 h-4 w-4" />
              )}
              <span>{isArchiving ? "Archyvuojama..." : "Archyvuoti laišką"}</span>
            </DropdownMenuItem>
          )}
          
          {email.entity_type && email.entity_id && (
            <DropdownMenuItem 
              onClick={() => window.open(`/dashboard/${email.entity_type}s/${email.entity_id}`, '_blank')}
            >
              <ExternalLinkIcon className="mr-2 h-4 w-4" />
              <span>Peržiūrėti susijusį {entityTypeLabels[email.entity_type] || email.entity_type}</span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Details Dialog */}
      <Dialog open={isViewingDetails} onOpenChange={setIsViewingDetails}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Laiško detalės</DialogTitle>
            <DialogDescription className="text-muted-foreground">
              Informacija apie laišką eilėje.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-4 gap-4 text-sm">
              <div className="font-medium text-foreground">ID</div>
              <div className="col-span-3">{email.id}</div>
              
              <div className="font-medium text-foreground">Būsena</div>
              <div className="col-span-3">
                {email.status === "pending" && "Laukiama"}
                {email.status === "sent" && "Išsiųstas"}
                {email.status === "failed" && "Nepavyko"}
                {email.status === "cancelled" && "Atšauktas"}
                {!["pending", "sent", "failed", "cancelled"].includes(email.status) && email.status}
              </div>
              
              {email.error_message && (
                <>
                  <div className="font-medium text-foreground">Klaida</div>
                  <div className="col-span-3 text-destructive">{email.error_message}</div>
                </>
              )}
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewingDetails(false)}>
              Uždaryti
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
} 