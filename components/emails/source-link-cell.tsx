import Link from 'next/link';
import { LinkIcon } from 'lucide-react';

interface SourceLinkCellProps {
  row: {
    original: {
      entity_type?: string;
      entity_id?: number;
      announcement_id?: number;
      poll_id?: number;
    };
  };
}

export default function SourceLinkCell({ row }: SourceLinkCellProps) {
  const { entity_type, entity_id, announcement_id, poll_id } = row.original;

  let href = null;
  let label = null;

  // Prioritize specific IDs if available
  if (announcement_id) {
    href = `/dashboard/announcements/${announcement_id}`;
    label = `Pranešimas #${announcement_id}`;
  } else if (poll_id) {
    href = `/dashboard/polls/${poll_id}`;
    label = `Apklausa #${poll_id}`;
  } else if (entity_type && entity_id) {
    // Fallback to generic entity type/id
    if (entity_type === 'announcement') {
      href = `/dashboard/announcements/${entity_id}`;
      label = `Pranešimas #${entity_id}`;
    } else if (entity_type === 'poll') {
      href = `/dashboard/polls/${entity_id}`;
      label = `Apklausa #${entity_id}`;
    }
    // Add other entity types here if needed
  }

  if (!href || !label) {
    return <span className="text-muted-foreground">-</span>;
  }

  return (
    <Link 
      href={href} 
      target="_blank" 
      className="inline-flex items-center text-sm text-blue-600 hover:underline hover:text-blue-800"
    >
      <LinkIcon className="h-3.5 w-3.5 mr-1.5" />
      {label}
    </Link>
  );
} 