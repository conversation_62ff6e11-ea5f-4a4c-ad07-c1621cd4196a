version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        # Pass Supabase environment variables as build arguments
        # This allows the build process to use real values instead of placeholders
        NEXT_PUBLIC_SUPABASE_URL: ${NEXT_PUBLIC_SUPABASE_URL}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${NEXT_PUBLIC_SUPABASE_ANON_KEY}
        SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
    container_name: dnsb-vakarai-web
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      # Application
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - PORT=3000
      - HOSTNAME=0.0.0.0
      - RUNTIME_ENV=true
      
      # URL Configuration for reverse proxy
      - NEXT_PUBLIC_SITE_URL=${NEXT_PUBLIC_SITE_URL}
      - NEXTAUTH_URL=${NEXT_PUBLIC_SITE_URL}
      
      # Supabase Configuration
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}

      # Email Configuration (SMTP settings stored in database)
      - EMAIL_DEV_MODE=${EMAIL_DEV_MODE:-live}
      - EMAIL_DEV_RECIPIENT=${EMAIL_DEV_RECIPIENT}
      
      # Security
      - CSRF_SECRET=${CSRF_SECRET}
      - CRON_API_KEY=${CRON_API_KEY}
      
      # Analytics & Logging
      - NEXT_PUBLIC_POSTHOG_KEY=${NEXT_PUBLIC_POSTHOG_KEY}
      - NEXT_PUBLIC_POSTHOG_HOST=${NEXT_PUBLIC_POSTHOG_HOST:-https://app.posthog.com}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      
      # Feature Flags
      - ENABLE_REGISTRATION=${ENABLE_REGISTRATION:-true}
      - ENABLE_PASSWORD_RESET=${ENABLE_PASSWORD_RESET:-true}
      
      # External Services (optional)
      - CLOUDINARY_URL=${CLOUDINARY_URL}
      - GOOGLE_MAPS_API_KEY=${GOOGLE_MAPS_API_KEY}
    
    volumes:
      # Mount logs directory for persistent logging
      - ./logs:/app/logs
      # Mount uploads directory if needed
      - ./uploads:/app/uploads
    
    networks:
      - dnsb-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    labels:
      # Coolify labels for automatic discovery and management
      - "coolify.managed=true"
      - "coolify.name=dnsb-vakarai"
      - "coolify.type=application"
      - "coolify.pullRequestDeployments=true"

networks:
  dnsb-network:
    driver: bridge

volumes:
  logs:
    driver: local
  uploads:
    driver: local
