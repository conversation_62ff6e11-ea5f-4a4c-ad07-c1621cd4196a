import { NextRequest, NextResponse } from "next/server";
import { getSecurityHeaders } from './lib/security/csp';
import { updateSession } from '@/lib/supabase/middleware';

/**
 * Middleware to apply security headers and route protection
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // First, handle Supabase auth session
  const supabaseResponse = await updateSession(request);

  // If Supabase middleware returned a redirect, use that
  if (supabaseResponse.status === 307 || supabaseResponse.status === 302) {
    return supabaseResponse;
  }

  // Otherwise, continue with security headers
  const response = NextResponse.next();

  // Determine if we're in development mode
  const isDev = process.env.NODE_ENV === 'development';

  // Note: We're not manually setting x-forwarded headers anymore, as this
  // could potentially bypass Next.js's built-in security checks.

  // Apply basic security headers in all environments, full CSP only in production
  const securityHeaders = getSecurityHeaders(isDev);
  Object.entries(securityHeaders).forEach(([key, value]) => {
    // Allow all security headers to be set
    if (value) {
      response.headers.set(key, value);
    }
  });

  // Admin routes are now protected by Supabase auth in the updateSession function
  // Additional admin-specific logic can be added here if needed

  // Disable caching for API routes
  if (request.nextUrl.pathname.startsWith('/api/')) {
    response.headers.set('Cache-Control', 'no-store, max-age=0');
  }

  return response;
}

/**
 * Configure the routes that should trigger this middleware
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public directory (public files)
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
};