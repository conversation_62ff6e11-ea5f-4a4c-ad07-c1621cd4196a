/**
 * Migration: Add User Import Fields
 * 
 * Adds fields needed for user import from CSV data:
 * - status field to users table
 * - area, registration_basis, and notes to flats table
 */

import { Pool } from 'pg';
import { config } from 'dotenv';
import path from 'path';
import fs from 'fs';

// Load environment variables
config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('Starting migration: add_user_import_fields');
    
    // Begin transaction
    await client.query('BEGIN');
    
    // Read and execute the SQL migration
    const sqlFile = path.join(__dirname, 'add_user_import_fields.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');
    await client.query(sql);
    
    // Commit transaction
    await client.query('COMMIT');
    
    console.log('Migration completed successfully');
  } catch (error) {
    // Rollback transaction on error
    await client.query('ROLLBACK');
    console.error('Migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the migration if called directly
if (require.main === module) {
  runMigration()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch(err => {
      console.error('Migration script failed:', err);
      process.exit(1);
    });
}

export default runMigration;