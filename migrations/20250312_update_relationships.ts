import { db } from "../lib/db";

/**
 * Migration to update the database schema for better relationships
 * between streets, houses, flats, users, and emergency contacts
 */
async function migrate() {
  console.log("🚀 Starting migration to update relationships...");
  
  try {
    // Begin transaction
    await db.query('BEGIN');
    
    // 1. Add flat_id to emergency_contacts if it doesn't exist
    console.log("1. Adding flat_id to emergency_contacts...");
    
    const checkColumn = await db.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'emergency_contacts' AND column_name = 'flat_id'
    `);
    
    if (checkColumn.rowCount === 0) {
      await db.query(`
        ALTER TABLE emergency_contacts 
        ADD COLUMN flat_id INTEGER REFERENCES flats(id)
      `);
      console.log("  ✅ Added flat_id column to emergency_contacts");
    } else {
      console.log("  ℹ️ flat_id column already exists in emergency_contacts");
    }
    
    // 2. Add foreign key constraints where missing
    console.log("2. Adding missing foreign key constraints...");
    
    // Check if the constraint already exists for houses.street_id
    const houseConstraint = await db.query(`
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name = 'houses' 
      AND constraint_type = 'FOREIGN KEY'
      AND constraint_name LIKE '%street_id%'
    `);
    
    if (houseConstraint.rowCount === 0) {
      await db.query(`
        ALTER TABLE houses
        ADD CONSTRAINT houses_street_id_fkey
        FOREIGN KEY (street_id) REFERENCES streets(id)
        ON DELETE CASCADE
      `);
      console.log("  ✅ Added foreign key constraint for houses.street_id");
    } else {
      console.log("  ℹ️ Foreign key constraint for houses.street_id already exists");
    }
    
    // Check if the constraint already exists for flats.house_id
    const flatConstraint = await db.query(`
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name = 'flats' 
      AND constraint_type = 'FOREIGN KEY'
      AND constraint_name LIKE '%house_id%'
    `);
    
    if (flatConstraint.rowCount === 0) {
      await db.query(`
        ALTER TABLE flats
        ADD CONSTRAINT flats_house_id_fkey
        FOREIGN KEY (house_id) REFERENCES houses(id)
        ON DELETE CASCADE
      `);
      console.log("  ✅ Added foreign key constraint for flats.house_id");
    } else {
      console.log("  ℹ️ Foreign key constraint for flats.house_id already exists");
    }
    
    // Check if the constraint already exists for users.flat_id
    const userConstraint = await db.query(`
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name = 'users' 
      AND constraint_type = 'FOREIGN KEY'
      AND constraint_name LIKE '%flat_id%'
    `);
    
    if (userConstraint.rowCount === 0) {
      await db.query(`
        ALTER TABLE users
        ADD CONSTRAINT users_flat_id_fkey
        FOREIGN KEY (flat_id) REFERENCES flats(id)
        ON DELETE SET NULL
      `);
      console.log("  ✅ Added foreign key constraint for users.flat_id");
    } else {
      console.log("  ℹ️ Foreign key constraint for users.flat_id already exists");
    }
    
    // 3. Create triggers to maintain data integrity
    console.log("3. Creating triggers for data integrity...");
    
    // Trigger to update house.street_id when street changes
    await db.query(`
      CREATE OR REPLACE FUNCTION update_street_id_in_houses() RETURNS TRIGGER AS $$
      BEGIN
        UPDATE houses SET street_id = NEW.id WHERE street_id = OLD.id;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    // Check if trigger exists
    const streetTrigger = await db.query(`
      SELECT 1 FROM pg_trigger WHERE tgname = 'update_street_id_in_houses_trigger'
    `);
    
    if (streetTrigger.rowCount === 0) {
      await db.query(`
        DROP TRIGGER IF EXISTS update_street_id_in_houses_trigger ON streets;
        CREATE TRIGGER update_street_id_in_houses_trigger
        AFTER UPDATE ON streets
        FOR EACH ROW
        EXECUTE FUNCTION update_street_id_in_houses();
      `);
      console.log("  ✅ Created trigger for updating houses when street changes");
    } else {
      console.log("  ℹ️ Trigger for updating houses when street changes already exists");
    }
    
    // Commit transaction
    await db.query('COMMIT');
    console.log("\n✅ Migration completed successfully!");
    
  } catch (error) {
    // Rollback on error
    await db.query('ROLLBACK');
    console.error("❌ Error during migration:", error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

migrate(); 