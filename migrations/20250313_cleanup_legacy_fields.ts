import { db } from "../lib/db";

async function migrate() {
  console.log("🚀 Starting cleanup migration for legacy fields in users table...");

  try {
    await db.query("BEGIN");

    // Drop legacy columns if they exist
    await db.query(`ALTER TABLE users DROP COLUMN IF EXISTS street`);
    await db.query(`ALTER TABLE users DROP COLUMN IF EXISTS house_number`);
    await db.query(`ALTER TABLE users DROP COLUMN IF EXISTS flat_number`);

    await db.query("COMMIT");
    console.log("✅ Legacy fields dropped successfully from users table.");
    process.exit(0);
  } catch (error) {
    await db.query("ROLLBACK");
    console.error("❌ Error dropping legacy fields:", error);
    process.exit(1);
  }
}

migrate(); 