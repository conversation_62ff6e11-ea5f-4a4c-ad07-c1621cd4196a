/**
 * Migration: Add password reset and magic link functionality
 * 
 * This migration adds:
 * 1. flat_payment_codes table for storing payment codes
 * 2. has_custom_password column in users table
 * 3. auth_attempts table for rate limiting
 */

import { db } from "@/lib/db/supabase-adapter";
import { hash } from "bcryptjs";
import { v4 as uuidv4 } from 'uuid';
import logger from "@/lib/logger";

async function checkTableExists(tableName: string): Promise<boolean> {
  const result = await db.query(`
    SELECT EXISTS (
      SELECT FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = $1
    );
  `, [tableName]);
  
  return result.rows[0].exists;
}

async function checkColumnExists(tableName: string, columnName: string): Promise<boolean> {
  const result = await db.query(`
    SELECT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = $1
      AND column_name = $2
    );
  `, [tableName, columnName]);
  
  return result.rows[0].exists;
}

/**
 * Main migration function that runs all the steps
 */
export async function migrate() {
  try {
    logger.info('Starting password reset and magic link migration');
    
    // Step 1: Create auth_attempts table
    const authAttemptsExists = await checkTableExists('auth_attempts');
    if (!authAttemptsExists) {
      logger.info('Creating auth_attempts table');
      await db.query(`
        CREATE TABLE auth_attempts (
          id SERIAL PRIMARY KEY,
          email VARCHAR(255) NOT NULL,
          type VARCHAR(50) NOT NULL,
          ip_address VARCHAR(50),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX idx_auth_attempts_email_created ON auth_attempts(email, created_at);
      `);
      logger.info('auth_attempts table created successfully');
    } else {
      logger.info('auth_attempts table already exists, skipping creation');
    }
    
    // Step 2: Create flat_payment_codes table
    const paymentCodesExists = await checkTableExists('flat_payment_codes');
    if (!paymentCodesExists) {
      logger.info('Creating flat_payment_codes table');
      await db.query(`
        CREATE TABLE flat_payment_codes (
          id SERIAL PRIMARY KEY,
          flat_id UUID NOT NULL REFERENCES flats(id),
          payment_code VARCHAR(64) NOT NULL,
          payment_code_hash VARCHAR(128) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(flat_id)
        );
      `);
      logger.info('flat_payment_codes table created successfully');
    } else {
      logger.info('flat_payment_codes table already exists, skipping creation');
    }
    
    // Step 3: Add has_custom_password column to users table
    const hasCustomPasswordExists = await checkColumnExists('users', 'has_custom_password');
    if (!hasCustomPasswordExists) {
      logger.info('Adding has_custom_password column to users table');
      await db.query(`
        ALTER TABLE users ADD COLUMN has_custom_password BOOLEAN DEFAULT FALSE;
      `);
      logger.info('has_custom_password column added to users table');
    } else {
      logger.info('has_custom_password column already exists, skipping addition');
    }
    
    // Step 4: Generate initial payment codes for flats and populate flat_payment_codes table
    if (!paymentCodesExists) {
      logger.info('Generating initial payment codes for flats');
      
      // Get all flats with their house information
      const flatsResult = await db.query(`
        SELECT 
          f.id as flat_id, 
          f.number as flat_number, 
          h.id as house_id,
          h.name as house_name
        FROM flats f
        JOIN houses h ON f.house_id = h.id
      `);
      
      logger.info(`Found ${flatsResult.rowCount} flats for payment code generation`);
      
      // Process each flat
      for (const flat of flatsResult.rows) {
        // Generate payment code using house-flat pattern
        const paymentCode = `${flat.house_name}-${flat.flat_number}`;
        
        // Hash payment code
        const paymentCodeHash = await hash(paymentCode, 10);
        
        // Store in flat_payment_codes table
        try {
          await db.query(`
            INSERT INTO flat_payment_codes 
            (flat_id, payment_code, payment_code_hash) 
            VALUES ($1, $2, $3)
          `, [flat.flat_id, paymentCode, paymentCodeHash]);
          
          // Log successful insert
          logger.info(`Generated payment code for flat ${flat.house_name}-${flat.flat_number}`);
        } catch (error) {
          logger.error(`Error storing payment code for flat ${flat.house_name}-${flat.flat_number}:`, error);
        }
      }
      
      logger.info('Payment code generation completed');
    }
    
    // Step 5: Mark users with non-default passwords as having custom passwords
    if (!hasCustomPasswordExists) {
      logger.info('Marking users with custom passwords');
      
      // This is challenging since we can't directly compare against original payment codes
      // For now, let's not mark any existing users and wait for them to change passwords
      await db.query(`
        UPDATE users SET has_custom_password = false;
      `);
      
      logger.info('All users marked as having default passwords');
    }
    
    logger.info('Migration completed successfully');
    return { success: true, message: 'Migration completed successfully' };
  } catch (error) {
    logger.error('Migration failed with error:', error);
    return { success: false, message: `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
}

// If script is run directly
if (require.main === module) {
  migrate()
    .then((result) => {
      console.log(result.message);
      process.exit(result.success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Unhandled error in migration:', error);
      process.exit(1);
    });
}