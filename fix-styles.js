// This script ensures all environment variables are set correctly
// and clears any cached data that might interfere with styles
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Set environment variables
process.env.NODE_ENV = 'development';

// Define paths for Next.js cache and build directories
const cacheDir = path.join(__dirname, '.next/cache');
const buildDir = path.join(__dirname, '.next');

// Log what we're doing
console.log('🔄 Fixing styles in development mode...');

// Clear Next.js cache if it exists
if (fs.existsSync(cacheDir)) {
  console.log('🗑️  Clearing Next.js cache...');
  try {
    execSync(`rm -rf ${cacheDir}`);
    console.log('✅ Cache cleared!');
  } catch (err) {
    console.error('❌ Failed to clear cache:', err);
  }
}

// Optionally, completely clean the Next.js build
if (fs.existsSync(buildDir)) {
  console.log('🗑️  Removing Next.js build...');
  try {
    execSync(`rm -rf ${buildDir}`);
    console.log('✅ Build directory removed!');
  } catch (err) {
    console.error('❌ Failed to remove build directory:', err);
  }
}

// Run the development server with NODE_ENV=development explicitly set
console.log('🚀 Starting dev server with correct environment...');
try {
  execSync('npx cross-env NODE_ENV=development next dev', { stdio: 'inherit' });
} catch (err) {
  console.error('❌ Error running dev server:', err);
} 