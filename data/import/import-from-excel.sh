#!/bin/bash

# Exit on error
set -e

# Check for required file argument
if [ -z "$1" ]; then
  echo "Error: Please provide the Excel file path as an argument"
  echo "Usage: ./import-from-excel.sh path/to/excel-file.xlsx"
  exit 1
fi

EXCEL_FILE="$1"
CSV_FILE="${EXCEL_FILE%.*}.csv"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)"
ROOT_DIR="$(cd "$SCRIPT_DIR/../.." &>/dev/null && pwd)"

echo "========================================================"
echo "🚀 Starting import process for $EXCEL_FILE"
echo "========================================================"

# Step 1: Apply unique constraints to database
echo "Step 1: Applying unique constraints to database..."
node "$ROOT_DIR/scripts/apply-unique-constraints.js"
echo "✅ Unique constraints applied successfully!"
echo

# Step 2: Convert Excel to CSV
echo "Step 2: Converting Excel file to CSV..."
node "$ROOT_DIR/scripts/convert-excel-to-csv.js" "$EXCEL_FILE" "$CSV_FILE"
echo "✅ Excel converted to CSV: $CSV_FILE"
echo

# Step 3: Import data
echo "Step 3: Importing data from CSV..."
node "$ROOT_DIR/scripts/import-user-data.js" "$CSV_FILE"
echo "✅ Data import completed successfully!"
echo

echo "========================================================"
echo "✨ Import process completed!"
echo "========================================================" 