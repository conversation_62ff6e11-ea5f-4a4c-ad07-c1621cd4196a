# User Data Import Tools

This directory contains tools for importing user data from an Excel/CSV file into the database with proper relationships between houses, flats, users, and emergency contacts.

## File Structure Requirements

The import tool expects a CSV or Excel file with the following columns:

- `Gatve` (Street Name)
- `<PERSON>o Nr.` (House Number)
- `<PERSON><PERSON>` (Flat Number)
- `<PERSON><PERSON><PERSON><PERSON>` (Payment Code, used as password)
- `<PERSON><PERSON><PERSON>` (Full Name, optional)
- `Email` (Email Address, optional)
- `Telefonas` (Phone Number, optional)
- `Kontaktinis Asmuo` (Emergency Contact Name, optional)
- `Kontaktinio Asmens Telefonas` (Emergency Contact Phone, optional)

If your file has different column names (e.g., in English), the conversion tool can map them automatically.

## Import Process

The import process creates the following structure:

1. House records for each unique combination of street name and house number
2. Flat records linked to houses
3. User records linked to flats (main users)
4. Emergency contact records linked to users

### Step 1: Convert Excel to CSV (if needed)

If your data is in Excel format, convert it to CSV first:

```bash
node scripts/convert-excel-to-csv.js path/to/your/file.xlsx data/import/converted-data.csv
```

### Step 2: Import the Data

Import the data into the database:

```bash
node scripts/import-user-data.js data/import/converted-data.csv
```

## Example Workflow

1. Place your Excel file in this directory (e.g., `residents-data.xlsx`)
2. Convert to CSV: `node scripts/convert-excel-to-csv.js data/import/residents-data.xlsx data/import/residents.csv`
3. Import the data: `node scripts/import-user-data.js data/import/residents.csv`
4. Check the output for statistics and any errors

## Notes

- Users will be created with a username in the format `S#-F` where:
  - S is a street name abbreviation (first letter of each word or first 3 letters of a single-word street)
  - \# is house number
  - F is flat number
  - Example: "SMI44-18" for "Smiltelės 44, flat 18"
- Passwords are set to the `Moketojo Kodas` (Payment Code) value
- If email is not provided, a placeholder email will be generated
- If a user with the same email already exists, their data will be updated
- Created users will have the "user" role and can update their profile as needed 