{"name": "dnsb-vakarai", "version": "0.1.2", "private": true, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "scripts": {"dev": "next dev", "dev:safe": "cross-env NODE_ENV=development next dev", "fix-styles": "node fix-styles.js", "build": "next build", "build:test": "bash scripts/test-build.sh", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest __tests__/integration", "test:utils": "jest __tests__/integration/utils.test.ts", "email:process": "tsx scripts/process-email-queue-pgmq.ts", "email:test-pgmq": "tsx scripts/test-pgmq-email-queue.ts", "email:test-pgmq-direct": "NODE_ENV=development npx tsx scripts/test-pgmq-direct.ts", "init:smtp-reply": "tsx scripts/init-smtp-reply-to.ts", "deploy:verify": "node scripts/verify-deployment.js", "deploy:env": "node scripts/coolify-generate-env.js", "dev:verify": "node scripts/verify-development.js", "supabase:test": "tsx scripts/test-supabase-connection.ts", "nixpacks:prepare": "node scripts/nixpacks-migration-helper.js prepare", "nixpacks:compare": "node scripts/nixpacks-migration-helper.js compare", "nixpacks:validate": "node scripts/nixpacks-migration-helper.js validate", "nixpacks:env": "node scripts/nixpacks-migration-helper.js env", "nixpacks:fix": "echo 'Applying Node.js 20 fix for Nixpacks...' && cp nixpacks-alternatives.toml nixpacks.toml && echo 'Fixed! Now commit and deploy.'", "nixpacks:minimal": "echo 'Applying minimal Nixpacks config...' && cp nixpacks-minimal.toml nixpacks.toml && echo 'Applied minimal config. Commit and deploy.'", "nixpacks:force-node": "echo 'Forcing Node.js detection...' && cp nixpacks-force-node.toml nixpacks.toml && echo 'Applied force Node.js config. Commit and deploy.'", "nixpacks:node20": "echo 'Using Node.js 20 force config...' && cp nixpacks-node20-force.toml nixpacks.toml && echo 'Applied Node.js 20 config. Commit and deploy.'", "nixpacks:env-only": "echo 'Use environment variables only. Set NIXPACKS_NODE_VERSION=22 in Coolify and remove nixpacks.toml'", "env:validate": "node scripts/validate-production-env.js"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@modelcontextprotocol/sdk": "^1.12.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@sentry/nextjs": "^9.22.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.8", "@tanstack/query-sync-storage-persister": "^5.77.2", "@tanstack/react-query": "^5.77.2", "@tanstack/react-query-devtools": "^5.77.2", "@tanstack/react-query-persist-client": "^5.77.2", "@tanstack/react-table": "^8.21.2", "@types/lodash": "^4.17.17", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "csv-parse": "^5.6.0", "csv-parser": "^3.2.0", "csv-writer": "^1.6.0", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "isomorphic-dompurify": "^2.19.0", "lodash": "^4.17.21", "lucide-react": "^0.477.0", "nanoid": "^5.1.3", "next": "^15.2.4", "next-themes": "^0.4.6", "nodemailer": "^6.9.15", "pino": "^9.6.0", "posthog-js": "^1.242.2", "react": "^19.0.0", "react-day-picker": "^9.6.1", "react-dom": "^19.0.0", "react-hook-form": "^7.51.2", "sonner": "^2.0.1", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "tsx": "^4.7.1", "uuid": "^11.1.0", "zod": "^3.22.4"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.15.29", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.22", "autoprefixer": "^10.4.18", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-next": "15.2.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "postcss": "^8.4.37", "tailwindcss": "^3.4.1", "ts-jest": "^29.3.3", "typescript": "^5.4.2"}}