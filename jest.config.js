/**
 * Jest configuration for integration testing
 *
 * Focuses on integration tests without mocks, testing against real services
 * where possible (Supabase test instances, etc.)
 */

module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['**/__tests__/**/*.test.ts'],
  setupFilesAfterEnv: ['<rootDir>/__tests__/setup.ts'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1'
  },
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: 'tsconfig.json'
    }]
  },
  moduleDirectories: ['node_modules', '<rootDir>'],
  testTimeout: 30000, // Increased for integration tests

  // Coverage configuration
  collectCoverage: false,
  coverageDirectory: 'coverage',
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/__tests__/',
    '/scripts/',
    '/migrations/',
    '/backups/'
  ],
  coverageReporters: [
    'json',
    'text',
    'lcov',
    'html'
  ],

  // File extensions
  moduleFileExtensions: [
    'js',
    'ts',
    'json'
  ],

  // Test environment setup
  globalSetup: undefined,
  globalTeardown: undefined,

  // Disable automocking - we want real implementations
  automock: false,
  clearMocks: false,
  resetMocks: false,
  restoreMocks: false,

  // Verbose output for better debugging
  verbose: true,

  // Fail fast on first test failure in CI
  bail: process.env.CI ? 1 : 0
};
