# Alternative Nixpacks Configurations for Node.js 22 Issue
# Choose one of these configurations based on your needs

# ===== OPTION 1: Node.js 20 (RECOMMENDED - Most Stable) =====
# This is the safest option and fully compatible with Next.js 15
[variables]
NIXPACKS_NODE_VERSION = "20"

[phases.setup]
nixPkgs = ["nodejs_20", "npm"]
nixpkgsArchive = "nixos-unstable"

[phases.install]
cmds = ["npm ci"]
dependsOn = ["setup"]

[phases.build]
cmds = ["npm run build"]
dependsOn = ["install"]

[start]
cmd = "node server.js"

[buildEnvs]
NODE_ENV = "production"
NEXT_TELEMETRY_DISABLED = "1"

# ===== OPTION 2: Try Node.js 22 with newer nixpkgs =====
# Uncomment this section if you specifically need Node.js 22
# [variables]
# NIXPACKS_NODE_VERSION = "22"

# [phases.setup]
# nixPkgs = ["nodejs_22", "npm"]
# # Use the latest nixpkgs that should include nodejs_22
# nixpkgsArchive = "https://github.com/NixOS/nixpkgs/archive/nixos-unstable.tar.gz"

# [phases.install]
# cmds = ["npm ci"]
# dependsOn = ["setup"]

# [phases.build]
# cmds = ["npm run build"]
# dependsOn = ["install"]

# [start]
# cmd = "node server.js"

# [buildEnvs]
# NODE_ENV = "production"
# NEXT_TELEMETRY_DISABLED = "1"

# ===== OPTION 3: Minimal Configuration (Let Nixpacks auto-detect) =====
# This relies on Nixpacks auto-detection and uses defaults
# [variables]
# NIXPACKS_NODE_VERSION = "20"

# [start]
# cmd = "node server.js"

# ===== OPTION 4: Environment Variable Only =====
# Use this if you want to set Node.js version via Coolify environment variables
# Just set NIXPACKS_NODE_VERSION=20 in Coolify and use minimal nixpacks.toml:
# [start]
# cmd = "node server.js"
