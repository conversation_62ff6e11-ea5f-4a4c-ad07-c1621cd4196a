'use client';

import { posthog } from './posthog';

/**
 * Captures JavaScript errors and sends them to PostHog
 */
export function setupErrorTracking() {
  if (typeof window === 'undefined') return;
  
  // Setup global error handler
  window.addEventListener('error', (event) => {
    const { message, filename, lineno, colno, error } = event;
    
    posthog.capture('error_occurred', {
      message,
      filename,
      lineno,
      colno,
      stack: error?.stack || 'No stack trace available',
      user_agent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
    });
  });
  
  // Setup promise rejection handler
  window.addEventListener('unhandledrejection', (event) => {
    posthog.capture('unhandled_promise_rejection', {
      message: event.reason?.message || 'Unknown promise rejection',
      stack: event.reason?.stack || 'No stack trace available',
      user_agent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
    });
  });
  
  // Setup HTTP error tracking helper
  const originalFetch = window.fetch;
  window.fetch = async (...args) => {
    try {
      const response = await originalFetch(...args);
      
      // Track failed API calls (4xx and 5xx)
      if (!response.ok) {
        let errorData;
        try {
          // Try to parse error response
          errorData = await response.clone().json();
        } catch {
          errorData = { error: 'Could not parse error response' };
        }
        
        posthog.capture('api_error', {
          url: typeof args[0] === 'string' ? args[0] : args[0].url,
          status: response.status,
          statusText: response.statusText,
          method: args[1]?.method || 'GET',
          errorData,
          timestamp: new Date().toISOString(),
        });
      }
      
      return response;
    } catch (error) {
      // Track network errors
      posthog.capture('network_error', {
        url: typeof args[0] === 'string' ? args[0] : args[0].url,
        method: args[1]?.method || 'GET',
        message: error.message,
        timestamp: new Date().toISOString(),
      });
      
      throw error;
    }
  };
  
  console.log('[ErrorTracking] Error tracking initialized');
}

/**
 * Manual error tracking function for custom error cases
 */
export function trackError(
  errorType: string,
  errorInfo: Record<string, any> = {},
  shouldThrow: boolean = false
) {
  if (typeof window === 'undefined') return;
  
  const errorData = {
    ...errorInfo,
    url: window.location.href,
    timestamp: new Date().toISOString(),
  };
  
  posthog.capture(`error_${errorType}`, errorData);
  
  if (shouldThrow) {
    throw new Error(`${errorType}: ${JSON.stringify(errorInfo)}`);
  }
}