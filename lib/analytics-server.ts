'use server'

import { getCurrentUser } from './supabase/auth'
import { serverLog, EventProperties } from './analytics'

/**
 * Server action for client events that need server logging
 * This can be called from client components
 */
export async function logClientEvent(
  eventData: {
    eventType: string,
    resourceId?: string,
    metadata?: EventProperties
  }
) {
  try {
    // Get the current user from Supabase
    const user = await getCurrentUser()
    const userId = user?.id?.toString() || 'anonymous'
    
    // Log the event with user context
    serverLog(
      eventData.eventType, 
      {
        userId,
        resourceId: eventData.resourceId,
        timestamp: new Date().toISOString(),
        ...eventData.metadata
      },
      `Client event: ${eventData.eventType}`
    )
    
    return { success: true }
  } catch (error) {
    console.error('Error logging client event:', error)
    
    // Log the event without user context if auth fails
    serverLog(
      eventData.eventType, 
      {
        userId: 'anonymous',
        resourceId: eventData.resourceId,
        timestamp: new Date().toISOString(),
        ...eventData.metadata
      },
      `Client event: ${eventData.eventType}`
    )
    
    return { success: true }
  }
}