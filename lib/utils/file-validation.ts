import crypto from 'crypto';

// File type categories
export const ALLOWED_FILE_TYPES = {
  images: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ],
  documents: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ],
  spreadsheets: [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ],
  text: [
    'text/plain',
    'text/csv',
    'text/markdown'
  ]
} as const;

// All allowed MIME types
export const ALL_ALLOWED_TYPES = Object.values(ALLOWED_FILE_TYPES).flat();

// File size limits
export const FILE_SIZE_LIMITS = {
  general: 50 * 1024 * 1024, // 50MB
  image: 10 * 1024 * 1024,   // 10MB
  document: 50 * 1024 * 1024, // 50MB
} as const;

// File extension to MIME type mapping
const MIME_TYPE_MAP: Record<string, string> = {
  // Images
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.png': 'image/png',
  '.gif': 'image/gif',
  '.webp': 'image/webp',
  '.svg': 'image/svg+xml',
  
  // Documents
  '.pdf': 'application/pdf',
  '.doc': 'application/msword',
  '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  
  // Spreadsheets
  '.xls': 'application/vnd.ms-excel',
  '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  
  // Text
  '.txt': 'text/plain',
  '.csv': 'text/csv',
  '.md': 'text/markdown',
};

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  sanitizedFileName?: string;
  mimeType?: string;
  category?: keyof typeof ALLOWED_FILE_TYPES;
}

/**
 * Validate a file based on type, size, and name
 */
export function validateFile(file: File): ValidationResult {
  // Check file size
  const sizeLimit = isImageFile(file.type) ? FILE_SIZE_LIMITS.image : FILE_SIZE_LIMITS.general;
  
  if (file.size > sizeLimit) {
    return {
      isValid: false,
      error: `Failas per didelis. Maksimalus dydis: ${formatBytes(sizeLimit)}`
    };
  }

  // Check file type
  if (!ALL_ALLOWED_TYPES.includes(file.type)) {
    return {
      isValid: false,
      error: 'Netinkamas failo tipas. Leidžiami: paveikslėliai, PDF, Word, Excel, tekstiniai failai'
    };
  }

  // Sanitize file name
  const sanitizedFileName = sanitizeFileName(file.name);
  
  if (!sanitizedFileName) {
    return {
      isValid: false,
      error: 'Netinkamas failo pavadinimas'
    };
  }

  // Determine file category
  const category = getFileCategory(file.type);

  return {
    isValid: true,
    sanitizedFileName,
    mimeType: file.type,
    category
  };
}

/**
 * Sanitize file name to prevent security issues
 */
export function sanitizeFileName(fileName: string): string {
  // Remove any path components
  const baseName = fileName.split(/[/\\]/).pop() || '';
  
  // Replace spaces with underscores
  let sanitized = baseName.replace(/\s+/g, '_');
  
  // Remove any characters that aren't alphanumeric, underscore, hyphen, or dot
  sanitized = sanitized.replace(/[^a-zA-Z0-9._-]/g, '');
  
  // Remove multiple consecutive dots
  sanitized = sanitized.replace(/\.{2,}/g, '.');
  
  // Ensure the file has an extension
  const lastDotIndex = sanitized.lastIndexOf('.');
  if (lastDotIndex === -1 || lastDotIndex === sanitized.length - 1) {
    return '';
  }
  
  // Limit file name length (max 255 chars)
  if (sanitized.length > 255) {
    const extension = sanitized.substring(lastDotIndex);
    const nameWithoutExt = sanitized.substring(0, lastDotIndex);
    const maxNameLength = 255 - extension.length;
    sanitized = nameWithoutExt.substring(0, maxNameLength) + extension;
  }
  
  // Add timestamp to ensure uniqueness
  const timestamp = Date.now();
  const extension = sanitized.substring(lastDotIndex);
  const nameWithoutExt = sanitized.substring(0, lastDotIndex);
  
  return `${nameWithoutExt}_${timestamp}${extension}`;
}

/**
 * Get MIME type from file name
 */
export function getMimeType(fileName: string): string {
  const extension = fileName.toLowerCase().match(/\.[^.]+$/)?.[0];
  
  if (extension && MIME_TYPE_MAP[extension]) {
    return MIME_TYPE_MAP[extension];
  }
  
  return 'application/octet-stream';
}

/**
 * Check if file is an image
 */
export function isImageFile(mimeType: string): boolean {
  return ALLOWED_FILE_TYPES.images.includes(mimeType);
}

/**
 * Get file category based on MIME type
 */
export function getFileCategory(mimeType: string): keyof typeof ALLOWED_FILE_TYPES | undefined {
  for (const [category, types] of Object.entries(ALLOWED_FILE_TYPES)) {
    if (types.includes(mimeType)) {
      return category as keyof typeof ALLOWED_FILE_TYPES;
    }
  }
  return undefined;
}

/**
 * Format bytes to human readable string
 */
export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

/**
 * Generate file hash for deduplication
 */
export async function generateFileHash(file: File): Promise<string> {
  const buffer = await file.arrayBuffer();
  const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  
  return hashHex;
}

/**
 * Extract tags from file path (for folder uploads)
 */
export function extractTagsFromPath(path: string): string[] {
  const parts = path.split('/').filter(Boolean);
  
  // Remove the file name (last part)
  if (parts.length > 1) {
    parts.pop();
  }
  
  // Clean and return folder names as tags
  return parts.map(part => part.replace(/[^a-zA-Z0-9\s-]/g, '').trim()).filter(Boolean);
}

/**
 * Validate multiple files
 */
export function validateFiles(files: File[]): {
  valid: File[];
  invalid: Array<{ file: File; error: string }>;
} {
  const valid: File[] = [];
  const invalid: Array<{ file: File; error: string }> = [];
  
  for (const file of files) {
    const result = validateFile(file);
    
    if (result.isValid) {
      valid.push(file);
    } else {
      invalid.push({ file, error: result.error || 'Nežinoma klaida' });
    }
  }
  
  return { valid, invalid };
}

/**
 * Check if total file size exceeds limit
 */
export function checkTotalFileSize(files: File[], maxTotalSize: number = 200 * 1024 * 1024): boolean {
  const totalSize = files.reduce((sum, file) => sum + file.size, 0);
  return totalSize <= maxTotalSize;
}