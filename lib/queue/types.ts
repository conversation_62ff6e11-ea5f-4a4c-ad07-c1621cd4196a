export interface EmailJobData {
  emailType: string;
  recipient: string;
  subject: string;
  content: string;
  entityType?: string;
  entityId?: number;
  priority?: number;
  scheduledFor?: string | Date;
  metadata?: Record<string, any>;
  userId?: number;
  replyTo?: string;
  announcementId?: number;
  pollId?: number;
  originalQueueId?: number; // For migration tracking
  testMode?: boolean;
}

export type JobResult = {
  success: boolean;
  messageId?: string;
  error?: string;
  skipped?: boolean;
  testMode?: boolean;
};