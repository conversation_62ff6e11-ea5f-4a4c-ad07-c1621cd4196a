import { NextRequest, NextResponse } from 'next/server';

/**
 * Error types for consistent error handling
 */
export enum ErrorType {
  AUTHENTICATION = 'AUTHENTICATION_ERROR',
  AUTHORIZATION = 'AUTHORIZATION_ERROR',
  VALIDATION = 'VALIDATION_ERROR',
  NOT_FOUND = 'NOT_FOUND_ERROR',
  CONFLICT = 'CONFLICT_ERROR',
  DATABASE = 'DATABASE_ERROR',
  SERVER = 'SERVER_ERROR',
  EXTERNAL = 'EXTERNAL_SERVICE_ERROR',
  RATE_LIMIT = 'RATE_LIMIT_ERROR',
}

/**
 * Standard error structure for API responses
 */
export interface ApiError {
  error: string;
  code: string;
  message: string;
  details?: any;
}

/**
 * Type guard to check if an error is an instance of Error
 */
function isError(error: any): error is Error {
  return error instanceof Error;
}

/**
 * Maps error types to HTTP status codes
 */
const ERROR_STATUS_CODES: Record<ErrorType, number> = {
  [ErrorType.AUTHENTICATION]: 401,
  [ErrorType.AUTHORIZATION]: 403,
  [ErrorType.VALIDATION]: 400,
  [ErrorType.NOT_FOUND]: 404,
  [ErrorType.CONFLICT]: 409,
  [ErrorType.DATABASE]: 500,
  [ErrorType.SERVER]: 500,
  [ErrorType.EXTERNAL]: 502,
  [ErrorType.RATE_LIMIT]: 429,
};

/**
 * User-friendly error messages in Lithuanian for common errors
 */
const ERROR_MESSAGES: Record<ErrorType, string> = {
  [ErrorType.AUTHENTICATION]: 'Naudotojas neprisijungęs arba sesija baigėsi',
  [ErrorType.AUTHORIZATION]: 'Neturite teisių atlikti šį veiksmą',
  [ErrorType.VALIDATION]: 'Pateikti duomenys yra neteisingi',
  [ErrorType.NOT_FOUND]: 'Prašomas resursas nerastas',
  [ErrorType.CONFLICT]: 'Duomenų konfliktas, atnaujinkite duomenis ir bandykite dar kartą',
  [ErrorType.DATABASE]: 'Duomenų bazės klaida, bandykite vėliau',
  [ErrorType.SERVER]: 'Serverio klaida, bandykite vėliau',
  [ErrorType.EXTERNAL]: 'Išorinės sistemos klaida, bandykite vėliau',
  [ErrorType.RATE_LIMIT]: 'Viršytas užklausų limitas, bandykite vėliau',
};

/**
 * Returns a standardized error response
 */
export function createErrorResponse(
  type: ErrorType,
  message?: string,
  details?: any,
  originalError?: any
): NextResponse {
  // Log the error for server-side debugging
  if (process.env.NODE_ENV !== 'production' || type === ErrorType.SERVER) {
    console.error(`[${type}] ${message || ERROR_MESSAGES[type]}`, {
      details,
      originalError: isError(originalError) 
        ? { message: originalError.message, stack: originalError.stack } 
        : originalError
    });
  }
  
  // Prepare the error response
  const errorResponse: ApiError = {
    error: ERROR_MESSAGES[type],
    code: type,
    message: message || ERROR_MESSAGES[type],
  };
  
  // Only include details in development or for validation errors
  if (process.env.NODE_ENV !== 'production' || type === ErrorType.VALIDATION) {
    errorResponse.details = details;
  }
  
  return NextResponse.json(
    errorResponse,
    { status: ERROR_STATUS_CODES[type] }
  );
}

/**
 * Helper function to handle errors in API routes
 */
export function handleApiError(error: unknown, req: NextRequest): NextResponse {
  // Handle known error types
  if (error instanceof Error) {
    // Database errors
    if (error.message.includes('duplicate key') || error.message.includes('violates unique constraint')) {
      return createErrorResponse(ErrorType.CONFLICT, 'Duomenys jau egzistuoja sistemoje', undefined, error);
    }
    
    // Generic server error
    return createErrorResponse(ErrorType.SERVER, undefined, undefined, error);
  }
  
  // Handle unknown errors
  return createErrorResponse(
    ErrorType.SERVER,
    'Nenumatyta klaida',
    process.env.NODE_ENV !== 'production' ? error : undefined
  );
} 