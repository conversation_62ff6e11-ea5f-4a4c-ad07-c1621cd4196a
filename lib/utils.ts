import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { format } from "date-fns";
import { lt as ltLocale } from "date-fns/locale";

// Combine className values with Tailwind classes
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Helper function to get API base URL for server components
 * This handles URL construction properly for both development and production
 */
export function getApiBaseUrl() {
  // Use the same URL construction logic but without NextAuth dependency
  const baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null;
  
  if (baseUrl) {
    return baseUrl;
  }
  
  // Fallback to localhost in development
  return process.env.NODE_ENV === 'development' 
    ? 'http://localhost:3000' 
    : '';
}

/**
 * Helper function to format a date in Lithuanian format
 */
export function formatDate(date: string | Date) {
  return new Date(date).toLocaleDateString('lt-LT', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

/**
 * Helper function to format a date with time in Lithuanian format
 */
export function formatDateTime(date: string | Date) {
  return new Date(date).toLocaleDateString('lt-LT', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric'
  });
}

/**
 * Format admin comment timestamps in a more readable way
 * This handles the ISO format timestamps that appear in admin comments
 */
export function formatAdminTimestamp(timestamp: string) {
  // Check if this is an ISO format timestamp
  if (timestamp.includes('T') && timestamp.includes('Z')) {
    const date = new Date(timestamp);
    
    // Format: "2023 m. kovo 8 d. 00:26"
    return date.toLocaleDateString('lt-LT', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }) + ' ' + date.toLocaleTimeString('lt-LT', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  // If it's not in the expected format, return it unchanged
  return timestamp;
}

// Add audit log entry
export async function addAuditLog({
  userId,
  action,
  entityType,
  entityId,
  changes,
  ipAddress,
}: {
  userId?: number;
  action: string;
  entityType: string;
  entityId?: number;
  changes?: Record<string, any>;
  ipAddress?: string;
}) {
  try {
    const response = await fetch("/api/audit", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        userId,
        action,
        entityType,
        entityId,
        changes,
        ipAddress,
      }),
    });

    if (!response.ok) {
      // Silent fail for audit logs in production
      // In a real app, we might want to queue failed logs for retry
      return false;
    }
    return true;
  } catch (error) {
    // Silently handle error without console.error
    // In a production app, this could trigger a background retry mechanism
    return false;
  }
}

// Handle Excel file upload and parsing
export async function parseExcelFile(file: File): Promise<any[]> {
  try {
    // Convert file to array buffer
    const buffer = await file.arrayBuffer();
    
    // Dynamically import ExcelJS (this avoids issues with server-side execution)
    const ExcelJS = await import('exceljs');
    
    // Create a workbook and load the Excel file
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(buffer);
    
    // Get the first worksheet
    const worksheet = workbook.worksheets[0];
    
    if (!worksheet) {
      throw new Error('Worksheet not found');
    }
    
    // Get the headers from the first row
    const headers: string[] = [];
    worksheet.getRow(1).eachCell((cell) => {
      if (cell.value) {
        headers.push(cell.value.toString().trim());
      }
    });
    
    if (headers.length === 0) {
      throw new Error('No column headers found in the first row');
    }
    
    // Convert the worksheet data to an array of objects
    const data: any[] = [];
    
    // Skip the header row and process data rows
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);
      
      // Skip empty rows
      if (row.cellCount === 0) continue;
      
      // Create an object for the current row
      const rowData: Record<string, any> = {};
      
      // Map cell values to column headers
      headers.forEach((header, index) => {
        const cell = row.getCell(index + 1); // ExcelJS is 1-indexed
        
        // Skip empty cells
        if (!cell || !cell.value) {
          rowData[header] = '';
          return;
        }
        
        // Handle different cell types
        if (cell.type === ExcelJS.ValueType.Number) {
          rowData[header] = cell.value;
        } else if (cell.type === ExcelJS.ValueType.Date) {
          rowData[header] = cell.value;
        } else if (cell.type === ExcelJS.ValueType.Boolean) {
          rowData[header] = cell.value;
        } else {
          // Convert to string for all other types
          rowData[header] = cell.value?.toString().trim() || '';
        }
      });
      
      // Add the row data to the result
      data.push(rowData);
    }
    
    return data;
  } catch (error) {
    console.error('Excel parsing error:', error);
    throw new Error(`Failed to parse Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Mask sensitive data for logging
export function maskSensitiveData(data: Record<string, any>) {
  const maskedData = { ...data };
  const sensitiveFields = ["password", "passwordHash", "token", "secret"];

  Object.keys(maskedData).forEach((key) => {
    if (sensitiveFields.some((field) => key.toLowerCase().includes(field))) {
      maskedData[key] = "********";
    }
  });

  return maskedData;
}

/**
 * Sanitize sensitive data from user outputs to prevent data leakage
 * @param data Object containing potentially sensitive data
 * @param fields Optional array of field names to sanitize (defaults to common sensitive fields)
 * @returns Sanitized data object
 */
export function sanitizeOutput<T extends Record<string, any>>(
  data: T, 
  fields: string[] = ['password', 'password_hash', 'token', 'secret']
): T {
  // Create a copy to avoid mutating the original
  const sanitized = { ...data };
  
  // Remove sensitive fields
  fields.forEach(field => {
    if (field in sanitized) {
      delete sanitized[field];
    }
  });
  
  return sanitized;
}

/**
 * Translates message/feedback category from English to Lithuanian
 * @param category The English category name
 * @returns The Lithuanian translation or the original string if no translation found
 */
export function translateCategory(category: string): string {
  const categoryLabels: Record<string, string> = {
    // Contact message categories
    general: "Bendras klausimas",
    technical: "Techninis gedimas",
    financial: "Finansai ir mokėjimai",
    noise: "Triukšmas ir konfliktai",
    suggestion: "Pasiūlymas",
    other: "Kita",
    
    // Feedback categories
    administration: "Administracijos darbas",
    maintenance: "Pastatų priežiūra",
    accounting: "Apskaita ir mokėjimai",
    communication: "Komunikacija",
    service: "Aptarnavimo kokybė",
    website: "Svetainės patogumas",
    feedback: "Atsiliepimai"
  };
  
  return categoryLabels[category] || category;
}