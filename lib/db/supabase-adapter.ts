import { createServiceClient } from '@/lib/supabase/server';
import { PostgrestSingleResponse } from '@supabase/supabase-js';

// Create a db module that uses Supabase but maintains the same interface
// as the old PostgreSQL module for backward compatibility

interface QueryResult<T = any> {
  rows: T[];
  rowCount: number | null;
  command: string;
  oid: number;
  fields: any[];
}

// Convert Supabase response to PostgreSQL-like result format
function toQueryResult<T = any>(data: T[] | null, error: any): QueryResult<T> {
  if (error) {
    throw error;
  }
  
  return {
    rows: data || [],
    rowCount: data ? data.length : 0,
    command: 'SELECT',
    oid: 0,
    fields: []
  };
}

// Parse SQL query to extract table name and operation
function parseQuery(sql: string): { operation: string; table: string | null } {
  const trimmed = sql.trim().toUpperCase();
  
  if (trimmed.startsWith('SELECT')) {
    const match = sql.match(/FROM\s+(\w+)/i);
    return { operation: 'SELECT', table: match ? match[1] : null };
  }
  
  if (trimmed.startsWith('INSERT')) {
    const match = sql.match(/INTO\s+(\w+)/i);
    return { operation: 'INSERT', table: match ? match[1] : null };
  }
  
  if (trimmed.startsWith('UPDATE')) {
    const match = sql.match(/UPDATE\s+(\w+)/i);
    return { operation: 'UPDATE', table: match ? match[1] : null };
  }
  
  if (trimmed.startsWith('DELETE')) {
    const match = sql.match(/FROM\s+(\w+)/i);
    return { operation: 'DELETE', table: match ? match[1] : null };
  }
  
  return { operation: 'UNKNOWN', table: null };
}

// Main query function that delegates to Supabase
export async function query(text: string, params: any[] = []): Promise<QueryResult> {
  const supabase = await createServiceClient();
  
  // Handle transaction commands
  if (text.toUpperCase().trim() === 'BEGIN') {
    // Supabase doesn't support explicit transactions in the same way
    // Just return success
    return { rows: [], rowCount: 0, command: 'BEGIN', oid: 0, fields: [] };
  }
  
  if (text.toUpperCase().trim() === 'COMMIT') {
    return { rows: [], rowCount: 0, command: 'COMMIT', oid: 0, fields: [] };
  }
  
  if (text.toUpperCase().trim() === 'ROLLBACK') {
    return { rows: [], rowCount: 0, command: 'ROLLBACK', oid: 0, fields: [] };
  }
  
  // For complex queries, we need to use Supabase's RPC functions
  // or rewrite them to use Supabase's query builder
  // For now, let's handle the most common patterns
  
  const { operation, table } = parseQuery(text);
  
  // Log for debugging
  console.log(`[DB Adapter] Operation: ${operation}, Table: ${table}`);
  console.log(`[DB Adapter] Query: ${text.substring(0, 100)}...`);
  
  // For complex queries, fall back to using Supabase's SQL RPC
  // This requires creating SQL functions in Supabase
  try {
    // Replace parameter placeholders ($1, $2, etc.) with actual values
    let processedQuery = text;
    params.forEach((param, index) => {
      // Escape single quotes in string parameters
      const escapedParam = typeof param === 'string' 
        ? `'${param.replace(/'/g, "''")}'`
        : param === null 
        ? 'NULL'
        : param;
      processedQuery = processedQuery.replace(new RegExp(`\\$${index + 1}(?!\\d)`, 'g'), escapedParam);
    });
    
    // Use Supabase's raw SQL execution via RPC
    // Note: This requires a custom RPC function to be created in Supabase
    const { data, error } = await supabase.rpc('execute_sql', {
      query: processedQuery
    });
    
    if (error) {
      console.error('[DB Adapter] Supabase error:', error);
      throw error;
    }
    
    return toQueryResult(data, null);
  } catch (error) {
    console.error('[DB Adapter] Failed to execute query:', error);
    throw error;
  }
}

// Export the same interface as the old db module
export const db = {
  query
};

export default db;