/**
 * ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
 * DATABASE SCHEMA REFERENCE
 * ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
 * 
 * This file provides TypeScript interfaces that describe the database schema.
 * These are NOT used for ORM but serve as documentation and type references.
 * 
 * ⚠️ IMPORTANT: The authoritative source of truth for the schema is in 'scripts/schema.sql'.
 * If you make changes here, ensure they match the actual database schema.
 */

// ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
// TYPES AND ENUMS
// ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

/**
 * User role types in the system
 */
export type UserRole = 'developer' | 'super_admin' | 'editor' | 'user';

/**
 * Announcement importance levels
 */
export type AnnouncementImportance = 'normal' | 'important' | 'urgent';

/**
 * Types of message recipients
 */
export type RecipientType = 'all' | 'houses' | 'flats' | 'users';

/**
 * Message status types
 */
export type MessageStatus = 'NEW' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED';

// ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
// USER MANAGEMENT
// ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

/**
 * User model
 * Represents a user in the system with authentication and profile data
 */
export interface User {
  id: string;
  username: string;          // Unique username (e.g., "{house_number}-{flat_number}")
  name: string;              // Full name
  email: string;             // Email address (unique)
  password_hash: string;     // Bcrypt hashed password
  street?: string;           // Street name
  house_number?: string;     // House number
  flat_number?: string;      // Flat/apartment number
  phone?: string;            // Phone number
  role: UserRole;            // User role (super_admin, editor, user)
  is_profile_updated: boolean; // Whether user has updated their profile
  gdpr_consent_given: boolean; // Whether user has given GDPR consent for data processing
  gdpr_consent_date?: Date;  // When user gave GDPR consent
  account_disabled: boolean; // Whether account is disabled (e.g., GDPR consent withdrawal)
  account_disabled_date?: Date; // When account was disabled
  account_disabled_reason?: string; // Reason why account was disabled
  created_at: Date;          // Creation timestamp
  updated_at?: Date;         // Last update timestamp
  last_login?: Date;         // Last login timestamp
  email_verified?: Date;     // Email verification timestamp
  flat_id?: string;          // Reference to flat/apartment
}

/**
 * Emergency contact model
 * Represents a contact that can be notified in case of emergencies
 */
export interface EmergencyContact {
  id: string;
  user_id: string;           // Owner of this contact
  name: string;              // Contact name
  phone?: string;            // Phone number
  email?: string;            // Email address
  relationship?: string;     // Relationship to user
  is_emergency: boolean;     // Whether to notify in emergencies
  created_at: Date;          // Creation timestamp
  updated_at?: Date;         // Last update timestamp
}

// ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
// PROPERTY MANAGEMENT
// ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

/**
 * House model
 * Represents a building with multiple flats/apartments
 */
export interface House {
  id: string;
  name: string;              // Display name 
  address: string;           // Full address
  floors: number;            // Number of floors
  created_at: Date;          // Creation timestamp
  updated_at?: Date;         // Last update timestamp
}

/**
 * Flat model
 * Represents an individual apartment/flat in a house
 */
export interface Flat {
  id: string;
  house_id: string;          // Reference to house
  number: string;            // Flat/apartment number
  floor: number;             // Floor number
  area?: number;             // Area in square meters
  created_at: Date;          // Creation timestamp
  updated_at?: Date;         // Last update timestamp
}

// ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
// COMMUNICATION
// ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

/**
 * Announcement model
 * Represents a message that can be sent to various recipients
 */
export interface Announcement {
  id: string;
  title: string;             // Announcement title
  content: string;           // Content (supports HTML)
  importance: AnnouncementImportance; // Importance level
  is_draft: boolean;         // Whether it's a draft
  sender_id: string;         // User who created this
  recipient_type: RecipientType; // Type of recipients
  created_at: Date;          // Creation timestamp
  updated_at?: Date;         // Last update timestamp
  sent_at?: Date;            // When it was sent
}

/**
 * Announcement attachment model
 * Represents files attached to announcements
 */
export interface AnnouncementAttachment {
  id: string;
  announcement_id: string;   // Reference to announcement
  file_name: string;         // Original file name
  file_path: string;         // Storage path in Supabase
  file_size: number;         // File size in bytes
  file_type: string;         // MIME type
  is_image: boolean;         // Whether file is an image
  created_at: Date;          // Upload timestamp
}

/**
 * Announcement-to-houses relation
 * Links announcements to specific houses
 */
export interface AnnouncementHouse {
  id: string;
  announcement_id: string;   // Reference to announcement
  house_id: string;          // Reference to house
}

/**
 * Announcement-to-users relation
 * Links announcements to specific users
 */
export interface AnnouncementUser {
  id: string;
  announcement_id: string;   // Reference to announcement
  user_id: string;           // Reference to user
}

/**
 * Notification model
 * Represents a notification that can be sent to users
 */
export interface Notification {
  id: string;
  title: string;             // Notification title
  content: string;           // Content (supports HTML)
  type: string;              // Notification type
  created_at: Date;          // Creation timestamp
  sent_at?: Date;            // When it was sent
  announcement_id?: string;  // Reference to announcement
}

/**
 * Notification target model
 * Links notifications to specific users and tracks read status
 */
export interface NotificationTarget {
  id: string;
  notification_id: string;   // Reference to notification
  user_id: string;           // Reference to user
  is_read: boolean;          // Whether user has read it
  read_at?: Date;            // When it was read
}

/**
 * Contact Message model
 * Represents a message sent from a user to administration
 */
export interface ContactMessage {
  id: string;
  userId: string;            // User who sent the message
  name: string;              // User's name
  email: string;             // User's email
  category: string;          // Message category
  subject: string;           // Message subject
  message: string;           // Message content
  phoneNumber?: string;      // Optional phone number
  status: MessageStatus;     // Status of the message
  adminNotes?: string;       // Admin notes on the message
  assignedTo?: string;       // Admin user assigned to handle this
  isAnonymous?: boolean;     // Whether the message is anonymous
  created_at: Date;          // Creation timestamp
  updated_at?: Date;         // Last update timestamp
  resolved_at?: Date;        // When it was resolved
}

/**
 * Feedback model
 * Represents feedback from users about administration services
 */
export interface Feedback {
  id: string;
  userId: string;            // User who sent the feedback
  name: string;              // User's name
  email: string;             // User's email
  category: string;          // Feedback category
  title: string;             // Feedback title
  content: string;           // Feedback content
  rating: number;            // Rating (1-5)
  allowContact: boolean;     // Whether admin can contact user
  isAnonymous?: boolean;     // Whether the feedback is anonymous
  status: MessageStatus;     // Status of the feedback
  adminResponse?: string;    // Admin response to feedback
  created_at: Date;          // Creation timestamp
  updated_at?: Date;         // Last update timestamp
  responded_at?: Date;       // When admin responded
}

/**
 * Admin Settings model
 * Represents configuration settings for the administration
 */
export interface AdminSettings {
  id: string;
  key: string;               // Setting key
  value: string;             // Setting value
  description?: string;      // Setting description
  created_at: Date;          // Creation timestamp
  updated_at?: Date;         // Last update timestamp
}

// ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
// AUDIT AND LOGGING
// ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

/**
 * Audit log model
 * Tracks user actions in the system for accountability
 */
export interface AuditLog {
  id: string;
  user_id?: string;          // User who performed the action
  action: string;            // Action taken
  entity_type: string;       // Type of entity affected
  entity_id?: string;        // ID of entity affected
  changes?: string;          // JSON stringified changes
  ip_address?: string;       // IP address of user
  created_at: Date;          // Creation timestamp
}

/**
 * ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
 * SCHEMA REFERENCE DIAGRAM
 * ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
 * 
 * users
 *   └── emergency_contacts (user_id)
 *   └── flat_id → flats
 *   └── contact_messages (user_id)
 *   └── feedback (user_id)
 * 
 * houses
 *   └── flats (house_id)
 * 
 * announcements
 *   └── announcement_houses (announcement_id)
 *   └── announcement_users (announcement_id)
 *   └── notifications (announcement_id)
 * 
 * notifications
 *   └── notification_targets (notification_id)
 */

/**
 * SQL Table Creation Reference
 * 
 * Below are the SQL statements that would create these tables.
 * This is for documentation purposes and not executed by this file.
 */

/*
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  password_hash TEXT NOT NULL,
  street TEXT,
  house_number TEXT,
  flat_number TEXT,
  phone TEXT,
  role TEXT NOT NULL DEFAULT 'user',
  is_profile_updated BOOLEAN NOT NULL DEFAULT false,
  gdpr_consent_given BOOLEAN NOT NULL DEFAULT false,
  gdpr_consent_date TIMESTAMP WITH TIME ZONE,
  account_disabled BOOLEAN NOT NULL DEFAULT false,
  account_disabled_date TIMESTAMP WITH TIME ZONE,
  account_disabled_reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE,
  last_login TIMESTAMP WITH TIME ZONE,
  email_verified TIMESTAMP WITH TIME ZONE,
  flat_id UUID REFERENCES flats(id)
);

CREATE TABLE emergency_contacts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  phone TEXT,
  email TEXT,
  relationship TEXT,
  is_emergency BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE contact_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id),
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  category TEXT NOT NULL,
  subject TEXT NOT NULL,
  message TEXT NOT NULL,
  phone_number TEXT,
  status TEXT NOT NULL DEFAULT 'NEW',
  admin_notes TEXT,
  assigned_to UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE,
  resolved_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE feedback (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id),
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  category TEXT NOT NULL,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
  allow_contact BOOLEAN NOT NULL DEFAULT FALSE,
  status TEXT NOT NULL DEFAULT 'NEW',
  admin_response TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE,
  responded_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE admin_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  key TEXT NOT NULL UNIQUE,
  value TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE
);
*/ 