import { createClient } from './server'
import { NextRequest, NextResponse } from 'next/server'

/**
 * Wrapper for API routes that require authentication
 */
export function withAuth(handler: (req: NextRequest, user: any) => Promise<NextResponse>) {
  return async (req: NextRequest) => {
    const supabase = createClient()
    
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    return handler(req, user)
  }
}

/**
 * Wrapper for API routes that require specific roles
 */
export function withRole(
  requiredRole: 'editor' | 'super_admin' | 'developer',
  handler: (req: NextRequest, user: any, profile: any) => Promise<NextResponse>
) {
  return async (req: NextRequest) => {
    const supabase = createClient()
    
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    // Get user profile to check role
    const { data: profile } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()
    
    if (!profile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      )
    }
    
    const roleHierarchy = {
      user: 0,
      editor: 1,
      super_admin: 2,
      developer: 3
    }
    
    const userLevel = roleHierarchy[profile.role as keyof typeof roleHierarchy]
    const requiredLevel = roleHierarchy[requiredRole]
    
    if (userLevel < requiredLevel) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }
    
    return handler(req, user, profile)
  }
}

/**
 * Standard error response
 */
export function errorResponse(message: string, status: number = 400) {
  return NextResponse.json(
    { error: message },
    { status }
  )
}

/**
 * Standard success response
 */
export function successResponse(data: any, status: number = 200) {
  return NextResponse.json(data, { status })
}

/**
 * Pagination helper
 */
export interface PaginationParams {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export function parsePaginationParams(searchParams: URLSearchParams): PaginationParams {
  return {
    page: parseInt(searchParams.get('page') || '1'),
    limit: Math.min(parseInt(searchParams.get('limit') || '10'), 100), // Max 100 items
    sortBy: searchParams.get('sortBy') || 'created_at',
    sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'
  }
}

/**
 * Apply pagination to Supabase query
 */
export function applyPagination(
  query: any,
  { page = 1, limit = 10, sortBy = 'created_at', sortOrder = 'desc' }: PaginationParams
) {
  const from = (page - 1) * limit
  const to = from + limit - 1
  
  return query
    .order(sortBy, { ascending: sortOrder === 'asc' })
    .range(from, to)
}

/**
 * Validate request body against schema
 */
export function validateRequestBody<T>(
  body: any,
  validator: (data: any) => T
): T | { error: string } {
  try {
    return validator(body)
  } catch (error: any) {
    return { error: error.message || 'Invalid request body' }
  }
}

/**
 * Handle CORS for API routes
 */
export function handleCors(req: NextRequest) {
  const origin = req.headers.get('origin')
  const allowedOrigins = [
    process.env.NEXT_PUBLIC_SITE_URL,
    'http://localhost:3000',
    'https://localhost:3000'
  ].filter(Boolean)
  
  const headers = new Headers()
  
  if (origin && allowedOrigins.includes(origin)) {
    headers.set('Access-Control-Allow-Origin', origin)
  }
  
  headers.set('Access-Control-Allow-Credentials', 'true')
  headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  
  return headers
}

/**
 * Handle OPTIONS requests for CORS
 */
export function handleOptions(req: NextRequest) {
  const headers = handleCors(req)
  return new NextResponse(null, { status: 200, headers })
}
