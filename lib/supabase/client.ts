import { createBrowserClient } from '@supabase/ssr';
import type { Database } from './types';

// Check if we're in build time (static generation)
const isBuildTime = typeof window === 'undefined' && process.env.NODE_ENV === 'production' && !process.env.RUNTIME_ENV;

export function createClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  // During build time, allow placeholder values to prevent build failures
  // Validation will happen at runtime when the client is actually used
  if (!isBuildTime) {
    if (!supabaseUrl || !supabaseAnonKey ||
        supabaseUrl.includes('placeholder') ||
        supabaseAnonKey.includes('placeholder')) {
      throw new Error(
        'Missing or invalid Supabase environment variables. Please check NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY'
      );
    }
  }

  // Use fallback values during build time to prevent errors
  const finalUrl = supabaseUrl || 'https://placeholder.supabase.co';
  const finalKey = supabaseAnonKey || 'placeholder-anon-key';

  return createBrowserClient<Database>(finalUrl, finalKey);
}