import { createClient } from './server';
import { createServiceClient } from './server';
import bcrypt from 'bcryptjs';
import { Database } from './types';

type UserRole = 'developer' | 'super_admin' | 'editor' | 'user';

interface SignInResult {
  data?: any;
  error?: string;
}

/**
 * Custom authentication for Supabase
 * This function allows users to login with either:
 * - Username (e.g., "31-7")
 * - Email address (if they have one set)
 * while maintaining compatibility with Supabase's email-based auth
 */
export async function signInWithUsername(
  usernameOrEmail: string, 
  password: string
): Promise<SignInResult> {
  try {
    // Use service client for user lookup and management
    const supabaseAdmin = await createServiceClient();
    // Use regular client for actual authentication to ensure cookies are set
    const supabase = await createClient();
    
    // 1. Look up user by username OR email in our custom users table
    const isEmail = usernameOrEmail.includes('@');
    
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('id, username, email, auth_user_id, password_hash, account_disabled, name')
      .or(`username.eq.${usernameOrEmail},email.eq.${usernameOrEmail}`)
      .single();

    if (userError || !user) {
      console.error('User lookup error:', userError);
      return { error: 'Invalid credentials' };
    }

    // Check if account is disabled
    if (user.account_disabled) {
      return { error: 'Account is disabled' };
    }

    const authEmail = user.email || generateAuthEmail(user.username);

    // 2. First, always check if we can validate against database password
    if (user.password_hash) {
      const isValidPassword = await bcrypt.compare(password, user.password_hash);
      
      if (!isValidPassword) {
        // Password doesn't match database, and we don't have auth_user_id to try Supabase
        if (!user.auth_user_id) {
          return { error: 'Invalid credentials' };
        }
        
        // Try Supabase auth as last resort
        const { data, error } = await supabase.auth.signInWithPassword({
          email: authEmail,
          password: password
        });

        if (error) {
          return { error: 'Invalid credentials' };
        }

        // Update last login
        await supabaseAdmin
          .from('users')
          .update({ last_login: new Date().toISOString() })
          .eq('id', user.id);

        return { data };
      }

      // Password is valid against database - now handle auth sync
      console.log('Database password valid, syncing with Supabase auth...');

      // Check if auth user exists (either linked or orphaned)
      let authUserId = user.auth_user_id;
      
      if (!authUserId) {
        // No auth_user_id, need to find or create auth user
        const { data: authUsersResult } = await supabaseAdmin.auth.admin.listUsers();
        const existingAuthUser = authUsersResult?.users?.find((u: any) => u.email === authEmail);
        
        if (existingAuthUser) {
          console.log('Found orphaned auth user, linking...');
          authUserId = existingAuthUser.id;
          
          // Update database with auth_user_id
          await supabaseAdmin
            .from('users')
            .update({ auth_user_id: authUserId })
            .eq('id', user.id);
        } else {
          // Create new auth user
          console.log('Creating new auth user...');
          
          const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
            email: authEmail,
            password: password,
            email_confirm: true,
            user_metadata: {
              username: user.username,
              name: user.name,
              user_id: user.id
            }
          });

          if (authError) {
            console.error('Failed to create auth user:', authError);
            return { error: 'Authentication setup failed' };
          }

          if (authData?.user) {
            authUserId = authData.user.id;
            
            // Update database with auth_user_id
            await supabaseAdmin
              .from('users')
              .update({ auth_user_id: authUserId })
              .eq('id', user.id);
          }
        }
      }

      // Update auth user password to match database
      if (authUserId) {
        const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
          authUserId,
          { password: password }
        );
        
        if (updateError) {
          console.error('Failed to update auth password:', updateError);
          // Continue anyway - password might already be correct
        }
      }

      // Try to sign in
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: authEmail,
        password: password
      });

      if (signInError) {
        console.error('Sign in error after password sync:', signInError);
        return { error: 'Authentication failed' };
      }

      // Update last login
      await supabaseAdmin
        .from('users')
        .update({ last_login: new Date().toISOString() })
        .eq('id', user.id);

      return { data: signInData };

    } else {
      // No password hash in database
      if (!user.auth_user_id) {
        return { error: 'Please contact administrator to reset your password' };
      }

      // Try Supabase auth
      const { data, error } = await supabase.auth.signInWithPassword({
        email: authEmail,
        password: password
      });

      if (error) {
        return { error: 'Invalid credentials' };
      }

      // Update last login
      await supabaseAdmin
        .from('users')
        .update({ last_login: new Date().toISOString() })
        .eq('id', user.id);

      return { data };
    }

  } catch (error) {
    console.error('Sign in error:', error);
    return { error: 'Authentication failed' };
  }
}

/**
 * Generate a dummy email for internal Supabase auth
 * Uses @dnsb.local domain to ensure no real emails are sent
 */
export function generateAuthEmail(username: string): string {
  // Replace hyphens with underscores and lowercase
  // "31-7" becomes "<EMAIL>"
  return `${username.toLowerCase().replace(/-/g, '_')}@dnsb.local`;
}

/**
 * Get the current user session with full user data
 */
export async function getCurrentUser() {
  try {
    const supabase = await createClient();
    
    // Use getUser() instead of getSession() for server-side auth checks
    const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      // Handle refresh token errors gracefully
      if (authError.message?.includes('refresh_token_not_found') || 
          authError.message?.includes('Invalid Refresh Token')) {
        console.log('Session expired or invalid, user needs to re-login');
        return null;
      }
      console.warn('Auth error in getCurrentUser:', authError.message);
      return null;
    }
    
    if (!authUser) {
      return null;
    }

  // Use service client to bypass RLS when getting user data
  const supabaseAdmin = await createServiceClient();
  
  // Get full user data from our custom users table
  const { data: user, error: userError } = await supabaseAdmin
    .from('users')
    .select('*')
    .eq('auth_user_id', authUser.id)
    .single();

  if (userError || !user) {
    // This is expected for new auth users who haven't been linked yet
    // Don't log as error to avoid console noise
    if (userError?.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.warn('User data not found for auth user:', authUser.id);
    }
    return null;
  }

  return {
    ...authUser,
    ...user,
    email: user.email || authUser.email // Prefer real email over dummy
  };
  
  } catch (error) {
    console.error('Unexpected error in getCurrentUser:', error);
    return null;
  }
}

/**
 * Sign out the current user
 */
export async function signOut() {
  const supabase = await createClient();
  return await supabase.auth.signOut();
}

/**
 * Check if user is authenticated
 */
export async function isAuthenticated(): Promise<boolean> {
  const user = await getCurrentUser();
  return !!user;
}

/**
 * Check if user has admin role
 */
export async function isAdmin(): Promise<boolean> {
  const user = await getCurrentUser();
  return user ? ['developer', 'super_admin', 'editor'].includes(user.role) : false;
}

/**
 * Update user profile
 */
export async function updateProfile(userId: string, updates: any) {
  const supabase = await createClient();
  
  return await supabase
    .from('users')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', userId);
}