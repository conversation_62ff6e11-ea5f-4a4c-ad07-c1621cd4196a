"use client";

import { createClient } from './client';

/**
 * Client-side function to get current user
 * Used in React components and hooks
 */
export async function getCurrentUserClient() {
  try {
    const supabase = createClient();
    const { data: { user: authUser }, error } = await supabase.auth.getUser();

    if (error) {
      // Handle refresh token errors gracefully
      if (error.message?.includes('refresh_token_not_found') ||
          error.message?.includes('Invalid Refresh Token')) {
        console.log('Session expired or invalid, user needs to re-login');
        return null;
      }
      console.warn('Auth error in getCurrentUserClient:', error.message);
      return null;
    }

    if (!authUser) {
      return null;
    }

    // Get user data from our custom users table using auth_user_id
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('auth_user_id', authUser.id)
      .single();

    if (userError || !userData) {
      // This is expected for new auth users who haven't been linked yet
      // Don't log as error to avoid console noise
      if (userError?.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.warn('User data not found for auth user:', authUser.id);
      }
      return null;
    }

    return {
      ...authUser,
      ...userData,
      email: userData.email || authUser.email // Prefer real email over dummy
    };
  } catch (error) {
    console.error('Error getting current user (client):', error);
    return null;
  }
}