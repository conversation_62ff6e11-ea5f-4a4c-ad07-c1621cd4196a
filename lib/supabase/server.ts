import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import type { Database } from './types';

// Check if we're in build time (static generation)
const isBuildTime = process.env.NODE_ENV === 'production' && !process.env.RUNTIME_ENV;

export async function createClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  // During build time, allow placeholder values to prevent build failures
  // Validation will happen at runtime when the client is actually used
  if (!isBuildTime) {
    if (!supabaseUrl || !supabaseAnonKey ||
        supabaseUrl.includes('placeholder') ||
        supabaseAnonKey.includes('placeholder')) {
      throw new Error(
        'Missing or invalid Supabase environment variables. Please check NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY'
      );
    }
  }

  // Use fallback values during build time to prevent errors
  const finalUrl = supabaseUrl || 'https://placeholder.supabase.co';
  const finalKey = supabaseAnonKey || 'placeholder-anon-key';

  const cookieStore = await cookies();

  return createServerClient<Database>(
    finalUrl,
    finalKey,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          } catch (error) {
            // The `set` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );
}

export async function createServiceClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  // During build time, allow placeholder values to prevent build failures
  // Validation will happen at runtime when the client is actually used
  if (!isBuildTime) {
    if (!supabaseUrl || !supabaseServiceKey ||
        supabaseUrl.includes('placeholder') ||
        supabaseServiceKey.includes('placeholder')) {
      throw new Error(
        'Missing or invalid Supabase environment variables. Please check NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY'
      );
    }
  }

  // Use fallback values during build time to prevent errors
  const finalUrl = supabaseUrl || 'https://placeholder.supabase.co';
  const finalServiceKey = supabaseServiceKey || 'placeholder-service-role-key';

  return createServerClient<Database>(
    finalUrl,
    finalServiceKey,
    {
      cookies: {
        getAll() {
          return [];
        },
        setAll() {
          // Service role doesn't need cookies
        },
      },
    }
  );
}