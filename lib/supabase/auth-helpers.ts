import { createClient } from './server'
import { createClient as createBrowser<PERSON>lient } from './client'
import { redirect } from 'next/navigation'
import type { User } from '@supabase/supabase-js'

export interface UserProfile {
  id: string
  username: string
  name: string
  email: string
  role: 'user' | 'editor' | 'super_admin' | 'developer'
  flat_id?: number
  phone?: string
  street?: string
  house_number?: string
  flat_number?: string
  is_profile_updated: boolean
  gdpr_consent_given: boolean
  account_disabled: boolean
  emergency_contact: boolean
  created_at: string
  updated_at: string
}

/**
 * Get the current user from server-side
 */
export async function getUser(): Promise<User | null> {
  const supabase = createClient()
  const { data: { user } } = await supabase.auth.getUser()
  return user
}

/**
 * Get the current user's profile
 */
export async function getUserProfile(): Promise<UserProfile | null> {
  const supabase = createClient()
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) return null
  
  const { data: profile } = await supabase
    .from('users')
    .select('*')
    .eq('id', user.id)
    .single()
  
  return profile
}

/**
 * Require authentication - redirect to login if not authenticated
 */
export async function requireAuth(): Promise<User> {
  const user = await getUser()
  
  if (!user) {
    redirect('/auth/login')
  }
  
  return user
}

/**
 * Require specific role - redirect to unauthorized if insufficient permissions
 */
export async function requireRole(
  requiredRole: 'editor' | 'super_admin' | 'developer'
): Promise<UserProfile> {
  const profile = await getUserProfile()
  
  if (!profile) {
    redirect('/auth/login')
  }
  
  const roleHierarchy = {
    user: 0,
    editor: 1,
    super_admin: 2,
    developer: 3
  }
  
  const userLevel = roleHierarchy[profile.role]
  const requiredLevel = roleHierarchy[requiredRole]
  
  if (userLevel < requiredLevel) {
    redirect('/unauthorized')
  }
  
  return profile
}

/**
 * Check if user has specific role
 */
export async function hasRole(role: 'editor' | 'super_admin' | 'developer'): Promise<boolean> {
  const profile = await getUserProfile()
  
  if (!profile) return false
  
  const roleHierarchy = {
    user: 0,
    editor: 1,
    super_admin: 2,
    developer: 3
  }
  
  const userLevel = roleHierarchy[profile.role]
  const requiredLevel = roleHierarchy[role]
  
  return userLevel >= requiredLevel
}

/**
 * Sign in with email and password
 */
export async function signIn(email: string, password: string) {
  const supabase = createBrowserClient()
  
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  })
  
  if (error) {
    throw error
  }
  
  return data
}

/**
 * Sign up with email and password
 */
export async function signUp(email: string, password: string, metadata?: any) {
  const supabase = createBrowserClient()
  
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: metadata
    }
  })
  
  if (error) {
    throw error
  }
  
  return data
}

/**
 * Sign out
 */
export async function signOut() {
  const supabase = createBrowserClient()
  
  const { error } = await supabase.auth.signOut()
  
  if (error) {
    throw error
  }
}

/**
 * Send password reset email
 */
export async function resetPassword(email: string) {
  const supabase = createBrowserClient()
  
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/auth/reset-password`
  })
  
  if (error) {
    throw error
  }
}

/**
 * Update password
 */
export async function updatePassword(password: string) {
  const supabase = createBrowserClient()
  
  const { error } = await supabase.auth.updateUser({
    password
  })
  
  if (error) {
    throw error
  }
}

/**
 * Send magic link
 */
export async function sendMagicLink(email: string) {
  const supabase = createBrowserClient()
  
  const { error } = await supabase.auth.signInWithOtp({
    email,
    options: {
      emailRedirectTo: `${window.location.origin}/auth/callback`
    }
  })
  
  if (error) {
    throw error
  }
}

/**
 * Update user profile
 */
export async function updateProfile(updates: Partial<UserProfile>) {
  const supabase = createClient()
  const user = await requireAuth()
  
  const { error } = await supabase
    .from('users')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', user.id)
  
  if (error) {
    throw error
  }
}

/**
 * Create user profile after signup
 */
export async function createUserProfile(
  userId: string,
  profileData: Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>
) {
  const supabase = createClient()
  
  const { error } = await supabase
    .from('users')
    .insert({
      id: userId,
      ...profileData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
  
  if (error) {
    throw error
  }
}

/**
 * Check if user account is disabled
 */
export async function checkAccountStatus(): Promise<boolean> {
  const profile = await getUserProfile()
  
  if (!profile) return false
  
  return !profile.account_disabled
}
