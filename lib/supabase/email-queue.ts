import { createServiceClient } from './server'
import { EmailJobData } from '@/lib/queue/types'
import { isTestEmail } from '@/lib/email-utils'
import { addAuditLog } from '@/lib/utils'

export interface EmailQueueItem {
  to_email: string
  subject: string
  content: string
  template_name?: string
  template_data?: any
  scheduled_for?: Date
  user_id?: string
  source_type?: string
  source_id?: string
  max_attempts?: number
}

export interface EmailTemplate {
  name: string
  subject: string
  html: string
  variables: string[]
}

// Get development email settings from database
async function getDevEmailSettings() {
  try {
    const supabase = await createServiceClient();
    const { data: settings } = await supabase
      .from('admin_settings')
      .select('key, value')
      .in('key', ['email_dev_mode', 'email_dev_recipient']);

    // Convert to an object
    const settingsObj: Record<string, string> = {};
    settings?.forEach(row => {
      settingsObj[row.key] = row.value;
    });

    return {
      mode: settingsObj.email_dev_mode || process.env.EMAIL_DEV_MODE || 'redirect',
      recipient: settingsObj.email_dev_recipient || process.env.EMAIL_DEV_RECIPIENT || '<EMAIL>',
    };
  } catch (error) {
    console.error("Error getting development email settings:", error);
    
    // Fallback to environment variables
    return {
      mode: process.env.EMAIL_DEV_MODE || 'redirect',
      recipient: process.env.EMAIL_DEV_RECIPIENT || '<EMAIL>',
    };
  }
}

/**
 * Add email to queue using PGMQ
 */
export async function queueEmail(data: EmailJobData) {
  try {
    const supabase = await createServiceClient();
    
    const {
      emailType,
      recipient,
      subject,
      content,
      entityType,
      entityId,
      priority = 3,
      scheduledFor,
      metadata,
      userId,
      replyTo,
      announcementId,
      pollId,
    } = data;

    // Check development mode and test users
    const isDev = process.env.NODE_ENV !== 'production';
    let finalRecipient = recipient;
    let devSkipped = false;
    let testMode = false;

    if (isDev) {
      const isAllowed = await isTestEmail(recipient);
      if (!isAllowed) {
        devSkipped = true;
        console.log(`[DEV] Email to ${recipient} will be skipped (not a test user).`);
      } else {
        testMode = true;
        console.log(`[DEV] Email to test recipient ${recipient} will be queued.`);
      }
    }

    // Check user preferences
    if (userId && !devSkipped) {
      console.log(`[queueEmail] Checking preferences for user ${userId}, type ${emailType}`);
      const { data: preferences } = await supabase
        .from('user_email_preferences')
        .select('enabled')
        .eq('user_id', userId)
        .eq('email_type', emailType)
        .single();

      // If the user has opted out, don't queue the email
      if (preferences && !preferences.enabled) {
        console.log(`[queueEmail] SKIPPING email for user ${userId} due to preferences.`);
        return { success: false, optedOut: true };
      }
      console.log(`[queueEmail] User preferences allow email for user ${userId}.`);
    }

    // Skip adding to the queue if in dev mode and not a test user
    if (devSkipped) {
      // Record in database for tracking
      const { data: queueResult } = await supabase
        .from('email_queue')
        .insert({
          email_type: emailType,
          recipient: recipient,
          subject: subject,
          content: content,
          entity_type: entityType,
          entity_id: entityId,
          priority: priority,
          scheduled_for: scheduledFor,
          metadata: metadata || null,
          announcement_id: announcementId,
          poll_id: pollId,
          status: 'dev_skipped',
        })
        .select('id')
        .single();

      return { 
        success: false, 
        skipped: true, 
        devMode: true,
        queueId: queueResult?.id
      };
    }

    // Prepare the message for PGMQ
    const message = {
      emailType,
      recipient: finalRecipient,
      subject,
      content,
      entityType,
      entityId,
      metadata,
      replyTo,
      announcementId,
      pollId,
      testMode,
      priority,
      scheduledFor,
      userId,
    };

    // Add to PGMQ queue
    const { data: pgmqResult, error: pgmqError } = await supabase
      .rpc('pgmq_send', {
        queue_name: 'email_queue',
        message: message
      });

    if (pgmqError) {
      console.error('Error adding message to PGMQ:', pgmqError);
      throw new Error(`PGMQ send failed: ${pgmqError.message}`);
    }

    // Also record in database for tracking
    const { data: queueResult } = await supabase
      .from('email_queue')
      .insert({
        email_type: emailType,
        recipient: finalRecipient,
        subject: subject,
        content: content,
        entity_type: entityType,
        entity_id: entityId,
        priority: priority,
        scheduled_for: scheduledFor,
        metadata: metadata || null,
        announcement_id: announcementId,
        poll_id: pollId,
        status: 'pending',
        pgmq_msg_id: pgmqResult?.msg_id,
      })
      .select('id')
      .single();

    const queueId = queueResult?.id;

    // Add audit log
    if (userId && queueId) {
      await addAuditLog({
        action: 'queue_email',
        userId,
        entityType: 'email_queue',
        entityId: queueId,
        changes: {
          emailType,
          recipient: finalRecipient,
          subject,
          entityType,
          entityId,
          announcementId,
          pollId,
          pgmqMsgId: pgmqResult?.msg_id,
        },
      });
    }

    return { success: true, queueId, messageId: pgmqResult?.msg_id };
  } catch (error) {
    console.error("Error queueing email:", error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

/**
 * Queue email using template
 */
export async function queueTemplateEmail(
  templateName: string,
  to: string,
  variables: Record<string, any>,
  options?: Partial<EmailQueueItem>
) {
  const template = await getEmailTemplate(templateName)
  
  if (!template) {
    throw new Error(`Email template '${templateName}' not found`)
  }
  
  // Replace variables in subject and content
  const subject = replaceVariables(template.subject, variables)
  const content = replaceVariables(template.html, variables)
  
  return queueEmail({
    to_email: to,
    subject,
    content,
    template_name: templateName,
    template_data: variables,
    ...options
  })
}

/**
 * Queue announcement email to multiple recipients
 */
export async function queueAnnouncementEmails(
  announcementId: string,
  recipients: string[],
  announcementData: {
    title: string
    content: string
    author: string
    importance: string
    hasAttachments?: boolean
  }
) {
  const emails = recipients.map(email => ({
    to_email: email,
    subject: `[DNSB Vakarai] ${announcementData.title}`,
    content: generateAnnouncementEmail(announcementData),
    template_name: 'announcement',
    template_data: announcementData,
    source_type: 'announcement',
    source_id: announcementId
  }))

  const supabase = createClient()

  const { data, error } = await supabase
    .from('email_queue')
    .insert(emails)
    .select()

  if (error) {
    console.error('Error queueing announcement emails:', error)
    throw error
  }

  return data
}

/**
 * Get email template with variables replaced
 */
export async function getEmailTemplate(name: string, variables?: Record<string, any>): Promise<{ subject: string; content: string } | null> {
  // First check database for custom templates
  try {
    const supabase = await createServiceClient();
    const { data: template } = await supabase
      .from('email_templates')
      .select('subject, content')
      .eq('name', name)
      .eq('is_active', true)
      .single();

    if (template && variables) {
      // Replace variables in template
      let subject = template.subject;
      let content = template.content;

      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        subject = subject.replace(regex, String(value));
        content = content.replace(regex, String(value));
      });

      return { subject, content };
    } else if (template) {
      return { subject: template.subject, content: template.content };
    }
  } catch (error) {
    console.log("No custom template found, using default");
  }

  // Fallback to hardcoded templates
  const templates: Record<string, EmailTemplate> = {
    announcement: {
      name: 'announcement',
      subject: '[DNSB Vakarai] {{title}}',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">{{title}}</h2>
          <div style="margin: 20px 0;">
            {{content}}
          </div>
          {{#hasAttachments}}
          <div style="background-color: #f3f4f6; border: 1px solid #d1d5db; border-radius: 6px; padding: 12px; margin: 20px 0;">
            <p style="margin: 0; color: #374151; font-size: 14px;">
              📎 <strong>Šis pranešimas turi priedų.</strong><br>
              Prisijunkite prie sistemos, kad galėtumėte juos peržiūrėti ir atsisiųsti.
            </p>
          </div>
          {{/hasAttachments}}
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
          <p style="color: #6b7280; font-size: 14px;">
            Pranešimą paskelbė: {{author}}<br>
            DNSB "Vakarai"
          </p>
        </div>
      `,
      variables: ['title', 'content', 'author', 'hasAttachments']
    },
    
    poll_notification: {
      name: 'poll_notification',
      subject: '[DNSB Vakarai] Naujas balsavimas: {{title}}',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">Naujas balsavimas</h2>
          <h3>{{title}}</h3>
          <div style="margin: 20px 0;">
            {{description}}
          </div>
          <div style="margin: 30px 0;">
            <a href="{{poll_url}}" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
              Balsuoti dabar
            </a>
          </div>
          <p style="color: #6b7280; font-size: 14px;">
            Balsavimas baigiasi: {{expires_at}}<br>
            DNSB "Vakarai"
          </p>
        </div>
      `,
      variables: ['title', 'description', 'poll_url', 'expires_at']
    },
    
    welcome: {
      name: 'welcome',
      subject: 'Sveiki atvykę į DNSB Vakarai',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">Sveiki atvykę!</h2>
          <p>Sveiki, {{name}}!</p>
          <p>Jūsų paskyra DNSB "Vakarai" sistemoje buvo sėkmingai sukurta.</p>
          <div style="margin: 30px 0;">
            <a href="{{login_url}}" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
              Prisijungti
            </a>
          </div>
          <p style="color: #6b7280; font-size: 14px;">
            DNSB "Vakarai"
          </p>
        </div>
      `,
      variables: ['name', 'login_url']
    }
  }
  
  const template = templates[name];
  if (!template) return null;
  
  // Apply variables if provided
  if (variables) {
    return {
      subject: replaceVariables(template.subject, variables),
      content: replaceVariables(template.html, variables)
    };
  }
  
  return {
    subject: template.subject,
    content: template.html
  };
}

/**
 * Replace variables in template string
 */
function replaceVariables(template: string, variables: Record<string, any>): string {
  let result = template
  
  Object.entries(variables).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g')
    result = result.replace(regex, String(value))
  })
  
  return result
}

/**
 * Generate announcement email HTML
 */
function generateAnnouncementEmail(data: {
  title: string
  content: string
  author: string
  importance: string
  hasAttachments?: boolean
}): string {
  const importanceColors = {
    normal: '#6b7280',
    important: '#f59e0b',
    urgent: '#ef4444'
  }

  const importanceLabels = {
    normal: '',
    important: '⚠️ Svarbu',
    urgent: '🚨 Skubu'
  }

  const color = importanceColors[data.importance as keyof typeof importanceColors] || importanceColors.normal
  const label = importanceLabels[data.importance as keyof typeof importanceLabels]

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      ${label ? `<div style="background-color: ${color}; color: white; padding: 8px 16px; border-radius: 4px; margin-bottom: 20px; font-weight: bold;">${label}</div>` : ''}
      <h2 style="color: #2563eb;">${data.title}</h2>
      <div style="margin: 20px 0; line-height: 1.6;">
        ${data.content.replace(/\n/g, '<br>')}
      </div>
      ${data.hasAttachments ? `
        <div style="background-color: #f3f4f6; border: 1px solid #d1d5db; border-radius: 6px; padding: 12px; margin: 20px 0;">
          <p style="margin: 0; color: #374151; font-size: 14px;">
            📎 <strong>Šis pranešimas turi priedų.</strong><br>
            Prisijunkite prie sistemos, kad galėtumėte juos peržiūrėti ir atsisiųsti.
          </p>
        </div>
      ` : ''}
      <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
      <p style="color: #6b7280; font-size: 14px;">
        Pranešimą paskelbė: ${data.author}<br>
        DNSB "Vakarai"
      </p>
    </div>
  `
}

/**
 * Get email queue statistics
 */
export async function getEmailQueueStats() {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('email_queue')
    .select('status')
  
  if (error) {
    throw error
  }
  
  const stats = data.reduce((acc, item) => {
    acc[item.status] = (acc[item.status] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  return {
    pending: stats.pending || 0,
    sent: stats.sent || 0,
    failed: stats.failed || 0,
    cancelled: stats.cancelled || 0,
    total: data.length
  }
}

/**
 * Cancel pending emails
 */
export async function cancelPendingEmails(sourceType: string, sourceId: string) {
  const supabase = createClient()
  
  const { error } = await supabase
    .from('email_queue')
    .update({ 
      status: 'cancelled',
      updated_at: new Date().toISOString()
    })
    .eq('source_type', sourceType)
    .eq('source_id', sourceId)
    .eq('status', 'pending')
  
  if (error) {
    throw error
  }
}
