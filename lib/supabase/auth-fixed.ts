import { createClient } from './server';
import { createServiceClient } from './server';
import bcrypt from 'bcryptjs';
import { Database } from './types';

type UserRole = 'developer' | 'super_admin' | 'editor' | 'user';

interface SignInResult {
  data?: any;
  error?: string;
}

/**
 * Custom username-based authentication for Supabase
 * This function allows users to login with their username (e.g., "31-7")
 * while maintaining compatibility with Supabase's email-based auth
 */
export async function signInWithUsername(
  username: string, 
  password: string
): Promise<SignInResult> {
  try {
    // Use service client for user lookup and management
    const supabaseAdmin = await createServiceClient();
    // Use regular client for actual authentication to ensure cookies are set
    const supabase = await createClient();
    
    // 1. Look up user by username in our custom users table
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('id, username, email, auth_user_id, password_hash, account_disabled, name')
      .eq('username', username)
      .single();

    if (userError || !user) {
      console.error('User lookup error:', userError);
      return { error: 'Invalid credentials' };
    }

    // Check if account is disabled
    if (user.account_disabled) {
      return { error: 'Account is disabled' };
    }

    const authEmail = user.email || generateAuthEmail(username);

    // 2. If user has Supabase auth, try to sign in
    if (user.auth_user_id) {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: authEmail,
        password: password
      });

      if (!error) {
        // Update last login
        await supabaseAdmin
          .from('users')
          .update({ last_login: new Date().toISOString() })
          .eq('id', user.id);

        return { data };
      }

      // FIXED: If Supabase auth fails, check database password
      console.log('Supabase auth failed, checking database password...');
      
      if (user.password_hash) {
        const isValidPassword = await bcrypt.compare(password, user.password_hash);
        
        if (isValidPassword) {
          console.log('Database password valid, updating Supabase auth...');
          
          // Update Supabase auth password to match database
          const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
            user.auth_user_id,
            { password: password }
          );
          
          if (updateError) {
            console.error('Failed to update Supabase password:', updateError);
            return { error: 'Authentication update failed' };
          }
          
          // Try signing in again with updated password
          const { data: retryData, error: retryError } = await supabase.auth.signInWithPassword({
            email: authEmail,
            password: password
          });
          
          if (retryError) {
            console.error('Failed to sign in after password update:', retryError);
            return { error: 'Authentication failed' };
          }
          
          // Update last login
          await supabaseAdmin
            .from('users')
            .update({ last_login: new Date().toISOString() })
            .eq('id', user.id);
          
          console.log('Successfully migrated password and signed in');
          return { data: retryData };
        }
      }
      
      // Neither Supabase nor database password worked
      return { error: 'Invalid credentials' };
    }

    // 3. For users without Supabase auth, verify password and create auth account
    if (!user.password_hash) {
      return { error: 'Please contact administrator to reset your password' };
    }

    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return { error: 'Invalid credentials' };
    }

    // Create Supabase auth account for this user
    console.log('Creating new Supabase auth account...');
    
    try {
      const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
        email: authEmail,
        password: password,
        email_confirm: true,
        user_metadata: {
          username: username,
          name: user.name,
          user_id: user.id
        }
      });

      if (authError) {
        // If email already exists, update the password
        if (authError.message?.includes('email address has already been registered')) {
          console.log('Auth user exists, updating password...');
          
          const { data: authUsersResult } = await supabaseAdmin.auth.admin.listUsers();
          const existingUser = authUsersResult?.users?.find((u: any) => u.email === authEmail);
          
          if (existingUser) {
            const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
              existingUser.id,
              { password: password }
            );
            
            if (updateError) {
              console.error('Failed to update password:', updateError);
              return { error: 'Authentication setup failed' };
            }
            
            // Update database with auth_user_id
            await supabaseAdmin
              .from('users')
              .update({ 
                auth_user_id: existingUser.id,
                last_login: new Date().toISOString()
              })
              .eq('id', user.id);
            
            // Sign in
            const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
              email: authEmail,
              password: password
            });
            
            if (signInError) {
              return { error: 'Authentication failed' };
            }
            
            return { data: signInData };
          }
        }
        
        console.error('Failed to create auth user:', authError);
        return { error: 'Authentication setup failed' };
      }

      if (authData?.user) {
        // Update database with auth_user_id
        await supabaseAdmin
          .from('users')
          .update({ 
            auth_user_id: authData.user.id,
            last_login: new Date().toISOString()
          })
          .eq('id', user.id);
        
        // Sign in with the newly created account
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: authEmail,
          password: password
        });
        
        if (signInError) {
          console.error('Failed to sign in with new account:', signInError);
          return { error: 'Authentication failed' };
        }
        
        return { data: signInData };
      }
    } catch (error) {
      console.error('Unexpected error during auth creation:', error);
      return { error: 'Authentication setup failed' };
    }

    return { error: 'Authentication failed' };
  } catch (error) {
    console.error('Sign in error:', error);
    return { error: 'Authentication failed' };
  }
}

/**
 * Generate a dummy email for internal Supabase auth
 * Uses @dnsb.local domain to ensure no real emails are sent
 */
export function generateAuthEmail(username: string): string {
  // Replace hyphens with underscores and lowercase
  // "31-7" becomes "<EMAIL>"
  return `${username.toLowerCase().replace(/-/g, '_')}@dnsb.local`;
}

/**
 * Get the current user session with full user data
 */
export async function getCurrentUser() {
  const supabase = await createClient();
  
  // Use getUser() instead of getSession() for server-side auth checks
  const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !authUser) {
    return null;
  }

  // Use service client to bypass RLS when getting user data
  const supabaseAdmin = await createServiceClient();
  
  // Get full user data from our custom users table
  const { data: user, error: userError } = await supabaseAdmin
    .from('users')
    .select('*')
    .eq('auth_user_id', authUser.id)
    .single();

  if (userError || !user) {
    console.error('Failed to get user data:', userError);
    return null;
  }

  return {
    ...authUser,
    ...user,
    email: user.email || authUser.email // Prefer real email over dummy
  };
}

/**
 * Sign out the current user
 */
export async function signOut() {
  const supabase = await createClient();
  return await supabase.auth.signOut();
}

/**
 * Check if user is authenticated
 */
export async function isAuthenticated(): Promise<boolean> {
  const user = await getCurrentUser();
  return !!user;
}

/**
 * Check if user has admin role
 */
export async function isAdmin(): Promise<boolean> {
  const user = await getCurrentUser();
  return user ? ['developer', 'super_admin', 'editor'].includes(user.role) : false;
}

/**
 * Update user profile
 */
export async function updateProfile(userId: string, updates: any) {
  const supabase = await createClient();
  
  return await supabase
    .from('users')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', userId);
}