// This file will be auto-generated by Supabase CLI
// For now, we'll create a basic type structure

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          username: string;
          name: string;
          email: string | null;
          phone: string | null;
          street: string | null;
          house_number: string | null;
          flat_number: string | null;
          role: 'developer' | 'super_admin' | 'editor' | 'user';
          is_profile_updated: boolean;
          gdpr_consent_given: boolean;
          gdpr_consent_date: string | null;
          account_disabled: boolean;
          account_disabled_date: string | null;
          account_disabled_reason: string | null;
          password_hash?: string;
          password_reset_token: string | null;
          password_reset_expires: string | null;
          magic_link_token: string | null;
          magic_link_expires: string | null;
          is_test_user: boolean;
          import_source: string | null;
          import_date: string | null;
          flat_id: string | null;
          created_at: string;
          updated_at: string;
          last_login: string | null;
          email_verified: string | null;
          auth_user_id: string | null;
        };
        Insert: Omit<Database['public']['Tables']['users']['Row'], 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Database['public']['Tables']['users']['Insert']>;
      };
      // Add other tables as needed
    };
    Views: {};
    Functions: {};
    Enums: {};
  };
}