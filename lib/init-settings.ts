import { createServiceClient } from "@/lib/supabase/server";

/**
 * Initialize default settings in the admin_settings table
 */
export async function initializeDefaultSettings() {
  try {
    const supabase = await createServiceClient();
    
    // Default settings to create
    const defaultSettings = [
      // SMTP Settings
      { key: 'smtp_host', value: '', description: 'SMTP server hostname' },
      { key: 'smtp_port', value: '587', description: 'SMTP server port' },
      { key: 'smtp_user', value: '', description: 'SMTP username' },
      { key: 'smtp_password', value: '', description: 'SMTP password' },
      { key: 'smtp_from', value: '<EMAIL>', description: 'Default from email address' },
      { key: 'smtp_reply_to', value: '', description: 'Default reply-to email address' },
      { key: 'smtp_secure', value: 'false', description: 'Use SSL/TLS for SMTP connection' },
      { key: 'smtp_enabled', value: 'false', description: 'Enable/disable email sending' },
      
      // Phone Settings
      { key: 'phone_emergency', value: '', description: 'Emergency contact phone number' },
      { key: 'phone_police', value: '', description: 'Police contact phone number' },
      { key: 'phone_fire', value: '', description: 'Fire department phone number' },
      { key: 'phone_ambulance', value: '', description: 'Ambulance phone number' },
      { key: 'phone_gas', value: '', description: 'Gas emergency phone number' },
      { key: 'phone_electricity', value: '', description: 'Electricity emergency phone number' },
      { key: 'phone_water', value: '', description: 'Water emergency phone number' },
      { key: 'phone_heating', value: '', description: 'Heating emergency phone number' },
      { key: 'phone_management', value: '', description: 'Building management phone number' },
      { key: 'phone_security', value: '', description: 'Security phone number' },
      
      // Email Development Settings
      { key: 'email_dev_mode', value: 'redirect', description: 'How to handle emails in development mode' },
      { key: 'email_dev_recipient', value: '', description: 'Email address to redirect all non-test emails to in development mode' },
      
      // Test Email Configuration
      { 
        key: 'test_email_config', 
        value: JSON.stringify({
          enabled: true,
          addresses: [],
          domains: [],
          streetPatterns: ['Debreceno']
        }), 
        description: 'Configuration for test emails in development environment' 
      },
    ];
    
    console.log('Initializing default settings...');
    
    // Insert settings using upsert to avoid conflicts
    for (const setting of defaultSettings) {
      const { error } = await supabase
        .from('admin_settings')
        .upsert({
          key: setting.key,
          value: setting.value,
          description: setting.description,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'key'
        });
      
      if (error) {
        console.error(`Error creating setting ${setting.key}:`, error);
      } else {
        console.log(`✓ Setting ${setting.key} initialized`);
      }
    }
    
    console.log('Default settings initialization complete');
    return true;
  } catch (error) {
    console.error('Error initializing default settings:', error);
    return false;
  }
}

/**
 * Check if settings are initialized
 */
export async function areSettingsInitialized(): Promise<boolean> {
  try {
    const supabase = await createServiceClient();
    const { data: settings, error } = await supabase
      .from('admin_settings')
      .select('key')
      .limit(1);
    
    if (error) {
      console.error('Error checking settings:', error);
      return false;
    }
    
    return (settings?.length || 0) > 0;
  } catch (error) {
    console.error('Error checking if settings are initialized:', error);
    return false;
  }
}
