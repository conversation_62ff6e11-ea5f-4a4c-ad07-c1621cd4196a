import pino from 'pino';
import { CDNError } from '@/lib/services/bunnycdn.service';

const logger = pino({ name: 'cdn-monitor' });

// Metric types
interface Histogram {
  startTimer: () => (labels?: Record<string, string>) => void;
}

interface Counter {
  inc: (labels?: Record<string, string>) => void;
}

interface Gauge {
  inc: () => void;
  dec: () => void;
  set: (value: number) => void;
}

// Simple in-memory metrics implementation
class SimpleHistogram implements Histogram {
  private name: string;
  private values: number[] = [];

  constructor(name: string) {
    this.name = name;
  }

  startTimer() {
    const start = Date.now();
    return (labels?: Record<string, string>) => {
      const duration = Date.now() - start;
      this.values.push(duration);
      logger.debug({ metric: this.name, duration, labels }, 'Histogram metric recorded');
    };
  }

  getStats() {
    if (this.values.length === 0) return null;
    
    const sorted = [...this.values].sort((a, b) => a - b);
    const sum = sorted.reduce((a, b) => a + b, 0);
    
    return {
      count: sorted.length,
      mean: sum / sorted.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      p50: sorted[Math.floor(sorted.length * 0.5)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)],
    };
  }
}

class SimpleCounter implements Counter {
  private name: string;
  private counts = new Map<string, number>();

  constructor(name: string) {
    this.name = name;
  }

  inc(labels?: Record<string, string>) {
    const key = labels ? JSON.stringify(labels) : 'default';
    const current = this.counts.get(key) || 0;
    this.counts.set(key, current + 1);
    logger.debug({ metric: this.name, count: current + 1, labels }, 'Counter incremented');
  }

  getValues() {
    return Array.from(this.counts.entries()).map(([key, value]) => ({
      labels: key === 'default' ? {} : JSON.parse(key),
      value,
    }));
  }
}

class SimpleGauge implements Gauge {
  private name: string;
  private value: number = 0;

  constructor(name: string) {
    this.name = name;
  }

  inc() {
    this.value++;
    logger.debug({ metric: this.name, value: this.value }, 'Gauge incremented');
  }

  dec() {
    this.value--;
    logger.debug({ metric: this.name, value: this.value }, 'Gauge decremented');
  }

  set(value: number) {
    this.value = value;
    logger.debug({ metric: this.name, value: this.value }, 'Gauge set');
  }

  getValue() {
    return this.value;
  }
}

// Alert severity levels
export enum AlertSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

// Alert interface
export interface Alert {
  severity: AlertSeverity;
  title: string;
  description: string;
  context?: Record<string, any>;
  timestamp?: Date;
}

// Simple alerting service
class AlertingService {
  async sendAlert(alert: Alert): Promise<void> {
    // In production, this would send to PagerDuty, Slack, etc.
    logger.error(
      {
        severity: alert.severity,
        title: alert.title,
        description: alert.description,
        context: alert.context,
        timestamp: alert.timestamp || new Date(),
      },
      'ALERT: CDN issue detected'
    );

    // TODO: Integrate with actual alerting service
    // Example: await sendToSlack(alert);
    // Example: await sendToPagerDuty(alert);
  }
}

/**
 * CDN monitoring service
 */
export class CDNMonitor {
  private static instance: CDNMonitor;
  
  private metrics = {
    uploadLatency: new SimpleHistogram('cdn_upload_latency_ms'),
    downloadLatency: new SimpleHistogram('cdn_download_latency_ms'),
    uploadErrors: new SimpleCounter('cdn_upload_errors_total'),
    downloadErrors: new SimpleCounter('cdn_download_errors_total'),
    storageUsage: new SimpleGauge('cdn_storage_usage_bytes'),
    activeUploads: new SimpleGauge('cdn_active_uploads'),
    bandwidthUsage: new SimpleGauge('cdn_bandwidth_usage_bytes'),
  };

  private alerting = new AlertingService();
  
  // Error tracking
  private recentErrors: Array<{ timestamp: Date; error: unknown }> = [];
  private errorWindowMs = 5 * 60 * 1000; // 5 minutes
  private criticalErrorThreshold = 10; // 10 errors in 5 minutes triggers critical alert

  private constructor() {
    // Start periodic cleanup of old errors
    setInterval(() => this.cleanupOldErrors(), 60 * 1000); // Every minute
  }

  static getInstance(): CDNMonitor {
    if (!CDNMonitor.instance) {
      CDNMonitor.instance = new CDNMonitor();
    }
    return CDNMonitor.instance;
  }

  /**
   * Track an upload operation
   */
  async trackUpload<T>(operation: () => Promise<T>): Promise<T> {
    const timer = this.metrics.uploadLatency.startTimer();
    this.metrics.activeUploads.inc();

    try {
      const result = await operation();
      timer({ status: 'success' });
      return result;
    } catch (error) {
      timer({ status: 'error' });
      
      const errorCode = error instanceof CDNError ? error.code : 'unknown';
      this.metrics.uploadErrors.inc({ error: errorCode });
      
      await this.handleError(error, 'upload');
      throw error;
    } finally {
      this.metrics.activeUploads.dec();
    }
  }

  /**
   * Track a download operation
   */
  async trackDownload<T>(operation: () => Promise<T>): Promise<T> {
    const timer = this.metrics.downloadLatency.startTimer();

    try {
      const result = await operation();
      timer({ status: 'success' });
      return result;
    } catch (error) {
      timer({ status: 'error' });
      
      const errorCode = error instanceof CDNError ? error.code : 'unknown';
      this.metrics.downloadErrors.inc({ error: errorCode });
      
      await this.handleError(error, 'download');
      throw error;
    }
  }

  /**
   * Update storage usage metric
   */
  updateStorageUsage(bytes: number): void {
    this.metrics.storageUsage.set(bytes);
  }

  /**
   * Update bandwidth usage metric
   */
  updateBandwidthUsage(bytes: number): void {
    this.metrics.bandwidthUsage.set(bytes);
  }

  /**
   * Get current metrics
   */
  getMetrics() {
    return {
      uploadLatency: (this.metrics.uploadLatency as SimpleHistogram).getStats(),
      downloadLatency: (this.metrics.downloadLatency as SimpleHistogram).getStats(),
      uploadErrors: (this.metrics.uploadErrors as SimpleCounter).getValues(),
      downloadErrors: (this.metrics.downloadErrors as SimpleCounter).getValues(),
      storageUsage: (this.metrics.storageUsage as SimpleGauge).getValue(),
      activeUploads: (this.metrics.activeUploads as SimpleGauge).getValue(),
      bandwidthUsage: (this.metrics.bandwidthUsage as SimpleGauge).getValue(),
    };
  }

  /**
   * Handle errors and determine if alerting is needed
   */
  private async handleError(error: unknown, operation: string): Promise<void> {
    // Track error
    this.recentErrors.push({ timestamp: new Date(), error });
    
    // Check if this is a critical error
    if (this.isCriticalError(error)) {
      await this.alerting.sendAlert({
        severity: AlertSeverity.CRITICAL,
        title: `Critical CDN ${operation} Failure`,
        description: error.message || 'Unknown error',
        context: {
          error: error instanceof CDNError ? {
            code: error.code,
            statusCode: error.statusCode,
            context: error.context,
          } : { message: (error as Error).message },
          operation,
        },
      });
      return;
    }
    
    // Check error rate
    const recentErrorCount = this.getRecentErrorCount();
    if (recentErrorCount >= this.criticalErrorThreshold) {
      await this.alerting.sendAlert({
        severity: AlertSeverity.ERROR,
        title: `High CDN Error Rate`,
        description: `${recentErrorCount} errors in the last 5 minutes`,
        context: {
          operation,
          threshold: this.criticalErrorThreshold,
          recentErrors: this.recentErrors.slice(-5).map(e => ({
            timestamp: e.timestamp,
            message: (e.error as Error).message || 'Unknown error',
          })),
        },
      });
    }
  }

  /**
   * Determine if an error is critical
   */
  private isCriticalError(error: unknown): boolean {
    if (error instanceof CDNError) {
      // Authentication errors are critical
      if (error.statusCode === 401 || error.statusCode === 403) {
        return true;
      }
      
      // Service unavailable is critical
      if (error.statusCode === 503) {
        return true;
      }
      
      // Specific error codes that are critical
      const criticalCodes = ['AUTH_FAILED', 'SERVICE_DOWN', 'QUOTA_EXCEEDED'];
      if (criticalCodes.includes(error.code)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * Get count of recent errors
   */
  private getRecentErrorCount(): number {
    const cutoff = new Date(Date.now() - this.errorWindowMs);
    return this.recentErrors.filter(e => e.timestamp > cutoff).length;
  }

  /**
   * Clean up old errors from memory
   */
  private cleanupOldErrors(): void {
    const cutoff = new Date(Date.now() - this.errorWindowMs);
    this.recentErrors = this.recentErrors.filter(e => e.timestamp > cutoff);
  }
}

// Export singleton instance
export const cdnMonitor = CDNMonitor.getInstance();