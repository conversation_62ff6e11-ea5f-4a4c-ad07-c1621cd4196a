import { createServiceClient } from "@/lib/supabase/server";

/**
 * Configuration for test email addresses
 */
export interface TestEmailConfig {
  enabled: boolean;          // Whether test emails are enabled
  addresses: string[];       // List of test email addresses
  domains: string[];         // List of test email domains
  streetPatterns: string[];  // Street name patterns for test users
}

/**
 * Get test email configuration from the database
 */
export async function getTestEmailConfig(): Promise<TestEmailConfig> {
  try {
    const supabase = await createServiceClient();
    const { data: setting, error } = await supabase
      .from('admin_settings')
      .select('value')
      .eq('key', 'test_email_config')
      .single();

    if (!error && setting) {
      try {
        return JSON.parse(setting.value);
      } catch (e) {
        console.error("[getTestEmailConfig] Error parsing JSON from DB:", e);
        // Continue to fallback if parse fails
      }
    }

    // DB value missing or parse failed -> Fallback
    console.log("[getTestEmailConfig] DB value missing or parse failed, using fallback from environment variables.");
    return {
      enabled: process.env.ALLOW_TEST_EMAILS === 'true',
      addresses: process.env.TEST_EMAIL_ADDRESSES ? process.env.TEST_EMAIL_ADDRESSES.split(',') : [],
      domains: process.env.TEST_EMAIL_DOMAINS ? process.env.TEST_EMAIL_DOMAINS.split(',') : [],
      streetPatterns: ['Debreceno'],
    };
  } catch (error) {
    console.error("Error fetching test email config:", error);
    
    // Fallback to environment variables
    return {
      enabled: process.env.ALLOW_TEST_EMAILS === 'true',
      addresses: process.env.TEST_EMAIL_ADDRESSES ? process.env.TEST_EMAIL_ADDRESSES.split(',') : [],
      domains: process.env.TEST_EMAIL_DOMAINS ? process.env.TEST_EMAIL_DOMAINS.split(',') : [],
      streetPatterns: ['Debreceno'],
    };
  }
}

/**
 * Check if an email address belongs to a test user
 */
export async function isTestEmail(email: string): Promise<boolean> {
  console.log(`[isTestEmail] Checking email: ${email}`);
  // Get the test email configuration
  const config = await getTestEmailConfig();
  console.log(`[isTestEmail] Using config:`, JSON.stringify(config, null, 2));
  
  // If test emails are not enabled, return false
  if (!config.enabled) {
    console.log(`[isTestEmail] Result for ${email}: false (config disabled)`);
    return false;
  }
  
  // Check if the email is in the test addresses list
  if (config.addresses.includes(email)) {
    console.log(`[isTestEmail] Result for ${email}: true (address match)`);
    return true;
  }
  
  // Check if the email domain is in the test domains list
  const domain = email.split('@')[1];
  if (domain && config.domains.includes(domain)) {
    console.log(`[isTestEmail] Result for ${email}: true (domain match)`);
    return true;
  }
  
  // Check if the email is associated with a user marked as test_user=true or on a test street
  try {
    console.log(`[isTestEmail] Checking database for user: ${email}`);
    // Check test_user flag first (modified query)
    const supabase = await createServiceClient();
    const { data: testUserData, error: testUserError } = await supabase
      .from('users')
      .select('id, test_user')
      .ilike('email', email)
      .single();

    console.log(`[isTestEmail] User query for ${email} result:`, testUserData);

    if (!testUserError && testUserData) {
      console.log(`[isTestEmail] Found user ${testUserData.id} for ${email}, test_user value: ${testUserData.test_user}`);
      // Now check the retrieved boolean value
      if (testUserData.test_user === true) {
        console.log(`[isTestEmail] Result for ${email}: true (user flag match)`);
        return true;
      }
    }
    // If no rows found OR test_user is not true
    console.log(`[isTestEmail] User flag check for ${email} did not match (or user not found by email).`);

    // Check street patterns if user flag didn't match
    if (config.streetPatterns.length > 0) {
      // For Supabase, we need to check each pattern individually due to limitations with complex joins
      for (const pattern of config.streetPatterns) {
        const { data: streetUserData, error: streetError } = await supabase
          .from('users')
          .select(`
            id,
            flats!inner(
              id,
              houses!inner(
                id,
                streets!inner(
                  name
                )
              )
            )
          `)
          .eq('email', email)
          .ilike('flats.houses.streets.name', `%${pattern}%`)
          .single();

        if (!streetError && streetUserData) {
          console.log(`[isTestEmail] Result for ${email}: true (street pattern match)`);
          return true;
        }
      }
      console.log(`[isTestEmail] Street pattern check for ${email} did not match.`);
    } else {
      console.log(`[isTestEmail] No street patterns configured to check for ${email}.`);
    }

  } catch (error) {
    console.error(`[isTestEmail] Error checking database for ${email}:`, error);
  }
  
  console.log(`[isTestEmail] Result for ${email}: false (no match found)`);
  return false;
}

/**
 * Check if a user is a test user based on their ID
 */
export async function isTestUser(userId: string | number): Promise<boolean> {
  try {
    // Get the user's email and test status
    const supabase = await createServiceClient();
    const { data: user, error } = await supabase
      .from('users')
      .select('email, test_user')
      .eq('id', userId)
      .single();

    if (error || !user) {
      return false;
    }
    
    // If test_user flag is set to true, this is a test user
    if (user.test_user === true) {
      return true;
    }
    
    // Check if the user's email is a test email
    if (await isTestEmail(user.email)) {
      return true;
    }
    
    // For street patterns, we would need to do a more complex query with joins
    // For now, we'll rely on the email check and test_user flag
    // TODO: Implement street pattern check if needed
    
    return false;
  } catch (error) {
    console.error("Error checking if user is a test user:", error);
    return false;
  }
} 