import nodemailer from "nodemailer";
import { createServiceClient } from "@/lib/supabase/server";
import { isTestEmail } from "@/lib/email-utils";

type EmailPayload = {
  to: string;
  subject: string;
  html: string;
  replyTo?: string;
};

/**
 * Get SMTP settings from database
 */
async function getSmtpSettings() {
  try {
    const supabase = await createServiceClient();
    const { data: settings, error } = await supabase
      .from('admin_settings')
      .select('key, value')
      .like('key', 'smtp_%');

    if (error) {
      console.error("Error fetching SMTP settings:", error);
      throw error;
    }

    // Convert to an object
    const settingsObj: Record<string, string> = {};
    settings?.forEach(row => {
      settingsObj[row.key] = row.value;
    });

    return {
      host: settingsObj.smtp_host || process.env.SMTP_HOST || "smtp.example.com",
      port: parseInt(settingsObj.smtp_port || process.env.SMTP_PORT || "587", 10),
      secure: settingsObj.smtp_secure === "true" || process.env.SMTP_SECURE === "true",
      auth: {
        user: settingsObj.smtp_user || process.env.SMTP_USER || "",
        pass: settingsObj.smtp_password || process.env.SMTP_PASSWORD || "",
      },
      from: settingsObj.smtp_from || process.env.SMTP_FROM || "<EMAIL>",
      replyTo: settingsObj.smtp_reply_to || process.env.SMTP_REPLY_TO || "",
      enabled: settingsObj.smtp_enabled === "true" || process.env.SMTP_ENABLED === "true",
    };
  } catch (error) {
    console.error("Error getting SMTP settings from database:", error);
    
    // Fallback to environment variables
    return {
      host: process.env.SMTP_HOST || "smtp.example.com",
      port: parseInt(process.env.SMTP_PORT || "587", 10),
      secure: process.env.SMTP_SECURE === "true",
      auth: {
        user: process.env.SMTP_USER || "",
        pass: process.env.SMTP_PASSWORD || "",
      },
      from: process.env.SMTP_FROM || "<EMAIL>",
      replyTo: process.env.SMTP_REPLY_TO || "",
      enabled: process.env.SMTP_ENABLED === "true",
    };
  }
}

export const sendEmail = async (data: EmailPayload) => {
  // Get SMTP settings from database
  const smtpSettings = await getSmtpSettings();
  
  // Check if SMTP settings are valid and enabled
  const isConfigIncomplete = 
    !smtpSettings.host || 
    smtpSettings.host === "smtp.example.com" || 
    !smtpSettings.auth.user ||
    !smtpSettings.auth.pass;
  
  const isDisabled = !smtpSettings.enabled;
  
  if (isConfigIncomplete || isDisabled) {
    const reason = isDisabled 
      ? "Email sending is disabled in settings" 
      : "SMTP configuration is incomplete";
    
    console.warn(`${reason}. Email sending will be skipped.`);
    return { 
      success: false, 
      skipped: true,
      error: reason
    };
  }
  
  // Configure transporter
  const transporter = nodemailer.createTransport({
    host: smtpSettings.host,
    port: smtpSettings.port,
    secure: smtpSettings.secure,
    auth: {
      user: smtpSettings.auth.user,
      pass: smtpSettings.auth.pass,
    },
  });
  
  const { to, subject, html, replyTo } = data;
  
  const emailDefaults = {
    from: {
      name: "DNSB Vakarai",
      address: smtpSettings.from,
    },
    ...(replyTo ? { replyTo } : (smtpSettings.replyTo ? { replyTo: smtpSettings.replyTo } : {})),
  };

  try {
    // Log the attempt in development mode
    if (process.env.NODE_ENV !== 'production') {
      const isTestEmailAddress = await isTestEmail(to);
      
      if (isTestEmailAddress) {
        console.log(`[DEV] Sending email to TEST recipient ${to} with subject "${subject}"`);
      } else {
        console.log(`[DEV] Attempting to send email to ${to} with subject "${subject}"`);
      }
    }
    
    const info = await transporter.sendMail({
      ...emailDefaults,
      to,
      subject,
      html,
    });

    console.log("Email sent successfully:", info.messageId);
    
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error("Error sending email:", error);
    
    // Log the error but don't throw an exception
    return { 
      success: false, 
      error: error instanceof Error ? error.message : String(error) 
    };
  }
}; 