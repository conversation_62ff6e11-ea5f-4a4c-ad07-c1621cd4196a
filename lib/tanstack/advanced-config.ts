'use client';

import { Query<PERSON><PERSON>, Query<PERSON><PERSON>, focus<PERSON>anager, onlineManager } from '@tanstack/react-query';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';
import { persistQueryClient } from '@tanstack/react-query-persist-client';

/**
 * Advanced TanStack Query configuration for production
 * Includes offline support, persistence, and optimized caching
 */

// Enhanced query client with production optimizations
export const createAdvancedQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Stale-while-revalidate strategy
        staleTime: 1000 * 60 * 5, // 5 minutes
        gcTime: 1000 * 60 * 30, // 30 minutes cache time
        
        // Network optimizations
        networkMode: 'offlineFirst', // Work offline when possible
        refetchOnWindowFocus: true,
        refetchOnReconnect: true,
        refetchOnMount: true,
        
        // Retry strategy for different scenarios
        retry: (failureCount, error: any) => {
          // Don't retry auth errors
          if (error?.status === 401 || error?.status === 403) {
            return false;
          }
          
          // Don't retry client errors (4xx)
          if (error?.status >= 400 && error?.status < 500) {
            return false;
          }
          
          // Retry server errors and network failures up to 3 times
          return failureCount < 3;
        },
        
        // Exponential backoff for retries
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        
        // Optimized refetch intervals
        refetchInterval: false, // We use real-time subscriptions instead
        refetchIntervalInBackground: false,
      },
      
      mutations: {
        // Network mode for mutations
        networkMode: 'online', // Mutations require network
        
        // Retry mutations
        retry: (failureCount, error: any) => {
          // Don't retry client errors
          if (error?.status >= 400 && error?.status < 500) {
            return false;
          }
          
          // Retry server errors
          return failureCount < 2;
        },
        
        // Exponential backoff for mutation retries
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
      },
    },
  });
};

/**
 * Offline support configuration
 */
export const setupOfflineSupport = (queryClient: QueryClient) => {
  // Configure focus manager for better tab/window focus handling
  focusManager.setEventListener((handleFocus) => {
    if (typeof window !== 'undefined' && window.addEventListener) {
      const handleVisibilityChange = () => {
        handleFocus(document.visibilityState === 'visible');
      };
      
      document.addEventListener('visibilitychange', handleVisibilityChange);
      window.addEventListener('focus', () => handleFocus(true));
      
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        window.removeEventListener('focus', () => handleFocus(true));
      };
    }
  });

  // Configure online manager
  onlineManager.setEventListener((setOnline) => {
    if (typeof window !== 'undefined' && window.addEventListener) {
      const handleOnline = () => setOnline(true);
      const handleOffline = () => setOnline(false);
      
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);
      
      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }
  });
};

/**
 * Data persistence for offline support
 */
export const setupQueryPersistence = (queryClient: QueryClient) => {
  if (typeof window === 'undefined') return;

  const persister = createSyncStoragePersister({
    storage: window.localStorage,
    key: 'dnsb-vakarai-query-cache',
    
    // Only persist important queries
    serialize: (data) => {
      return JSON.stringify({
        ...data,
        clientState: {
          ...data.clientState,
          queries: data.clientState.queries.filter((query: any) => {
            const queryKey = query.queryKey as QueryKey;
            const key = queryKey[0] as string;
            
            // Persist user session, announcements, polls, and housing data
            return [
              'user',
              'announcements', 
              'polls',
              'housing',
              'tags',
              'emergencyContacts'
            ].includes(key);
          })
        }
      });
    },
    
    deserialize: JSON.parse,
  });

  persistQueryClient({
    queryClient,
    persister,
    maxAge: 1000 * 60 * 60 * 24, // 24 hours
    hydrateOptions: {
      // Don't hydrate stale data
      defaultOptions: {
        queries: {
          staleTime: 1000 * 60 * 5, // 5 minutes
        },
      },
    },
  });
};

/**
 * Advanced cache strategies
 */
export const cacheStrategies = {
  // High-frequency data (real-time updates)
  realtime: {
    staleTime: 1000 * 30, // 30 seconds
    gcTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  },
  
  // Medium-frequency data (user interactions)
  interactive: {
    staleTime: 1000 * 60 * 2, // 2 minutes
    gcTime: 1000 * 60 * 15, // 15 minutes
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  },
  
  // Low-frequency data (configuration, lookup data)
  static: {
    staleTime: 1000 * 60 * 30, // 30 minutes
    gcTime: 1000 * 60 * 60, // 1 hour
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  },
  
  // User-specific data (profiles, preferences)
  user: {
    staleTime: 1000 * 60 * 10, // 10 minutes
    gcTime: 1000 * 60 * 60, // 1 hour
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  },
};

/**
 * Background sync for offline actions
 */
export class OfflineActionQueue {
  private queue: Array<{
    id: string;
    action: () => Promise<any>;
    type: 'vote' | 'create_announcement' | 'create_poll';
    data: any;
    timestamp: number;
  }> = [];

  constructor(private queryClient: QueryClient) {
    this.setupOnlineListener();
  }

  // Add action to queue when offline
  enqueue(action: () => Promise<any>, type: string, data: any) {
    const id = `${type}-${Date.now()}-${Math.random()}`;
    
    this.queue.push({
      id,
      action,
      type: type as any,
      data,
      timestamp: Date.now(),
    });

    // Try to execute if online
    if (onlineManager.isOnline()) {
      this.processQueue();
    }

    return id;
  }

  // Process queued actions when back online
  private async processQueue() {
    if (!onlineManager.isOnline() || this.queue.length === 0) return;

    const actionsToProcess = [...this.queue];
    this.queue = [];

    for (const item of actionsToProcess) {
      try {
        await item.action();
        console.log(`Offline action ${item.id} synced successfully`);
      } catch (error) {
        console.error(`Failed to sync offline action ${item.id}:`, error);
        
        // Re-queue if it's a temporary error
        if (this.isRetryableError(error)) {
          this.queue.push(item);
        }
      }
    }
  }

  private isRetryableError(error: any): boolean {
    // Retry server errors but not client errors
    return error?.status >= 500 || !error?.status;
  }

  private setupOnlineListener() {
    const cleanup = onlineManager.subscribe((isOnline) => {
      if (isOnline) {
        console.log('Back online, processing queued actions...');
        this.processQueue();
      }
    });

    // Return cleanup function
    return cleanup;
  }

  // Get queue status
  getQueueStatus() {
    return {
      length: this.queue.length,
      items: this.queue.map(item => ({
        id: item.id,
        type: item.type,
        timestamp: item.timestamp,
      })),
    };
  }
}

/**
 * Performance monitoring helpers
 */
export const queryPerformanceMonitor = {
  // Track query performance
  trackQuery: (queryKey: QueryKey, startTime: number) => {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    if (duration > 1000) { // Log slow queries
      console.warn(`Slow query detected:`, {
        queryKey,
        duration: `${duration.toFixed(2)}ms`,
      });
    }
    
    // Send to analytics if configured
    if (typeof window !== 'undefined' && (window as any).analytics) {
      (window as any).analytics.track('Query Performance', {
        queryKey: JSON.stringify(queryKey),
        duration,
        slow: duration > 1000,
      });
    }
  },

  // Track cache hit/miss rates
  trackCacheHit: (queryKey: QueryKey, fromCache: boolean) => {
    if (typeof window !== 'undefined' && (window as any).analytics) {
      (window as any).analytics.track('Cache Performance', {
        queryKey: JSON.stringify(queryKey),
        cacheHit: fromCache,
      });
    }
  },
};