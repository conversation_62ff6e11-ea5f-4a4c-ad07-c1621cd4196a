'use client';

import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useEffect, useCallback } from 'react';
import { queryKeys } from './query-client';
import { cacheStrategies } from './advanced-config';

/**
 * Intelligent prefetching system for better navigation performance
 * Preloads data before users navigate to reduce loading times
 */

export function usePrefetching() {
  const queryClient = useQueryClient();
  
  /**
   * Prefetch announcement details when hovering over announcement cards
   */
  const prefetchAnnouncement = useCallback(async (announcementId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.announcements.detail(announcementId),
      queryFn: async () => {
        const response = await fetch(`/api/announcements/${announcementId}`);
        if (!response.ok) throw new Error('Failed to fetch announcement');
        return response.json();
      },
      ...cacheStrategies.interactive,
    });
  }, [queryClient]);

  /**
   * Prefetch poll details and responses
   */
  const prefetchPoll = useCallback(async (pollId: string) => {
    // Prefetch poll details
    const pollPromise = queryClient.prefetchQuery({
      queryKey: queryKeys.polls.detail(pollId),
      queryFn: async () => {
        const response = await fetch(`/api/polls?id=${pollId}`);
        if (!response.ok) throw new Error('Failed to fetch poll');
        const data = await response.json();
        return data[0];
      },
      ...cacheStrategies.interactive,
    });

    // Prefetch poll responses
    const responsesPromise = queryClient.prefetchQuery({
      queryKey: queryKeys.polls.responses(pollId),
      queryFn: async () => {
        const response = await fetch(`/api/polls/responses?pollId=${pollId}`);
        if (!response.ok) throw new Error('Failed to fetch poll responses');
        return response.json();
      },
      ...cacheStrategies.realtime,
    });

    // Prefetch poll stats
    const statsPromise = queryClient.prefetchQuery({
      queryKey: queryKeys.polls.stats(pollId),
      queryFn: async () => {
        const response = await fetch(`/api/polls/stats?pollId=${pollId}`);
        if (!response.ok) throw new Error('Failed to fetch poll stats');
        return response.json();
      },
      ...cacheStrategies.realtime,
    });

    await Promise.all([pollPromise, responsesPromise, statsPromise]);
  }, [queryClient]);

  /**
   * Prefetch user profile data
   */
  const prefetchUserProfile = useCallback(async (userId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.user.profile(userId),
      queryFn: async () => {
        const response = await fetch(`/api/users/${userId}`);
        if (!response.ok) throw new Error('Failed to fetch user profile');
        return response.json();
      },
      ...cacheStrategies.user,
    });
  }, [queryClient]);

  /**
   * Prefetch housing data (streets, houses, flats)
   */
  const prefetchHousingData = useCallback(async () => {
    const streetsPromise = queryClient.prefetchQuery({
      queryKey: queryKeys.housing.streets(),
      queryFn: async () => {
        const response = await fetch('/api/streets');
        if (!response.ok) throw new Error('Failed to fetch streets');
        return response.json();
      },
      ...cacheStrategies.static,
    });

    const housesPromise = queryClient.prefetchQuery({
      queryKey: queryKeys.housing.houses(),
      queryFn: async () => {
        const response = await fetch('/api/houses');
        if (!response.ok) throw new Error('Failed to fetch houses');
        return response.json();
      },
      ...cacheStrategies.static,
    });

    await Promise.all([streetsPromise, housesPromise]);
  }, [queryClient]);

  /**
   * Prefetch admin data
   */
  const prefetchAdminData = useCallback(async () => {
    const messagesPromise = queryClient.prefetchQuery({
      queryKey: queryKeys.admin.messages(),
      queryFn: async () => {
        const response = await fetch('/api/admin/messages');
        if (!response.ok) throw new Error('Failed to fetch admin messages');
        return response.json();
      },
      ...cacheStrategies.interactive,
    });

    const recentMessagesPromise = queryClient.prefetchQuery({
      queryKey: queryKeys.admin.recentMessages(),
      queryFn: async () => {
        const response = await fetch('/api/admin/messages/recent');
        if (!response.ok) throw new Error('Failed to fetch recent messages');
        return response.json();
      },
      ...cacheStrategies.realtime,
    });

    await Promise.all([messagesPromise, recentMessagesPromise]);
  }, [queryClient]);

  return {
    prefetchAnnouncement,
    prefetchPoll,
    prefetchUserProfile,
    prefetchHousingData,
    prefetchAdminData,
  };
}

/**
 * Route-based prefetching hook
 * Automatically prefetches data based on current route and likely next destinations
 */
export function useRoutePrefetching() {
  const queryClient = useQueryClient();
  const { prefetchAnnouncement, prefetchPoll, prefetchAdminData, prefetchHousingData } = usePrefetching();

  /**
   * Prefetch data for dashboard
   */
  const prefetchDashboard = useCallback(async () => {
    // Prefetch announcements
    queryClient.prefetchQuery({
      queryKey: queryKeys.announcements.all(),
      queryFn: async () => {
        const response = await fetch('/api/announcements');
        if (!response.ok) throw new Error('Failed to fetch announcements');
        return response.json();
      },
      ...cacheStrategies.interactive,
    });

    // Prefetch active polls
    queryClient.prefetchQuery({
      queryKey: queryKeys.polls.active(),
      queryFn: async () => {
        const response = await fetch('/api/polls?status=active');
        if (!response.ok) throw new Error('Failed to fetch active polls');
        return response.json();
      },
      ...cacheStrategies.realtime,
    });

    // Prefetch current user
    queryClient.prefetchQuery({
      queryKey: queryKeys.user.current(),
      queryFn: async () => {
        const { getCurrentUserClient } = await import('@/lib/supabase/auth-client');
        return getCurrentUserClient();
      },
      ...cacheStrategies.user,
    });
  }, [queryClient]);

  /**
   * Prefetch data for announcements page
   */
  const prefetchAnnouncementsPage = useCallback(async () => {
    // Prefetch tags for filtering
    queryClient.prefetchQuery({
      queryKey: queryKeys.tags(),
      queryFn: async () => {
        const response = await fetch('/api/tags');
        if (!response.ok) throw new Error('Failed to fetch tags');
        return response.json();
      },
      ...cacheStrategies.static,
    });

    // Prefetch housing data for targeting
    await prefetchHousingData();
  }, [queryClient, prefetchHousingData]);

  /**
   * Prefetch data for polls page
   */
  const prefetchPollsPage = useCallback(async () => {
    // Prefetch all polls
    queryClient.prefetchQuery({
      queryKey: queryKeys.polls.all(),
      queryFn: async () => {
        const response = await fetch('/api/polls');
        if (!response.ok) throw new Error('Failed to fetch polls');
        return response.json();
      },
      ...cacheStrategies.interactive,
    });

    // Prefetch poll audience data
    queryClient.prefetchQuery({
      queryKey: queryKeys.polls.audience(),
      queryFn: async () => {
        const response = await fetch('/api/polls/audience');
        if (!response.ok) throw new Error('Failed to fetch poll audience');
        return response.json();
      },
      ...cacheStrategies.static,
    });
  }, [queryClient]);

  /**
   * Prefetch data for admin pages
   */
  const prefetchAdminPages = useCallback(async () => {
    await prefetchAdminData();
    await prefetchHousingData();
  }, [prefetchAdminData, prefetchHousingData]);

  return {
    prefetchDashboard,
    prefetchAnnouncementsPage,
    prefetchPollsPage,
    prefetchAdminPages,
  };
}

/**
 * Link prefetching component
 * Automatically prefetches data when hovering over links
 */
interface PrefetchLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  prefetchData?: () => Promise<void>;
}

export function PrefetchLink({ href, children, className, prefetchData }: PrefetchLinkProps) {
  const router = useRouter();
  const { 
    prefetchDashboard, 
    prefetchAnnouncementsPage, 
    prefetchPollsPage, 
    prefetchAdminPages 
  } = useRoutePrefetching();

  const handlePrefetch = useCallback(async () => {
    // Custom prefetch function takes priority
    if (prefetchData) {
      await prefetchData();
      return;
    }

    // Auto-detect prefetch based on href
    if (href === '/dashboard') {
      await prefetchDashboard();
    } else if (href.startsWith('/dashboard/announcements')) {
      await prefetchAnnouncementsPage();
    } else if (href.startsWith('/dashboard/polls')) {
      await prefetchPollsPage();
    } else if (href.startsWith('/dashboard/admin')) {
      await prefetchAdminPages();
    }
  }, [href, prefetchData, prefetchDashboard, prefetchAnnouncementsPage, prefetchPollsPage, prefetchAdminPages]);

  const handleMouseEnter = useCallback(() => {
    // Prefetch with slight delay to avoid unnecessary requests
    const timeoutId = setTimeout(handlePrefetch, 100);
    return () => clearTimeout(timeoutId);
  }, [handlePrefetch]);

  const handleClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    router.push(href);
  }, [router, href]);

  return (
    <a
      href={href}
      className={className}
      onMouseEnter={handleMouseEnter}
      onClick={handleClick}
      data-prefetch="true"
    >
      {children}
    </a>
  );
}

/**
 * Intersection observer prefetching
 * Prefetches data when elements come into view
 */
export function useIntersectionPrefetch(
  prefetchFn: () => Promise<void>,
  options: IntersectionObserverInit = {}
) {
  const ref = useCallback((node: HTMLElement | null) => {
    if (!node) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          prefetchFn();
          observer.disconnect(); // Only prefetch once
        }
      },
      { threshold: 0.1, ...options }
    );

    observer.observe(node);

    return () => observer.disconnect();
  }, [prefetchFn]);

  return ref;
}