'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from './query-client';
import { toast } from 'sonner';

/**
 * Optimistic mutation hooks for immediate UI feedback
 * These provide instant updates while the server request is processing
 */

interface PollVote {
  pollId: string;
  questionId: string;
  optionId: string;
  explanation?: string;
}

interface PollVotePayload {
  pollId: string;
  responses: PollVote[];
}

/**
 * Optimistic voting mutation
 * Updates UI immediately, then syncs with server
 */
export function useOptimisticVote() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: PollVotePayload) => {
      const response = await fetch('/api/polls/responses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error('Failed to submit vote');
      }

      return response.json();
    },

    // Optimistic update - runs immediately
    onMutate: async (variables) => {
      const { pollId, responses } = variables;

      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: queryKeys.polls.detail(pollId) });
      await queryClient.cancelQueries({ queryKey: queryKeys.polls.responses(pollId) });
      await queryClient.cancelQueries({ queryKey: queryKeys.polls.stats(pollId) });

      // Snapshot the previous values
      const previousPoll = queryClient.getQueryData(queryKeys.polls.detail(pollId));
      const previousResponses = queryClient.getQueryData(queryKeys.polls.responses(pollId));
      const previousStats = queryClient.getQueryData(queryKeys.polls.stats(pollId));

      // Optimistically update the poll to show user has voted
      queryClient.setQueryData(queryKeys.polls.detail(pollId), (old: any) => {
        if (!old) return old;
        return {
          ...old,
          userResponse: responses,
          hasVoted: true,
          // Optimistically increment vote count
          totalVotes: (old.totalVotes || 0) + 1,
        };
      });

      // Show immediate success feedback
      toast.success('Balsas sėkmingai pateiktas!', {
        description: 'Jūsų balsas skaičiuojamas...'
      });

      // Return a context object with the snapshot values
      return { previousPoll, previousResponses, previousStats, pollId };
    },

    // If the mutation succeeds, update with real data
    onSuccess: (data, variables, context) => {
      // The server response will trigger a real-time update
      // so we don't need to manually update here
      toast.success('Balsas patvirtintas!');
    },

    // If the mutation fails, roll back to previous state
    onError: (err, variables, context) => {
      console.error('Vote failed:', err);
      
      if (context) {
        // Restore previous state
        queryClient.setQueryData(queryKeys.polls.detail(context.pollId), context.previousPoll);
        queryClient.setQueryData(queryKeys.polls.responses(context.pollId), context.previousResponses);
        queryClient.setQueryData(queryKeys.polls.stats(context.pollId), context.previousStats);
      }

      toast.error('Nepavyko pateikti balso', {
        description: 'Bandykite dar kartą'
      });
    },

    // Always refetch after mutation settles (success or error)
    onSettled: (data, error, variables, context) => {
      if (context) {
        // Invalidate and refetch to ensure we have the latest data
        queryClient.invalidateQueries({ queryKey: queryKeys.polls.detail(context.pollId) });
        queryClient.invalidateQueries({ queryKey: queryKeys.polls.responses(context.pollId) });
        queryClient.invalidateQueries({ queryKey: queryKeys.polls.stats(context.pollId) });
      }
    },
  });
}

/**
 * Optimistic announcement creation
 */
export function useOptimisticCreateAnnouncement() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (announcementData: any) => {
      const response = await fetch('/api/announcements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(announcementData),
      });

      if (!response.ok) {
        throw new Error('Failed to create announcement');
      }

      return response.json();
    },

    onMutate: async (variables) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.announcements.all() });

      // Snapshot previous value
      const previousAnnouncements = queryClient.getQueryData(queryKeys.announcements.all());

      // Create a temporary announcement object
      const tempAnnouncement = {
        id: 'temp-' + Date.now(),
        ...variables,
        created_at: new Date().toISOString(),
        author_name: 'You', // Will be replaced with real data
        tags: variables.tagIds?.map((id: number) => ({ id, name: 'Tag', color: 'blue' })) || [],
        is_draft: variables.isDraft || false,
        sending: true, // Flag to show it's being sent
      };

      // Optimistically add to the list
      queryClient.setQueryData(queryKeys.announcements.all(), (old: any[]) => {
        if (!old) return [tempAnnouncement];
        return [tempAnnouncement, ...old];
      });

      toast.success('Pranešimas kuriamas...', {
        description: variables.isDraft ? 'Išsaugoma kaip juodraštis' : 'Siunčiama vartotojams'
      });

      return { previousAnnouncements, tempId: tempAnnouncement.id };
    },

    onSuccess: (data, variables, context) => {
      // Remove temp announcement and let real-time update add the real one
      queryClient.setQueryData(queryKeys.announcements.all(), (old: any[]) => {
        if (!old) return old;
        return old.filter((announcement: any) => announcement.id !== context?.tempId);
      });

      toast.success(variables.isDraft ? 'Juodraštis išsaugotas!' : 'Pranešimas išsiųstas!');
    },

    onError: (err, variables, context) => {
      console.error('Announcement creation failed:', err);
      
      if (context) {
        // Restore previous state
        queryClient.setQueryData(queryKeys.announcements.all(), context.previousAnnouncements);
      }

      toast.error('Nepavyko sukurti pranešimo', {
        description: 'Bandykite dar kartą'
      });
    },

    onSettled: () => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: queryKeys.announcements.all() });
    },
  });
}

/**
 * Optimistic poll creation
 */
export function useOptimisticCreatePoll() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (pollData: any) => {
      const response = await fetch('/api/polls', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pollData),
      });

      if (!response.ok) {
        throw new Error('Failed to create poll');
      }

      return response.json();
    },

    onMutate: async (variables) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.polls.all() });
      await queryClient.cancelQueries({ queryKey: queryKeys.polls.active() });

      const previousPolls = queryClient.getQueryData(queryKeys.polls.all());
      const previousActivePolls = queryClient.getQueryData(queryKeys.polls.active());

      // Create temporary poll
      const tempPoll = {
        id: 'temp-' + Date.now(),
        ...variables,
        created_at: new Date().toISOString(),
        status: 'active',
        created_by_name: 'You',
        totalVotes: 0,
        creating: true,
      };

      // Add to both active and all polls
      queryClient.setQueryData(queryKeys.polls.all(), (old: any[]) => {
        if (!old) return [tempPoll];
        return [tempPoll, ...old];
      });

      queryClient.setQueryData(queryKeys.polls.active(), (old: any[]) => {
        if (!old) return [tempPoll];
        return [tempPoll, ...old];
      });

      toast.success('Apklausa kuriama...', {
        description: 'Nustatomas balsavimas'
      });

      return { previousPolls, previousActivePolls, tempId: tempPoll.id };
    },

    onSuccess: (data, variables, context) => {
      // Remove temp poll
      [queryKeys.polls.all(), queryKeys.polls.active()].forEach(queryKey => {
        queryClient.setQueryData(queryKey, (old: any[]) => {
          if (!old) return old;
          return old.filter((poll: any) => poll.id !== context?.tempId);
        });
      });

      toast.success('Apklausa sukurta!');
    },

    onError: (err, variables, context) => {
      console.error('Poll creation failed:', err);
      
      if (context) {
        queryClient.setQueryData(queryKeys.polls.all(), context.previousPolls);
        queryClient.setQueryData(queryKeys.polls.active(), context.previousActivePolls);
      }

      toast.error('Nepavyko sukurti apklausos', {
        description: 'Bandykite dar kartą'
      });
    },

    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.polls.all() });
      queryClient.invalidateQueries({ queryKey: queryKeys.polls.active() });
    },
  });
}