'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';
import { getCurrentUserClient } from '@/lib/supabase/auth-client';
import { queryKeys, invalidateQueries } from './query-client';

// Re-export invalidateQueries for convenience
export { invalidateQueries };

// =============================================================================
// USER QUERIES
// =============================================================================

export function useCurrentUser() {
  return useQuery({
    queryKey: queryKeys.user.current(),
    queryFn: getCurrentUserClient,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes (keep user data longer)
  });
}

export function useUserProfile(userId: string) {
  return useQuery({
    queryKey: queryKeys.user.profile(userId),
    queryFn: async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!userId,
  });
}

// =============================================================================
// ANNOUNCEMENTS QUERIES
// =============================================================================

export function useAnnouncements(filters?: { houseId?: string; flatId?: string }) {
  const queryKey = filters?.houseId 
    ? queryKeys.announcements.byHouse(filters.houseId)
    : filters?.flatId
    ? queryKeys.announcements.byFlat(filters.flatId)
    : queryKeys.announcements.all();

  return useQuery({
    queryKey,
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      if (filters?.houseId) searchParams.set('houseId', filters.houseId);
      if (filters?.flatId) searchParams.set('flatId', filters.flatId);
      
      const response = await fetch(`/api/announcements?${searchParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch announcements');
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 2, // 2 minutes (announcements change frequently)
  });
}

export function useAnnouncementDetail(id: string) {
  return useQuery({
    queryKey: queryKeys.announcements.detail(id),
    queryFn: async () => {
      const response = await fetch(`/api/announcements/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch announcement');
      }
      return response.json();
    },
    enabled: !!id,
  });
}

export function useCreateAnnouncement() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (announcementData: any) => {
      const response = await fetch('/api/announcements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(announcementData),
      });

      if (!response.ok) {
        throw new Error('Failed to create announcement');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch announcements
      invalidateQueries.announcements();
    },
  });
}

// =============================================================================
// POLLS QUERIES
// =============================================================================

export function usePolls(status?: 'active' | 'closed') {
  const queryKey = status === 'active' 
    ? queryKeys.polls.active()
    : status === 'closed'
    ? queryKeys.polls.closed()
    : queryKeys.polls.all();

  return useQuery({
    queryKey,
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      if (status) searchParams.set('status', status);
      
      const response = await fetch(`/api/polls?${searchParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch polls');
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 1, // 1 minute (polls change frequently with voting)
  });
}

export function usePollDetail(id: string) {
  return useQuery({
    queryKey: queryKeys.polls.detail(id),
    queryFn: async () => {
      const response = await fetch(`/api/polls?id=${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch poll');
      }
      const data = await response.json();
      return data[0]; // API returns array, we want the single poll
    },
    enabled: !!id,
  });
}

export function usePollResponses(pollId: string) {
  return useQuery({
    queryKey: queryKeys.polls.responses(pollId),
    queryFn: async () => {
      const response = await fetch(`/api/polls/responses?pollId=${pollId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch poll responses');
      }
      return response.json();
    },
    enabled: !!pollId,
  });
}

export function usePollStats(pollId: string) {
  return useQuery({
    queryKey: queryKeys.polls.stats(pollId),
    queryFn: async () => {
      const response = await fetch(`/api/polls/stats?pollId=${pollId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch poll stats');
      }
      return response.json();
    },
    enabled: !!pollId,
  });
}

export function useVotePoll() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ pollId, responses }: { pollId: string; responses: any[] }) => {
      const response = await fetch('/api/polls/responses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pollId,
          responses,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit vote');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.polls.detail(variables.pollId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.polls.responses(variables.pollId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.polls.stats(variables.pollId) });
      invalidateQueries.polls();
    },
  });
}

export function useCreatePoll() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (pollData: any) => {
      const response = await fetch('/api/polls', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pollData),
      });

      if (!response.ok) {
        throw new Error('Failed to create poll');
      }

      return response.json();
    },
    onSuccess: () => {
      invalidateQueries.polls();
    },
  });
}

// Re-export optimistic mutations from mutations.ts
export { 
  useOptimisticVote, 
  useOptimisticCreateAnnouncement, 
  useOptimisticCreatePoll 
} from './mutations';

// Re-export real-time hooks from real-time.ts
export { 
  useAnnouncementRealtime, 
  usePollRealtime, 
  usePollResponsesRealtime 
} from './real-time';

// =============================================================================
// HOUSING QUERIES
// =============================================================================

export function useStreets() {
  return useQuery({
    queryKey: queryKeys.housing.streets(),
    queryFn: async () => {
      const response = await fetch('/api/streets');
      if (!response.ok) {
        throw new Error('Failed to fetch streets');
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 30, // 30 minutes (streets don't change often)
  });
}

export function useHouses(streetId?: string) {
  const queryKey = streetId 
    ? queryKeys.housing.housesByStreet(streetId)
    : queryKeys.housing.houses();

  return useQuery({
    queryKey,
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      if (streetId) searchParams.set('streetId', streetId);
      
      const response = await fetch(`/api/houses?${searchParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch houses');
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 30, // 30 minutes
  });
}

export function useFlats(houseId?: string) {
  const queryKey = houseId 
    ? queryKeys.housing.flatsByHouse(houseId)
    : queryKeys.housing.flats();

  return useQuery({
    queryKey,
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      if (houseId) searchParams.set('houseId', houseId);
      
      const response = await fetch(`/api/flats?${searchParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch flats');
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 30, // 30 minutes
  });
}

// =============================================================================
// USERS QUERIES
// =============================================================================

export function useUsers(options?: { excludeAdmins?: boolean }) {
  return useQuery({
    queryKey: queryKeys.users.list(options?.excludeAdmins ? 'excludeAdmins' : 'all'),
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      if (options?.excludeAdmins) searchParams.set('excludeAdmins', 'true');
      
      const response = await fetch(`/api/users?${searchParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// =============================================================================
// TAGS QUERIES
// =============================================================================

export function useTags() {
  return useQuery({
    queryKey: queryKeys.tags(),
    queryFn: async () => {
      const response = await fetch('/api/tags');
      if (!response.ok) {
        throw new Error('Failed to fetch tags');
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 15, // 15 minutes
  });
}