'use client';

import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';
import { queryKeys } from './query-client';

/**
 * Custom hook for setting up real-time subscriptions with TanStack Query
 * This integrates Supabase real-time with TanStack Query's cache invalidation
 */

export function useRealtimeSubscriptions() {
  const queryClient = useQueryClient();

  useEffect(() => {
    const supabase = createClient();

    // Subscribe to announcements table changes
    const announcementsSubscription = supabase
      .channel('announcements-changes')
      .on(
        'postgres_changes',
        {
          event: '*', // Listen to all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'announcements'
        },
        (payload) => {
          console.log('Real-time announcement change:', payload);
          
          // Invalidate all announcement-related queries
          queryClient.invalidateQueries({ 
            queryKey: queryKeys.announcements.all() 
          });
          
          // If it's a specific announcement, also invalidate its detail query
          if (payload.new?.id || payload.old?.id) {
            const announcementId = (payload.new?.id || payload.old?.id).toString();
            queryClient.invalidateQueries({ 
              queryKey: queryKeys.announcements.detail(announcementId) 
            });
          }
        }
      )
      .subscribe();

    // Subscribe to polls table changes
    const pollsSubscription = supabase
      .channel('polls-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'polls'
        },
        (payload) => {
          console.log('Real-time poll change:', payload);
          
          // Invalidate all poll-related queries
          queryClient.invalidateQueries({ 
            queryKey: queryKeys.polls.all() 
          });
          
          // Invalidate specific poll queries
          if (payload.new?.id || payload.old?.id) {
            const pollId = (payload.new?.id || payload.old?.id).toString();
            queryClient.invalidateQueries({ 
              queryKey: queryKeys.polls.detail(pollId) 
            });
            queryClient.invalidateQueries({ 
              queryKey: queryKeys.polls.responses(pollId) 
            });
            queryClient.invalidateQueries({ 
              queryKey: queryKeys.polls.stats(pollId) 
            });
          }
        }
      )
      .subscribe();

    // Subscribe to poll responses (for real-time voting updates)
    const pollResponsesSubscription = supabase
      .channel('poll-responses-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'poll_responses'
        },
        (payload) => {
          console.log('Real-time poll response change:', payload);
          
          // Get the poll_id from the response
          const pollId = (payload.new?.poll_id || payload.old?.poll_id)?.toString();
          
          if (pollId) {
            // Invalidate poll-specific queries
            queryClient.invalidateQueries({ 
              queryKey: queryKeys.polls.detail(pollId) 
            });
            queryClient.invalidateQueries({ 
              queryKey: queryKeys.polls.responses(pollId) 
            });
            queryClient.invalidateQueries({ 
              queryKey: queryKeys.polls.stats(pollId) 
            });
            
            // Also invalidate general polls queries to update vote counts
            queryClient.invalidateQueries({ 
              queryKey: queryKeys.polls.active() 
            });
            queryClient.invalidateQueries({ 
              queryKey: queryKeys.polls.all() 
            });
          }
        }
      )
      .subscribe();

    // Subscribe to users table changes (for profile updates)
    const usersSubscription = supabase
      .channel('users-changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'users'
        },
        (payload) => {
          console.log('Real-time user change:', payload);
          
          // If it's the current user, invalidate their session
          const userId = payload.new?.id?.toString();
          if (userId) {
            queryClient.invalidateQueries({ 
              queryKey: queryKeys.user.profile(userId) 
            });
            
            // Also invalidate current user query
            queryClient.invalidateQueries({ 
              queryKey: queryKeys.user.current() 
            });
          }
        }
      )
      .subscribe();

    // Cleanup subscriptions on unmount
    return () => {
      console.log('Cleaning up real-time subscriptions');
      supabase.removeChannel(announcementsSubscription);
      supabase.removeChannel(pollsSubscription);
      supabase.removeChannel(pollResponsesSubscription);
      supabase.removeChannel(usersSubscription);
    };
  }, [queryClient]);
}

/**
 * Hook for subscribing to specific announcement changes
 */
export function useAnnouncementRealtime(announcementId: string | null) {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!announcementId) return;

    const supabase = createClient();
    
    const subscription = supabase
      .channel(`announcement-${announcementId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'announcements',
          filter: `id=eq.${announcementId}`
        },
        (payload) => {
          console.log(`Real-time change for announcement ${announcementId}:`, payload);
          
          // Invalidate specific announcement query
          queryClient.invalidateQueries({ 
            queryKey: queryKeys.announcements.detail(announcementId) 
          });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [announcementId, queryClient]);
}

/**
 * Hook for subscribing to specific poll changes
 */
export function usePollRealtime(pollId: string | null) {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!pollId) return;

    const supabase = createClient();
    
    const subscription = supabase
      .channel(`poll-${pollId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'polls',
          filter: `id=eq.${pollId}`
        },
        (payload) => {
          console.log(`Real-time change for poll ${pollId}:`, payload);
          
          // Invalidate specific poll queries
          queryClient.invalidateQueries({ 
            queryKey: queryKeys.polls.detail(pollId) 
          });
          queryClient.invalidateQueries({ 
            queryKey: queryKeys.polls.responses(pollId) 
          });
          queryClient.invalidateQueries({ 
            queryKey: queryKeys.polls.stats(pollId) 
          });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [pollId, queryClient]);
}

/**
 * Hook for subscribing to poll response changes for a specific poll
 * This is useful for real-time vote counting
 */
export function usePollResponsesRealtime(pollId: string | null) {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!pollId) return;

    const supabase = createClient();
    
    const subscription = supabase
      .channel(`poll-responses-${pollId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'poll_responses',
          filter: `poll_id=eq.${pollId}`
        },
        (payload) => {
          console.log(`Real-time response change for poll ${pollId}:`, payload);
          
          // Invalidate poll response and stats queries
          queryClient.invalidateQueries({ 
            queryKey: queryKeys.polls.responses(pollId) 
          });
          queryClient.invalidateQueries({ 
            queryKey: queryKeys.polls.stats(pollId) 
          });
          
          // Also update the poll detail to reflect new vote counts
          queryClient.invalidateQueries({ 
            queryKey: queryKeys.polls.detail(pollId) 
          });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [pollId, queryClient]);
}