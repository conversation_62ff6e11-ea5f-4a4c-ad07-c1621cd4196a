'use client';

import { QueryClient } from '@tanstack/react-query';

// Create a new QueryClient instance with optimized defaults
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Stale time: How long data is considered fresh
      staleTime: 1000 * 60 * 15, // 15 minutes (increased from 5)
      
      // GC time: How long inactive data stays in cache (formerly cacheTime)
      gcTime: 1000 * 60 * 30, // 30 minutes (increased from 10)
      
      // Retry failed requests
      retry: (failureCount, error: any) => {
        // Don't retry authentication errors
        if (error?.status === 401 || error?.status === 403) {
          return false;
        }
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
      
      // Refetch settings - reduce unnecessary refetches
      refetchOnWindowFocus: false, // Changed from true
      refetchOnReconnect: true,
      refetchOnMount: false, // Changed from true
    },
    mutations: {
      // Retry mutations up to 2 times
      retry: 2,
      
      // Global error handler for mutations
      onError: (error: any) => {
        console.error('Mutation error:', error);
        // You could add toast notifications here
      },
    },
  },
});

// Query key factory for consistent key management
export const queryKeys = {
  // User-related queries
  user: {
    current: () => ['user', 'current'] as const,
    profile: (userId: string) => ['user', 'profile', userId] as const,
    contacts: (userId: string) => ['user', 'contacts', userId] as const,
  },
  
  // Users list queries  
  users: {
    list: (filter: 'all' | 'excludeAdmins') => ['users', 'list', filter] as const,
  },
  
  // Announcements
  announcements: {
    all: () => ['announcements'] as const,
    byHouse: (houseId: string) => ['announcements', 'house', houseId] as const,
    byFlat: (flatId: string) => ['announcements', 'flat', flatId] as const,
    detail: (id: string) => ['announcements', 'detail', id] as const,
    tags: (id: string) => ['announcements', id, 'tags'] as const,
  },
  
  // Polls
  polls: {
    all: () => ['polls'] as const,
    active: () => ['polls', 'active'] as const,
    closed: () => ['polls', 'closed'] as const,
    detail: (id: string) => ['polls', 'detail', id] as const,
    responses: (id: string) => ['polls', id, 'responses'] as const,
    stats: (id: string) => ['polls', id, 'stats'] as const,
    audience: () => ['polls', 'audience'] as const,
  },
  
  // Admin
  admin: {
    users: () => ['admin', 'users'] as const,
    messages: () => ['admin', 'messages'] as const,
    recentMessages: () => ['admin', 'messages', 'recent'] as const,
    emails: () => ['admin', 'emails'] as const,
    settings: () => ['admin', 'settings'] as const,
  },
  
  // Housing
  housing: {
    streets: () => ['housing', 'streets'] as const,
    houses: () => ['housing', 'houses'] as const,
    flats: () => ['housing', 'flats'] as const,
    housesByStreet: (streetId: string) => ['housing', 'houses', 'street', streetId] as const,
    flatsByHouse: (houseId: string) => ['housing', 'flats', 'house', houseId] as const,
  },
  
  // Tags
  tags: () => ['tags'] as const,
  
  // Emergency contacts
  emergencyContacts: () => ['emergency-contacts'] as const,
} as const;

// Helper function to invalidate related queries
export const invalidateQueries = {
  user: () => queryClient.invalidateQueries({ queryKey: queryKeys.user.current() }),
  users: () => queryClient.invalidateQueries({ queryKey: ['users'] }),
  announcements: () => queryClient.invalidateQueries({ queryKey: queryKeys.announcements.all() }),
  polls: () => queryClient.invalidateQueries({ queryKey: queryKeys.polls.all() }),
  admin: () => queryClient.invalidateQueries({ queryKey: ['admin'] }),
  housing: () => queryClient.invalidateQueries({ queryKey: ['housing'] }),
} as const;