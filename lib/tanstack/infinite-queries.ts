'use client';

import { useInfiniteQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from './query-client';
import { cacheStrategies } from './advanced-config';

/**
 * Infinite scroll queries for large datasets
 * Provides efficient pagination with automatic loading
 */

interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: Record<string, any>;
}

interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * Infinite announcements query for efficient scrolling
 */
export function useInfiniteAnnouncements(params: PaginationParams = {}) {
  return useInfiniteQuery({
    queryKey: [...queryKeys.announcements.all(), 'infinite', params],
    
    queryFn: async ({ pageParam = 1 }) => {
      const searchParams = new URLSearchParams({
        page: pageParam.toString(),
        limit: (params.limit || 20).toString(),
        ...(params.search && { search: params.search }),
        ...(params.sortBy && { sortBy: params.sortBy }),
        ...(params.sortOrder && { sortOrder: params.sortOrder }),
        ...Object.fromEntries(
          Object.entries(params.filters || {}).map(([key, value]) => [key, String(value)])
        ),
      });

      const response = await fetch(`/api/announcements/paginated?${searchParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch announcements');
      }

      return response.json() as Promise<PaginatedResponse<any>>;
    },

    initialPageParam: 1,
    
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.hasNext ? lastPage.pagination.page + 1 : undefined;
    },
    
    getPreviousPageParam: (firstPage) => {
      return firstPage.pagination.hasPrev ? firstPage.pagination.page - 1 : undefined;
    },

    // Use interactive cache strategy
    ...cacheStrategies.interactive,
  });
}

/**
 * Infinite polls query
 */
export function useInfinitePolls(params: PaginationParams & { status?: 'active' | 'closed' } = {}) {
  return useInfiniteQuery({
    queryKey: [...queryKeys.polls.all(), 'infinite', params],
    
    queryFn: async ({ pageParam = 1 }) => {
      const searchParams = new URLSearchParams({
        page: pageParam.toString(),
        limit: (params.limit || 20).toString(),
        ...(params.status && { status: params.status }),
        ...(params.search && { search: params.search }),
        ...(params.sortBy && { sortBy: params.sortBy }),
        ...(params.sortOrder && { sortOrder: params.sortOrder }),
      });

      const response = await fetch(`/api/polls/paginated?${searchParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch polls');
      }

      return response.json() as Promise<PaginatedResponse<any>>;
    },

    initialPageParam: 1,
    
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.hasNext ? lastPage.pagination.page + 1 : undefined;
    },
    
    getPreviousPageParam: (firstPage) => {
      return firstPage.pagination.hasPrev ? firstPage.pagination.page - 1 : undefined;
    },

    // Use interactive cache strategy
    ...cacheStrategies.interactive,
  });
}

/**
 * Infinite users query for admin
 */
export function useInfiniteUsers(params: PaginationParams = {}) {
  return useInfiniteQuery({
    queryKey: [...queryKeys.admin.users(), 'infinite', params],
    
    queryFn: async ({ pageParam = 1 }) => {
      const searchParams = new URLSearchParams({
        page: pageParam.toString(),
        limit: (params.limit || 50).toString(),
        ...(params.search && { search: params.search }),
        ...(params.sortBy && { sortBy: params.sortBy }),
        ...(params.sortOrder && { sortOrder: params.sortOrder }),
        ...Object.fromEntries(
          Object.entries(params.filters || {}).map(([key, value]) => [key, String(value)])
        ),
      });

      const response = await fetch(`/api/admin/users/paginated?${searchParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      return response.json() as Promise<PaginatedResponse<any>>;
    },

    initialPageParam: 1,
    
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.hasNext ? lastPage.pagination.page + 1 : undefined;
    },

    // Use static cache strategy for admin data
    ...cacheStrategies.static,
  });
}

/**
 * Infinite poll responses query
 */
export function useInfinitePollResponses(pollId: string, params: PaginationParams = {}) {
  return useInfiniteQuery({
    queryKey: [...queryKeys.polls.responses(pollId), 'infinite', params],
    
    queryFn: async ({ pageParam = 1 }) => {
      const searchParams = new URLSearchParams({
        page: pageParam.toString(),
        limit: (params.limit || 30).toString(),
        pollId,
        ...(params.search && { search: params.search }),
        ...(params.sortBy && { sortBy: params.sortBy }),
        ...(params.sortOrder && { sortOrder: params.sortOrder }),
      });

      const response = await fetch(`/api/polls/responses/paginated?${searchParams.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch poll responses');
      }

      return response.json() as Promise<PaginatedResponse<any>>;
    },

    initialPageParam: 1,
    
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.hasNext ? lastPage.pagination.page + 1 : undefined;
    },

    enabled: !!pollId,

    // Use realtime cache strategy for active data
    ...cacheStrategies.realtime,
  });
}

/**
 * Hook for efficient infinite scroll UI
 */
export function useInfiniteScroll<T>(
  infiniteQuery: ReturnType<typeof useInfiniteQuery>,
  options: {
    threshold?: number;
    rootMargin?: string;
  } = {}
) {
  const { threshold = 0.1, rootMargin = '100px' } = options;

  // Intersection Observer for auto-loading
  const observerRef = React.useRef<IntersectionObserver>();
  const loadMoreRef = React.useCallback(
    (node: HTMLDivElement | null) => {
      if (infiniteQuery.isFetchingNextPage) return;
      
      if (observerRef.current) observerRef.current.disconnect();
      
      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && infiniteQuery.hasNextPage) {
            infiniteQuery.fetchNextPage();
          }
        },
        { threshold, rootMargin }
      );
      
      if (node) observerRef.current.observe(node);
    },
    [infiniteQuery.isFetchingNextPage, infiniteQuery.hasNextPage, infiniteQuery.fetchNextPage, threshold, rootMargin]
  );

  // Flatten pages into single array
  const allItems = React.useMemo(() => {
    return infiniteQuery.data?.pages.flatMap(page => page.data) || [];
  }, [infiniteQuery.data]);

  // Total count across all pages
  const totalCount = infiniteQuery.data?.pages[0]?.pagination.total || 0;

  return {
    items: allItems,
    totalCount,
    loadMoreRef,
    isLoading: infiniteQuery.isLoading,
    isFetchingNextPage: infiniteQuery.isFetchingNextPage,
    hasNextPage: infiniteQuery.hasNextPage,
    error: infiniteQuery.error,
    refetch: infiniteQuery.refetch,
  };
}

/**
 * Virtual scrolling for extremely large lists
 */
export function useVirtualizedInfiniteQuery<T>(
  infiniteQuery: ReturnType<typeof useInfiniteQuery>,
  itemHeight: number,
  containerHeight: number
) {
  const allItems = infiniteQuery.data?.pages.flatMap(page => page.data) || [];
  const totalItems = allItems.length;
  
  const [scrollTop, setScrollTop] = React.useState(0);
  
  // Calculate visible range
  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / itemHeight) + 1,
    totalItems
  );
  
  // Get visible items
  const visibleItems = allItems.slice(startIndex, endIndex);
  
  // Load more when approaching end
  React.useEffect(() => {
    const threshold = 10; // Load when 10 items from end
    if (
      endIndex >= totalItems - threshold &&
      infiniteQuery.hasNextPage &&
      !infiniteQuery.isFetchingNextPage
    ) {
      infiniteQuery.fetchNextPage();
    }
  }, [endIndex, totalItems, infiniteQuery]);

  return {
    visibleItems,
    startIndex,
    endIndex,
    totalHeight: totalItems * itemHeight,
    onScroll: (e: React.UIEvent<HTMLDivElement>) => {
      setScrollTop(e.currentTarget.scrollTop);
    },
    isLoading: infiniteQuery.isLoading,
    isFetchingNextPage: infiniteQuery.isFetchingNextPage,
  };
}

// React import for hooks
import React from 'react';