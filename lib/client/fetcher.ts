/**
 * Enhanced fetch wrapper for client components
 * - Automatically adds CSRF tokens to non-GET requests
 * - Handles common error scenarios
 * - Provides type safety for requests and responses
 */

const CSRF_HEADER = 'X-CSRF-Token';

// Cache for CSRF token to reduce unnecessary requests
let csrfTokenCache: string | null = null;
let csrfTokenExpiry: number | null = null;

/**
 * Fetch a CSRF token from the server
 */
async function fetchCsrfToken(): Promise<string> {
  // If we have a cached token that hasn't expired, use it
  const now = Date.now();
  if (csrfTokenCache && csrfTokenExpiry && now < csrfTokenExpiry) {
    return csrfTokenCache;
  }
  
  // Fetch a new token
  const response = await fetch('/api/csrf', {
    method: 'GET',
    credentials: 'include',
  });
  
  if (!response.ok) {
    throw new Error('Failed to fetch CSRF token');
  }
  
  const data = await response.json();
  
  // Cache the token for 1 hour
  csrfTokenCache = data.token;
  csrfTokenExpiry = now + 1000 * 60 * 60; // 1 hour
  
  return data.token;
}

type FetchOptions = RequestInit & {
  noAuth?: boolean;
  noCsrf?: boolean;
};

/**
 * Enhanced fetch function for client components
 */
export async function clientFetch<T = any>(
  url: string,
  options: FetchOptions = {}
): Promise<T> {
  // Clone the options to avoid modifying the original
  const fetchOptions: FetchOptions = { 
    ...options,
    credentials: 'include', // Always include credentials for auth
  };
  
  // Add headers if they don't exist
  if (!fetchOptions.headers) {
    fetchOptions.headers = {};
  }
  
  // Convert fetchOptions.headers to a Headers object if it's not already
  const headers = new Headers(fetchOptions.headers as HeadersInit);
  
  // Add CSRF token for non-GET requests
  if (!fetchOptions.noCsrf && 
      fetchOptions.method && 
      fetchOptions.method !== 'GET' && 
      fetchOptions.method !== 'HEAD') {
    try {
      const csrfToken = await fetchCsrfToken();
      headers.set(CSRF_HEADER, csrfToken);
    } catch (error) {
      console.error('Failed to add CSRF token:', error);
      // Continue without CSRF token, the server will reject if necessary
    }
  }
  
  // Set content type to JSON if we're sending a body and content-type isn't already set
  if (fetchOptions.body && !headers.has('Content-Type')) {
    headers.set('Content-Type', 'application/json');
  }
  
  // Replace the headers in fetchOptions
  fetchOptions.headers = headers;
  
  // Add detailed debugging for DELETE requests
  if (fetchOptions.method === 'DELETE') {
    console.log(`[DEBUG] Sending DELETE request to ${url}`, {
      headers: Object.fromEntries([...headers.entries()]),
      credentials: fetchOptions.credentials,
      noCsrf: fetchOptions.noCsrf
    });
  }

  // Make the request
  const response = await fetch(url, fetchOptions);
  
  // Handle errors
  if (!response.ok) {
    // Add more detailed error logging
    console.error(`[ERROR] Request failed: ${fetchOptions.method} ${url}`, {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries([...headers.entries()])
    });
    
    // Try to parse the error response
    try {
      const errorData = await response.json();
      console.error('[ERROR] Server response:', errorData);
      throw new Error(errorData.message || errorData.error || 'Unknown error');
    } catch (e) {
      // If we can't parse the error, just throw a generic error
      throw new Error(`Request failed with status ${response.status}: ${response.statusText}`);
    }
  }
  
  // Parse the response
  try {
    return await response.json() as T;
  } catch (e) {
    // If the response is empty or not JSON, return an empty object
    return {} as T;
  }
} 