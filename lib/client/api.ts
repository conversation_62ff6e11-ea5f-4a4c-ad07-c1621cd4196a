import { clientFetch } from './fetcher';

/**
 * User API types and methods
 */
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'ADMIN' | 'USER';
  createdAt: string;
  updatedAt: string;
}

export interface UpdateUserPayload {
  name?: string;
  email?: string;
  password?: string;
  role?: 'ADMIN' | 'USER';
}

export const userApi = {
  /**
   * Get all users
   */
  getAll: async (): Promise<User[]> => {
    return clientFetch('/api/users') as Promise<User[]>;
  },

  /**
   * Get user by ID
   */
  getById: async (userId: string): Promise<User> => {
    return clientFetch(`/api/users/${userId}`) as Promise<User>;
  },

  /**
   * Update user
   */
  update: async (userId: string, data: UpdateUserPayload): Promise<User> => {
    return clientFetch(`/api/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }) as Promise<User>;
  },

  /**
   * Delete user
   */
  delete: async (userId: string): Promise<void> => {
    await clientFetch(`/api/users/${userId}`, {
      method: 'DELETE',
    });
  }
};

/**
 * Announcement API types and methods
 */
export interface Announcement {
  id: string;
  title: string;
  content: string;
  authorId: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateAnnouncementPayload {
  title: string;
  content: string;
}

export interface UpdateAnnouncementPayload {
  title?: string;
  content?: string;
}

export const announcementApi = {
  /**
   * Get all announcements
   */
  getAll: async (): Promise<Announcement[]> => {
    return clientFetch('/api/announcements') as Promise<Announcement[]>;
  },

  /**
   * Get announcement by ID
   */
  getById: async (id: string): Promise<Announcement> => {
    return clientFetch(`/api/announcements/${id}`) as Promise<Announcement>;
  },

  /**
   * Create new announcement
   */
  create: async (data: CreateAnnouncementPayload): Promise<Announcement> => {
    return clientFetch('/api/announcements', {
      method: 'POST',
      body: JSON.stringify(data),
    }) as Promise<Announcement>;
  },

  /**
   * Update announcement
   */
  update: async (id: string, data: UpdateAnnouncementPayload): Promise<Announcement> => {
    return clientFetch(`/api/announcements/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }) as Promise<Announcement>;
  },

  /**
   * Delete announcement
   */
  delete: async (id: string): Promise<void> => {
    await clientFetch(`/api/announcements/${id}`, {
      method: 'DELETE',
    });
  }
};

/**
 * Feedback API types and methods
 */
export interface Feedback {
  id: string;
  content: string;
  userId: string;
  createdAt: string;
}

export interface CreateFeedbackPayload {
  content: string;
}

export const feedbackApi = {
  /**
   * Get all feedback
   */
  getAll: async (): Promise<Feedback[]> => {
    return clientFetch('/api/feedback') as Promise<Feedback[]>;
  },

  /**
   * Create new feedback
   */
  create: async (data: CreateFeedbackPayload): Promise<Feedback> => {
    return clientFetch('/api/feedback', {
      method: 'POST',
      body: JSON.stringify(data),
    }) as Promise<Feedback>;
  },

  /**
   * Delete feedback
   */
  delete: async (id: string): Promise<void> => {
    await clientFetch(`/api/feedback/${id}`, {
      method: 'DELETE',
    });
  }
};

/**
 * Contact API types and methods
 */
export interface Contact {
  id: string;
  name: string;
  email: string;
  message: string;
  createdAt: string;
}

export interface CreateContactPayload {
  name: string;
  email: string;
  message: string;
}

export const contactApi = {
  /**
   * Get all contact messages
   */
  getAll: async (): Promise<Contact[]> => {
    return clientFetch('/api/contacts') as Promise<Contact[]>;
  },

  /**
   * Create new contact message
   */
  create: async (data: CreateContactPayload): Promise<Contact> => {
    return clientFetch('/api/contacts', {
      method: 'POST',
      body: JSON.stringify(data),
    }) as Promise<Contact>;
  },

  /**
   * Delete contact message
   */
  delete: async (id: string): Promise<void> => {
    await clientFetch(`/api/contacts/${id}`, {
      method: 'DELETE',
    });
  }
};

/**
 * Emergency Contact API types and methods
 */
export interface EmergencyContact {
  id: string;
  name: string;
  phone: string;
  email?: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateEmergencyContactPayload {
  name: string;
  phone: string;
  email?: string;
  role: string;
}

export interface UpdateEmergencyContactPayload {
  name?: string;
  phone?: string;
  email?: string;
  role?: string;
}

export const emergencyContactApi = {
  /**
   * Get all emergency contacts
   */
  getAll: async (): Promise<EmergencyContact[]> => {
    return clientFetch('/api/emergency-contacts') as Promise<EmergencyContact[]>;
  },

  /**
   * Get emergency contact by ID
   */
  getById: async (id: string): Promise<EmergencyContact> => {
    return clientFetch(`/api/emergency-contacts/${id}`) as Promise<EmergencyContact>;
  },

  /**
   * Create new emergency contact
   */
  create: async (data: CreateEmergencyContactPayload): Promise<EmergencyContact> => {
    return clientFetch('/api/emergency-contacts', {
      method: 'POST',
      body: JSON.stringify(data),
    }) as Promise<EmergencyContact>;
  },

  /**
   * Update emergency contact
   */
  update: async (id: string, data: UpdateEmergencyContactPayload): Promise<EmergencyContact> => {
    return clientFetch(`/api/emergency-contacts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }) as Promise<EmergencyContact>;
  },

  /**
   * Delete emergency contact
   */
  delete: async (id: string): Promise<void> => {
    await clientFetch(`/api/emergency-contacts/${id}`, {
      method: 'DELETE',
    });
  }
}; 