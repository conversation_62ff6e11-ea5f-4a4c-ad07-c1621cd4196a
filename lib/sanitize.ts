import DOMPurify from 'isomorphic-dompurify';

// Use isomorphic-dompurify which works in both browser and Node.js environments
const purify = DOMPurify;

/**
 * Sanitizes HTML content to prevent XSS attacks using DOMPurify
 * 
 * Allows basic formatting tags, links, and lists. Strips out everything else.
 * 
 * @param html The potentially unsafe HTML string to sanitize
 * @param options Optional DOMPurify configuration object
 * @returns Sanitized HTML string, or an empty string if input is invalid/error occurs
 */
export function sanitizeHtml(html: string, options?: DOMPurify.Config): string {
  try {
    if (typeof html !== 'string' || !html) {
      return '';
    }
    
    // Define allowed tags and attributes for safety
    const defaultOptions: DOMPurify.Config = {
      ALLOWED_TAGS: [
        'b', 'i', 'u', 'strong', 'em', 'strike', 'br', 'p', 'ul', 'ol', 'li',
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'a',
      ],
      ALLOWED_ATTR: ['href', 'target', 'rel', 'title'],
      FORBID_TAGS: [
        'script', 'style', 'iframe', 'object', 'embed', 'form', 'input', 'textarea', 'button', 'base'
      ],
      FORBID_ATTR: [ // Forbid attributes starting with 'on' and other dangerous ones
        'onclick', 'onerror', 'onload', 'onmouseover', 'onmouseout', 'onfocus', 'onblur', 'onchange', 
        'onsubmit', 'onreset', 'onkeydown', 'onkeypress', 'onkeyup', 'style', 'class', 'id'
      ],
      ALLOW_DATA_ATTR: false, // Disallow data-* attributes
      USE_PROFILES: { html: true }, // Use HTML profile
      ...options, // Allow overriding defaults
    };
    
    // Sanitize the HTML
    const cleanHtml = purify.sanitize(html, defaultOptions);
    
    return cleanHtml;
  } catch (error) {
    console.error('HTML sanitization failed:', error);
    // In case of error, return empty string to be safe
    return '';
  }
}

/**
 * Sanitizes a text string by escaping HTML special characters
 * Use this for plain text that should not contain any HTML
 * 
 * @param text The text to sanitize
 * @returns Sanitized text with HTML entities escaped
 */
export function sanitizeText(text: string): string {
  if (typeof text !== 'string') {
    return '';
  }
  
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
} 