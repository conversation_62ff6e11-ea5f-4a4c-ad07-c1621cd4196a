import { db } from "@/lib/db/supabase-adapter";
import { sendEmail } from "@/lib/email";
import { addAuditLog } from "@/lib/utils";
import { isTestEmail } from "@/lib/email-utils";

export type QueueEmailOptions = {
  emailType: string;
  recipient: string;
  subject: string;
  content: string;
  entityType?: string;
  entityId?: number;
  priority?: number;
  scheduledFor?: Date;
  metadata?: Record<string, any>;
  userId?: number;
  replyTo?: string;
  announcementId?: number;
  pollId?: number;
};

export type EmailTemplate = {
  id: number;
  name: string;
  subject: string;
  content: string;
};

/**
 * Get development email settings from database
 */
async function getDevEmailSettings() {
  try {
    const result = await db.query(
      `SELECT key, value FROM admin_settings WHERE key IN ('email_dev_mode', 'email_dev_recipient')`
    );

    // Convert to an object
    const settings: Record<string, string> = {};
    result.rows.forEach(row => {
      settings[row.key] = row.value;
    });

    return {
      mode: settings.email_dev_mode || process.env.EMAIL_DEV_MODE || 'redirect',
      recipient: settings.email_dev_recipient || process.env.EMAIL_DEV_RECIPIENT || '<EMAIL>',
    };
  } catch (error) {
    console.error("Error getting development email settings:", error);
    
    // Fallback to environment variables
    return {
      mode: process.env.EMAIL_DEV_MODE || 'redirect',
      recipient: process.env.EMAIL_DEV_RECIPIENT || '<EMAIL>',
    };
  }
}

/**
 * Get SMTP settings from database - just for the replyTo field
 */
async function getSmtpReplyTo(): Promise<string> {
  try {
    const result = await db.query(
      `SELECT value FROM admin_settings WHERE key = 'smtp_reply_to'`
    );

    if (result.rows.length > 0) {
      return result.rows[0].value;
    }
    
    return process.env.SMTP_REPLY_TO || '';
  } catch (error) {
    console.error("Error getting SMTP reply-to setting:", error);
    return process.env.SMTP_REPLY_TO || '';
  }
}

/**
 * Add an email to the queue
 */
export async function queueEmail(options: QueueEmailOptions) {
  const {
    emailType,
    recipient,
    subject,
    content,
    entityType,
    entityId,
    priority = 3,
    scheduledFor,
    metadata,
    userId,
    replyTo,
    announcementId,
    pollId,
  } = options;

  try {
    const isDev = process.env.NODE_ENV !== 'production';
    let finalRecipient = recipient;
    let targetStatus = 'pending'; // Default status

    if (isDev) {
      const isAllowed = await isTestEmail(recipient);
      if (!isAllowed) {
        targetStatus = 'dev_skipped'; // Set status back for non-test users in dev
        console.log(`[DEV] Queuing email to ${recipient} with status ${targetStatus}.`);
      } else {
        // Status remains 'pending' for test users
        console.log(`[DEV] Queuing email to test recipient ${recipient} with status ${targetStatus}.`);
      }
    } else {
      console.log(`[PROD] Queuing email to ${recipient} with status ${targetStatus}`);
    }
    
    // Get the reply-to address
    const systemReplyTo = await getSmtpReplyTo();
    const finalReplyTo = replyTo || systemReplyTo;
    
    // Check user preferences 
    if (userId) {
      console.log(`[queueEmail] Checking preferences for user ${userId}, type ${emailType}`);
      const userPreferences = await db.query(
        `SELECT enabled FROM user_email_preferences WHERE user_id = $1 AND email_type = $2`,
        [userId, emailType]
      );

      // If the user has opted out, don't queue the email
      if (userPreferences.rows.length > 0 && !userPreferences.rows[0].enabled) {
        console.log(`[queueEmail] SKIPPING email for user ${userId} due to preferences (before insert).`);
        return { success: false, optedOut: true };
      }
      console.log(`[queueEmail] User preferences allow email for user ${userId}.`);
    } else {
      console.log(`[queueEmail] No userId provided, skipping preference check.`);
    }

    // Determine specific association ID based on entityType
    let specificAnnouncementId = entityType === 'announcement' ? entityId : announcementId;
    let specificPollId = entityType === 'poll' ? entityId : pollId;

    // Queue the email including the determined status
    const result = await db.query(
      `INSERT INTO email_queue
        (email_type, recipient, subject, content, entity_type, entity_id,
         priority, scheduled_for, metadata, announcement_id, poll_id, status) -- Added status column
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12) -- Added $12 for status
       RETURNING id`,
      [
        emailType,
        finalRecipient,
        subject,
        content,
        entityType,
        entityId,
        priority,
        scheduledFor,
        metadata ? JSON.stringify(metadata) : null,
        specificAnnouncementId,
        specificPollId,
        targetStatus, // Pass the determined status
      ]
    );

    const queueId = result.rows[0].id;

    // Add audit log
    if (userId) {
      await addAuditLog({
        action: 'queue_email',
        userId,
        entityType: 'email_queue',
        entityId: queueId,
        changes: {
          emailType,
          recipient: finalRecipient,
          subject,
          entityType,
          entityId,
          announcementId: specificAnnouncementId,
          pollId: specificPollId,
        },
      });
    }

    return { success: true, queueId };
  } catch (error) {
    console.error("Error queueing email:", error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

/**
 * Process the email queue - send pending emails
 */
export async function processEmailQueue() {
  try {
    // Get emails that need to be sent (pending and scheduled for now or past)
    const result = await db.query(
      `SELECT * FROM email_queue 
       WHERE status = 'pending' 
       AND (scheduled_for IS NULL OR scheduled_for <= NOW()) 
       ORDER BY priority ASC, scheduled_for ASC 
       LIMIT 50`
    );

    const emails = result.rows;
    console.log(`Processing ${emails.length} emails from queue`);
    const isDev = process.env.NODE_ENV !== 'production'; // Check dev mode

    for (const email of emails) {
      try {
        // ---> DEV CHECK: Only send to test users in dev mode <--- 
        if (isDev) {
          const allowedToSend = await isTestEmail(email.recipient);
          if (!allowedToSend) {
            console.log(`[DEV][Process] Skipping send for ${email.recipient} (not a test user). ID: ${email.id}`);
            continue; // Skip to the next email in the loop
          }
          console.log(`[DEV][Process] Proceeding with send for test user ${email.recipient}. ID: ${email.id}`);
        }
        // ---> END DEV CHECK <---

        // Mark as processing and increase attempt count
        await db.query(
          `UPDATE email_queue 
           SET attempts = attempts + 1, last_attempt_at = NOW() 
           WHERE id = $1`,
          [email.id]
        );

        // Send the email
        const sendResult = await sendEmail({
          to: email.recipient,
          subject: email.subject,
          html: email.content,
          replyTo: email.reply_to,
        });

        if (sendResult.success) {
          // Mark as sent
          await db.query(
            `UPDATE email_queue 
             SET status = 'sent', sent_at = NOW() 
             WHERE id = $1`,
            [email.id]
          );

          // Update notification_delivery if this is tied to a notification
          if (email.entity_type === 'notification' && email.entity_id) {
            await db.query(
              `UPDATE notification_delivery 
               SET email_sent_at = NOW() 
               WHERE notification_id = $1 AND user_id = (
                 SELECT id FROM users WHERE email = $2 LIMIT 1
               )`,
              [email.entity_id, email.recipient]
            );
          }
        } else {
          // Handle failure based on retry count
          const maxAttempts = email.max_attempts || 3;
          if (email.attempts >= maxAttempts) {
            await db.query(
              `UPDATE email_queue 
               SET status = 'failed', error_message = $2 
               WHERE id = $1`,
              [email.id, sendResult.error || 'Max retry attempts reached']
            );
          } else {
            // Calculate next retry with exponential backoff
            const nextRetryMinutes = Math.pow(2, email.attempts) * 15; // 15min, 30min, 1hr, 2hr, etc.
            const nextRetry = new Date();
            nextRetry.setMinutes(nextRetry.getMinutes() + nextRetryMinutes);

            await db.query(
              `UPDATE email_queue 
               SET scheduled_for = $2, error_message = $3 
               WHERE id = $1`,
              [email.id, nextRetry, sendResult.error || 'Temporary failure']
            );
          }
        }
      } catch (emailError) {
        console.error(`Error processing email ${email.id}:`, emailError);
        
        // Update the error in the database
        await db.query(
          `UPDATE email_queue 
           SET error_message = $2 
           WHERE id = $1`,
          [email.id, emailError instanceof Error ? emailError.message : String(emailError)]
        );
      }
    }

    return { success: true, processed: emails.length };
  } catch (error) {
    console.error("Error processing email queue:", error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

/**
 * Get a template by name and replace placeholders
 */
export async function getEmailTemplate(
  templateName: string,
  replacements: Record<string, string>
): Promise<EmailTemplate | null> {
  try {
    const result = await db.query(
      `SELECT * FROM email_templates WHERE name = $1`,
      [templateName]
    );

    if (result.rows.length === 0) {
      return null;
    }

    const template = result.rows[0];
    
    // Replace placeholders in subject and content
    let subject = template.subject;
    let content = template.content;
    
    Object.keys(replacements).forEach((key) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      subject = subject.replace(regex, replacements[key]);
      content = content.replace(regex, replacements[key]);
    });

    return {
      ...template,
      subject,
      content,
    };
  } catch (error) {
    console.error("Error getting email template:", error);
    return null;
  }
}

/**
 * Cancel an email in the queue
 */
export async function cancelQueuedEmail(id: number, userId: number) {
  try {
    const result = await db.query(
      `UPDATE email_queue 
       SET status = 'cancelled' 
       WHERE id = $1 AND status = 'pending' 
       RETURNING *`,
      [id]
    );

    if (result.rows.length === 0) {
      return { success: false, error: 'Email not found or already processed' };
    }

    // Add audit log
    await addAuditLog({
      action: 'cancel_email',
      userId,
      entityType: 'email_queue',
      entityId: id,
      changes: {
        status: 'cancelled',
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error cancelling email:", error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

/**
 * Update user email preferences
 */
export async function updateUserEmailPreferences(
  userId: number, 
  preferences: Record<string, boolean>
) {
  try {
    // Start a transaction
    await db.query('BEGIN');

    for (const [emailType, enabled] of Object.entries(preferences)) {
      // Check if the preference already exists
      const checkResult = await db.query(
        `SELECT * FROM user_email_preferences WHERE user_id = $1 AND email_type = $2`,
        [userId, emailType]
      );

      if (checkResult.rows.length > 0) {
        // Update existing preference
        await db.query(
          `UPDATE user_email_preferences 
           SET enabled = $3, updated_at = NOW() 
           WHERE user_id = $1 AND email_type = $2`,
          [userId, emailType, enabled]
        );
      } else {
        // Insert new preference
        await db.query(
          `INSERT INTO user_email_preferences (user_id, email_type, enabled) 
           VALUES ($1, $2, $3)`,
          [userId, emailType, enabled]
        );
      }
    }

    // Commit the transaction
    await db.query('COMMIT');

    return { success: true };
  } catch (error) {
    // Rollback in case of error
    await db.query('ROLLBACK');
    console.error("Error updating email preferences:", error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

/**
 * Get user email preferences
 */
export async function getUserEmailPreferences(userId: number) {
  try {
    const result = await db.query(
      `SELECT email_type, enabled FROM user_email_preferences WHERE user_id = $1`,
      [userId]
    );

    // Convert to a key-value object
    const preferences: Record<string, boolean> = {};
    result.rows.forEach((row) => {
      preferences[row.email_type] = row.enabled;
    });

    return { success: true, preferences };
  } catch (error) {
    console.error("Error getting email preferences:", error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

/**
 * Reschedule a queued email
 */
export async function rescheduleQueuedEmail(id: number, newDate: Date, userId: number) {
  try {
    const result = await db.query(
      `UPDATE email_queue 
       SET scheduled_for = $2, status = 'pending' 
       WHERE id = $1 
       RETURNING *`,
      [id, newDate]
    );

    if (result.rows.length === 0) {
      return { success: false, error: 'Email not found' };
    }

    // Add audit log
    await addAuditLog({
      action: 'reschedule_email',
      userId,
      entityType: 'email_queue',
      entityId: id,
      changes: {
        scheduled_for: newDate,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error rescheduling email:", error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
} 