import pino from 'pino'

const logLevel = process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug')

// Simple logger configuration that works in all environments
const logger = pino({
  level: logLevel,
  // No transport configuration to avoid Next.js bundling issues
  formatters: {
    level: (label) => {
      return { level: label }
    }
  },
  base: {
    env: process.env.NODE_ENV
  }
})

export default logger 