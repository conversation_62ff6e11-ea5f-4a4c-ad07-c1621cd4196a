import { BunnyCDNService, type CDNFile, type UploadOptions, type UrlOptions, type StorageStats } from './bunnycdn.service';
import { isBunnyCDNConfigured } from '@/lib/config/bunnycdn';
import path from 'path';
import fs from 'fs/promises';
import crypto from 'crypto';
import pino from 'pino';

const logger = pino({ name: 'storage-factory' });

// Common storage interface
export interface StorageService {
  uploadFile(fileBuffer: Buffer, filePath: string, options?: UploadOptions): Promise<CDNFile>;
  deleteFile(filePath: string): Promise<boolean>;
  getFileUrl(filePath: string, options?: UrlOptions): Promise<string>;
  listFiles(directory?: string): Promise<CDNFile[]>;
  getStorageStats(): Promise<StorageStats>;
}

/**
 * Local storage service for development
 */
export class LocalStorageService implements StorageService {
  private basePath: string;
  private baseUrl: string;

  constructor(basePath: string = './uploads', baseUrl: string = '/uploads') {
    this.basePath = basePath;
    this.baseUrl = baseUrl;
  }

  async uploadFile(
    fileBuffer: Buffer,
    filePath: string,
    options?: UploadOptions
  ): Promise<CDNFile> {
    try {
      // Ensure upload directory exists
      const fullPath = path.join(this.basePath, filePath);
      const directory = path.dirname(fullPath);
      
      await fs.mkdir(directory, { recursive: true });

      // Check if file exists
      if (options?.overwrite !== true) {
        try {
          await fs.access(fullPath);
          throw new Error('File already exists');
        } catch (error) {
          // File doesn't exist, which is what we want
          if ((error as NodeJS.ErrnoException).code !== 'ENOENT') {
            throw error;
          }
        }
      }

      // Write file
      await fs.writeFile(fullPath, fileBuffer);
      
      logger.info({ path: filePath }, 'File uploaded to local storage');

      // Call progress callback if provided
      if (options?.onProgress) {
        options.onProgress(100);
      }

      return {
        path: filePath,
        url: `${this.baseUrl}/${filePath}`,
        size: fileBuffer.length,
        lastModified: new Date(),
        contentType: options?.contentType,
      };
    } catch (error) {
      logger.error({ error, path: filePath }, 'Failed to upload file locally');
      throw error;
    }
  }

  async deleteFile(filePath: string): Promise<boolean> {
    try {
      const fullPath = path.join(this.basePath, filePath);
      await fs.unlink(fullPath);
      
      logger.info({ path: filePath }, 'File deleted from local storage');
      return true;
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
        logger.warn({ path: filePath }, 'File not found for deletion');
        return false;
      }
      
      logger.error({ error, path: filePath }, 'Failed to delete file locally');
      throw error;
    }
  }

  async getFileUrl(filePath: string, options?: UrlOptions): Promise<string> {
    let url = `${this.baseUrl}/${filePath}`;
    
    // Add query parameters if needed
    const params = new URLSearchParams();
    
    if (options?.download) {
      params.set('download', '1');
    }
    
    if (options?.fileName) {
      params.set('filename', options.fileName);
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    return url;
  }

  async listFiles(directory: string = ''): Promise<CDNFile[]> {
    try {
      const fullPath = path.join(this.basePath, directory);
      const entries = await fs.readdir(fullPath, { withFileTypes: true });
      
      const files: CDNFile[] = [];
      
      for (const entry of entries) {
        if (entry.isFile()) {
          const filePath = path.join(directory, entry.name);
          const fullFilePath = path.join(this.basePath, filePath);
          const stats = await fs.stat(fullFilePath);
          
          files.push({
            path: filePath,
            url: `${this.baseUrl}/${filePath}`,
            size: stats.size,
            lastModified: stats.mtime,
          });
        }
      }
      
      return files;
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
        return [];
      }
      
      logger.error({ error, directory }, 'Failed to list files locally');
      throw error;
    }
  }

  async getStorageStats(): Promise<StorageStats> {
    try {
      const files = await this.getAllFiles(this.basePath);
      
      let totalSize = 0;
      for (const file of files) {
        const stats = await fs.stat(file);
        totalSize += stats.size;
      }
      
      return {
        totalSize,
        fileCount: files.length,
        bandwidthUsed: 0, // Not tracked in local storage
      };
    } catch (error) {
      logger.error({ error }, 'Failed to get storage stats');
      throw error;
    }
  }

  private async getAllFiles(dir: string, files: string[] = []): Promise<string[]> {
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
          await this.getAllFiles(fullPath, files);
        } else {
          files.push(fullPath);
        }
      }
      
      return files;
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
        return files;
      }
      throw error;
    }
  }
}

/**
 * Storage factory to get appropriate storage service
 */
export class StorageFactory {
  private static instance: StorageService | null = null;

  static getStorage(): StorageService {
    if (!this.instance) {
      // Check if we're in production and BunnyCDN is configured
      const isProduction = process.env.NODE_ENV === 'production';
      const useBunnyCDN = isProduction || process.env.USE_BUNNYCDN === 'true';
      
      if (useBunnyCDN && isBunnyCDNConfigured()) {
        logger.info('Using BunnyCDN storage service');
        this.instance = new BunnyCDNService();
      } else {
        logger.info('Using local storage service for development');
        this.instance = new LocalStorageService();
      }
    }
    
    return this.instance;
  }

  // Reset instance (useful for testing)
  static reset(): void {
    this.instance = null;
  }
}

// Helper function to generate CDN-friendly file paths
export function generateCDNPath(
  announcementId: string,
  fileName: string,
  category: 'attachments' | 'thumbnails' = 'attachments'
): string {
  // Generate a unique identifier
  const timestamp = Date.now();
  const randomBytes = crypto.randomBytes(8).toString('hex');
  
  // Get file extension
  const extension = path.extname(fileName).toLowerCase();
  const nameWithoutExt = path.basename(fileName, extension);
  
  // Create a safe file name
  const safeName = nameWithoutExt
    .replace(/[^a-zA-Z0-9-_]/g, '_')
    .substring(0, 50); // Limit length
  
  // Build the path
  return `announcements/${announcementId}/${category}/${safeName}_${timestamp}_${randomBytes}${extension}`;
}

// Export the default storage service
export function getStorageService(): StorageService {
  return StorageFactory.getStorage();
}