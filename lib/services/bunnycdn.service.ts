import { getBunnyCDNConfig, getStorageEndpoint, type BunnyCDNConfig } from '@/lib/config/bunnycdn';
import pino from 'pino';

const logger = pino({ name: 'bunnycdn-service' });

export interface CDNFile {
  path: string;
  url: string;
  size: number;
  lastModified: Date;
  contentType?: string;
}

export interface UploadOptions {
  contentType?: string;
  overwrite?: boolean;
  onProgress?: (progress: number) => void;
}

export interface UrlOptions {
  expiresIn?: number; // seconds
  download?: boolean;
  fileName?: string;
}

export interface StorageStats {
  totalSize: number;
  fileCount: number;
  bandwidthUsed: number;
}

export class CDNError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public context?: Record<string, any>
  ) {
    super(message);
    this.name = 'CDNError';
  }
}

export class BunnyCDNService {
  private config: BunnyCDNConfig;
  private storageUrl: string;

  constructor(config?: BunnyCDNConfig) {
    this.config = config || getBunnyCDNConfig();
    this.storageUrl = getStorageEndpoint(this.config.storageZoneRegion, this.config.storageZoneName);
  }

  /**
   * Upload a file to BunnyCDN storage
   */
  async uploadFile(
    fileBuffer: Buffer,
    path: string,
    options: UploadOptions = {}
  ): Promise<CDNFile> {
    const startTime = Date.now();
    
    try {
      // Ensure path doesn't start with /
      const cleanPath = path.startsWith('/') ? path.slice(1) : path;
      const uploadUrl = `${this.storageUrl}/${cleanPath}`;

      logger.info({ path: cleanPath }, 'Uploading file to BunnyCDN');

      // Check if file exists and handle overwrite
      if (!options.overwrite) {
        const exists = await this.fileExists(cleanPath);
        if (exists) {
          throw new CDNError(
            'File already exists',
            'FILE_EXISTS',
            409,
            { path: cleanPath }
          );
        }
      }

      const headers: Record<string, string> = {
        'AccessKey': this.config.storageZonePassword,
        'Content-Type': options.contentType || 'application/octet-stream',
      };

      const response = await fetch(uploadUrl, {
        method: 'PUT',
        headers,
        body: fileBuffer,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new CDNError(
          `Upload failed: ${errorText}`,
          'UPLOAD_FAILED',
          response.status,
          { path: cleanPath, error: errorText }
        );
      }

      const cdnUrl = `${this.config.pullZoneUrl}/${cleanPath}`;
      const duration = Date.now() - startTime;
      
      logger.info({ path: cleanPath, duration }, 'File uploaded successfully');

      return {
        path: cleanPath,
        url: cdnUrl,
        size: fileBuffer.length,
        lastModified: new Date(),
        contentType: options.contentType,
      };
    } catch (error) {
      logger.error({ error, path }, 'Failed to upload file');
      
      if (error instanceof CDNError) {
        throw error;
      }
      
      throw new CDNError(
        'Failed to upload file',
        'UPLOAD_ERROR',
        500,
        { originalError: error }
      );
    }
  }

  /**
   * Delete a file from BunnyCDN storage
   */
  async deleteFile(path: string): Promise<boolean> {
    try {
      const cleanPath = path.startsWith('/') ? path.slice(1) : path;
      const deleteUrl = `${this.storageUrl}/${cleanPath}`;

      logger.info({ path: cleanPath }, 'Deleting file from BunnyCDN');

      const response = await fetch(deleteUrl, {
        method: 'DELETE',
        headers: {
          'AccessKey': this.config.storageZonePassword,
        },
      });

      if (response.status === 404) {
        logger.warn({ path: cleanPath }, 'File not found for deletion');
        return false;
      }

      if (!response.ok) {
        const errorText = await response.text();
        throw new CDNError(
          `Delete failed: ${errorText}`,
          'DELETE_FAILED',
          response.status,
          { path: cleanPath, error: errorText }
        );
      }

      logger.info({ path: cleanPath }, 'File deleted successfully');
      return true;
    } catch (error) {
      logger.error({ error, path }, 'Failed to delete file');
      
      if (error instanceof CDNError) {
        throw error;
      }
      
      throw new CDNError(
        'Failed to delete file',
        'DELETE_ERROR',
        500,
        { originalError: error }
      );
    }
  }

  /**
   * Get the public URL for a file
   */
  async getFileUrl(path: string, options: UrlOptions = {}): Promise<string> {
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    let url = `${this.config.pullZoneUrl}/${cleanPath}`;

    // Add query parameters if needed
    const params = new URLSearchParams();
    
    if (options.download) {
      params.set('download', '1');
    }
    
    if (options.fileName) {
      params.set('filename', options.fileName);
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    return url;
  }

  /**
   * List files in a directory
   */
  async listFiles(directory: string = ''): Promise<CDNFile[]> {
    try {
      const cleanDir = directory.startsWith('/') ? directory.slice(1) : directory;
      const listUrl = `${this.storageUrl}/${cleanDir}/`;

      logger.info({ directory: cleanDir }, 'Listing files in directory');

      const response = await fetch(listUrl, {
        method: 'GET',
        headers: {
          'AccessKey': this.config.storageZonePassword,
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new CDNError(
          `List failed: ${errorText}`,
          'LIST_FAILED',
          response.status,
          { directory: cleanDir, error: errorText }
        );
      }

      const files = await response.json();
      
      return files.map((file: any) => ({
        path: file.ObjectName,
        url: `${this.config.pullZoneUrl}/${file.ObjectName}`,
        size: file.Length,
        lastModified: new Date(file.LastChanged),
        contentType: file.ContentType,
      }));
    } catch (error) {
      logger.error({ error, directory }, 'Failed to list files');
      
      if (error instanceof CDNError) {
        throw error;
      }
      
      throw new CDNError(
        'Failed to list files',
        'LIST_ERROR',
        500,
        { originalError: error }
      );
    }
  }

  /**
   * Purge cache for a specific file or pattern
   */
  async purgeCache(path: string): Promise<boolean> {
    try {
      const cleanPath = path.startsWith('/') ? path.slice(1) : path;
      
      logger.info({ path: cleanPath }, 'Purging cache for file');

      // Get pull zone ID from the API first
      const pullZoneId = await this.getPullZoneId();
      
      const response = await fetch(`https://api.bunny.net/pullzone/${pullZoneId}/purgeCache`, {
        method: 'POST',
        headers: {
          'AccessKey': this.config.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          urls: [`${this.config.pullZoneUrl}/${cleanPath}`],
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new CDNError(
          `Purge failed: ${errorText}`,
          'PURGE_FAILED',
          response.status,
          { path: cleanPath, error: errorText }
        );
      }

      logger.info({ path: cleanPath }, 'Cache purged successfully');
      return true;
    } catch (error) {
      logger.error({ error, path }, 'Failed to purge cache');
      
      if (error instanceof CDNError) {
        throw error;
      }
      
      throw new CDNError(
        'Failed to purge cache',
        'PURGE_ERROR',
        500,
        { originalError: error }
      );
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats(): Promise<StorageStats> {
    try {
      logger.info('Fetching storage statistics');

      const response = await fetch(`https://api.bunny.net/storagezone/${this.config.storageZoneName}`, {
        method: 'GET',
        headers: {
          'AccessKey': this.config.apiKey,
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new CDNError(
          `Stats failed: ${errorText}`,
          'STATS_FAILED',
          response.status,
          { error: errorText }
        );
      }

      const data = await response.json();
      
      return {
        totalSize: data.StorageUsed || 0,
        fileCount: data.FilesStored || 0,
        bandwidthUsed: data.BandwidthUsed || 0,
      };
    } catch (error) {
      logger.error({ error }, 'Failed to get storage stats');
      
      if (error instanceof CDNError) {
        throw error;
      }
      
      throw new CDNError(
        'Failed to get storage stats',
        'STATS_ERROR',
        500,
        { originalError: error }
      );
    }
  }

  /**
   * Check if a file exists
   */
  private async fileExists(path: string): Promise<boolean> {
    try {
      const checkUrl = `${this.storageUrl}/${path}`;
      
      const response = await fetch(checkUrl, {
        method: 'HEAD',
        headers: {
          'AccessKey': this.config.storageZonePassword,
        },
      });

      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Get pull zone ID from hostname
   */
  private async getPullZoneId(): Promise<number> {
    try {
      const hostname = new URL(this.config.pullZoneUrl).hostname;
      
      const response = await fetch('https://api.bunny.net/pullzone', {
        method: 'GET',
        headers: {
          'AccessKey': this.config.apiKey,
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch pull zones');
      }

      const pullZones = await response.json();
      const pullZone = pullZones.find((zone: any) => 
        zone.Hostnames.some((h: any) => h.Value === hostname)
      );

      if (!pullZone) {
        throw new Error('Pull zone not found');
      }

      return pullZone.Id;
    } catch (error) {
      throw new CDNError(
        'Failed to get pull zone ID',
        'PULLZONE_ERROR',
        500,
        { originalError: error }
      );
    }
  }

  /**
   * Upload file with retry logic
   */
  async uploadFileWithRetry(
    fileBuffer: Buffer,
    path: string,
    options: UploadOptions = {},
    maxRetries: number = 3
  ): Promise<CDNFile> {
    let lastError: Error | undefined;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.uploadFile(fileBuffer, path, options);
      } catch (error) {
        lastError = error as Error;
        logger.warn(
          { error, attempt, maxRetries, path },
          'Upload attempt failed, retrying...'
        );
        
        if (attempt < maxRetries) {
          // Exponential backoff: 1s, 2s, 4s
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));
        }
      }
    }
    
    throw lastError || new Error('Upload failed after retries');
  }
}

// Singleton instance
let bunnyCDNService: BunnyCDNService | null = null;

export function getBunnyCDNService(): BunnyCDNService {
  if (!bunnyCDNService) {
    bunnyCDNService = new BunnyCDNService();
  }
  return bunnyCDNService;
}