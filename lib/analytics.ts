import logger from './logger'

export type EventProperties = Record<string, any>

/**
 * PostHog will be initialized on the client-side.
 * This is a placeholder for the actual implementation.
 */

/**
 * Server-side logging function that uses the logger
 */
export function serverLog(
  event: string, 
  properties: EventProperties = {},
  message?: string
) {
  logger.info({ event, ...properties }, message || `Event: ${event}`)
}

// Note: The logClientEvent server action has been moved to analytics-server.ts

/**
 * Log authentication events
 */
export function logAuth(action: 'login' | 'logout' | 'failed', userId?: string, metadata: EventProperties = {}) {
  serverLog(`auth_${action}`, {
    userId,
    timestamp: new Date().toISOString(),
    ...metadata
  })
}

/**
 * Log announcement views
 */
export function logAnnouncementView(announcementId: string, userId?: string, metadata: EventProperties = {}) {
  serverLog('announcement_view', {
    userId,
    announcementId,
    timestamp: new Date().toISOString(),
    ...metadata
  })
}

/**
 * Log user interactions with specific features
 */
export function logInteraction(
  feature: string, 
  action: string, 
  userId?: string, 
  resourceId?: string, 
  metadata: EventProperties = {}
) {
  serverLog(`interaction_${feature}_${action}`, {
    userId,
    resourceId,
    timestamp: new Date().toISOString(),
    ...metadata
  })
} 