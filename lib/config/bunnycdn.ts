export interface BunnyCDNConfig {
  apiKey: string;
  storageZoneName: string;
  storageZonePassword: string;
  storageZoneRegion: string;
  pullZoneUrl: string;
  storageApiUrl: string;
}

export function getBunnyCDNConfig(): BunnyCDNConfig {
  const config = {
    apiKey: process.env.BUNNYCDN_API_KEY || '',
    storageZoneName: process.env.BUNNYCDN_STORAGE_ZONE_NAME || '',
    storageZonePassword: process.env.BUNNYCDN_STORAGE_ZONE_PASSWORD || '',
    storageZoneRegion: process.env.BUNNYCDN_STORAGE_ZONE_REGION || 'de',
    pullZoneUrl: process.env.BUNNYCDN_PULL_ZONE_URL || '',
    storageApiUrl: process.env.BUNNYCDN_STORAGE_API_URL || 'https://storage.bunnycdn.com',
  };

  // Validate required configuration
  const requiredFields = [
    'apiKey',
    'storageZoneName',
    'storageZonePassword',
    'pullZoneUrl'
  ] as const;

  for (const field of requiredFields) {
    if (!config[field]) {
      throw new Error(`Missing required BunnyCDN configuration: ${field}`);
    }
  }

  return config;
}

// Storage zone regions mapping
export const STORAGE_REGIONS = {
  de: 'Frankfurt',
  uk: 'London',
  ny: 'New York',
  la: 'Los Angeles',
  sg: 'Singapore',
  syd: 'Sydney'
} as const;

export type StorageRegion = keyof typeof STORAGE_REGIONS;

// Helper to get storage endpoint URL based on region
export function getStorageEndpoint(region: string, storageZoneName: string): string {
  if (region === 'de') {
    // Frankfurt is the primary region, uses base URL
    return `https://storage.bunnycdn.com/${storageZoneName}`;
  }
  // Other regions use region-specific subdomains
  return `https://${region}.storage.bunnycdn.com/${storageZoneName}`;
}

// Helper to check if BunnyCDN is configured
export function isBunnyCDNConfigured(): boolean {
  try {
    getBunnyCDNConfig();
    return true;
  } catch {
    return false;
  }
}