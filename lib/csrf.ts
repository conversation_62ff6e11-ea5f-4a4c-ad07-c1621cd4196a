import { NextRequest, NextResponse } from 'next/server';
import { nanoid } from 'nanoid';
import { createHash } from 'crypto';
import { cookies } from 'next/headers';
import { ErrorType, createErrorResponse } from './error-handler';

// CSRF token cookie name
const CSRF_COOKIE_NAME = 'X-CSRF-Token';

// CSRF token header name
const CSRF_HEADER_NAME = 'X-CSRF-Token';

// Configuration for CSRF cookie
const CSRF_COOKIE_OPTIONS = {
  httpOnly: true,          // Not accessible via JavaScript
  secure: process.env.NODE_ENV === 'production', // HTTPS only in production
  sameSite: 'lax' as const, // Protects against CSRF in most contexts
  path: '/',               // Available across the site
  maxAge: 60 * 60 * 3,     // 3 hours
};

/**
 * Generates a new CSRF token and sets it in a cookie
 */
export async function generateCsrfToken(): Promise<string> {
  const token = nanoid(32);
  const cookieStore = await cookies();
  
  // Set the CSRF token in a cookie
  cookieStore.set(CSRF_COOKIE_NAME, token, CSRF_COOKIE_OPTIONS);
  
  return token;
}

/**
 * Gets the CSRF token from cookies or generates a new one
 */
export async function getCsrfToken(): Promise<string> {
  const cookieStore = await cookies();
  const token = cookieStore.get(CSRF_COOKIE_NAME)?.value;
  
  if (token) {
    return token;
  }
  
  return generateCsrfToken();
}

/**
 * Validates the CSRF token in the request
 * @param req NextRequest object
 * @returns true if valid, false if invalid
 */
export async function validateCsrfToken(req: NextRequest): Promise<boolean> {
  // Skip CSRF validation for GET requests and OPTIONS requests
  if (req.method === 'GET' || req.method === 'OPTIONS' || req.method === 'HEAD') {
    return true;
  }
  
  // Get the CSRF token from the request header
  const tokenFromHeader = req.headers.get(CSRF_HEADER_NAME);
  
  // Get the CSRF token from the cookie
  const cookieStore = await cookies();
  const tokenFromCookie = cookieStore.get(CSRF_COOKIE_NAME)?.value;
  
  // If either token is missing, return false
  if (!tokenFromHeader || !tokenFromCookie) {
    return false;
  }
  
  // Compare the tokens
  return tokenFromHeader === tokenFromCookie;
}

/**
 * CSRF protection middleware for API routes
 * @param req NextRequest object
 * @returns NextResponse if CSRF validation fails, otherwise undefined
 */
export async function csrfProtection(req: NextRequest): Promise<NextResponse | undefined> {
  // Skip CSRF validation for GET requests and OPTIONS requests
  if (req.method === 'GET' || req.method === 'OPTIONS' || req.method === 'HEAD') {
    return undefined;
  }
  
  // Validate the CSRF token
  const isValid = await validateCsrfToken(req);
  
  if (!isValid) {
    return createErrorResponse(
      ErrorType.AUTHENTICATION,
      'CSRF token is invalid or missing',
      { message: 'Provide a valid CSRF token in the X-CSRF-Token header' }
    );
  }
  
  return undefined;
}

/**
 * HOC (Higher Order Component) for Client Components to get a CSRF token
 * and include it in fetch requests
 */
export function withCsrfToken(fetch: typeof globalThis.fetch): typeof globalThis.fetch {
  return async (input, init) => {
    // Don't add CSRF token for GET requests
    if (!init || init.method === 'GET' || init.method === undefined) {
      return fetch(input, init);
    }
    
    try {
      // Get the CSRF token
      const response = await fetch('/api/csrf');
      const { token } = await response.json();
      
      // Add the CSRF token to the request headers
      const headers = new Headers(init.headers);
      headers.set(CSRF_HEADER_NAME, token);
      
      // Clone the init object and add the headers
      const newInit = { ...init, headers };
      
      // Make the actual request
      return fetch(input, newInit);
    } catch (error) {
      console.error('Failed to get CSRF token:', error);
      return fetch(input, init);
    }
  };
} 