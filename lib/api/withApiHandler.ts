import { NextRequest, NextResponse } from 'next/server';
import { ZodSchema } from 'zod';
import { ErrorType, handleApiError } from '../error-handler';
import { validateRequest } from '../validators';
import { csrfProtection } from '../csrf';
import { getCurrentUser } from '../supabase/auth';

type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
type Role = 'ADMIN' | 'USER' | 'super_admin' | 'editor' | 'user' | 'ANY';

interface ApiHandlerOptions<T> {
  method: HttpMethod;
  requireAuth?: boolean;
  requiredRole?: Role;
  schema?: ZodSchema<T>;
  csrfProtected?: boolean;
}

// Define our own validation result type to match what validateRequest returns
interface ValidationSuccess<T> {
  success: true;
  data: T;
}

interface ValidationError {
  success: false;
  error: NextResponse;
}

type ValidationResult<T> = ValidationSuccess<T> | ValidationError;

/**
 * Type guard to check if validation result is successful
 */
function isValidationSuccess<T>(result: ValidationResult<T>): result is ValidationSuccess<T> {
  return result.success === true;
}

/**
 * Type guard to check if validation result is an error
 */
function isValidationError<T>(result: ValidationResult<T>): result is ValidationError {
  return result.success === false;
}

/**
 * Higher-order function to wrap API handlers with common functionality
 * - Request method validation
 * - Authentication and authorization
 * - CSRF protection
 * - Input validation
 * - Error handling
 */
export function withApiHandler<T = any, R = any>(
  options: ApiHandlerOptions<T>,
  handler: (
    req: NextRequest,
    validatedData?: T,
    user?: any
  ) => Promise<NextResponse<R>>
) {
  return async function (
    req: NextRequest
  ): Promise<NextResponse> {
    try {
      // 1. Validate HTTP method
      if (req.method !== options.method) {
        return NextResponse.json(
          { error: `Method ${req.method} Not Allowed` },
          { status: 405 }
        );
      }

      // 2. Apply CSRF protection for state-changing operations
      if (
        options.csrfProtected !== false && 
        ['POST', 'PUT', 'DELETE', 'PATCH'].includes(options.method)
      ) {
        const csrfResult = await csrfProtection(req);
        if (csrfResult instanceof NextResponse) {
          return csrfResult;
        }
      }

      // 3. Authentication check
      let user = null;
      if (options.requireAuth) {
        try {
          user = await getCurrentUser();
          
          if (!user) {
            return NextResponse.json(
              { error: 'Authentication required' },
              { status: 401 }
            );
          }

          // 4. Role-based authorization
          if (
            options.requiredRole && 
            options.requiredRole !== 'ANY' && 
            user.role !== options.requiredRole
          ) {
            return NextResponse.json(
              { error: 'Insufficient permissions' },
              { status: 403 }
            );
          }
        } catch (error) {
          return NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          );
        }
      }

      // 5. Input validation
      let validatedData: T | undefined;
      if (options.schema) {
        const validation = await validateRequest(req, options.schema) as ValidationResult<T>;
        
        // Use type guard to narrow the type
        if (isValidationError(validation)) {
          return validation.error;
        } else {
          validatedData = validation.data;
        }
      }

      // 6. Call the handler function with validated data and user
      return await handler(req, validatedData, user);
    } catch (error: unknown) {
      // 7. Centralized error handling
      return handleApiError(error as Error, req);
    }
  };
} 