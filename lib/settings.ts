import { createServiceClient } from "@/lib/supabase/server";

export interface Setting {
  key: string;
  value: string;
  description?: string;
}

/**
 * Get settings from the database
 * @param prefix Optional prefix to filter settings by
 */
export async function getSettings(prefix?: string): Promise<Setting[]> {
  try {
    const supabase = await createServiceClient();
    
    let query = supabase
      .from('admin_settings')
      .select('key, value, description')
      .order('key');
    
    if (prefix) {
      query = query.like('key', `${prefix}_%`);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error("Error fetching settings:", error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error("Error fetching settings:", error);
    return [];
  }
}

/**
 * Get a single setting by key
 */
export async function getSetting(key: string): Promise<Setting | null> {
  try {
    const supabase = await createServiceClient();
    
    const { data, error } = await supabase
      .from('admin_settings')
      .select('key, value, description')
      .eq('key', key)
      .single();
    
    if (error || !data) {
      return null;
    }
    
    return data;
  } catch (error) {
    console.error(`Error fetching setting ${key}:`, error);
    return null;
  }
}

/**
 * Create a setting if it doesn't exist or update it if it does
 */
export async function createOrUpdateSetting(
  key: string, 
  value: string, 
  description: string = `Setting for ${key}`
): Promise<boolean> {
  try {
    const supabase = await createServiceClient();
    
    // Use upsert to insert or update in one operation
    const { error } = await supabase
      .from('admin_settings')
      .upsert(
        {
          key,
          value,
          description,
          updated_at: new Date().toISOString()
        },
        {
          onConflict: 'key'
        }
      );
    
    if (error) {
      console.error(`Error creating/updating setting ${key}:`, error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error(`Error creating/updating setting ${key}:`, error);
    return false;
  }
}

/**
 * Update multiple settings at once, creating any that don't exist
 */
export async function updateSettings(settings: Setting[]): Promise<boolean> {
  try {
    const supabase = await createServiceClient();
    
    // Prepare data for upsert
    const settingsData = settings.map(setting => ({
      key: setting.key,
      value: setting.value,
      description: setting.description || `Setting for ${setting.key}`,
      updated_at: new Date().toISOString()
    }));
    
    const { error } = await supabase
      .from('admin_settings')
      .upsert(settingsData, {
        onConflict: 'key'
      });
    
    if (error) {
      console.error("Error updating settings:", error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error("Error updating settings:", error);
    return false;
  }
}

/**
 * Get all phone numbers
 */
export async function getPhoneNumbers(): Promise<Setting[]> {
  return getSettings('phone');
}

/**
 * Get settings converted to a key-value object
 */
export async function getSettingsAsObject(prefix?: string): Promise<Record<string, string>> {
  const settings = await getSettings(prefix);
  
  const result: Record<string, string> = {};
  for (const setting of settings) {
    result[setting.key] = setting.value;
  }
  
  return result;
}

/**
 * Update phone number settings
 */
export async function updatePhoneNumbers(phones: Record<string, string>): Promise<boolean> {
  const settings: Setting[] = [];
  
  for (const [key, value] of Object.entries(phones)) {
    // Make sure it's a phone_* key
    if (!key.startsWith('phone_')) continue;
    
    settings.push({
      key,
      value
    });
  }
  
  return updateSettings(settings);
}

/**
 * Get a specific phone number by key
 * @param key The phone key (e.g., 'emergency', 'police')
 */
export async function getPhoneNumber(key: string): Promise<string | null> {
  const setting = await getSetting(`phone_${key}`);
  return setting ? setting.value : null;
}

/**
 * Get a specific phone number with fallback
 */
export async function getPhoneNumberWithFallback(key: string, fallback: string): Promise<string> {
  const phone = await getPhoneNumber(key);
  return phone !== null ? phone : fallback;
}

/**
 * Get all email-related settings
 */
export async function getEmailSettings(): Promise<Setting[]> {
  // Get SMTP settings and other email-related settings
  const smtpSettings = await getSettings('smtp');
  const emailDevMode = await getSetting('email_dev_mode');
  const emailDevRecipient = await getSetting('email_dev_recipient');
  const testEmailConfig = await getSetting('test_email_config');
  
  const allSettings = [...smtpSettings];
  
  if (emailDevMode) allSettings.push(emailDevMode);
  if (emailDevRecipient) allSettings.push(emailDevRecipient);
  if (testEmailConfig) allSettings.push(testEmailConfig);
  
  return allSettings;
}

/**
 * Update email dev settings
 */
export async function updateEmailDevSettings(
  mode: 'redirect' | 'skip',
  recipient: string
): Promise<boolean> {
  return updateSettings([
    {
      key: 'email_dev_mode',
      value: mode,
      description: 'How to handle emails in development mode'
    },
    {
      key: 'email_dev_recipient',
      value: recipient,
      description: 'Email address to redirect all non-test emails to in development mode'
    }
  ]);
}

/**
 * Get environment information
 */
export function getEnvironmentInfo() {
  return {
    nodeEnv: process.env.NODE_ENV || 'development',
    isDevelopment: process.env.NODE_ENV !== 'production',
    isProduction: process.env.NODE_ENV === 'production',
  };
} 