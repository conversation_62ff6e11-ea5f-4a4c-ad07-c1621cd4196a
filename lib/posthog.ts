'use client'

/**
 * PostHog client integration.
 * This file configures and exports PostHog for client-side analytics.
 */

import posthog from 'posthog-js'

type PosthogEventProperties = Record<string, any>

/**
 * Initialize PostHog.
 * Call this function in your app layout component.
 */
export const initPostHog = () => {
  if (typeof window !== 'undefined') {
    const apiKey = process.env.NEXT_PUBLIC_POSTHOG_KEY
    const apiHost = process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://app.posthog.com'
    
    if (!apiKey) {
      console.warn('[PostHog] API key not found in environment variables')
      return
    }
    
    // Check for opt-out cookie before initializing
    if (isAnalyticsOptedOut()) {
      console.log('[PostHog] User has opted out of analytics tracking')
      return
    }
    
    posthog.init(apiKey, {
      api_host: apiHost,
      capture_pageview: true,
      loaded: (ph: any) => {
        if (process.env.NODE_ENV === 'development') {
          ph.debug()
        }
      },
      disable_session_recording: true, // Disable session recording by default
      autocapture: false  // Disable autocapture for better privacy and control
    })
  }
}

/**
 * Check if user has opted out of analytics
 */
export const isAnalyticsOptedOut = (): boolean => {
  if (typeof window === 'undefined') return false
  return document.cookie.includes('analytics_opt_out=true')
}

/**
 * Opt out of analytics tracking
 */
export const optOutAnalytics = (): void => {
  document.cookie = 'analytics_opt_out=true; path=/; max-age=31536000; SameSite=Lax' // 1 year expiry
  if (typeof window !== 'undefined' && posthog) {
    posthog.opt_out_capturing()
  }
}

/**
 * Opt in to analytics tracking
 */
export const optInAnalytics = (): void => {
  document.cookie = 'analytics_opt_out=false; path=/; max-age=31536000; SameSite=Lax' // 1 year expiry
  if (typeof window !== 'undefined' && posthog) {
    posthog.opt_in_capturing()
  }
}

// Export PostHog for use in other files
export { posthog } 