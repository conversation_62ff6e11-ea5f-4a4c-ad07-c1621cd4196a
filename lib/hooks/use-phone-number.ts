"use client";

import { useState, useEffect } from 'react';

/**
 * React hook to retrieve phone numbers from settings
 * @param phoneType The type of phone number (without the 'phone_' prefix)
 * @param fallback Fallback value if phone can't be retrieved
 */
export function usePhoneNumber(phoneType: string, fallback: string = '') {
  const [phoneNumber, setPhoneNumber] = useState<string>(fallback);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isMounted = true;
    
    const fetchPhoneNumber = async () => {
      setIsLoading(true);
      
      try {
        const response = await fetch('/api/settings/phones');
        
        if (!response.ok) {
          throw new Error('Failed to fetch phone numbers');
        }
        
        const data = await response.json();
        
        if (isMounted) {
          setPhoneNumber(data[phoneType] || fallback);
          setError(null);
        }
      } catch (err) {
        if (isMounted) {
          console.error('Error fetching phone number:', err);
          setError(err instanceof Error ? err : new Error('Unknown error'));
          // Use fallback on error
          setPhoneNumber(fallback);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };
    
    fetchPhoneNumber();
    
    return () => {
      isMounted = false;
    };
  }, [phoneType, fallback]);

  return { phoneNumber, isLoading, error };
}

/**
 * Format a phone number for display
 * @param phoneNumber The raw phone number
 */
export function formatPhoneNumber(phoneNumber: string): string {
  // If it already contains spaces, assume it's already formatted
  if (phoneNumber.includes(' ')) {
    return phoneNumber;
  }
  
  // Lithuanian landline format: 846 XXXXXX
  if (/^846\d{6}$/.test(phoneNumber)) {
    return phoneNumber.replace(/^(\d{3})(\d{6})$/, '$1 $2');
  }
  
  // Lithuanian mobile format: +370 XXXXXXXX
  if (/^\+370\d{8}$/.test(phoneNumber)) {
    return phoneNumber.replace(/^(\+370)(\d{2})(\d{3})(\d{3})$/, '$1 $2 $3 $4');
  }
  
  // Generic format for international numbers
  if (phoneNumber.startsWith('+')) {
    return phoneNumber.replace(/^(\+\d{3})(\d{2})(\d{3})(\d{4})$/, '$1 $2 $3 $4');
  }
  
  // 3 digit emergency numbers
  if (/^\d{3}$/.test(phoneNumber)) {
    return phoneNumber;
  }
  
  // Return as-is if no formatting patterns match
  return phoneNumber;
} 