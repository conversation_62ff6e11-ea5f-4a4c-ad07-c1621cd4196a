"use client";

import { useEffect, useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import { debounce } from 'lodash';

interface UseAutoSaveProps<T> {
  onSave: (data: T) => Promise<void>;
  debounceMs?: number;
}

export function useAutoSave<T>({ onSave, debounceMs = 2000 }: UseAutoSaveProps<T>) {
  const { watch } = useFormContext<T>();

  const debouncedSave = useCallback(
    debounce((data: T) => {
      onSave(data);
    }, debounceMs),
    [onSave, debounceMs]
  );

  useEffect(() => {
    const subscription = watch((value) => {
      debouncedSave(value as T);
    });
    return () => subscription.unsubscribe();
  }, [watch, debouncedSave]);
} 