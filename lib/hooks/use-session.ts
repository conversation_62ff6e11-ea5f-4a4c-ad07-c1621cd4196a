"use client";

import { useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { useCurrentUser } from '@/lib/tanstack/queries';
import { queryClient } from '@/lib/tanstack/query-client';

export function useSession() {
  const { data: user, isLoading, error } = useCurrentUser();

  // Subscribe to auth changes and invalidate query when needed
  useEffect(() => {
    const supabase = createClient();
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event) => {
      // When auth state changes, invalidate the current user query
      if (event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {
        queryClient.invalidateQueries({ queryKey: ['user', 'current'] });
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Determine status based on query state
  const getStatus = () => {
    if (isLoading) return 'loading';
    if (error || !user) return 'unauthenticated';
    return 'authenticated';
  };

  return {
    data: user ? { user } : null,
    status: getStatus()
  };
}