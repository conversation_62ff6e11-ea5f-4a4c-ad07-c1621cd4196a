'use client'

import { useCallback } from 'react'
import { logClientEvent } from '../analytics-server'
import { posthog, isAnalyticsOptedOut } from '../posthog'

type EventProperties = Record<string, any>

/**
 * Hook for tracking events in client components.
 * 
 * Uses PostHog for client-side tracking and server actions for events
 * that need to be logged on the server.
 */
export function useAnalytics() {
  // Check if analytics is enabled
  const isEnabled = useCallback(() => {
    return typeof window !== 'undefined' && !isAnalyticsOptedOut()
  }, [])

  // Track regular events with PostHog
  const trackEvent = useCallback((eventName: string, properties: EventProperties = {}) => {
    if (!isEnabled()) return
    
    if (typeof window !== 'undefined') {
      posthog.capture(eventName, properties)
      
      // Also log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Analytics] Tracking event: ${eventName}`, properties)
      }
    }
  }, [isEnabled])
  
  // Track important events (logs to both client and server)
  const trackImportantEvent = useCallback(async (
    eventName: string, 
    resourceId?: string,
    properties: EventProperties = {}
  ) => {
    // Log client-side with PostHog
    trackEvent(eventName, { resourceId, ...properties })
    
    // Also log on server
    await logClientEvent({
      eventType: eventName,
      resourceId,
      metadata: properties
    })
  }, [trackEvent])
  
  // Track page views
  const trackPageView = useCallback((path: string, properties: EventProperties = {}) => {
    if (!isEnabled()) return
    
    // Log the custom page_view event
    trackEvent('page_view', { path, ...properties })
    
    // Also use PostHog's built-in pageview tracking
    if (typeof window !== 'undefined') {
      posthog.capture('$pageview', { path, ...properties })
    }
  }, [trackEvent, isEnabled])
  
  // Track announcement views
  const trackAnnouncementView = useCallback((
    announcementId: string, 
    title?: string,
    properties: EventProperties = {}
  ) => {
    trackImportantEvent('announcement_view', announcementId, { title, ...properties })
  }, [trackImportantEvent])
  
  // Track user interactions
  const trackInteraction = useCallback((
    feature: string,
    action: string,
    resourceId?: string,
    properties: EventProperties = {}
  ) => {
    trackEvent(`interaction_${feature}_${action}`, { resourceId, ...properties })
  }, [trackEvent])
  
  // Track important user interactions (logged on server too)
  const trackImportantInteraction = useCallback((
    feature: string,
    action: string,
    resourceId?: string,
    properties: EventProperties = {}
  ) => {
    trackImportantEvent(`interaction_${feature}_${action}`, resourceId, properties)
  }, [trackImportantEvent])
  
  // Identity management - associate user with their analytics data
  const identifyUser = useCallback((userId: string, traits: EventProperties = {}) => {
    if (!isEnabled()) return
    
    if (typeof window !== 'undefined') {
      posthog.identify(userId, traits)
    }
  }, [isEnabled])
  
  // Reset user identity
  const resetIdentity = useCallback(() => {
    if (typeof window !== 'undefined') {
      posthog.reset()
    }
  }, [])
  
  return {
    trackEvent,
    trackImportantEvent,
    trackPageView,
    trackAnnouncementView,
    trackInteraction,
    trackImportantInteraction,
    identifyUser,
    resetIdentity,
    isEnabled
  }
} 