import { createServiceClient } from "@/lib/supabase/server";

export interface Contact {
  id: number;
  label: string;
  phone_number: string;
  description?: string;
  company_name?: string;
  category: string;
  display_in_footer: boolean;
  display_in_contacts: boolean;
  display_in_emergency: boolean;
  display_order: number;
  icon?: string;
  created_at: Date;
  updated_at?: Date;
}

export interface CreateContactData {
  label: string;
  phone_number: string;
  description?: string;
  company_name?: string;
  category: string;
  display_in_footer?: boolean;
  display_in_contacts?: boolean;
  display_in_emergency?: boolean;
  display_order?: number;
  icon?: string;
}

export interface UpdateContactData {
  label?: string;
  phone_number?: string;
  description?: string;
  company_name?: string;
  category?: string;
  display_in_footer?: boolean;
  display_in_contacts?: boolean;
  display_in_emergency?: boolean;
  display_order?: number;
  icon?: string;
}

export const CONTACT_CATEGORIES = {
  emergency: {
    name: "Svarbiausi kontaktai",
    description: "Pagalbos ir avarinių tarnybų numeriai"
  },
  maintenance: {
    name: "Kiti avariniai kontaktai",
    description: "Priežiūros ir remonto tarnybų numeriai"
  },
  building: {
    name: "Bendrijos kontaktai", 
    description: "DNSB administracijos kontaktai"
  },
  custom: {
    name: "Kiti kontaktai",
    description: "Papildomi kontaktai"
  }
};

/**
 * Get all contacts
 */
export async function getContacts(): Promise<Contact[]> {
  try {
    const supabase = await createServiceClient();
    
    const { data, error } = await supabase
      .from('contacts')
      .select('*')
      .order('category')
      .order('display_order');
    
    if (error) {
      console.error("Error fetching contacts:", error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error("Error fetching contacts:", error);
    return [];
  }
}

/**
 * Get contacts for display in different sections
 */
export async function getContactsForDisplay(displayLocation: 'footer' | 'contacts' | 'emergency'): Promise<Contact[]> {
  try {
    const supabase = await createServiceClient();
    const locationField = `display_in_${displayLocation}`;
    
    const { data, error } = await supabase
      .from('contacts')
      .select('*')
      .eq(locationField, true)
      .order('category')
      .order('display_order');
    
    if (error) {
      console.error(`Error fetching contacts for ${displayLocation}:`, error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error(`Error fetching contacts for ${displayLocation}:`, error);
    return [];
  }
}

/**
 * Get a single contact by ID
 */
export async function getContactById(id: number): Promise<Contact | null> {
  try {
    const supabase = await createServiceClient();
    
    const { data, error } = await supabase
      .from('contacts')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error || !data) {
      return null;
    }
    
    return data;
  } catch (error) {
    console.error(`Error fetching contact ${id}:`, error);
    return null;
  }
}

/**
 * Create a new contact
 */
export async function createContact(data: CreateContactData): Promise<Contact | null> {
  try {
    const supabase = await createServiceClient();
    
    const {
      label,
      phone_number,
      description,
      company_name,
      category,
      display_in_footer = false,
      display_in_contacts = true,
      display_in_emergency = false,
      display_order = 0,
      icon
    } = data;
    
    const { data: contact, error } = await supabase
      .from('contacts')
      .insert({
        label,
        phone_number,
        description,
        company_name,
        category,
        display_in_footer,
        display_in_contacts,
        display_in_emergency,
        display_order,
        icon
      })
      .select()
      .single();
    
    if (error) {
      console.error("Error creating contact:", error);
      return null;
    }
    
    return contact;
  } catch (error) {
    console.error("Error creating contact:", error);
    return null;
  }
}

/**
 * Update an existing contact
 */
export async function updateContact(id: number, data: UpdateContactData): Promise<Contact | null> {
  try {
    const supabase = await createServiceClient();
    
    // Filter out undefined values
    const updateData = Object.fromEntries(
      Object.entries(data).filter(([_, value]) => value !== undefined)
    );
    
    // Add updated_at timestamp
    updateData.updated_at = new Date().toISOString();
    
    const { data: contact, error } = await supabase
      .from('contacts')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      console.error(`Error updating contact ${id}:`, error);
      return null;
    }
    
    return contact;
  } catch (error) {
    console.error(`Error updating contact ${id}:`, error);
    return null;
  }
}

/**
 * Delete a contact
 */
export async function deleteContact(id: number): Promise<boolean> {
  try {
    const supabase = await createServiceClient();
    
    const { error } = await supabase
      .from('contacts')
      .delete()
      .eq('id', id);
    
    if (error) {
      console.error(`Error deleting contact ${id}:`, error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error(`Error deleting contact ${id}:`, error);
    return false;
  }
}

/**
 * Reorder contacts within a category
 */
export async function reorderContacts(category: string, orderedIds: number[]): Promise<boolean> {
  try {
    const supabase = await createServiceClient();
    
    // Update each contact's display_order
    const updatePromises = orderedIds.map((id, index) =>
      supabase
        .from('contacts')
        .update({
          display_order: index,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('category', category)
    );
    
    const results = await Promise.all(updatePromises);
    
    // Check if any updates failed
    const hasError = results.some(result => result.error);
    
    if (hasError) {
      console.error('Error reordering contacts:', results.filter(r => r.error).map(r => r.error));
      return false;
    }
    
    return true;
  } catch (error) {
    console.error(`Error reordering contacts:`, error);
    return false;
  }
} 