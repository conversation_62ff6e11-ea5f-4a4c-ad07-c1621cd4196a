import { z } from 'zod';
import { NextRequest, NextResponse } from 'next/server';

// Common validation schemas
export const userSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email format'),
  role: z.enum(['developer', 'super_admin', 'editor', 'user'], {
    required_error: 'Role must be one of: developer, super_admin, editor, user',
  }),
  password: z.string().min(8, 'Password must be at least 8 characters').optional(),
  street: z.string().optional(),
  house_number: z.string().optional(),
  flat_number: z.string().optional(),
  phone: z.string().optional(),
  is_profile_updated: z.boolean().default(false),
  test_user: z.boolean().default(false).optional(),
});

export const announcementSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  importance: z.enum(['normal', 'important', 'urgent']).default('normal'),
  isDraft: z.boolean().default(false),
  recipientType: z.enum(['all', 'streets', 'houses', 'flats', 'users']).default('all'),
  streetIds: z.array(z.string()).optional(),
  houseIds: z.array(z.string()).optional(),
  flatIds: z.array(z.string()).optional(),
  userIds: z.array(z.string()).optional(),
});

export const contactMessageSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email format'),
  category: z.string().min(1, 'Category is required'),
  subject: z.string().min(1, 'Subject is required'),
  message: z.string().min(1, 'Message is required'),
  phone_number: z.string().optional(),
});

export const feedbackSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email format'),
  category: z.string().min(1, 'Category is required'),
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  rating: z.number().min(1).max(5),
  allow_contact: z.boolean().default(false),
});

export const emergencyContactSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  phone: z.string().min(1, 'Phone is required'),
  relationship: z.string().min(1, 'Relationship is required'),
  custom_relationship: z.string().optional(),
  is_emergency_contact: z.boolean().default(true),
});

// Improve type safety for validation results
type ValidationSuccess<T> = { 
  success: true; 
  data: T 
};

type ValidationError = { 
  success: false; 
  error: NextResponse 
};

type ValidationResult<T> = ValidationSuccess<T> | ValidationError;

// Generic validation middleware for API routes
export async function validateRequest<T>(
  req: NextRequest,
  schema: z.ZodSchema<T>,
): Promise<ValidationResult<T>> {
  try {
    let body;
    
    // Parse the request body
    try {
      body = await req.json();
    } catch (error) {
      return {
        success: false,
        error: NextResponse.json(
          { error: 'Invalid JSON in request body' },
          { status: 400 }
        )
      };
    }
    
    // Validate with Zod schema
    const result = schema.safeParse(body);
    
    if (!result.success) {
      // Extract formatted error messages
      const formattedErrors = result.error.format();
      
      return {
        success: false,
        error: NextResponse.json(
          { 
            error: 'Validation failed',
            details: formattedErrors
          },
          { status: 400 }
        )
      };
    }
    
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('Validation error:', error);
    
    return {
      success: false,
      error: NextResponse.json(
        { error: 'Server error during validation' },
        { status: 500 }
      )
    };
  }
} 