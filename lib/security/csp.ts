/**
 * Content Security Policy Configuration
 * 
 * This file contains the CSP configuration for the application.
 * It defines which sources are allowed for various resource types.
 */

// Define trusted domains for resources
const SELF = "'self'";
const NONE = "'none'";
const UNSAFE_INLINE = "'unsafe-inline'";
const UNSAFE_EVAL = "'unsafe-eval'";
const BLOB = "blob:";
const DATA = "data:";

// Define trusted external domains
const TRUSTED_DOMAINS = {
  fonts: [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com',
  ],
  images: [
    'https://res.cloudinary.com',
    'https://images.unsplash.com',
    DATA,
  ],
  scripts: [
    // Add any trusted script sources here
  ],
  styles: [
    'https://fonts.googleapis.com',
  ],
  connects: [
    // Add any trusted API endpoints or external services here
    'wss://*.dnsbvakarai.lt',
    'https://*.dnsbvakarai.lt',
    'http://supabasekong-y40ggo44c8gg4cw4ssws8c4c.***********.sslip.io',
    'https://app.posthog.com',
    'https://eu.i.posthog.com',
    'wss://*', // For WebSocket connections
  ],
};

/**
 * Create a Content Security Policy string based on environment
 */
export function createCSP(isDev = process.env.NODE_ENV !== 'production'): string {
  // Log the environment for debugging
  console.log('Creating CSP with NODE_ENV:', process.env.NODE_ENV, 'isDev:', isDev);
  
  // Only disable CSP in development, never in production
  // This ensures proper security in production environment
  if (process.env.NODE_ENV === 'development') return '';
  
  // In development, return an empty string to disable CSP
  if (isDev || process.env.NODE_ENV === 'development') {
    return '';
  }

  // Always allow unsafe-inline for styles since Next.js uses them extensively
  const styleSrc = [SELF, UNSAFE_INLINE, ...TRUSTED_DOMAINS.styles];
  
  // Next.js requires unsafe-inline and unsafe-eval for scripts in production
  const scriptSrc = [SELF, UNSAFE_INLINE, UNSAFE_EVAL, ...TRUSTED_DOMAINS.scripts];

  const directives = {
    'default-src': [SELF],
    'script-src': scriptSrc,
    'style-src': styleSrc,
    'font-src': [SELF, DATA, ...TRUSTED_DOMAINS.fonts],
    'img-src': [SELF, DATA, BLOB, ...TRUSTED_DOMAINS.images],
    'connect-src': [SELF, ...TRUSTED_DOMAINS.connects],
    'media-src': [SELF, DATA, BLOB],
    'worker-src': [SELF, BLOB], // Allow web workers from blob URLs
    'object-src': [NONE],
    'base-uri': [SELF],
    'form-action': [SELF],
    'frame-ancestors': [NONE],
    'upgrade-insecure-requests': [],
  };

  return Object.entries(directives)
    .map(([key, values]) => {
      if (values.length === 0) return key;
      return `${key} ${values.join(' ')}`;
    })
    .join('; ');
}

/**
 * Create secure headers with CSP and other security headers
 */
export function getSecurityHeaders(isDev = process.env.NODE_ENV !== 'production'): Record<string, string> {
  const csp = createCSP(isDev);
  
  // In development, don't apply any CSP
  if (isDev || process.env.NODE_ENV === 'development') {
    return {
      'X-DNS-Prefetch-Control': 'on',
      'X-XSS-Protection': '1; mode=block',
      'X-Frame-Options': 'DENY',
      'X-Content-Type-Options': 'nosniff',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
    };
  }
  
  return {
    ...(csp ? { 'Content-Security-Policy': csp } : {}),
    'X-DNS-Prefetch-Control': 'on',
    'X-XSS-Protection': '1; mode=block',
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    ...(process.env.NODE_ENV === 'production' && {
      'Strict-Transport-Security': 'max-age=63072000; includeSubDomains; preload',
    }),
  };
} 