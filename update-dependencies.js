#!/usr/bin/env node

/**
 * This script updates package.json dependencies to the latest versions
 * specified for React 19, Next.js 15.2.2, and Tailwind 4.0
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Path to package.json
const packageJsonPath = path.join(process.cwd(), 'package.json');

// Check if package.json exists
if (!fs.existsSync(packageJsonPath)) {
  console.error('package.json not found in the current directory');
  process.exit(1);
}

// Read package.json
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// Define the versions to update to
const targetVersions = {
  // Core dependencies
  "react": "^19.0.0",
  "react-dom": "^19.0.0",
  "next": "^15.2.2",
  
  // Tailwind dependencies
  "tailwindcss": "^4.0.0",
  "@tailwindcss/postcss": "^0.1.0",
  "postcss": "^8.4.35",
  
  // Additional dependencies that should be updated
  "@types/react": "^19.0.1",
  "@types/react-dom": "^19.0.0",
  "eslint-config-next": "^15.2.2",
};

// Update dependencies
['dependencies', 'devDependencies'].forEach(depType => {
  if (packageJson[depType]) {
    Object.keys(packageJson[depType]).forEach(dep => {
      if (targetVersions[dep]) {
        console.log(`Updating ${dep} from ${packageJson[depType][dep]} to ${targetVersions[dep]}`);
        packageJson[depType][dep] = targetVersions[dep];
      }
    });
  }
});

// Add overrides for React 19 compatibility
if (!packageJson.overrides) {
  packageJson.overrides = {};
}

packageJson.overrides = {
  ...packageJson.overrides,
  "react-is": "^19.0.0"
};

// Write updated package.json
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
console.log('package.json updated');

// Create a backup of postcss.config.js if it exists
const postcssConfigPath = path.join(process.cwd(), 'postcss.config.js');
if (fs.existsSync(postcssConfigPath)) {
  const backupPath = postcssConfigPath + '.backup';
  fs.copyFileSync(postcssConfigPath, backupPath);
  console.log(`Backup of postcss.config.js created at ${backupPath}`);
}

// Update postcss.config.js for Tailwind 4.0
const newPostcssConfig = `module.exports = {
  plugins: {
    '@tailwindcss/postcss': {},
  },
};
`;

fs.writeFileSync(postcssConfigPath, newPostcssConfig);
console.log('postcss.config.js updated for Tailwind 4.0');

// Create a backup of tailwind.config.js if it exists
const tailwindConfigPath = path.join(process.cwd(), 'tailwind.config.js');
if (fs.existsSync(tailwindConfigPath)) {
  const backupPath = tailwindConfigPath + '.backup';
  fs.copyFileSync(tailwindConfigPath, backupPath);
  console.log(`Backup of tailwind.config.js created at ${backupPath}`);
}

// Instructions for next steps
console.log('\n=== Next Steps ===');
console.log('1. Run: npm install --legacy-peer-deps');
console.log('2. Create a globals.css file with:');
console.log(`   @import "tailwindcss";
   
   @theme {
     /* Transfer your theme settings from tailwind.config.js to here */
   }`);
console.log('3. Test your application and fix any issues');
console.log('4. See UPDATE-NOTES.md for more detailed information about the upgrades'); 