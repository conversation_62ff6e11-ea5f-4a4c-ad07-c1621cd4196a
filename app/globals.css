@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 240 60% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 239 84% 98%;
    --accent-foreground: 240 60% 50%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  /* Toast progress animation */
  @keyframes toast-progress {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(0%);
    }
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 0 0% 100%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
  
  /* High contrast mode for better accessibility */
  .high-contrast {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    
    --primary: 240 100% 35%;
    --primary-foreground: 0 0% 100%;
    
    --secondary: 0 0% 90%;
    --secondary-foreground: 0 0% 0%;
    
    --muted: 0 0% 95%;
    --muted-foreground: 0 0% 25%;
    
    --accent: 240 100% 95%;
    --accent-foreground: 240 100% 30%;
    
    --destructive: 0 100% 40%;
    --destructive-foreground: 0 0% 100%;
    
    --border: 0 0% 40%;
    --input: 0 0% 90%;
    --ring: 240 100% 35%;
  }
  
  /* High contrast dark mode */
  .dark.high-contrast {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    
    --card: 0 0% 10%;
    --card-foreground: 0 0% 100%;
    
    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;
    
    --primary: 240 100% 60%;
    --primary-foreground: 0 0% 100%;
    
    --secondary: 0 0% 20%;
    --secondary-foreground: 0 0% 100%;
    
    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 85%;
    
    --accent: 240 100% 20%;
    --accent-foreground: 0 0% 100%;
    
    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;
    
    --border: 0 0% 80%;
    --input: 0 0% 20%;
    --ring: 240 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
}

/* Critical styles that should be available globally */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.main-heading {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #111827;
}

.sub-heading {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #374151;
}

/* Accessibility improvements */
/* High contrast text for better readability */
.high-contrast-text {
  color: #000000;
  font-weight: 500;
}

/* Larger font options for better readability */
.text-accessible {
  font-size: 1.05rem;
  line-height: 1.6;
}

/* Button accessibility - ensure adequate padding and clear borders */
.btn-accessible {
  padding: 0.6rem 1.2rem;
  border: 2px solid transparent;
  font-weight: 500;
  font-size: 1rem;
  border-radius: 0.375rem;
}

.btn-accessible:focus {
  outline: 3px solid rgba(99, 102, 241, 0.6);
  outline-offset: 2px;
}

/* Focus styles for interactive elements */
.focus-visible {
  @apply ring-2 ring-indigo-500 ring-offset-2;
  outline: none;
}

/* High contrast mode additional styles */
.high-contrast button {
  border: 2px solid currentColor;
}

.high-contrast a {
  text-decoration: underline;
  text-underline-offset: 2px;
}

.high-contrast input,
.high-contrast select,
.high-contrast textarea {
  border: 2px solid var(--border);
}

.high-contrast *:focus {
  outline: 3px solid var(--ring);
  outline-offset: 2px;
}

.high-contrast .muted-foreground {
  color: hsl(var(--muted-foreground));
  font-weight: 500; /* Increase weight for better readability */
}

.high-contrast .border {
  border-width: 2px;
}

/* Calendar and date picker fixes */
.rdp {
  margin: 0;
  position: relative;
  z-index: 50 !important;
}

.rdp-months {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.rdp-button:hover:not([disabled]) {
  background-color: rgba(79, 70, 229, 0.1);
}

.rdp-day_selected, 
.rdp-day_selected:focus-visible, 
.rdp-day_selected:hover {
  background-color: #4f46e5 !important;
  color: white !important;
}

/* Fix for the popover positioning */
div[data-radix-popper-content-wrapper] {
  z-index: 999 !important; 
}