/* This file ensures consistent styling regardless of CSP */

/* Force light mode on all form elements */
input, 
textarea, 
select,
button,
.radix-dropdown,
[role="dialog"],
[role="tabpanel"],
[role="tab"],
[role="listbox"] {
  color-scheme: light !important;
  background-color: white;
  color: #000;
}

/* Card styling */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  margin-bottom: 1rem;
}

/* Emergency contact cards */
.emergency-card {
  background-color: #f0f9ff;
  border-left: 4px solid #3b82f6;
}

.emergency-card.water {
  background-color: #ecfdf5;
  border-left-color: #10b981;
}

.emergency-card.service {
  background-color: #fef2f2;
  border-left-color: #ef4444;
}

.emergency-card.lift {
  background-color: #fffbeb;
  border-left-color: #f59e0b;
}

/* Phone numbers */
.phone-number {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e40af;
}

/* Container styling */
.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

/* Heading styling */
.main-heading {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #111827;
}

.sub-heading {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #374151;
}

/* Emergency service icons */
.service-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 9999px;
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  margin-bottom: 0.5rem;
} 