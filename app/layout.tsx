import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster as CustomToaster } from "@/components/ui/toaster";
import { SupabaseAuthProvider } from "@/components/supabase-auth-provider";
import { ThemeProvider } from "@/components/theme-provider";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Toaster } from "sonner";
import { AnalyticsProvider } from "@/components/analytics-provider";
import { QueryProvider } from "@/components/providers/simple-query-provider";

const inter = Inter({ 
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap"
});

export const metadata: Metadata = {
  title: "DNSB Vakarai",
  description: "Daugiabučių namų savininkų bendrija",
  metadataBase: new URL('https://dnsbvakarai.lt'),
  alternates: {
    canonical: '/',
  },
  keywords: ["DNSB Vakarai", "Daugiabučių namų savininkų bendrija", "Klaipėda"],
  openGraph: {
    title: "DNSB Vakarai",
    description: "Daugiabučių namų savininkų bendrija",
    url: 'https://dnsbvakarai.lt',
    siteName: 'DNSB Vakarai',
    locale: 'lt_LT',
    type: 'website',
    images: [
      {
        url: '/images/logo/dnsb-vakarai-logo-white.svg',
        width: 800,
        height: 600,
        alt: 'DNSB Vakarai logotipas',
      }
    ],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
  icons: {
    icon: [
      { url: '/favicon-96x96.png', sizes: '96x96', type: 'image/png' },
      { url: '/favicon.svg', type: 'image/svg+xml' },
      { url: '/favicon.ico' }
    ],
    apple: {
      url: '/apple-touch-icon.png',
      sizes: '180x180',
    },
    shortcut: { url: '/favicon.ico' },
  },
  manifest: '/site.webmanifest',
  appleWebApp: {
    title: 'DNSB Vakarai',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="lt" suppressHydrationWarning>
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "DNSB Vakarai",
              "description": "Daugiabučių namų savininkų bendrija",
              "url": "https://dnsbvakarai.lt",
              "logo": "https://dnsbvakarai.lt/images/logo/dnsb-vakarai-logo-white.svg",
              "address": {
                "@type": "PostalAddress",
                "streetAddress": "Smiltelės g. 53 – 2",
                "addressLocality": "Klaipėda",
                "postalCode": "LT-94262",
                "addressCountry": "LT"
              },
              "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "+370 67646766",
                "contactType": "customer service",
                "email": "<EMAIL>"
              },
              "foundingDate": "",
              "taxID": "*********"
            })
          }}
        />
      </head>
      <body className={`${inter.className} ${inter.variable} antialiased`}>
        <QueryProvider>
          <ThemeProvider>
            <SupabaseAuthProvider>
              <AnalyticsProvider>
                <TooltipProvider>
                  <main className="min-h-screen">{children}</main>
                  <CustomToaster />
                  <Toaster richColors position="top-center" />
                </TooltipProvider>
              </AnalyticsProvider>
            </SupabaseAuthProvider>
          </ThemeProvider>
        </QueryProvider>
      </body>
    </html>
  );
}