import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import Image from "next/image";
import { MagicLinkForm } from "@/components/auth/magic-link-form";
import { ArrowLeft } from "lucide-react";

export const metadata: Metadata = {
  title: "Prisijungi<PERSON> el. pa<PERSON>tu | DNSB Vakarai",
  description: "Prisijunkite prie DNSB Vakarai sistemos naudodami el. paštą",
};

export default function MagicLinkPage() {
  return (
    <div className="relative min-h-screen flex">
      {/* Left side - Magic Link Form */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-bold text-gray-900">
              DNSB &quot;Vakarai&quot;
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Daugiabučių namų savininkų bendrija
            </p>
          </div>

          <div>
            <Link 
              href="/auth/login"
              className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Grįžti į prisijungimą
            </Link>
          </div>

          <MagicLinkForm />

          <div className="text-center text-sm text-gray-600">
            <p>
              Susisiekite su administratoriumi:{" "}
              <a href="mailto:<EMAIL>" className="text-indigo-600 hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>

      {/* Right side - Image */}
      <div className="hidden lg:block relative w-0 flex-1">
        <Image
          className="absolute inset-0 h-full w-full object-cover"
          src="/images/auth-bg.jpg"
          alt="DNSB Vakarai"
          width={1920}
          height={1080}
          priority
        />
        <div className="absolute inset-0 bg-indigo-900 opacity-20"></div>
        <div className="absolute inset-0 flex items-center justify-center p-12">
          <div className="max-w-xl text-white text-center">
            <h1 className="text-4xl font-bold mb-4">
              Prisijungimas el. paštu
            </h1>
            <p className="text-xl opacity-90">
              Galite prisijungti naudodami el. paštą, jei esate užpildę 
              savo profilį ir nurodę el. pašto adresą.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}