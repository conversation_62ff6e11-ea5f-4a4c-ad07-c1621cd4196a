import React from 'react';
import { Metadata } from 'next';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Mail, ArrowLeft } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Patikrinkite savo el. paštą | DNSB Vakarai',
  description: 'Patvirtinti prisijungimą prie DNSB Vakarai sistemos',
};

export default function VerifyRequestPage() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold">Patikrinkite savo el. paštą</CardTitle>
          <CardDescription>
            Prisijungimo nuoroda išsiųsta į jūsų el. paštą
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex justify-center">
            <div className="bg-blue-50 p-6 rounded-full">
              <Mail className="h-12 w-12 text-blue-600" />
            </div>
          </div>
          
          <div className="space-y-4 text-center">
            <p>
              Prisijungimo nuoroda išsiųsta į jūsų nurodytą el. pašto adresą.
              Patikrinkite savo el. paštą ir spustelėkite nuorodą, kad galėtumėte prisijungti.
            </p>
            
            <div className="p-3 bg-amber-50 rounded-md text-sm">
              <p className="text-amber-800">
                <strong>Svarbu:</strong> nuoroda galioja tik 10 minučių.
              </p>
            </div>
            
            <p className="text-sm text-gray-500 pt-4">
              Negavote el. laiško? Patikrinkite savo šlamšto (angl. spam) aplanką 
              arba bandykite prisijungti dar kartą.
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Link href="/auth/login" passHref>
            <Button variant="outline" className="w-full">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Grįžti į prisijungimo puslapį
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}