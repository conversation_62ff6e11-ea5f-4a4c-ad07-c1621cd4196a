'use client';

import { Suspense, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CheckCircle, XCircle, Mail } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

function VerifyMagicLinkContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  
  const [status, setStatus] = useState<'verifying' | 'success' | 'error'>('verifying');
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    if (!token) {
      setStatus('error');
      setErrorMessage('Nepateikta prisijungimo nuoroda');
      return;
    }

    verifyMagicLink(token);
  }, [token]);

  async function verifyMagicLink(token: string) {
    try {
      const response = await fetch('/api/auth/verify-magic-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();

      if (response.ok) {
        setStatus('success');
        // Redirect to dashboard after 2 seconds
        setTimeout(() => {
          router.push('/dashboard');
        }, 2000);
      } else {
        setStatus('error');
        setErrorMessage(data.error || 'Nepavyko prisijungti');
      }
    } catch (error) {
      setStatus('error');
      setErrorMessage('Įvyko klaida. Bandykite vėliau.');
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4">
              <Mail className="h-12 w-12 text-indigo-600" />
            </div>
            <CardTitle>Prisijungimas el. paštu</CardTitle>
            <CardDescription>
              Tikriname jūsų prisijungimo nuorodą
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {status === 'verifying' && (
              <div className="text-center space-y-4">
                <Loader2 className="h-8 w-8 animate-spin mx-auto text-indigo-600" />
                <p className="text-sm text-gray-600">
                  Tikrinama prisijungimo nuoroda...
                </p>
              </div>
            )}

            {status === 'success' && (
              <div className="text-center space-y-4">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto" />
                <div>
                  <p className="text-lg font-medium text-green-900">
                    Sėkmingai prisijungta!
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    Nukreipiame jus į pagrindinį puslapį...
                  </p>
                </div>
              </div>
            )}

            {status === 'error' && (
              <div className="space-y-4">
                <div className="text-center space-y-4">
                  <XCircle className="h-12 w-12 text-red-500 mx-auto" />
                  <div>
                    <p className="text-lg font-medium text-red-900">
                      Prisijungti nepavyko
                    </p>
                    <p className="text-sm text-gray-600 mt-1">
                      {errorMessage}
                    </p>
                  </div>
                </div>

                <div className="flex flex-col gap-3">
                  <Button asChild variant="outline">
                    <Link href="/auth/login">
                      Grįžti į prisijungimą
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function VerifyMagicLinkPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto mb-4">
                <Mail className="h-12 w-12 text-indigo-600" />
              </div>
              <CardTitle>Prisijungimas el. paštu</CardTitle>
              <CardDescription>
                Tikriname jūsų prisijungimo nuorodą
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center space-y-4">
                <Loader2 className="h-8 w-8 animate-spin mx-auto text-indigo-600" />
                <p className="text-sm text-gray-600">
                  Tikrinama prisijungimo nuoroda...
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    }>
      <VerifyMagicLinkContent />
    </Suspense>
  );
}