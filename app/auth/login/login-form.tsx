"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter, useSearchParams } from "next/navigation";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { Icons } from "@/components/ui/icons";
import { useAnalytics } from "@/lib/hooks/useAnalytics";
import { trackError } from "@/lib/errorTracking";

const formSchema = z.object({
  username: z.string().min(3, {
    message: "Prisijungimo vardas turi būti bent 3 simbolių.",
  }),
  password: z.string().min(3, {
    message: "Slaptažodis turi būti bent 3 simbolių.",
  }),
});

type LoginFormValues = z.infer<typeof formSchema>;

export function LoginForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl") || "/dashboard";
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [error, setError] = React.useState<string>("");
  const { trackEvent, trackImportantEvent } = useAnalytics();

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: searchParams.get("username") || "",
      password: "", // Never pre-populate password for security
    },
  });

  // Check for error param in URL
  React.useEffect(() => {
    const errorParam = searchParams.get("error");
    if (errorParam === "CredentialsSignin") {
      setError("Neteisingas prisijungimo vardas arba slaptažodis.");
      console.log("Login error detected in URL: CredentialsSignin");
      trackEvent('auth_failed', { reason: 'CredentialsSignin', source: 'url_param' });
    }
  }, [searchParams, trackEvent]);

  // Pre-populate username from URL (but NEVER password for security)
  React.useEffect(() => {
    const username = searchParams.get("username");
    const password = searchParams.get("password");
    
    if (username && !isLoading) {
      console.log("Pre-populating username from URL");
      form.setValue("username", username);
    }
    
    // SECURITY: Clear any password from URL immediately
    if (password) {
      console.warn("Password found in URL - removing for security");
      const url = new URL(window.location.href);
      url.searchParams.delete('password');
      window.history.replaceState({}, '', url.toString());
    }
  }, [searchParams, form, isLoading]);

  async function onSubmit(data: LoginFormValues) {
    setIsLoading(true);
    setError("");

    console.log("Login attempt for:", data.username);
    trackEvent('auth_login_attempt', { username_length: data.username.length });

    try {
      // Call our Supabase login API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: data.username,
          password: data.password,
        }),
      });

      const result = await response.json();
      console.log("Login result:", result);

      if (!response.ok || result.error || !result.success) {
        console.error("Login failed:", result.error);
        const errorMessage = result.error === 'Invalid credentials' 
          ? "Neteisingas prisijungimo vardas arba slaptažodis"
          : result.error === 'Account is disabled' 
          ? "Jūsų paskyra yra išjungta"
          : result.error;
        
        setError(`Prisijungimo klaida: ${errorMessage}`);
        
        // Track login failure with analytics
        trackEvent('auth_failed', { 
          reason: result.error,
          source: 'form_submission'
        });
        
        // Track credential errors with error tracking system
        trackError('auth_credentials', {
          reason: result.error,
          username_length: data.username.length,
          source: 'form_submission'
        });
      } else {
        console.log("Login successful, redirecting to:", callbackUrl);
        
        // Track successful login
        trackImportantEvent('auth_login', undefined, {
          method: 'credentials',
          redirect_to: callbackUrl
        });
        
        // Show success toast first
        toast({
          title: "Sėkmingai prisijungta",
          description: "Sveiki sugrįžę į DNSB Vakarai portalą.",
        });
        
        // Wait a moment for the session to be set, then redirect
        setTimeout(() => {
          console.log("Executing redirect to:", callbackUrl);
          router.push(callbackUrl);
          router.refresh(); // Refresh to update the auth state
        }, 500);
      }
    } catch (error) {
      console.error("Unexpected login error:", error);
      setError("Įvyko klaida bandant prisijungti. Bandykite dar kartą.");
      
      // Track unexpected error with both analytics and error tracking
      const errorMessage = error instanceof Error ? error.message : String(error);
      trackEvent('auth_error', { 
        error_type: 'unexpected',
        error_message: errorMessage
      });
      
      // Use the dedicated error tracking
      trackError('auth_login', {
        username_length: data.username.length,
        error_message: errorMessage,
        error_stack: error instanceof Error ? error.stack : undefined,
        source: 'login_form'
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="grid gap-5">
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="grid gap-4">
          <div className="grid gap-5">
            <div className="flex flex-col space-y-1">
              <Label htmlFor="username" className="text-indigo-900 font-medium text-base">Prisijungimo vardas arba el. paštas</Label>
              <p className="text-sm text-gray-600 mb-1">
                Gyventojams: namo-buto numeris (pvz., 5-1) arba jūsų el. paštas<br />
                Administratoriams: el. paštas arba vartotojo vardas
              </p>
            </div>
            <Input
              id="username"
              type="text"
              className="border-indigo-100 focus:border-indigo-300 text-base h-11"
              autoCapitalize="none"
              autoComplete="username email"
              autoCorrect="off"
              disabled={isLoading}
              {...form.register("username")}
            />
            {form.formState.errors.username && (
              <p className="text-red-500 text-sm mt-1">
                {form.formState.errors.username.message}
              </p>
            )}
          </div>
          <div className="flex flex-col space-y-2">
            <div className="flex flex-col space-y-1">
              <Label htmlFor="password" className="text-indigo-900 font-medium text-base">Slaptažodis</Label>
            </div>
            <Input
              id="password"
              type="password"
              className="border-indigo-100 focus:border-indigo-300 text-base h-11"
              autoCapitalize="none"
              autoComplete="current-password"
              autoCorrect="off"
              disabled={isLoading}
              {...form.register("password")}
            />
            {form.formState.errors.password && (
              <p className="text-red-500 text-sm mt-1">
                {form.formState.errors.password.message}
              </p>
            )}
          </div>
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3 text-red-700 text-sm">
              {error}
            </div>
          )}
          <div className="flex items-center justify-between pt-2">
            <Button
              type="submit"
              className="w-full bg-indigo-600 hover:bg-indigo-700 text-base h-11 focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2"
              disabled={isLoading}
            >
              {isLoading && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Prisijungti
            </Button>
          </div>
        </div>
      </form>
      <div className="bg-indigo-50 border border-indigo-100 rounded-md p-4">
        <h3 className="font-medium text-indigo-900 mb-1">Pirmą kartą jungiatės?</h3>
        <p className="text-sm text-indigo-700">
          Prisijungimo duomenys buvo išsiųsti į jūsų el. paštą arba perduoti asmeniškai. Jei negalite prisijungti, kreipkitės į administratorių.
        </p>
      </div>
    </div>
  );
}