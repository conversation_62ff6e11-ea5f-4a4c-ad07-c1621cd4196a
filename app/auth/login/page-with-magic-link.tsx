import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import Image from "next/image";
import { LoginForm } from "./login-form";
import { MagicLinkForm } from "@/components/auth/magic-link-form";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Key, Mail } from "lucide-react";

export const metadata: Metadata = {
  title: "Prisijungimas | DNSB Vakarai",
  description: "Prisijunkite prie DNSB Vakarai sistemos",
};

export default function LoginPage() {
  return (
    <div className="relative min-h-screen flex">
      {/* Left side - Login Form */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-bold text-gray-900">
              DNSB &quot;Vakarai&quot;
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Daugiabučių namų savininkų bendrija
            </p>
          </div>

          <Tabs defaultValue="username" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="username" className="flex items-center gap-2">
                <Key className="h-4 w-4" />
                Vartotojo vardas
              </TabsTrigger>
              <TabsTrigger value="email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                El. paštas
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="username" className="mt-6">
              <LoginForm />
            </TabsContent>
            
            <TabsContent value="email" className="mt-6">
              <MagicLinkForm />
            </TabsContent>
          </Tabs>

          <div className="text-center text-sm text-gray-600">
            <p>
              Susisiekite su administratoriumi:{" "}
              <a href="mailto:<EMAIL>" className="text-indigo-600 hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>

      {/* Right side - Image */}
      <div className="hidden lg:block relative w-0 flex-1">
        <Image
          className="absolute inset-0 h-full w-full object-cover"
          src="/images/auth-bg.jpg"
          alt="DNSB Vakarai"
          width={1920}
          height={1080}
          priority
        />
        <div className="absolute inset-0 bg-indigo-900 opacity-20"></div>
        <div className="absolute inset-0 flex items-center justify-center p-12">
          <div className="max-w-xl text-white text-center">
            <h1 className="text-4xl font-bold mb-4">
              Sveiki atvykę į DNSB &quot;Vakarai&quot; sistemą
            </h1>
            <p className="text-xl opacity-90">
              Čia galite gauti svarbią informaciją apie namo valdymą, 
              pranešimus ir dalyvauti bendruomenės sprendimuose.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}