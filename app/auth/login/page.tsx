import { Metada<PERSON> } from "next";
import Link from "next/link";
import { LoginForm } from "./login-form";
import Image from "next/image";
import { ChevronLeft } from "lucide-react";
import { Suspense } from "react";
import { LogoImage } from "@/components/ui/logo-image";

export const metadata: Metadata = {
  title: "Prisijungimas | DNSB Vakarai",
  description: "Prisijungimo puslapis",
};

// Loading component for Suspense fallback
function LoginFormLoading() {
  return (
    <div className="grid gap-5 animate-pulse">
      <div className="h-10 bg-gray-200 rounded mb-4"></div>
      <div className="h-10 bg-gray-200 rounded mb-4"></div>
      <div className="h-10 bg-gray-200 rounded"></div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <div 
      className="min-h-screen w-full flex flex-col items-center justify-center relative" 
      style={{ 
        backgroundColor: "#002855", 
        backgroundImage: "linear-gradient(135deg, #002855 0%, #004080 100%)"
      }}
    >
      {/* Background pattern overlay */}
      <div 
        className="absolute inset-0 w-full h-full opacity-10" 
        style={{ 
          backgroundImage: "url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")",
        }}
      />
      
      {/* Back button */}
      <Link
        href="/"
        className="absolute left-4 top-4 md:left-8 md:top-8 text-white hover:text-blue-200 transition-colors flex items-center"
      >
        <ChevronLeft className="mr-1 h-4 w-4" />
        Atgal
      </Link>
      
      {/* Login form card */}
      <div className="w-full max-w-md p-8 mx-auto rounded-xl shadow-xl bg-white/95 backdrop-blur-sm relative z-10">
        <div className="flex flex-col items-center space-y-4 mb-6">
          <LogoImage
            width={180}
            height={60}
            alt="DNSB Vakarai"
            className="mx-auto bg-[#002855] p-4 rounded-lg"
            variant="light"
            priority={true}
          />
          <h1 className="text-2xl font-semibold tracking-tight text-[#002855] mt-4">
            Sveiki atvykę
          </h1>
          <p className="text-sm text-gray-600 text-center">
            Prisijunkite prie savo paskyros
          </p>
        </div>
        
        {/* Wrap the LoginForm component in a Suspense boundary */}
        <Suspense fallback={<LoginFormLoading />}>
          <LoginForm />
        </Suspense>
        
        {/* Footer */}
        <div className="mt-8 text-center text-xs text-gray-500">
          <p>© {new Date().getFullYear()} DNSB Vakarai. Visos teisės saugomos.</p>
        </div>
      </div>
    </div>
  );
}