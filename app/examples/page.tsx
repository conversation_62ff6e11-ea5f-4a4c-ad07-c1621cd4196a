import { ArrowRight } from "lucide-react"
import Link from "next/link"

export default function ExamplesHome() {
  return (
    <div className="max-w-3xl mx-auto space-y-8">
      <div className="bg-white p-6 rounded-lg shadow">
        <h1 className="text-3xl font-bold mb-4 text-indigo-900">Komponentų pavyzdžiai</h1>
        
        <p className="text-gray-700 mb-6">
          Šioje sekcijoje rasite įvairių komponentų pavyzdžius, kurie padės jums kurti
          vienodą ir patogią vartotojo sąsają.
        </p>
        
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-indigo-800">Pasiekiami pavyzdžiai:</h2>
          
          <div className="bg-indigo-50 p-4 rounded-lg border border-indigo-100">
            <h3 className="font-medium text-indigo-900 mb-2">Datos pasirinkimo komponentai</h3>
            <p className="text-gray-700 mb-3">
              Taisyklingai veikiantys datos ir laiko pasirinkimo komponentai, kurie sprendžia
              pozicionavimo problemas ir veikia korektiškai.
            </p>
            <Link 
              href="/examples/date-picker"
              className="flex items-center text-indigo-600 font-medium hover:text-indigo-800"
            >
              Peržiūrėti pavyzdį <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
          
          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
            <h3 className="font-medium text-yellow-900 mb-2">Klaidų sekimo sistema</h3>
            <p className="text-gray-700 mb-3">
              Demonstracija kaip veikia integruota klaidų sekimo sistema su PostHog. 
              Pavyzdys rodo įvairių tipų klaidų fiksavimą ir pranešimą.
            </p>
            <Link 
              href="/examples/error-tracking"
              className="flex items-center text-yellow-600 font-medium hover:text-yellow-800"
            >
              Peržiūrėti pavyzdį <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
          
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
            <h3 className="font-medium text-blue-900 mb-2">PostHog testavimas</h3>
            <p className="text-gray-700 mb-3">
              PostHog integracijos testavimo puslapis. Siųskite bandomuosius įvykius ir patikrinkite,
              ar jie pasiekia jūsų PostHog prietaisų skydelį.
            </p>
            <Link 
              href="/examples/posthog-test"
              className="flex items-center text-blue-600 font-medium hover:text-blue-800"
            >
              Testuoti PostHog <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
          
          <div className="bg-red-50 p-4 rounded-lg border border-red-100">
            <h3 className="font-medium text-red-900 mb-2">PostHog Diagnozė</h3>
            <p className="text-gray-700 mb-3">
              Diagnostikos puslapis PostHog integracijai. Rodo išsamią informaciją apie PostHog konfigūraciją ir leidžia
              siųsti tiesioginius įvykius gedimų diagnostikai.
            </p>
            <Link 
              href="/examples/posthog-debug"
              className="flex items-center text-red-600 font-medium hover:text-red-800"
            >
              Diagnostikos puslapis <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg border border-green-100">
            <h3 className="font-medium text-green-900 mb-2">Vartotojų Srautų Sekimas</h3>
            <p className="text-gray-700 mb-3">
              Demonstruoja kaip sekti vartotojų veiklas ir srautus su PostHog. Pateikiami praktiniai pavyzdžiai
              sekant prisijungimus, formų pateikimus, balsavimus ir kitas svarbias vartotojų veiklas.
            </p>
            <Link 
              href="/examples/user-flow-tracking"
              className="flex items-center text-green-600 font-medium hover:text-green-800"
            >
              Vartotojų sekimo pavyzdžiai <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
        </div>
      </div>
      
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-bold mb-4 text-indigo-900">Kaip naudoti datos pasirinkimo komponentus</h2>
        
        <div className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 className="font-medium text-gray-900 mb-2">1. Importuokite komponentą</h3>
            <pre className="bg-gray-800 text-gray-100 p-3 rounded-md overflow-x-auto text-sm">
              {`import { DateTimePicker, SimpleDatePicker } from "@/components/ui/date-time-picker"`}
            </pre>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 className="font-medium text-gray-900 mb-2">2. Paruoškite būsenos kintamuosius</h3>
            <pre className="bg-gray-800 text-gray-100 p-3 rounded-md overflow-x-auto text-sm">
              {`// Pilnas datos ir laiko pasirinkimas
const [date, setDate] = useState<Date | undefined>(undefined)
const [time, setTime] = useState<string | undefined>(undefined)

// Arba tik datos pasirinkimas
const [simpleDate, setSimpleDate] = useState<Date | undefined>(undefined)`}
            </pre>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 className="font-medium text-gray-900 mb-2">3. Panaudokite komponentą</h3>
            <pre className="bg-gray-800 text-gray-100 p-3 rounded-md overflow-x-auto text-sm">
              {`// Pilnas datos ir laiko pasirinkimas
<DateTimePicker
  date={date}
  setDate={setDate}
  time={time}
  setTime={setTime}
/>

// Arba tik datos pasirinkimas
<SimpleDatePicker
  date={simpleDate}
  setDate={setSimpleDate}
/>`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
} 