'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useAnalytics } from '@/lib/hooks/useAnalytics';
import { trackError } from '@/lib/errorTracking';
import { posthog } from '@/lib/posthog';

export default function PostHogTestPage() {
  const [eventCount, setEventCount] = useState(0);
  const { trackEvent, trackImportantEvent, trackPageView, identifyUser } = useAnalytics();
  
  // Track page view when component mounts
  useState(() => {
    trackPageView('/examples/posthog-test', {
      test_page: true,
      timestamp: new Date().toISOString()
    });
  }, []);
  
  const sendTestEvent = () => {
    const count = eventCount + 1;
    setEventCount(count);
    
    trackEvent('test_basic_event', {
      count,
      timestamp: new Date().toISOString(),
      test_source: 'test_page'
    });
    
    alert('Basic event sent to PostHog!');
  };
  
  const sendImportantEvent = () => {
    const count = eventCount + 1;
    setEventCount(count);
    
    trackImportantEvent('test_important_event', 'test-123', {
      count,
      timestamp: new Date().toISOString(),
      priority: 'high'
    });
    
    alert('Important event sent to PostHog and server!');
  };
  
  const sendErrorEvent = () => {
    const count = eventCount + 1;
    setEventCount(count);
    
    trackError('test_error', {
      count,
      timestamp: new Date().toISOString(),
      synthetic: true,
      error_message: 'This is a test error'
    });
    
    alert('Error event sent to PostHog!');
  };
  
  const identifyCurrentUser = () => {
    identifyUser('test-user-' + Math.floor(Math.random() * 1000), {
      email: '<EMAIL>',
      name: 'Test User',
      role: 'tester'
    });
    
    alert('User identified in PostHog!');
  };
  
  const checkPostHogStatus = () => {
    if (typeof window !== 'undefined') {
      alert(`PostHog status:
- Loaded: ${!!posthog.__loaded}
- API Host: ${posthog.config?.api_host || 'Not set'}
- Debug: ${posthog.config?.debug ? 'On' : 'Off'}
- Session recording: ${posthog.config?.disable_session_recording ? 'Disabled' : 'Enabled'}`);
    }
  };

  const showApiKey = () => {
    if (typeof window !== 'undefined') {
      const apiKey = posthog.config?.token || process.env.NEXT_PUBLIC_POSTHOG_KEY || 'Not available';
      alert(`PostHog API Key: ${apiKey}\n\nSource: ${apiKey === posthog.config?.token ? 'Runtime config' : 'Environment variable'}`);
      console.log('[PostHog Diagnostic] API Key:', apiKey);
    }
  };
  
  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">PostHog Integration Test</h1>
      
      <Card className="p-6 mb-8 bg-blue-50">
        <h2 className="text-xl font-semibold mb-4">PostHog Status</h2>
        <p className="mb-4">This page lets you test the PostHog integration by sending test events.</p>
        <div className="flex flex-col space-y-2">
          <Button onClick={checkPostHogStatus} className="mb-2">Check PostHog Status</Button>
          <Button onClick={showApiKey} variant="outline" className="border-amber-500 text-amber-700 hover:bg-amber-100">Show API Key (Diagnostic)</Button>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          Open your browser console to see event logs (in development mode only).
        </p>
      </Card>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="p-6 bg-green-50">
          <h2 className="text-lg font-semibold mb-4">Basic Event</h2>
          <p className="mb-4">Sends a basic event to PostHog.</p>
          <Button onClick={sendTestEvent} variant="outline" className="border-green-500 text-green-700 hover:bg-green-100">
            Send Test Event
          </Button>
        </Card>
        
        <Card className="p-6 bg-purple-50">
          <h2 className="text-lg font-semibold mb-4">Important Event</h2>
          <p className="mb-4">Sends an event to PostHog and logs it on the server.</p>
          <Button onClick={sendImportantEvent} variant="outline" className="border-purple-500 text-purple-700 hover:bg-purple-100">
            Send Important Event
          </Button>
        </Card>
        
        <Card className="p-6 bg-red-50">
          <h2 className="text-lg font-semibold mb-4">Error Event</h2>
          <p className="mb-4">Sends a test error event to PostHog.</p>
          <Button onClick={sendErrorEvent} variant="outline" className="border-red-500 text-red-700 hover:bg-red-100">
            Send Error Event
          </Button>
        </Card>
        
        <Card className="p-6 bg-yellow-50">
          <h2 className="text-lg font-semibold mb-4">User Identification</h2>
          <p className="mb-4">Identifies the current user in PostHog.</p>
          <Button onClick={identifyCurrentUser} variant="outline" className="border-yellow-500 text-yellow-700 hover:bg-yellow-100">
            Identify Test User
          </Button>
        </Card>
      </div>
      
      <div className="mt-8 p-6 bg-gray-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Event Count: {eventCount}</h2>
        <p>
          To view these events, go to your PostHog dashboard and check the "Live Events" or "Events" section.
          You should see events with names like "test_basic_event", "test_important_event", etc.
        </p>
      </div>
    </div>
  );
}