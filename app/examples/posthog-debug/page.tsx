'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { posthog } from '@/lib/posthog';

export default function PostHogDebugPage() {
  const [diagnostics, setDiagnostics] = useState<any>({
    apiKey: 'Checking...',
    apiHost: 'Checking...',
    isLoaded: 'Checking...',
    isDebugEnabled: 'Checking...',
    cookies: 'Checking...',
    envVariables: {
      NEXT_PUBLIC_POSTHOG_KEY: 'Checking...',
      NEXT_PUBLIC_POSTHOG_HOST: 'Checking...'
    }
  });
  
  useEffect(() => {
    // Check basic environment variables
    setDiagnostics(prev => ({
      ...prev,
      envVariables: {
        NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY || 'Not set',
        NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST || 'Not set'
      }
    }));
    
    // Check cookies
    if (typeof window !== 'undefined') {
      setDiagnostics(prev => ({
        ...prev,
        cookies: document.cookie || 'No cookies found'
      }));
    }
    
    // Check PostHog configuration
    setTimeout(() => {
      if (typeof window !== 'undefined' && window.posthog) {
        setDiagnostics(prev => ({
          ...prev,
          apiKey: window.posthog?.config?.token || 'Not found',
          apiHost: window.posthog?.config?.api_host || 'Not found',
          isLoaded: window.posthog?.__loaded ? 'Yes' : 'No',
          isDebugEnabled: window.posthog?.config?.debug ? 'Yes' : 'No'
        }));
      } else {
        setDiagnostics(prev => ({
          ...prev,
          apiKey: 'PostHog not initialized',
          apiHost: 'PostHog not initialized',
          isLoaded: 'PostHog not initialized',
          isDebugEnabled: 'PostHog not initialized'
        }));
      }
    }, 1000); // Give PostHog time to initialize
  }, []);
  
  const sendTestEvent = () => {
    if (typeof window !== 'undefined' && window.posthog) {
      const eventName = 'debug_test_event';
      window.posthog.capture(eventName, {
        timestamp: new Date().toISOString(),
        test_source: 'debug_page'
      });
      
      alert(`Test event "${eventName}" sent directly via window.posthog.capture()`);
      console.log(`[PostHog Debug] Sent test event: ${eventName}`);
    } else {
      alert('PostHog is not available on window object');
    }
  };
  
  const reinitializePostHog = () => {
    if (typeof window !== 'undefined') {
      // Clear any previous instance
      window.posthog = undefined;
      
      // Manually initialize PostHog with hardcoded values
      const script = document.createElement('script');
      script.innerHTML = `
        !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init capture register register_once register_for_session unregister unregister_for_session getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey getNextSurveyStep identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty createPersonProfile opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing debug".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
        posthog.init('${process.env.NEXT_PUBLIC_POSTHOG_KEY || 'phc_4Qg337unB09c2duGIgep6jmn6wcKKKvMULIllrvi44f'}', {
          api_host: '${process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://eu.i.posthog.com'}',
          debug: true
        });
        console.log("[PostHog Debug] Manually initialized with snippet");
      `;
      document.head.appendChild(script);
      
      // Update diagnostics after a moment
      setTimeout(() => {
        setDiagnostics(prev => ({
          ...prev,
          apiKey: window.posthog?.config?.token || 'Still not found',
          apiHost: window.posthog?.config?.api_host || 'Still not found',
          isLoaded: window.posthog?.__loaded ? 'Yes' : 'No',
          isDebugEnabled: window.posthog?.config?.debug ? 'Yes' : 'No',
          reinitialized: 'Yes, using snippet approach'
        }));
      }, 2000);
      
      alert('Attempted to manually initialize PostHog using the snippet approach');
    }
  };
  
  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">PostHog Debug Page</h1>
      
      <Card className="p-6 mb-8 bg-red-50">
        <h2 className="text-xl font-semibold mb-4">PostHog Diagnostics</h2>
        <div className="bg-white p-4 rounded border mb-4 font-mono text-sm overflow-auto">
          <pre>{JSON.stringify(diagnostics, null, 2)}</pre>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
          <Button onClick={sendTestEvent} className="bg-green-600 hover:bg-green-700">
            Send Direct Test Event
          </Button>
          
          <Button onClick={reinitializePostHog} className="bg-yellow-600 hover:bg-yellow-700">
            Re-initialize PostHog Manually
          </Button>
        </div>
        
        <p className="text-sm text-gray-700">
          This page shows diagnostics about your PostHog setup. Check your browser console for 
          additional logs with "[PostHog Debug]" prefix.
        </p>
      </Card>
      
      <Card className="p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">Setup Instructions</h2>
        <ol className="list-decimal pl-5 space-y-2">
          <li>Ensure your .env.local file has the correct variables:
            <pre className="bg-gray-100 p-2 rounded mt-2 text-sm">
              NEXT_PUBLIC_POSTHOG_KEY=your_project_api_key
              NEXT_PUBLIC_POSTHOG_HOST=https://eu.i.posthog.com
            </pre>
          </li>
          <li>Make sure your PostHog project is properly set up in the dashboard</li>
          <li>Check that you have no adblockers or privacy extensions blocking PostHog</li>
          <li>Verify that the analytics provider component is loaded in your application</li>
        </ol>
      </Card>
    </div>
  );
}

declare global {
  interface Window {
    posthog?: any;
  }
}