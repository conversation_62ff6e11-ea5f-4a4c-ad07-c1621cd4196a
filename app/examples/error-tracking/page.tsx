'use client';

import { PageHeader } from "@/components/ui/page-header";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { ErrorTrackingDemo } from "@/components/examples/error-tracking-demo";
import { Breadcrumb } from "@/components/ui/breadcrumb";

/**
 * Error Tracking Example Page
 * 
 * This page demonstrates the error tracking capabilities implemented with PostHog.
 * It includes a demo component with buttons to trigger various types of errors.
 */
export default function ErrorTrackingExamplePage() {
  return (
    <div className="container mx-auto py-6">
      <Breadcrumb
        items={[
          { label: "Pavyzdžiai", href: "/examples" },
          { label: "Klaidų sekimas", href: "/examples/error-tracking" },
        ]}
      />
      
      <PageHeader
        heading="Klaidų sekimo demonstracija"
        description="Šis puslapis demonstruoja kaip veikia klaidų sekimas su PostHog"
      />
      
      <ErrorTrackingDemo />
      
      <Card>
        <CardHeader>
          <CardTitle>Apie klaidų sekimą</CardTitle>
          <CardDescription>
            DNSB Vakarai sistemoje įdiegtas automatinis klaidų sekimas naudojant PostHog
          </CardDescription>
        </CardHeader>
        <CardContent>
          <h3 className="font-medium text-lg mb-2">Sekamų klaidų tipai</h3>
          <ul className="list-disc list-inside space-y-1 mb-4">
            <li>JavaScript klaidos (runtime errors)</li>
            <li>Neapdorotos pažadų (Promises) klaidos</li>
            <li>API užklausų klaidos (4xx, 5xx statuso kodai)</li>
            <li>Tinklo ryšio klaidos</li>
            <li>Rankiniu būdu fiksuojamos klaidos</li>
          </ul>
          
          <h3 className="font-medium text-lg mb-2">Kaip veikia?</h3>
          <p className="mb-4">
            Klaidų sekimas veikia naudojant globalius įvykių klausytojus (event listeners) 
            ir patobulintą fetch API. Kiekviena klaida yra užfiksuojama su kontekstine informacija 
            ir siunčiama į PostHog analizei.
          </p>
          
          <h3 className="font-medium text-lg mb-2">Privatumas</h3>
          <p>
            Klaidų sekimas gerbia vartotojų privatumo nustatymus. Jei vartotojas
            atsisakė analitikos sekimo (opt-out), klaidų sekimas taip pat neveiks.
            Jautri informacija iš klaidų pranešimų yra pašalinama prieš siunčiant į PostHog.
          </p>
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-4">
          <p className="text-sm text-gray-500">
            Dokumentaciją apie klaidų sekimą rasite <code>/docs/ERROR_TRACKING_GUIDE.md</code> faile.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}