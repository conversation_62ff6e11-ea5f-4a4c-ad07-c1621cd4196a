"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface ExamplesLayoutProps {
  children: React.ReactNode
}

export default function ExamplesLayout({ children }: ExamplesLayoutProps) {
  const pathname = usePathname()
  
  const examples = [
    {
      name: "Datos pasirinkimas",
      href: "/examples/date-picker",
    },
  ]

  return (
    <div className="min-h-screen bg-slate-50">
      <header className="sticky top-0 z-40 border-b bg-white shadow-sm">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/" className="font-bold text-indigo-900">
            ← Grįžti į pradžią
          </Link>
          <h1 className="text-xl font-bold text-indigo-900">Komponentų pavyzdžiai</h1>
        </div>
      </header>
      
      <div className="container py-8">
        <div className="flex flex-col md:flex-row gap-8">
          <aside className="md:w-64 flex-shrink-0">
            <nav className="flex flex-col space-y-1">
              {examples.map((example) => (
                <Link
                  key={example.href}
                  href={example.href}
                  className={cn(
                    "px-4 py-2 text-sm rounded-md",
                    pathname === example.href
                      ? "bg-indigo-100 text-indigo-900 font-medium"
                      : "text-gray-600 hover:bg-gray-100"
                  )}
                >
                  {example.name}
                </Link>
              ))}
            </nav>
          </aside>
          
          <main className="flex-1">
            {children}
          </main>
        </div>
      </div>
    </div>
  )
} 