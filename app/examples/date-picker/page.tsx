"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { DateTimePicker, SimpleDatePicker } from "@/components/ui/date-time-picker"
import { Button } from "@/components/ui/button"

export default function DatePickerExample() {
  // Address selection state
  const [selectedAddress, setSelectedAddress] = useState("0")
  
  // Date and time state
  const [date, setDate] = useState<Date | undefined>(undefined)
  const [time, setTime] = useState<string | undefined>(undefined)
  
  // Simple date state
  const [simpleDate, setSimpleDate] = useState<Date | undefined>(undefined)
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Display the selected values
    alert(`
      Selected Address: ${selectedAddress}
      Selected Date: ${date ? date.toISOString().split('T')[0] : 'None'}
      Selected Time: ${time || 'None'}
    `)
  }
  
  return (
    <div className="container mx-auto py-10 space-y-8">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-indigo-900">Taisyklingai veikiantis datos pasirinkimas</h1>
        
        <Card>
          <CardHeader>
            <CardTitle>Pasirinkite adresą ir laiką</CardTitle>
            <CardDescription>
              Pasirinkite adresą ir nustatykite norimą datą bei laiką
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-8">
              <div className="space-y-4">
                <h3 className="font-medium text-lg">Adresas</h3>
                
                <RadioGroup 
                  value={selectedAddress}
                  onValueChange={setSelectedAddress}
                  className="space-y-3"
                >
                  <div className="flex items-center space-x-2">
                    <RadioItem value="0" id="address-0" />
                    <Label htmlFor="address-0" className="cursor-pointer">0 (Debreceno 0, Vilnius)</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <RadioItem value="44" id="address-44" />
                    <Label htmlFor="address-44" className="cursor-pointer">44 (Debreceno 44, Vilnius)</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <RadioItem value="5" id="address-5" />
                    <Label htmlFor="address-5" className="cursor-pointer">5 (Lazdynų g. 5, Vilnius)</Label>
                  </div>
                </RadioGroup>
              </div>
              
              <div className="space-y-4">
                <h3 className="font-medium text-lg">Data ir laikas</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <DateTimePicker
                    date={date}
                    setDate={setDate}
                    time={time}
                    setTime={setTime}
                  />
                  
                  <div className="space-y-4">
                    <h4 className="font-medium">Arba tiesiog pasirinkite datą:</h4>
                    <SimpleDatePicker
                      date={simpleDate}
                      setDate={setSimpleDate}
                    />
                    {simpleDate && (
                      <p className="text-sm text-gray-500">
                        Pasirinkta data: {simpleDate.toLocaleDateString('lt-LT')}
                      </p>
                    )}
                  </div>
                </div>
              </div>
              
              <Button type="submit" className="bg-indigo-600 hover:bg-indigo-700">
                Patvirtinti pasirinkimą
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 