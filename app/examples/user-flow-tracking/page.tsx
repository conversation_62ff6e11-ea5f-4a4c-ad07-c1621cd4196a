'use client';

import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { UserFlowTrackingDemo } from "@/components/examples/user-flow-tracking-demo";
import Link from "next/link";

/**
 * User Flow Tracking Example Page
 * 
 * This page demonstrates how to track different user flows and actions
 * using the analytics hooks in the application.
 */
export default function UserFlowTrackingPage() {
  return (
    <div className="container mx-auto py-6">
      <Breadcrumb
        items={[
          { label: "Pavyzdžiai", href: "/examples" },
          { label: "Vartotojų Srautų Sekimas", href: "/examples/user-flow-tracking" },
        ]}
      />
      
      <PageHeader
        heading="Vartotojų Srautų Sekimo Demonstracija"
        description="Pavyzdžiai kaip sekti vartotojų veiksmus ir srautus naudojant PostHog"
      />
      
      <div className="mb-6 bg-blue-50 p-4 rounded-lg border border-blue-100">
        <h2 className="text-lg font-medium text-blue-900 mb-2">PostHog Sekimo Dokumentacija</h2>
        <p className="text-gray-700 mb-4">
          Norėdami sužinoti daugiau apie tai, kaip sekti vartotojų srautus, peržiūrėkite išsamią dokumentaciją, 
          kurioje paaiškinamos visos vartotojų srautų sekimo funkcijos.
        </p>
        <div className="flex space-x-4">
          <Link 
            href="/examples/posthog-debug"
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            PostHog Diagnostikos Puslapis
          </Link>
          <Link 
            href="/examples/posthog-test"
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            PostHog Testavimo Puslapis
          </Link>
        </div>
      </div>
      
      <UserFlowTrackingDemo />
      
      <div className="mt-8 bg-gray-50 p-6 rounded-lg border border-gray-200">
        <h2 className="text-xl font-semibold mb-4">Kaip Rasti Sekimo Duomenis PostHog Dashboarduose</h2>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">Vartotojų Sekimas:</h3>
            <ol className="list-decimal list-inside text-gray-700 space-y-1 pl-4">
              <li>PostHog platformoje pasirinkite "Persons"</li>
              <li>Ieškokite vartotojo pagal ID arba savybes</li>
              <li>Peržiūrėkite vartotojo veiksmų istoriją</li>
            </ol>
          </div>
          
          <div>
            <h3 className="font-medium mb-2">Srautų Analizė:</h3>
            <ol className="list-decimal list-inside text-gray-700 space-y-1 pl-4">
              <li>Eikite į "Insights" → "Paths"</li>
              <li>Pasirinkite analizuotinus įvykius (pvz., login → view announcement → vote)</li>
              <li>Matykite dažniausius vartotojų kelių šablonus</li>
            </ol>
          </div>
          
          <div>
            <h3 className="font-medium mb-2">Piltuvėlių (Funnels) Analizė:</h3>
            <ol className="list-decimal list-inside text-gray-700 space-y-1 pl-4">
              <li>Eikite į "Insights" → "Funnels"</li>
              <li>Sukurkite naują piltuvėlį su norimais etapais</li>
              <li>Analizuokite, kuriuose etapuose vartotojai atsijungia</li>
            </ol>
          </div>
          
          <div>
            <h3 className="font-medium mb-2">Įvykių Tyrinėjimas:</h3>
            <ol className="list-decimal list-inside text-gray-700 space-y-1 pl-4">
              <li>Eikite į "Events"</li>
              <li>Filtruokite norimus įvykių tipus</li>
              <li>Peržiūrėkite įvykių savybes ir kontekstą</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}