import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Shield, Mail, Phone, Home } from "lucide-react";
import Link from "next/link";

export default function AccountDisabledPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <Shield className="mx-auto h-16 w-16 text-red-500" />
          <h1 className="mt-4 text-3xl font-bold text-gray-900">
            Paskyra išjungta
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Jūsų paskyra buvo išjungta pagal jūsų prašymą
          </p>
        </div>

        {/* Main Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-red-500" />
              Paskyros būsena
            </CardTitle>
            <CardDescription>
              Jūsų paskyra DNSB "Vakarai" platformoje buvo išjungta, nes nesutikote 
              su duomenų tvarkymo sąlygomis.
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* What this means */}
            <div className="rounded-lg border p-4 bg-red-50">
              <h3 className="font-medium text-red-900 mb-2">Ką tai reiškia?</h3>
              <ul className="text-sm text-red-800 space-y-1">
                <li>• Nebegalite prisijungti prie platformos</li>
                <li>• Negausite el. pašto pranešimų</li>
                <li>• Negalėsite dalyvauti bendruomenės balsavimuose</li>
                <li>• Neturite prieigos prie platformos funkcijų</li>
              </ul>
            </div>

            {/* Data handling */}
            <div className="rounded-lg border p-4 bg-blue-50">
              <h3 className="font-medium text-blue-900 mb-2">Jūsų duomenys</h3>
              <p className="text-sm text-blue-800">
                Pagal BDAR reikalavimus, jūsų asmens duomenys buvo pažymėti kaip 
                nebeaktyvūs ir nebenaudojami komunikacijai. Jei norite, kad jūsų 
                duomenys būtų visiškai ištrinti, susisiekite su administratoriumi.
              </p>
            </div>

            {/* Contact Information */}
            <div className="rounded-lg border p-4">
              <h3 className="font-medium mb-3">Susisiekite su administratoriumi</h3>
              <p className="text-sm text-gray-600 mb-4">
                Jei turite klausimų arba norite atkurti paskyrą, susisiekite su 
                DNSB "Vakarai" pirmininku:
              </p>
              
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">El. paštas</p>
                    <a 
                      href="mailto:<EMAIL>" 
                      className="text-sm text-blue-600 hover:underline"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Telefonas</p>
                    <a 
                      href="tel:+37061630230" 
                      className="text-sm text-blue-600 hover:underline"
                    >
                      +370 616 30230
                    </a>
                  </div>
                </div>
              </div>
            </div>

            {/* What you can do */}
            <div className="rounded-lg border p-4 bg-amber-50">
              <h3 className="font-medium text-amber-900 mb-2">Ką galite daryti?</h3>
              <div className="text-sm text-amber-800 space-y-2">
                <p>
                  <strong>Atkurti paskyrą:</strong> Susisiekite su administratoriumi 
                  ir paprašykite atkurti paskyrą. Turėsite iš naujo sutikti su 
                  duomenų tvarkymo sąlygomis.
                </p>
                <p>
                  <strong>Ištrinti duomenis:</strong> Paprašykite visiškai ištrinti 
                  jūsų asmens duomenis iš sistemos.
                </p>
                <p>
                  <strong>Gauti informaciją:</strong> Paprašykite informacijos apie 
                  tai, kokie jūsų duomenys saugomi sistemoje.
                </p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button asChild className="flex-1">
                <a href="mailto:<EMAIL>?subject=Paskyros atkūrimas">
                  <Mail className="h-4 w-4 mr-2" />
                  Susisiekti dėl paskyros atkūrimo
                </a>
              </Button>
              
              <Button asChild variant="outline" className="flex-1">
                <Link href="/">
                  <Home className="h-4 w-4 mr-2" />
                  Grįžti į pagrindinį puslapį
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <p>
            DNSB "Vakarai" | Duomenų apsauga pagal BDAR reikalavimus
          </p>
        </div>
      </div>
    </div>
  );
}