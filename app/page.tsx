import Link from "next/link";
import Image from "next/image";
import { Phone, User, ArrowRight, Info, Building, MapPin, Calculator, Zap } from "lucide-react";
import { Home as HomeIcon } from "lucide-react";
import { HomepageContacts } from "@/components/association-contacts";
import { LogoImage } from "@/components/ui/logo-image";
import { ScrollNavBar } from "@/components/scroll-nav-bar";

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Scroll-triggered Navigation Bar */}
      <ScrollNavBar />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative h-screen min-h-[600px] w-full overflow-hidden">
          {/* Building image background */}
          <div className="absolute inset-0">
            <Image 
              src="/images/website/VAKARAI foto small.jpg" 
              alt="DNSB Vakarai Building" 
              fill 
              className="object-cover" 
              priority
            />
          </div>
          {/* Brand color overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-[#001830]/85 to-[#002855]/80">
          </div>
          {/* Pattern overlay for visual interest */}
          <div 
            className="absolute inset-0 opacity-10" 
            style={{ 
              backgroundImage: "url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")",
            }}
          />
          <div className="container mx-auto relative h-full z-10 flex flex-col justify-center items-center text-white px-4 py-8">
            {/* Large logo display in hero section */}
            <div className="mb-8 sm:mb-12">
              <img
                src="/images/logo/dnsb-vakarai-logo-white.svg"
                alt="DNSB Vakarai"
                className="mx-auto h-64 w-auto"
              />
            </div>
            
            <div className="bg-[#002855]/80 backdrop-blur-sm rounded-xl p-6 sm:p-8 max-w-2xl mx-auto text-center shadow-2xl">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-8 sm:mb-10 leading-tight">
                Daugiabučių namų savininkų bendrijos portalas
              </h1>
              
              <Link href="/auth/login">
                <button className="h-12 sm:h-14 px-6 sm:px-8 py-3 rounded-lg text-base sm:text-lg flex items-center gap-3 bg-white text-[#002855] hover:bg-gray-50 hover:shadow-xl hover:scale-105 transition-all duration-300 border border-gray-200 font-bold shadow-lg mx-auto justify-center min-w-[200px] sm:min-w-[240px]">
                  Prisijungti prie portalo
                  <ArrowRight size={20} />
                </button>
              </Link>
            </div>
          </div>
        </section>

        {/* About Section - Simplified */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold mb-8 text-center">Apie mūsų bendriją</h2>
            <div className="bg-gray-50 p-6 rounded-lg max-w-3xl mx-auto">
              <div className="flex items-start gap-3 mb-4">
                <Info className="h-6 w-6 text-blue-600 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-xl font-semibold mb-2">DNSB Vakarai</h3>
                  <p className="text-gray-700">
                  DNSB Vakarai yra daugiabučio namo savininkų bendrija, vienijanti visus namo gyventojus. Mūsų tikslas – užtikrinti efektyvų namo valdymą ir priežiūrą, todėl sukūrėme patogų ir greitą būdą sekti bendrijos naujienas bei svarbią informaciją. Prisijungę prie platformos, galėsite gauti svarbius pranešimus, peržiūrėti bendrijos dokumentus ir tiesiogiai susisiekti su administracija visais namo priežiūros klausimais.
                    </p>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* Administered Houses Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold mb-8 text-center">Administruojami namai</h2>
            <div className="max-w-4xl mx-auto">
              <div className="grid md:grid-cols-2 gap-8 mb-10">
                <div className="bg-white p-6 rounded-lg shadow-sm">
                  <div className="flex items-start gap-3 mb-4">
                    <Building className="h-6 w-6 text-blue-600 flex-shrink-0" />
                    <h3 className="text-xl font-semibold">Varpų gatvė</h3>
                  </div>
                  <div className="pl-9">
                    <p className="text-lg font-medium mb-2">Namai:</p>
                    <p className="text-gray-700 text-lg">6, 8, 10, 12, 14, 16, 18, 20</p>
                  </div>
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow-sm">
                  <div className="flex items-start gap-3 mb-4">
                    <Building className="h-6 w-6 text-blue-600 flex-shrink-0" />
                    <h3 className="text-xl font-semibold">Smiltelės gatvė</h3>
                  </div>
                  <div className="pl-9">
                    <p className="text-lg font-medium mb-2">Namai:</p>
                    <p className="text-gray-700 text-lg">31, 33, 35, 37, 39, 41, 43, 45, 49, 51, 53</p>
                  </div>
                </div>
              </div>
              
              {/* Map section */}
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-start gap-3 mb-5">
                  <MapPin className="h-6 w-6 text-blue-600 flex-shrink-0" />
                  <h3 className="text-xl font-semibold">Namų žemėlapis</h3>
                </div>
                <div className="relative bg-white rounded-lg overflow-hidden">
                  <Image 
                    src="/images/website/DNSB Vakarai map.png" 
                    alt="Administruojamų namų žemėlapis" 
                    width={1200} 
                    height={800}
                    className="w-full h-auto object-contain rounded"
                  />
                  <div className="text-sm text-gray-600 mt-3 text-center">
                    Paveikslėlyje pažymėti administruojami pastatai Varpų g. ir Smiltelės g.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold mb-8 text-center">Svarbūs kontaktai</h2>
            <div className="max-w-4xl mx-auto">
              {/* Chairman Contact - Prominent Display */}
              <div className="bg-blue-50 p-6 rounded-lg shadow-sm border border-blue-100 mb-8">
                <h3 className="text-xl font-semibold mb-4 text-blue-800">Bendrijos pirmininkas</h3>
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  <div className="flex items-center gap-3">
                    <User className="h-6 w-6 text-blue-600" />
                    <span className="text-lg font-medium">Pirmininkas</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="h-5 w-5 text-blue-600" />
                    <a href="tel:+37061630230" className="text-lg font-semibold text-blue-700">
                      +370 61630230
                    </a>
                  </div>
                </div>
                <div className="mt-4 text-sm text-gray-600 border-t border-blue-100 pt-3">
                  <p>Kreipkitės dėl bendrijos valdymo, dokumentų ir kitų svarbių administracinių klausimų.</p>
                </div>
              </div>
              
              <div className="grid md:grid-cols-2 gap-6">
                {/* Association Address */}
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-xl font-semibold mb-4">Administracijos adresas</h3>
                  <div className="flex items-start gap-3 mb-3">
                    <HomeIcon className="h-5 w-5 text-blue-600 mt-1" />
                    <div>
                      <p className="font-medium">Bendrijos būstinė:</p>
                      <p className="text-gray-700">Smiltelės g. 53 – 2, Klaipėda, LT-94262</p>
                    </div>
                  </div>
                  <div className="mt-4 text-sm text-gray-500 border-t pt-3">
                    <p>Išsami informacija apie administraciją prieinama tik prisijungusiems namo gyventojams.</p>
                  </div>
                </div>
                
                {/* Other Association Contacts */}
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-xl font-semibold mb-4">Kiti bendrijos kontaktai</h3>
                  <div className="space-y-4">
                    <div className="flex flex-col gap-3 pb-3 border-b border-gray-100">
                      <div className="flex items-center gap-3">
                        <Calculator className="h-5 w-5 text-blue-600 flex-shrink-0" />
                        <span className="font-medium">Buhalterė:</span>
                      </div>
                      <a href="tel:+37061630989" className="text-blue-700 font-bold text-lg whitespace-nowrap ml-8">
                        +370 61630989
                      </a>
                    </div>
                    <div className="flex flex-col gap-3 pb-3 border-b border-gray-100">
                      <div className="flex items-center gap-3">
                        <Zap className="h-5 w-5 text-blue-600 flex-shrink-0" />
                        <span className="font-medium">Elektros paslaugos (UAB "Jubis"):</span>
                      </div>
                      <a href="tel:+37067719115" className="text-blue-700 font-bold text-lg whitespace-nowrap ml-8">
                        +370 67719115
                      </a>
                    </div>
                    <div className="flex flex-col gap-3 pb-3 border-b border-gray-100">
                      <div className="flex items-center gap-3">
                        <Building className="h-5 w-5 text-blue-600 flex-shrink-0" />
                        <span className="font-medium">Šildymas, vanduo (UAB "SOBO sistemos"):</span>
                      </div>
                      <a href="tel:+37046342508" className="text-blue-700 font-bold text-lg whitespace-nowrap ml-8">
                        +370 46342508
                      </a>
                    </div>
                    {/* Space for additional contacts */}
                    {/* Example of how additional contacts would appear */}
                    <div className="flex items-center gap-3 pb-2 border-b border-gray-100 text-gray-400">
                      <MapPin className="h-5 w-5" />
                      <span className="font-medium">Papildomas kontaktas</span>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-xl font-semibold mb-4">24/7 Avarinės tarnybos</h3>
                  <div className="space-y-4">
                    <div className="flex flex-col gap-3 pb-3 border-b border-gray-100">
                      <div className="flex items-center gap-3">
                        <Phone className="h-5 w-5 text-red-600 flex-shrink-0" />
                        <span className="font-medium">Bendrasis pagalbos numeris</span>
                      </div>
                      <div className="text-2xl font-bold text-red-600 whitespace-nowrap ml-8">112</div>
                    </div>
                    <div className="flex flex-col gap-3 pb-3 border-b border-gray-100">
                      <div className="flex items-center gap-3">
                        <Phone className="h-5 w-5 text-blue-600 flex-shrink-0" />
                        <span className="font-medium">Šildymas, vanduo, kanalizacija (UAB "Skaidrola")</span>
                      </div>
                      <div className="ml-8">
                        <div className="text-lg font-bold text-blue-700 whitespace-nowrap">+370 70055007</div>
                        <div className="text-sm text-gray-600 whitespace-nowrap">arba +370 46366577</div>
                      </div>
                    </div>
                    <div className="flex flex-col gap-3 pb-3 border-b border-gray-100">
                      <div className="flex items-center gap-3">
                        <Phone className="h-5 w-5 text-yellow-600 flex-shrink-0" />
                        <span className="font-medium">Elektros avarinė (UAB "Jubis")</span>
                      </div>
                      <div className="text-lg font-bold text-yellow-700 whitespace-nowrap ml-8">+370 67719115</div>
                    </div>
                    <div className="flex flex-col gap-3 pb-3 border-b border-gray-100">
                      <div className="flex items-center gap-3">
                        <Phone className="h-5 w-5 text-green-600 flex-shrink-0" />
                        <span className="font-medium">Liftų avarinė (UAB "Schindler-liftas")</span>
                      </div>
                      <div className="text-lg font-bold text-green-700 whitespace-nowrap ml-8">+370 62072137</div>
                    </div>
                  </div>
                </div>
                
                {/* Office Hours */}
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-xl font-semibold mb-4">Darbo laikas</h3>

                  {/* Community Office Hours */}
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-800 mb-2">Bendrijos biuro darbo laikas:</h4>
                    <div className="space-y-1">
                      <div className="grid grid-cols-2 pb-1 border-b border-gray-100">
                        <span className="font-medium">Pirmadienis - Penktadienis:</span>
                        <span>9:00 - 17:00</span>
                      </div>
                      <div className="grid grid-cols-2 pb-1 border-b border-gray-100">
                        <span className="font-medium">Šeštadienis, Sekmadienis:</span>
                        <span>Uždaryta</span>
                      </div>
                    </div>
                  </div>

                  {/* Resident Reception Hours */}
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-2">Gyventojų priėmimo laikas:</h4>
                    <div className="space-y-1">
                      <div className="grid grid-cols-2 pb-1 border-b border-gray-100">
                        <span className="font-medium">Pirmadienis:</span>
                        <span>15:00 - 19:00</span>
                      </div>
                      <div className="grid grid-cols-2 pb-1 border-b border-gray-100">
                        <span className="font-medium">Antradienis - Ketvirtadienis:</span>
                        <span>9:00 - 12:00</span>
                      </div>
                    </div>
                  </div>

                  <div className="mt-3 text-sm text-gray-600">
                    <p>Gyventojų priėmimo metu galite susitikti su administracija asmeniškai.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-10">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8">
            <div>
              <div className="mb-4">
                <LogoImage
                  alt="DNSB Vakarai"
                  width={240}
                  height={76}
                  className="h-[2.5rem] w-auto max-w-[180px]"
                  variant="light"
                />
              </div>
              <p className="text-gray-400">
                Daugiabučio namo savininkų bendrija, vienijanti visus namo gyventojus.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Darbo laikas</h3>
              <div className="text-gray-400">
                <div className="mb-3">
                  <p className="font-medium text-gray-300">Biuro darbo laikas:</p>
                  <p>Pirm. - Penkt.: 9:00 - 17:00</p>
                  <p>Šešt., Sekm.: Uždaryta</p>
                </div>
                <div>
                  <p className="font-medium text-gray-300">Gyventojų priėmimas:</p>
                  <p>Pirm.: 15:00 - 19:00</p>
                  <p>Antr. - Ketv.: 9:00 - 12:00</p>
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Greita prieiga</h3>
              <div className="flex flex-col gap-2">
                <Link href="/auth/login" className="text-gray-400 hover:text-white transition-colors">
                  Prisijungti
                </Link>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  Privatumo politika
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  Taisyklės
                </a>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-500">
            <p>© {new Date().getFullYear()} DNSB Vakarai. Visos teisės saugomos.</p>
            <p className="mt-2 text-sm">Įmonės kodas: 141796064</p>
          </div>
        </div>
      </footer>
    </div>
  );
}