"use server";

import { revalidatePath } from "next/cache";
import { createServiceClient } from "@/lib/supabase/server";
import { addAuditLog } from "@/lib/utils";
import { getCurrentUser } from "@/lib/supabase/auth";

export async function deleteAnnouncement(announcementId: string): Promise<void> {
  const user = await getCurrentUser();

  if (!user) {
    throw new Error("Unauthorized");
  }

  const supabase = await createServiceClient();
  const { error } = await supabase
    .from("announcements")
    .delete()
    .eq("id", announcementId);

  if (error) {
    console.error("Failed to delete announcement:", error);
    throw new Error("Failed to delete announcement");
  }

  await addAuditLog({
    action: "delete_announcement",
    userId: parseInt(user.id),
    entityType: "announcement",
    entityId: parseInt(announcementId),
  });

  revalidatePath("/dashboard/announcements");
} 