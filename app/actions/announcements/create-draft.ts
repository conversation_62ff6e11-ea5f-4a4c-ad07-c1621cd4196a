"use server";

import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { addAuditLog } from "@/lib/utils";

/**
 * Creates a new draft announcement and redirects to its edit page.
 * @returns {Promise<void>}
 */
export async function createDraftAnnouncement(): Promise<void> {
  const user = await getCurrentUser();

  if (!user || (user.role !== "developer" && user.role !== "super_admin" && user.role !== "editor")) {
    // This should ideally not be hit if page-level checks are in place,
    // but serves as an additional security layer.
    return redirect("/dashboard");
  }

  const supabase = await createServiceClient();

  const { data: draft, error } = await supabase
    .from("announcements")
    .insert({
      title: "",
      content: "",
      author_id: parseInt(user.id),
      sender_id: parseInt(user.id),
      is_draft: true,
      importance: "normal",
      recipient_type: "all", // Default to all, can be changed during editing
    })
    .select("id")
    .single();

  if (error) {
    console.error("Failed to create draft announcement:", error);
    // Redirect to a generic error page or show a toast notification on the client.
    // For now, redirecting to the announcements list is a safe fallback.
    return redirect("/dashboard/announcements?error=draft-creation-failed");
  }

  await addAuditLog({
    action: "create_announcement_draft",
    userId: parseInt(user.id),
    entityType: "announcement",
    entityId: draft.id,
    changes: { title: "New Draft Created" },
  });

  // Redirect to the edit page for the newly created draft.
  redirect(`/dashboard/announcements/edit/${draft.id}`);
} 