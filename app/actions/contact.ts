'use server'

import { z } from 'zod';
import { db } from '@/lib/db/supabase-adapter';
import { sendEmail } from '@/lib/email';
import { getCurrentUser } from '@/lib/supabase/auth';

// Schema for validation
const contactFormSchema = z.object({
  category: z.string({
    required_error: "Pasirinkite pranešimo kategoriją",
  }),
  subject: z.string().min(5, {
    message: "Tema turi būti bent 5 simboliai",
  }).max(100, {
    message: "Tema negali būti ilgesnė nei 100 simbolių",
  }),
  message: z.string().min(20, {
    message: "Pranešimas turi būti bent 20 simbolių",
  }).max(1000, {
    message: "Pranešimas negali būti ilgesnis nei 1000 simbolių",
  }),
  phoneNumber: z.string().optional(),
  isAnonymous: z.boolean().default(false),
});

// Form action for contact submissions
export async function submitContactForm(prevState: any, formData: FormData) {
  try {
    // Get current user session
    const user = await getCurrentUser();
    
    if (!session?.user) {
      return {
        success: false,
        error: "Turite būti prisijungęs, kad galėtumėte siųsti pranešimą"
      };
    }

    // Extract form data
    const rawValues = {
      category: formData.get('category'),
      subject: formData.get('subject'),
      message: formData.get('message'),
      phoneNumber: formData.get('phoneNumber'),
      isAnonymous: formData.get('isAnonymous'),
    };
    
    // Log the raw values for debugging
    console.log("Raw form data received:", rawValues);
    
    // Convert empty string to undefined for optional fields
    const values = {
      category: rawValues.category?.toString() || '',
      subject: rawValues.subject?.toString() || '',
      message: rawValues.message?.toString() || '',
      phoneNumber: rawValues.phoneNumber?.toString() || undefined,
      isAnonymous: rawValues.isAnonymous === 'true',
    };

    console.log("Processing contact form submission:", values);

    // Validate the form data
    const validationResult = contactFormSchema.safeParse(values);
    if (!validationResult.success) {
      const formattedErrors = validationResult.error.format();
      console.error("Validation failed:", formattedErrors);
      
      // Extract error messages for each field
      return {
        success: false,
        error: "Užpildykite visus būtinus laukus teisingai",
        errors: {
          category: formattedErrors.category?._errors[0] || null,
          subject: formattedErrors.subject?._errors[0] || null,
          message: formattedErrors.message?._errors[0] || null,
          phoneNumber: formattedErrors.phoneNumber?._errors[0] || null,
        }
      };
    }

    // Get the validated data
    const validData = validationResult.data;
    
    // Create record in database
    const contactMessage = await db.contactMessage.create({
      userId: user.id,
      email: user.email || '',
      name: user.name || '',
      category: validData.category,
      subject: validData.subject,
      message: validData.message,
      phoneNumber: validData.phoneNumber || null,
      isAnonymous: validData.isAnonymous,
      status: "NEW",
    });

    console.log("Message created successfully with ID:", contactMessage.id);

    // Try to send email notification, but don't fail if it doesn't work
    try {
      // Categories in Lithuanian for the email
      const categoryLabels: Record<string, string> = {
        general: "Bendras klausimas",
        technical: "Techninis gedimas",
        financial: "Finansai ir mokėjimai",
        noise: "Triukšmas ir konfliktai",
        suggestion: "Pasiūlymas",
        other: "Kita",
      };

      // Get admin email from settings or use default
      let adminEmail = "<EMAIL>"; // Default
      
      try {
        const adminSettings = await db.adminSettings.findFirst({
          where: { key: "contactFormEmail" }
        });
        
        if (adminSettings?.value) {
          adminEmail = adminSettings.value;
        }
      } catch (error) {
        console.error("Error fetching admin settings:", error);
        // Continue with default email
      }

      // Send email notification
      const emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #002855;">Naujas pranešimas iš gyventojo</h2>
          <p>Gavote naują pranešimą nuo gyventojo:</p>
          
          <div style="border-left: 3px solid #002855; padding-left: 10px; margin: 15px 0;">
            <p><strong>Tema:</strong> ${contactMessage.subject}</p>
            <p><strong>Siuntėjas:</strong> ${contactMessage.isAnonymous ? "Anoniminis" : contactMessage.name}</p>
            ${!contactMessage.isAnonymous ? `<p><strong>El. paštas:</strong> ${contactMessage.email}</p>` : ''}
            ${!contactMessage.isAnonymous && contactMessage.phoneNumber ? `<p><strong>Telefono nr.:</strong> ${contactMessage.phoneNumber}</p>` : ''}
            <p><strong>Kategorija:</strong> ${categoryLabels[contactMessage.category] || contactMessage.category}</p>
            <p><strong>Žinutė:</strong><br>${contactMessage.message.replace(/\n/g, '<br>')}</p>
          </div>
          
          <p>Norėdami peržiūrėti ir atnaujinti šį pranešimą, spauskite žemiau esantį mygtuką:</p>
          
          <div style="text-align: center; margin: 20px 0;">
            <a href="/dashboard/admin/messages/contact/${contactMessage.id}" style="background-color: #002855; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Peržiūrėti pranešimą
            </a>
          </div>
          
          <p style="font-size: 12px; color: #666; margin-top: 20px;">
            Šis laiškas sugeneruotas automatiškai. Prašome neatsakyti į šį laišką.
          </p>
        </div>
      `;

      await sendEmail({
        to: adminEmail,
        subject: `Naujas pranešimas: ${validData.subject}`,
        html: emailHtml,
      });
      
      console.log("Email notification sent successfully");
    } catch (emailError) {
      // Log the error but don't fail the form submission
      console.error("Error sending email notification:", emailError);
      // Continue without email notification
    }

    // Return success even if email sending failed
    return { 
      success: true, 
      message: "Pranešimas sėkmingai išsiųstas",
      id: contactMessage.id 
    };
    
  } catch (error) {
    console.error("Error processing contact message:", error);
    return { 
      success: false,
      error: "Įvyko klaida apdorojant jūsų pranešimą" 
    };
  }
} 