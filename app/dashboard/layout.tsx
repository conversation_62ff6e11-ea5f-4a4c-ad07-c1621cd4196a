import { getCurrentUser } from "@/lib/supabase/auth"; // Use Supabase server-side auth
import { redirect } from "next/navigation";
import { DashboardNav } from "@/components/dashboard/nav";
import { UserNav } from "@/components/dashboard/user-nav";
import { ProfileCompletionPopup } from "@/components/dashboard/profile-completion-popup";

// Mark layout as dynamic to prevent build-time static rendering issues with auth
export const dynamic = 'force-dynamic';
// Client-side hooks moved to a separate component or handled differently
// import { useEffect, useState } from "react"; 
import { Menu, X, ChevronLeft, ChevronRight, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import Link from "next/link";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { SidebarClientWrapper } from "@/components/dashboard/SidebarClientWrapper"; // Corrected path
import { HeaderClientWrapper } from "@/components/dashboard/HeaderClientWrapper"; // Corrected path
import { AccessDenied } from "@/components/ui/AccessDenied"; // Corrected path


export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {

  try {
    const user = await getCurrentUser(); // Fetch user from Supabase

    // --- Server-Side Authorization Check ---
    if (!user) {
      redirect("/auth/login?callbackUrl=/dashboard"); // Redirect unauthenticated users
    }

    // Optional: Add role-based access control if needed for the entire dashboard
    // For example, if only 'editor' or 'super_admin' can access the dashboard:
    // if (user.role !== 'editor' && user.role !== 'super_admin') {
    //   return (
    //     <div className="flex h-screen items-center justify-center">
    //       <AccessDenied />
    //     </div>
    //   );
    // }
    // --- End Server-Side Authorization Check ---

    return (
      <div className="flex min-h-screen flex-col bg-slate-50">
        {/* Profile Completion Popup - Remains a Client Component, needs user data */}
        {user && <ProfileCompletionPopup user={user} />}

        {/* Header - Wrap client-interactive parts in HeaderClientWrapper */}
        <HeaderClientWrapper user={user} />
        
        {/* Main content area */}
        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar - Wrap client-interactive parts in SidebarClientWrapper */}
          <SidebarClientWrapper user={user} />

          {/* Main Content Area */}
          <main 
            className={cn(
              "flex flex-col flex-1 overflow-x-hidden transition-all duration-300 ease-in-out",
              "p-4 md:p-6 lg:p-8",
              "lg:ml-[80px]" // Assuming collapsed sidebar initially
            )}
            id="main-content"
            role="main"
            tabIndex={-1}
          >
            <div className="mx-auto w-full max-w-6xl">
              {children} 
            </div>
          </main>
        </div>
        
        {/* Skip to main content link */}
        <a 
          href="#main-content" 
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:p-4 focus:bg-white focus:text-indigo-600 focus:shadow-lg focus:rounded-md"
        >
          Pereiti į pagrindinį turinį
        </a>
      </div>
    );
  } catch (error) {
    // Handle authentication errors gracefully with detailed debugging
    console.error("Authentication error in dashboard layout:", error);
    
    // For all auth errors, simply redirect to login page
    // This avoids the client component error and is more reliable
    redirect("/auth/login?callbackUrl=/dashboard&error=auth");
    
    // We won't reach this point, but TypeScript expects a return
    return null;
  }
}

// --- Removed Client-Side Logic ---
/* 
  The following logic needs to be extracted into client components: 
  - useSession hook (replaced by server-side `auth()`)
  - useState for sidebarOpen, isMobile
  - useEffect hooks for resize checks, navigation closing, status check
  - toggleSidebar function
  
  Create:
  1.  `HeaderClientWrapper`: Manages mobile toggle button state and includes `<UserNav />`.
      Receives `user` prop.
  2.  `SidebarClientWrapper`: Manages sidebar state (open/closed, mobile/desktop), 
      resize events, navigation events, toggle buttons, and includes `<DashboardNav />`.
      Receives `user` prop.
  3.  `AccessDenied` component (optional, for role checks).
*/