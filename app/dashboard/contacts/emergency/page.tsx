import { Metadata } from "next";
import { EmergencyContactsCard } from "@/components/dashboard/emergency-contacts";
import { Breadcrumb } from "@/components/ui/breadcrumb";

export const metadata: Metadata = {
  title: "Avariniai Kontaktai | DNSB Vakarai",
  description: "Svarbūs kontaktai avarijos atveju",
};

export default function EmergencyContactsPage() {
  return (
    <div className="space-y-6">
      <Breadcrumb 
        items={[
          { href: "/dashboard/contacts/emergency", label: "Avariniai kontaktai", isLast: true }
        ]}
        className="mb-4"
      />
      
      <EmergencyContactsCard />
    </div>
  );
} 