import { Metadata } from "next";
import { EmergencyContactsDisplay } from "@/components/emergency-contact-display";
import { Breadcrumb } from "@/components/ui/breadcrumb";

export const metadata: Metadata = {
  title: "Svarbūs kontaktai | DNSB Vakarai",
  description: "Avariniai ir bendrijos kontaktai",
};

export default function ContactsPage() {
  return (
    <div className="container pb-10">
      <div className="mb-6">
        <Breadcrumb 
          items={[
            { href: "/dashboard", label: "Valdymo skydelis" },
            { href: "/dashboard/contacts", label: "Kontaktai", isLast: true }
          ]}
        />
      </div>
      
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-gradient-to-r from-indigo-50 to-white p-6 rounded-lg border border-indigo-100 shadow-sm mb-6">
        <h1 className="text-3xl font-bold text-indigo-900 tracking-tight"><PERSON><PERSON><PERSON><PERSON><PERSON> kontaktai</h1>
        <div className="text-sm text-indigo-700">
          Avariniai ir bendrijos kontaktai
        </div>
      </div>
      
      <div className="grid gap-6">
        <EmergencyContactsDisplay />
      </div>
    </div>
  );
} 