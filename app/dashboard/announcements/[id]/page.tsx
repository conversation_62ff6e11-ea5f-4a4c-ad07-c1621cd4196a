import { Metadata } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { BellRing, Calendar, ChevronLeft, Clock, FileText, Info, User, Users } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Tag } from "@/components/ui/tag";
import { cn } from "@/lib/utils";
import { AnnouncementActions } from "./actions";
import { AttachmentList } from "@/components/announcements/attachment-list";

// Interface for announcement data
interface Announcement {
  id: string;
  title: string;
  content: string;
  importance: string;
  created_at: string;
  updated_at?: string;
  sent_at?: string;
  recipient_type: string;
  author_name: string;
  author_id: string;
  is_draft: boolean;
  recipientInfo?: {
    type: 'street' | 'house' | 'flat' | 'user';
    items: Array<{
      name?: string;
      displayName?: string;
    }>;
  };
  tags: Tag[];
}

interface Tag {
  id: number;
  name: string;
  color: string;
  category: string;
}

// Color class mapping
const colorClassMap: Record<string, { bg: string, text: string, border: string, hover: string }> = {
  slate: { bg: 'bg-slate-100', text: 'text-slate-800', border: 'border-slate-200', hover: 'hover:bg-slate-200' },
  gray: { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200', hover: 'hover:bg-gray-200' },
  red: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200', hover: 'hover:bg-red-200' },
  orange: { bg: 'bg-orange-100', text: 'text-orange-800', border: 'border-orange-200', hover: 'hover:bg-orange-200' },
  amber: { bg: 'bg-amber-100', text: 'text-amber-800', border: 'border-amber-200', hover: 'hover:bg-amber-200' },
  yellow: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200', hover: 'hover:bg-yellow-200' },
  lime: { bg: 'bg-lime-100', text: 'text-lime-800', border: 'border-lime-200', hover: 'hover:bg-lime-200' },
  green: { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200', hover: 'hover:bg-green-200' },
  emerald: { bg: 'bg-emerald-100', text: 'text-emerald-800', border: 'border-emerald-200', hover: 'hover:bg-emerald-200' },
  teal: { bg: 'bg-teal-100', text: 'text-teal-800', border: 'border-teal-200', hover: 'hover:bg-teal-200' },
  cyan: { bg: 'bg-cyan-100', text: 'text-cyan-800', border: 'border-cyan-200', hover: 'hover:bg-cyan-200' },
  blue: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200', hover: 'hover:bg-blue-200' },
  indigo: { bg: 'bg-indigo-100', text: 'text-indigo-800', border: 'border-indigo-200', hover: 'hover:bg-indigo-200' },
  violet: { bg: 'bg-violet-100', text: 'text-violet-800', border: 'border-violet-200', hover: 'hover:bg-violet-200' },
  purple: { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-200', hover: 'hover:bg-purple-200' },
  fuchsia: { bg: 'bg-fuchsia-100', text: 'text-fuchsia-800', border: 'border-fuchsia-200', hover: 'hover:bg-fuchsia-200' },
  pink: { bg: 'bg-pink-100', text: 'text-pink-800', border: 'border-pink-200', hover: 'hover:bg-pink-200' },
  rose: { bg: 'bg-rose-100', text: 'text-rose-800', border: 'border-rose-200', hover: 'hover:bg-rose-200' }
};

// Function to get color classes for a tag
const getTagColorClasses = (color: string) => {
  return colorClassMap[color] || colorClassMap.gray; // Default to gray if color not found
};

// Fetch announcement from Supabase directly
async function getAnnouncement(id: string): Promise<Announcement | null> {
  try {
    const supabase = await createServiceClient();
    
    // Get the announcement details
    const { data: announcement, error: announcementError } = await supabase
      .from('announcements')
      .select(`
        id,
        title,
        content,
        importance,
        recipient_type,
        is_draft,
        created_at,
        updated_at,
        sent_at,
        author_id
      `)
      .eq('id', id)
      .single();
    
    if (announcementError || !announcement) {
      console.error("Error fetching announcement:", announcementError);
      return null;
    }
    
    // Get author name
    const { data: author } = await supabase
      .from('users')
      .select('id, name')
      .eq('id', announcement.author_id)
      .single();
    
    const announcementWithAuthor = {
      ...announcement,
      author_name: author?.name || 'Unknown',
      author_id: announcement.author_id
    };
    
    // Transform the data to match the expected format
    let recipientInfo = null;
    
    // Get associated data based on recipient type
    if (announcement.recipient_type === 'houses') {
      const { data: houseRelations } = await supabase
        .from('announcement_houses')
        .select('house_id')
        .eq('announcement_id', id);
      
      if (houseRelations && houseRelations.length > 0) {
        const houseIds = houseRelations.map(r => r.house_id);
        const { data: houses } = await supabase
          .from('houses')
          .select('id, name, address')
          .in('id', houseIds);
        
        if (houses && houses.length > 0) {
          recipientInfo = {
            type: 'house' as const,
            items: houses.map(house => ({ 
              name: house.name, 
              displayName: `${house.name} (${house.address || ''})` 
            }))
          };
        }
      }
    } else if (announcement.recipient_type === 'flats') {
      const { data: flatRelations } = await supabase
        .from('announcement_flats')
        .select('flat_id')
        .eq('announcement_id', id);
      
      if (flatRelations && flatRelations.length > 0) {
        const flatIds = flatRelations.map(r => r.flat_id);
        const { data: flats } = await supabase
          .from('flats')
          .select('id, number, floor, house_id')
          .in('id', flatIds);
        
        if (flats && flats.length > 0) {
          const houseIds = [...new Set(flats.map(f => f.house_id))];
          const { data: houses } = await supabase
            .from('houses')
            .select('id, name')
            .in('id', houseIds);
          
          const houseMap = (houses || []).reduce((acc, house) => {
            acc[house.id] = house.name;
            return acc;
          }, {} as Record<number, string>);
          
          recipientInfo = {
            type: 'flat' as const,
            items: flats.map(flat => ({ 
              name: flat.number,
              displayName: `${houseMap[flat.house_id] || 'Unknown'} - ${flat.number}` 
            }))
          };
        }
      }
    } else if (announcement.recipient_type === 'users') {
      const { data: userRelations } = await supabase
        .from('announcement_users')
        .select('user_id')
        .eq('announcement_id', id);
      
      if (userRelations && userRelations.length > 0) {
        const userIds = userRelations.map(r => r.user_id);
        const { data: users } = await supabase
          .from('users')
          .select('id, name')
          .in('id', userIds);
        
        if (users && users.length > 0) {
          recipientInfo = {
            type: 'user' as const,
            items: users.map(user => ({ 
              name: user.name,
              displayName: user.name 
            }))
          };
        }
      }
    }
    
    // Fetch tags for the announcement
    const { data: tagRelations } = await supabase
      .from('announcement_tags')
      .select('tag_id')
      .eq('announcement_id', id);
    
    let tags = [];
    if (tagRelations && tagRelations.length > 0) {
      const tagIds = tagRelations.map(r => r.tag_id);
      const { data: tagsData } = await supabase
        .from('tags')
        .select('id, name, color, category')
        .in('id', tagIds);
      
      tags = tagsData || [];
    }
    
    return {
      ...announcementWithAuthor,
      recipientInfo,
      tags
    };
  } catch (error) {
    console.error("Error fetching announcement:", error);
    return null;
  }
}

// Helper function to generate dummy tags based on title and importance
function generateDummyTags(title: string, importance: string): string[] {
  // This is just for demo purposes - in a real app, you'd get real tags
  const allPossibleTags = ["Bendrijos informacija", "Finansai", "Renovacija", "Šildymas", "Teritorija", "Remontas", "Vandentiekis", "Svarbu", "Susirinkimas", "Bendruomenė", "Darbai", "Parkavimas", "Saugumas"];
  
  // Pick 1-3 random tags
  const numTags = Math.floor(Math.random() * 3) + 1;
  const selectedTags = [];
  
  for (let i = 0; i < numTags; i++) {
    const randomIndex = Math.floor(Math.random() * allPossibleTags.length);
    const tag = allPossibleTags[randomIndex];
    if (!selectedTags.includes(tag)) {
      selectedTags.push(tag);
    }
  }
  
  // Add an importance-related tag
  if (importance === 'urgent' && !selectedTags.includes('Skubu')) {
    selectedTags.push('Skubu');
  } else if (importance === 'important' && !selectedTags.includes('Svarbu')) {
    selectedTags.push('Svarbu');
  }
  
  return selectedTags;
}

// Get formatted recipient type description with details
const getRecipientTypeDescription = (announcement: Announcement) => {
  const { recipient_type, recipientInfo } = announcement;
  
  // If we have specific recipient information, show it
  if (recipientInfo && recipientInfo.items && recipientInfo.items.length > 0) {
    switch (recipientInfo.type) {
      case 'street':
        const streetNames = recipientInfo.items
          .map(item => item.name)
          .join(', ');
        return `Gatvės gyventojams: ${streetNames}`;
      
      case 'house':
        const houseNames = recipientInfo.items
          .map(item => item.displayName)
          .join(', ');
        return `Namo gyventojams: ${houseNames}`;
      
      case 'flat':
        const flatNames = recipientInfo.items
          .map(item => item.displayName)
          .join(', ');
        return `Buto gyventojams: ${flatNames}`;
      
      case 'user':
        const userNames = recipientInfo.items
          .map(item => item.name)
          .join(', ');
        return `Pasirinktiems naudotojams: ${userNames}`;
    }
  }
  
  // If no specific recipient info, fall back to generic descriptions
  switch (recipient_type) {
    case 'all':
      return 'Visiems namo gyventojams';
    case 'board':
      return 'Valdybos nariams';
    case 'debtors':
      return 'Skolininkams';
    case 'house':
      return 'Konkretaus namo gyventojams';
    case 'flat':
      return 'Konkretaus buto gyventojams';
    case 'street':
      return 'Gatvės gyventojams';
    case 'user':
    case 'users':
      return 'Pasirinktiems naudotojams';
    default:
      return recipient_type ? `Gavėjai: ${recipient_type}` : 'Nenurodyta';
  }
};

// Get metadata for announcement page
export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
  try {
    // Properly handle async params for Next.js 15
    const { id } = await params;
    if (!id) {
      return { title: "Announcement Not Found" };
    }
    
    const announcement = await getAnnouncement(id);
    
    if (!announcement) {
      return { title: "Announcement Not Found" };
    }
    
    return {
      title: `${announcement.title} | DNSB Vakarai`,
      description: announcement.content.substring(0, 160),
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    return { title: "Error Loading Announcement" };
  }
}

export default async function AnnouncementPage({ params }: { params: Promise<{ id: string }> }) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return notFound();
    }
    
    const isAdmin = user.role === "developer" || user.role === "super_admin" || user.role === "editor";
    
    // Properly handle async params for Next.js 15
    const { id } = await params;
    if (!id) {
      return notFound();
    }
    
    const announcement = await getAnnouncement(id);
    
    if (!announcement) {
      return notFound();
    }
    
    // Prevent non-admins from viewing draft announcements
    if (announcement.is_draft && !isAdmin) {
      return notFound();
    }
    
    // Get importance configuration
    const getImportanceConfig = (importance: string) => {
      switch (importance) {
        case 'urgent':
          return {
            bgColor: 'bg-red-25 border-red-100',
            iconBg: 'bg-red-500',
            textColor: 'text-red-800',
            icon: <BellRing className="h-5 w-5" />,
            tagVariant: 'danger' as const
          };
        case 'important':
          return {
            bgColor: 'bg-amber-25 border-amber-100',
            iconBg: 'bg-amber-500',
            textColor: 'text-amber-800',
            icon: <Info className="h-5 w-5" />,
            tagVariant: 'important' as const
          };
        default:
          return {
            bgColor: 'bg-gray-25 border-gray-100',
            iconBg: 'bg-gray-500',
            textColor: 'text-gray-800',
            icon: <FileText className="h-5 w-5" />,
            tagVariant: 'secondary' as const
          };
      }
    };
    
    // Format date to a more readable format
    const formatDate = (dateString: string) => {
      const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
      return new Date(dateString).toLocaleDateString('lt-LT', options);
    };
    
    const importanceConfig = getImportanceConfig(announcement.importance);
    
    return (
      <div className="space-y-6">
        <Breadcrumb 
          items={[
            { href: "/dashboard/announcements", label: "Pranešimai" },
            { href: `/dashboard/announcements/${id}`, label: announcement.title, isLast: true }
          ]}
          className="mb-4"
        />
        
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-gradient-to-r from-indigo-50 to-white p-6 rounded-lg border border-indigo-100 shadow-sm mb-6">
          <h1 className="text-3xl font-bold text-indigo-900 tracking-tight">{announcement.title}</h1>
          <Button asChild variant="outline" size="sm" className="w-auto border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700">
            <Link href="/dashboard/announcements">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Grįžti į pranešimų sąrašą
            </Link>
          </Button>
        </div>
        
        <div className="p-1 bg-gradient-to-br from-indigo-100 to-transparent rounded-lg">
          <Card className={cn(
            "overflow-hidden border-2 shadow-md",
            announcement.importance === 'urgent' ? 'bg-red-25 border-red-200' : 
            announcement.importance === 'important' ? 'bg-amber-25 border-amber-200' : 
            'bg-white border-slate-200'
          )}>
            <CardHeader className="pb-4">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "text-white p-2 rounded-full",
                    announcement.importance === 'urgent' ? 'bg-red-500' : 
                    announcement.importance === 'important' ? 'bg-amber-500' : 
                    'bg-indigo-500'
                  )}>
                    {importanceConfig.icon}
                  </div>
                  <CardTitle className={cn(
                    "text-xl",
                    announcement.importance === 'urgent' ? 'text-red-800' : 
                    announcement.importance === 'important' ? 'text-amber-800' : 
                    'text-indigo-900'
                  )}>
                    {announcement.title}
                  </CardTitle>
                </div>
                <div className="flex flex-wrap gap-2">
                  <div 
                    className={cn(
                      "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
                      announcement.importance === 'urgent' ? 'bg-red-100 text-red-800 border border-red-200' : 
                      announcement.importance === 'important' ? 'bg-amber-100 text-amber-800 border border-amber-200' : 
                      'bg-slate-100 text-slate-800 border border-slate-200'
                    )}
                  >
                    {announcement.importance === 'urgent' ? 'Skubus' : 
                     announcement.importance === 'important' ? 'Svarbus' : 
                     'Įprastas'}
                  </div>
                  {announcement.tags?.map((tag) => {
                    const colorClasses = getTagColorClasses(tag.color);
                    return (
                      <div 
                        key={tag.id} 
                        className={cn(
                          "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium",
                          colorClasses.bg,
                          colorClasses.text,
                          colorClasses.border
                        )}
                      >
                        {tag.name}
                      </div>
                    );
                  })}
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4 mt-3 text-sm text-slate-500">
                <div className="flex items-center gap-1.5">
                  <Clock className="h-3.5 w-3.5" />
                  <span>Paskelbta: {formatDate(announcement.created_at)}</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <User className="h-3.5 w-3.5" />
                  <span>Autorius: {announcement.author_name}</span>
                </div>
                {isAdmin && (
                  <div className="flex items-center gap-1.5">
                    <Users className="h-3.5 w-3.5" />
                    <span>Gavėjai: {getRecipientTypeDescription(announcement)}</span>
                  </div>
                )}
              </div>
            </CardHeader>
            
            <CardContent className="pb-6">
              <div
                className="prose max-w-none prose-headings:text-indigo-900 prose-p:text-slate-700 prose-strong:text-slate-900 prose-strong:font-semibold prose-a:text-indigo-600 hover:prose-a:text-indigo-800 prose-a:no-underline hover:prose-a:underline prose-img:rounded-md"
                dangerouslySetInnerHTML={{ __html: announcement.content }}
              />

              <div className="mt-6">
                <AttachmentList announcementId={announcement.id} />
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="flex flex-col sm:flex-row justify-between gap-4 mt-8">
          <Button asChild variant="outline" className="border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700">
            <Link href="/dashboard/announcements">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Visi pranešimai
            </Link>
          </Button>
          
          {/* Show edit and send actions for drafts */}
          {isAdmin ? (
            <AnnouncementActions id={announcement.id} isDraft={announcement.is_draft} />
          ) : (
            /* Example of related links, would be dynamic in a real app */
            <div className="flex flex-wrap gap-2">
              <Button asChild className="bg-indigo-600 hover:bg-indigo-700">
                <Link href="/dashboard/contact">
                  Susisiekti
                </Link>
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error rendering announcement page:", error);
    return notFound();
  }
} 