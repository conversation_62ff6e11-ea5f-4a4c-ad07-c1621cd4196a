"use client";

import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Edit, Send } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface AnnouncementActionsProps {
  id: string;
  isDraft: boolean;
}

export function AnnouncementActions({ id, isDraft }: AnnouncementActionsProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleEdit = () => {
    router.push(`/dashboard/announcements/edit/${id}`);
  };

  const handleSend = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/announcements/${id}/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sendEmails: true
        })
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      toast.success("Pranešimas sėkmingai iš<PERSON>ų<PERSON>");
      router.refresh();
    } catch (error) {
      console.error("Failed to send announcement:", error);
      toast.error("Nepavyko išsiųsti pranešimo");
    } finally {
      setIsLoading(false);
    }
  };

  if (!isDraft) {
    return null;
  }

  return (
    <div className="flex gap-2">
      <Button
        variant="outline"
        onClick={handleEdit}
        className="border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700"
      >
        <Edit className="mr-2 h-4 w-4" />
        Redaguoti juodraštį
      </Button>
      <Button
        onClick={handleSend}
        disabled={isLoading}
        className="bg-indigo-600 hover:bg-indigo-700"
      >
        <Send className="mr-2 h-4 w-4" />
        {isLoading ? "Siunčiama..." : "Siųsti pranešimą"}
      </Button>
    </div>
  );
}
