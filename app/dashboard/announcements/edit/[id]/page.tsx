import { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { AnnouncementForm } from "@/components/announcements/announcement-form";
import { Announcement } from "@/lib/db/schema";

interface PageParams {
  id: string;
}

async function getAnnouncement(id: string): Promise<any | null> {
  const supabase = await createServiceClient();

  // 1. Get the core announcement
  const { data: announcement, error } = await supabase
    .from("announcements")
    .select("*")
    .eq("id", id)
    .single();

  if (error || !announcement) {
    console.error(`Error fetching core announcement with id=${id}:`, error);
    return null;
  }

  // 2. Get the relations in parallel
  const [
    { data: streets },
    { data: houses },
    { data: flats },
    { data: users },
    { data: tagRelations }
  ] = await Promise.all([
    supabase.from('announcement_streets').select('street_id').eq('announcement_id', id),
    supabase.from('announcement_houses').select('house_id').eq('announcement_id', id),
    supabase.from('announcement_flats').select('flat_id').eq('announcement_id', id),
    supabase.from('announcement_users').select('user_id').eq('announcement_id', id),
    supabase.from('announcement_tags').select('tags(*)').eq('announcement_id', id)
  ]);

  // 3. Combine the data
  const formattedData = {
    ...announcement,
    street_ids: streets?.map((s: any) => s.street_id) || [],
    house_ids: houses?.map((h: any) => h.house_id) || [],
    flat_ids: flats?.map((f: any) => f.flat_id) || [],
    user_ids: users?.map((u: any) => u.user_id) || [],
    tags: tagRelations?.map((t: any) => t.tags) || [],
  };

  return formattedData;
}

export async function generateMetadata({ params: rawParams }: { params: PageParams }): Promise<Metadata> {
  const params = await rawParams;
  const announcement = await getAnnouncement(params.id);
  if (!announcement) {
    return {
      title: "Pranešimas nerastas",
    };
  }
  return {
    title: `Redaguoti: ${announcement.title} | DNSB Vakarai`,
  };
}

export default async function EditAnnouncementPage({ params: rawParams }: { params: PageParams }) {
  const params = await rawParams;
  const user = await getCurrentUser();
  const announcement = await getAnnouncement(params.id);

  if (!announcement || !user) {
    notFound();
  }

  // The form expects a userId, so we add it to the announcement object.
  const announcementWithUser = {
    ...announcement,
    userId: parseInt(user.id),
  };

  return (
    <div className="space-y-6">
      <Breadcrumb
        items={[
          { href: "/dashboard/announcements", label: "Pranešimai" },
          { href: `/dashboard/announcements/edit/${params.id}`, label: "Redaguoti pranešimą", isLast: true },
        ]}
        className="mb-4"
      />

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-gradient-to-r from-indigo-50 to-white p-6 rounded-lg border border-indigo-100 shadow-sm mb-6">
        <h1 className="text-3xl font-bold text-indigo-900 tracking-tight">Redaguoti pranešimą</h1>
        <p className="text-slate-600 mt-2 max-w-2xl">
          Atlikite pakeitimus ir išsaugokite kaip juodraštį arba išsiųskite gyventojams.
        </p>
      </div>
      
      <div className="p-1 bg-gradient-to-br from-indigo-100 to-transparent rounded-lg">
        <AnnouncementForm 
          key={announcement.id}
          existingAnnouncement={announcementWithUser}
          editMode={true}
        />
      </div>
    </div>
  );
}
