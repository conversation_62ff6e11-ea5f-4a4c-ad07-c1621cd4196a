import { Metada<PERSON> } from "next";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import { AnnouncementForm } from "@/components/announcements/announcement-form";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { createDraftAnnouncement } from "@/app/actions/announcements/create-draft";

export const metadata: Metadata = {
  title: "Naujas pranešimas | DNSB Vakarai",
  description: "Sukurkite naują pranešimą",
};

export default async function CreateAnnouncementPage() {
  // The logic inside createDraftAnnouncement will handle user checks and redirection.
  await createDraftAnnouncement();

  // This return is for compliance, as the user will be redirected by the action.
  return null;
} 