"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Tag } from "@/components/ui/tag";
import { 
  BellRing, 
  Clock, 
  FileText, 
  Info, 
  Plus, 
  Search,
  X,
  Edit,
  Send,
  ChevronDown,
  ChevronUp,
  ChevronRight
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useEffect, useState, useMemo } from "react";
import { useSession } from "@/lib/hooks/use-session";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { useAnnouncements, useTags, invalidateQueries } from "@/lib/tanstack/queries";
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from "@/components/ui/alert-dialog";

// Interface for announcement data
interface Announcement {
  id: string;
  title: string;
  content: string;
  importance: string;
  created_at: string;
  updated_at?: string;
  sent_at?: string;
  recipient_type: string;
  author_name: string;
  author_id: string;
  tags: AnnouncementTag[];
  is_draft?: boolean;
}

interface AnnouncementTag {
  id: number;
  name: string;
  color: string;
  category: string;
}

// Color class mapping
const colorClassMap: Record<string, { bg: string, text: string, border: string, hover: string }> = {
  slate: { bg: 'bg-slate-100', text: 'text-slate-800', border: 'border-slate-200', hover: 'hover:bg-slate-200' },
  gray: { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200', hover: 'hover:bg-gray-200' },
  red: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200', hover: 'hover:bg-red-200' },
  orange: { bg: 'bg-orange-100', text: 'text-orange-800', border: 'border-orange-200', hover: 'hover:bg-orange-200' },
  amber: { bg: 'bg-amber-100', text: 'text-amber-800', border: 'border-amber-200', hover: 'hover:bg-amber-200' },
  yellow: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200', hover: 'hover:bg-yellow-200' },
  lime: { bg: 'bg-lime-100', text: 'text-lime-800', border: 'border-lime-200', hover: 'hover:bg-lime-200' },
  green: { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200', hover: 'hover:bg-green-200' },
  emerald: { bg: 'bg-emerald-100', text: 'text-emerald-800', border: 'border-emerald-200', hover: 'hover:bg-emerald-200' },
  teal: { bg: 'bg-teal-100', text: 'text-teal-800', border: 'border-teal-200', hover: 'hover:bg-teal-200' },
  cyan: { bg: 'bg-cyan-100', text: 'text-cyan-800', border: 'border-cyan-200', hover: 'hover:bg-cyan-200' },
  blue: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200', hover: 'hover:bg-blue-200' },
  indigo: { bg: 'bg-indigo-100', text: 'text-indigo-800', border: 'border-indigo-200', hover: 'hover:bg-indigo-200' },
  violet: { bg: 'bg-violet-100', text: 'text-violet-800', border: 'border-violet-200', hover: 'hover:bg-violet-200' },
  purple: { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-200', hover: 'hover:bg-purple-200' },
  fuchsia: { bg: 'bg-fuchsia-100', text: 'text-fuchsia-800', border: 'border-fuchsia-200', hover: 'hover:bg-fuchsia-200' },
  pink: { bg: 'bg-pink-100', text: 'text-pink-800', border: 'border-pink-200', hover: 'hover:bg-pink-200' },
  rose: { bg: 'bg-rose-100', text: 'text-rose-800', border: 'border-rose-200', hover: 'hover:bg-rose-200' }
};

// Function to get color classes for a tag
const getTagColorClasses = (color: string) => {
  return colorClassMap[color] || colorClassMap.gray; // Default to gray if color not found
};

export default function AnnouncementsPage() {
  const { data: session } = useSession();
  const isAdmin = session?.user?.role === "super_admin" || session?.user?.role === "editor";
  
  // Replace manual state with TanStack Query hooks
  const { data: announcements = [], isLoading: loading } = useAnnouncements();
  const { data: availableTags = [] } = useTags();
  
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedImportance, setSelectedImportance] = useState("all");
  const [selectedTags, setSelectedTags] = useState<number[]>([]);
  const [sendingAnnouncementId, setSendingAnnouncementId] = useState<string | null>(null);
  const [isSendingConfirmOpen, setIsSendingConfirmOpen] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [expandedAnnouncementIds, setExpandedAnnouncementIds] = useState<string[]>([]);
  const [hoveredAnnouncementId, setHoveredAnnouncementId] = useState<string | null>(null);
  
  const router = useRouter();

  // Data loading is now handled by TanStack Query hooks above
  
  // Helper function to get tag data by ID
  const getTagById = (tagId: number) => {
    return availableTags.find(tag => tag.id === tagId);
  };
  
  // Format date to a more readable format
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('lt-LT', options);
  };
  
  // Get importance configuration
  const getImportanceConfig = (importance: string) => {
    switch (importance) {
      case 'urgent':
        return {
          bgColor: 'bg-red-25 border-red-100',
          iconBg: 'bg-red-500',
          textColor: 'text-red-800',
          icon: <BellRing className="h-4 w-4" />,
          tagVariant: 'danger' as const
        };
      case 'important':
        return {
          bgColor: 'bg-amber-25 border-amber-100',
          iconBg: 'bg-amber-500',
          textColor: 'text-amber-800',
          icon: <Info className="h-4 w-4" />,
          tagVariant: 'important' as const
        };
      default:
        return {
          bgColor: 'bg-gray-25 border-gray-100',
          iconBg: 'bg-gray-500',
          textColor: 'text-gray-800',
          icon: <FileText className="h-4 w-4" />,
          tagVariant: 'secondary' as const
        };
    }
  };
  
  // All available tags from announcements
  const allTags = [...availableTags].sort((a, b) => a.name.localeCompare(b.name));
  
  // Filter announcements based on search, importance, and tags
  const filteredAnnouncements = useMemo(() => {
    let filtered = [...announcements];
    
    // Search term filtering
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(a => 
        a.title.toLowerCase().includes(term) || 
        a.content.toLowerCase().includes(term) ||
        a.tags.some(tag => tag.name.toLowerCase().includes(term))
      );
    }
    
    // Importance filtering
    if (selectedImportance !== 'all') {
      filtered = filtered.filter(a => a.importance === selectedImportance);
    }
    
    // Tag filtering
    if (selectedTags.length > 0) {
      filtered = filtered.filter(a => 
        a.tags.some(tag => selectedTags.includes(tag.id))
      );
    }
    
    return filtered;
  }, [searchTerm, selectedImportance, selectedTags, announcements]);
  
  // Toggle tag selection
  const toggleTag = (tagId: number) => {
    setSelectedTags(prev => 
      prev.includes(tagId) 
        ? prev.filter(t => t !== tagId) 
        : [...prev, tagId]
    );
  };

  // Separate drafts from published announcements
  const draftAnnouncements = filteredAnnouncements.filter(a => a.is_draft);
  const publishedAnnouncements = filteredAnnouncements.filter(a => !a.is_draft);

  const handleSendDraft = async (announcementId: string) => {
    setSendingAnnouncementId(announcementId);
    setIsSendingConfirmOpen(true);
  };

  const confirmSendAnnouncement = async () => {
    if (!sendingAnnouncementId) return;
    
    try {
      setIsSending(true);
      const response = await fetch(`/api/announcements/${sendingAnnouncementId}/send`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      toast.success("Pranešimas sėkmingai išsiųstas");
      
      // Refresh the announcements list
      invalidateQueries.announcements();
    } catch (error) {
      console.error("Failed to send announcement:", error);
      toast.error("Nepavyko išsiųsti pranešimo");
    } finally {
      // Close the dialog and reset the ID
      setIsSending(false);
      setIsSendingConfirmOpen(false);
      setSendingAnnouncementId(null);
    }
  };

  // Function to toggle announcement expansion
  const toggleAnnouncement = (id: string) => {
    setExpandedAnnouncementIds(prev => 
      prev.includes(id) 
        ? prev.filter(announcementId => announcementId !== id)
        : [...prev, id]
    );
  };

  return (
    <div className="space-y-6">
      <Breadcrumb 
        items={[
          { href: "/dashboard/announcements", label: "Pranešimai", isLast: true }
        ]}
        className="mb-4"
      />
      
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-gradient-to-r from-indigo-50 to-white p-6 rounded-lg border border-indigo-100 shadow-sm mb-6">
        <h1 className="text-3xl font-bold text-indigo-900 tracking-tight">Pranešimai</h1>
        {isAdmin && (
          <Button>
            <Link href="/dashboard/announcements/create" className="flex items-center">
              <Plus className="mr-2 h-4 w-4" />
              Sukurti pranešimą
            </Link>
          </Button>
        )}
      </div>
      
      <div className="p-1 bg-gradient-to-br from-indigo-100 to-transparent rounded-lg">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-xl text-indigo-900">Pranešimų sąrašas</CardTitle>
            <CardDescription>
              Peržiūrėkite visus jums galiojančius pranešimus
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400" />
                  <Input 
                    type="search" 
                    placeholder="Ieškoti pranešimų..."
                    className="pl-8 border-slate-200 focus-visible:ring-indigo-500"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div>
                  <Tabs 
                    defaultValue="all" 
                    value={selectedImportance}
                    onValueChange={setSelectedImportance}
                    className="w-full"
                  >
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="all">Visi</TabsTrigger>
                      <TabsTrigger value="important">Svarbūs</TabsTrigger>
                      <TabsTrigger value="urgent">Skubūs</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
              </div>
              
              {/* Tag filtering section */}
              <div className="border rounded-md border-slate-200 p-4 bg-slate-50/50">
                <h3 className="text-sm font-medium text-slate-700 mb-3">Filtruoti pagal žymas:</h3>
                <div className="flex flex-wrap gap-2">
                  {allTags.map(tag => {
                    const isSelected = selectedTags.includes(tag.id);
                    const colorClasses = getTagColorClasses(tag.color);
                    
                    return (
                      <button
                        key={tag.id}
                        onClick={() => toggleTag(tag.id)}
                        className={cn(
                          "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors",
                          "border",
                          isSelected ? [
                            colorClasses.bg, 
                            colorClasses.text, 
                            colorClasses.border,
                          ] : [
                            "bg-white",
                            "text-slate-700",
                            "border-slate-200",
                            "hover:bg-slate-100",
                          ]
                        )}
                      >
                        {isSelected && (
                          <X className="h-3 w-3 mr-1" />
                        )}
                        {tag.name}
                      </button>
                    );
                  })}
                  
                  {selectedTags.length > 0 && (
                    <button
                      onClick={() => setSelectedTags([])}
                      className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium border border-slate-200 bg-white text-slate-700 hover:bg-slate-100"
                    >
                      <X className="h-3 w-3 mr-1" />
                      Išvalyti visus
                    </button>
                  )}
                </div>
              </div>
              
              {/* Announcements list */}
              {loading ? (
                <div className="py-8">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
                  </div>
                  <p className="text-center text-slate-500 mt-4">Kraunami pranešimai...</p>
                </div>
              ) : filteredAnnouncements.length === 0 ? (
                <div className="text-center py-8 border rounded-md border-slate-200 bg-slate-50">
                  <FileText className="mx-auto h-10 w-10 text-slate-400" />
                  <h3 className="mt-2 text-lg font-medium text-slate-700">Pranešimų nerasta</h3>
                  <p className="mt-1 text-sm text-slate-500">
                    Nėra pranešimų atitinkančių jūsų paieškos kriterijus.
                  </p>
                </div>
              ) : (
                <div className="space-y-8">
                  {/* Draft announcements section - only visible to admins */}
                  {isAdmin && draftAnnouncements.length > 0 && (
                    <div>
                      <div className="flex items-center gap-2 mb-4 px-4 py-2 bg-slate-100 rounded-md border border-slate-200">
                        <FileText className="h-5 w-5 text-slate-600" />
                        <h3 className="text-lg font-medium text-slate-800">Juodraščiai ({draftAnnouncements.length})</h3>
                      </div>
                      <div className="space-y-4">
                        {draftAnnouncements.map((announcement) => {
                          const importanceConfig = getImportanceConfig(announcement.importance);
                          const isExpanded = expandedAnnouncementIds.includes(announcement.id);
                          
                          return (
                            <div
                              key={announcement.id}
                              className={cn(
                                "rounded-lg border-2 transition-all shadow-sm hover:shadow-md p-4",
                                announcement.importance === 'urgent' ? 'border-red-200' : 
                                announcement.importance === 'important' ? 'border-amber-200' : 
                                'border-slate-200',
                                importanceConfig.bgColor
                              )}
                            >
                              <div className="flex gap-3">
                                <div className={`flex-shrink-0 h-10 w-10 flex items-center justify-center text-white rounded-full ${importanceConfig.iconBg}`}>
                                  {importanceConfig.icon}
                                </div>
                                
                                <div className="flex-1">
                                  <div className="flex justify-between items-start">
                                    <div className="flex-1">
                                      <Link 
                                        href={`/dashboard/announcements/${announcement.id}`}
                                        className="block group"
                                      >
                                        <h3 className="text-lg font-medium text-indigo-900 group-hover:text-indigo-700 group-hover:underline transition-colors">
                                          {announcement.title}
                                        </h3>
                                      </Link>
                                      <div className="flex flex-wrap items-center gap-2 text-sm text-slate-500 mt-1">
                                        <div className="flex items-center gap-1">
                                          <Clock className="h-3.5 w-3.5" />
                                          <span>{formatDate(announcement.created_at)}</span>
                                        </div>
                                        <span>•</span>
                                        <span>{announcement.author_name}</span>
                                      </div>
                                    </div>
                                    
                                    <div className="flex items-center gap-2">
                                      <div 
                                        className={cn(
                                          "flex items-center gap-1 rounded-full px-2 py-0.5 text-xs font-medium",
                                          importanceConfig.bgColor,
                                          importanceConfig.textColor
                                        )}
                                      >
                                        {importanceConfig.icon}
                                        <span>
                                          {announcement.importance === 'urgent' ? 'Skubus' : 
                                            announcement.importance === 'important' ? 'Svarbus' : 
                                            'Įprastas'}
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                  
                                  <div className="flex flex-wrap gap-1.5 mt-2">
                                    {announcement.tags.length > 0 && (
                                      announcement.tags.map(tag => {
                                        const colorClasses = getTagColorClasses(tag.color);
                                        return (
                                          <span 
                                            key={tag.id} 
                                            className={cn(
                                              "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
                                              colorClasses.bg,
                                              colorClasses.text,
                                              "border",
                                              colorClasses.border
                                            )}
                                          >
                                            {tag.name}
                                          </span>
                                        );
                                      })
                                    )}
                                  </div>
                                  
                                  <p className="mt-3 text-gray-700">
                                    {announcement.content.replace(/<[^>]*>/g, '').substring(0, 200)}
                                    {announcement.content.replace(/<[^>]*>/g, '').length > 200 ? '...' : ''}
                                  </p>
                                  
                                  <div className="mt-4 flex justify-end gap-1.5">
                                    {isAdmin && (
                                      <>
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="flex items-center gap-1 text-slate-700 hover:text-indigo-600 hover:bg-indigo-50"
                                          onClick={() => router.push(`/dashboard/announcements/edit/${announcement.id}`)}
                                          aria-label="Redaguoti pranešimą"
                                        >
                                          <Edit className="h-4 w-4" />
                                          <span>Redaguoti</span>
                                        </Button>
                                        
                                        {announcement.is_draft && (
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            className="flex items-center gap-1 text-slate-700 hover:text-green-600 hover:bg-green-50"
                                            onClick={(e) => {
                                              e.preventDefault();
                                              handleSendDraft(announcement.id);
                                            }}
                                            aria-label="Siųsti pranešimą"
                                            disabled={isSending && sendingAnnouncementId === announcement.id}
                                          >
                                            {isSending && sendingAnnouncementId === announcement.id ? (
                                              <>
                                                <svg className="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                Siunčiama...
                                              </>
                                            ) : (
                                              <>
                                                <Send className="h-4 w-4" />
                                                <span>Siųsti</span>
                                              </>
                                            )}
                                          </Button>
                                        )}
                                      </>
                                    )}
                                    <Button 
                                      variant="outline" 
                                      asChild 
                                      size="sm"
                                      className="flex items-center gap-1 text-slate-700"
                                      aria-label="Skaityti pranešimą"
                                    >
                                      <Link href={`/dashboard/announcements/${announcement.id}`}>
                                        <FileText className="h-4 w-4 mr-1" />
                                        Skaityti
                                      </Link>
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                  
                  {/* Published announcements section */}
                  {publishedAnnouncements.length > 0 && (
                    <div>
                      <div className="flex items-center gap-2 mb-4 px-4 py-2 bg-indigo-50 rounded-md border border-indigo-100">
                        <BellRing className="h-5 w-5 text-indigo-600" />
                        <h3 className="text-lg font-medium text-indigo-800">Pranešimai ({publishedAnnouncements.length})</h3>
                      </div>
                      <div className="space-y-4">
                        {publishedAnnouncements.map((announcement) => {
                          const importanceConfig = getImportanceConfig(announcement.importance);
                          const isExpanded = expandedAnnouncementIds.includes(announcement.id);
                          
                          return (
                            <div
                              key={announcement.id}
                              className={cn(
                                "rounded-lg border-2 transition-all shadow-sm hover:shadow-md p-4",
                                announcement.importance === 'urgent' ? 'border-red-200' : 
                                announcement.importance === 'important' ? 'border-amber-200' : 
                                'border-slate-200',
                                importanceConfig.bgColor
                              )}
                            >
                              <div className="flex gap-3">
                                <div className={`flex-shrink-0 h-10 w-10 flex items-center justify-center text-white rounded-full ${importanceConfig.iconBg}`}>
                                  {importanceConfig.icon}
                                </div>
                                
                                <div className="flex-1">
                                  <div className="flex justify-between items-start">
                                    <div className="flex-1">
                                      <Link 
                                        href={`/dashboard/announcements/${announcement.id}`}
                                        className="block group"
                                      >
                                        <h3 className="text-lg font-medium text-indigo-900 group-hover:text-indigo-700 group-hover:underline transition-colors">
                                          {announcement.title}
                                        </h3>
                                      </Link>
                                      <div className="flex flex-wrap items-center gap-2 text-sm text-slate-500 mt-1">
                                        <div className="flex items-center gap-1">
                                          <Clock className="h-3.5 w-3.5" />
                                          <span>{formatDate(announcement.created_at)}</span>
                                        </div>
                                        <span>•</span>
                                        <span>{announcement.author_name}</span>
                                      </div>
                                    </div>
                                    
                                    <div className="flex items-center gap-2">
                                      <div 
                                        className={cn(
                                          "flex items-center gap-1 rounded-full px-2 py-0.5 text-xs font-medium",
                                          importanceConfig.bgColor,
                                          importanceConfig.textColor
                                        )}
                                      >
                                        {importanceConfig.icon}
                                        <span>
                                          {announcement.importance === 'urgent' ? 'Skubus' : 
                                            announcement.importance === 'important' ? 'Svarbus' : 
                                            'Įprastas'}
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                  
                                  <div className="flex flex-wrap gap-1.5 mt-2">
                                    {announcement.tags.length > 0 && (
                                      announcement.tags.map(tag => {
                                        const colorClasses = getTagColorClasses(tag.color);
                                        return (
                                          <span 
                                            key={tag.id} 
                                            className={cn(
                                              "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
                                              colorClasses.bg,
                                              colorClasses.text,
                                              "border",
                                              colorClasses.border
                                            )}
                                          >
                                            {tag.name}
                                          </span>
                                        );
                                      })
                                    )}
                                  </div>
                                  
                                  <p className="mt-3 text-gray-700">
                                    {announcement.content.replace(/<[^>]*>/g, '').substring(0, 200)}
                                    {announcement.content.replace(/<[^>]*>/g, '').length > 200 ? '...' : ''}
                                  </p>
                                  
                                  <div className="mt-4 flex justify-end gap-1.5">
                                    {isAdmin && (
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="flex items-center gap-1 text-slate-700 hover:text-indigo-600 hover:bg-indigo-50"
                                        onClick={() => router.push(`/dashboard/announcements/edit/${announcement.id}`)}
                                        aria-label="Redaguoti pranešimą"
                                      >
                                        <Edit className="h-4 w-4" />
                                        <span>Redaguoti</span>
                                      </Button>
                                    )}
                                    <Button 
                                      variant="outline" 
                                      asChild 
                                      size="sm"
                                      className="flex items-center gap-1 text-slate-700"
                                      aria-label="Skaityti pranešimą"
                                    >
                                      <Link href={`/dashboard/announcements/${announcement.id}`}>
                                        <FileText className="h-4 w-4 mr-1" />
                                        Skaityti
                                      </Link>
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Alert Dialog for send confirmation */}
      <AlertDialog open={isSendingConfirmOpen} onOpenChange={(open) => {
        // Only allow closing if we're not in the middle of sending
        if (!isSending || !open) {
          setIsSendingConfirmOpen(open);
          if (!open) {
            setSendingAnnouncementId(null);
          }
        }
      }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Patvirtinkite siuntimą</AlertDialogTitle>
            <AlertDialogDescription>
              Ar tikrai norite išsiųsti šį pranešimą? Išsiųstas pranešimas bus matomas visiems gavėjams ir nebegalės būti redaguojamas.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex items-center justify-end gap-2">
            <AlertDialogCancel onClick={() => setSendingAnnouncementId(null)} disabled={isSending}>
              Atšaukti
            </AlertDialogCancel>
            <AlertDialogAction onClick={confirmSendAnnouncement} disabled={isSending} className="relative">
              {isSending ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Siunčiama...
                </>
              ) : (
                "Siųsti"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 