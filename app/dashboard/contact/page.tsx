import { Metadata } from "next";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import ContactForm from "@/components/forms/contact-form";

export const metadata: Metadata = {
  title: "Susisiekite | DNSB Vakarai",
  description: "Susisiekite su administracija",
};

export default async function ContactPage() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/auth/login");
  }
  
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Susisiekite su administracija</h1>
        <p className="text-muted-foreground mt-2">
          Užpildykite formą, kad susisiektumėte su bendrijos administracija
        </p>
      </div>
      
      <div className="grid md:grid-cols-3 gap-8">
        <div className="md:col-span-2">
          <ContactForm />
        </div>
        
        <div className="space-y-6">
          <div className="bg-blue-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-blue-700">Darbo laikas</h2>

            <div className="mb-4">
              <h3 className="font-semibold text-blue-800 mb-2">Bendrijos biuro darbo laikas:</h3>
              <div className="space-y-1 text-gray-700">
                <p>Pirm. - Penkt.: 9:00 - 17:00</p>
                <p>Šešt., Sekm.: Uždaryta</p>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-blue-800 mb-2">Gyventojų priėmimo laikas:</h3>
              <div className="space-y-1 text-gray-700">
                <p>Pirm.: 15:00 - 19:00</p>
                <p>Antr. - Ketv.: 9:00 - 12:00</p>
              </div>
            </div>

            <p className="mt-4 text-sm text-gray-600">
              Gyventojų priėmimo metu galite susitikti su administracija asmeniškai.
            </p>
          </div>
          
          <div className="bg-gray-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Bendrijos kontaktai</h2>
            <div className="space-y-3">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 pb-2 border-b border-gray-200">
                <span className="font-medium">Pirmininkas:</span>
                <a href="tel:+37061630230" className="text-blue-700 font-bold text-lg whitespace-nowrap">+370 61630230</a>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 pb-2 border-b border-gray-200">
                <span className="font-medium">Buhalterė:</span>
                <a href="tel:+37061630989" className="text-blue-700 font-bold text-lg whitespace-nowrap">+370 61630989</a>
              </div>
              <div className="flex flex-col gap-2 pb-2 border-b border-gray-200">
                <span className="font-medium">Elektros paslaugos (UAB "Jubis"):</span>
                <a href="tel:+37067719115" className="text-blue-700 font-bold text-lg whitespace-nowrap ml-4">+370 67719115</a>
              </div>
              <div className="flex flex-col gap-2 pb-2 border-b border-gray-200">
                <span className="font-medium">Šildymas, vanduo (UAB "SOBO sistemos"):</span>
                <a href="tel:+37046342508" className="text-blue-700 font-bold text-lg whitespace-nowrap ml-4">+370 46342508</a>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Atsakymo laikas</h2>
            <p className="text-gray-700">
              Stengiamės atsakyti į visus pranešimus per 1-2 darbo dienas. 
              Sudėtingesni klausimai gali užtrukti ilgiau.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 