"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { TestEmailConfig } from "@/lib/email-utils";
import { useRouter } from "next/navigation";

interface TestEmailFormProps {
  initialConfig: TestEmailConfig;
}

export default function TestEmailForm({ initialConfig }: TestEmailFormProps) {
  const [config, setConfig] = useState<TestEmailConfig>(initialConfig);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  // Helper to handle changes to config
  const updateConfig = (key: keyof TestEmailConfig, value: any) => {
    setConfig((prev) => ({ ...prev, [key]: value }));
  };

  // Helper to convert array to string
  const arrayToString = (arr: string[]) => arr.join("\n");
  
  // Helper to convert string to array
  const stringToArray = (str: string) => 
    str.split("\n").map(line => line.trim()).filter(Boolean);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Convert arrays to strings for the form submission
      const response = await fetch("/api/admin/settings/test-emails", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          config: {
            ...config,
            // Make sure arrays are properly formatted
            addresses: typeof config.addresses === 'string' 
              ? stringToArray(config.addresses as string) 
              : config.addresses,
            domains: typeof config.domains === 'string' 
              ? stringToArray(config.domains as string) 
              : config.domains,
            streetPatterns: typeof config.streetPatterns === 'string' 
              ? stringToArray(config.streetPatterns as string) 
              : config.streetPatterns,
          },
        }),
      });

      if (response.ok) {
        toast({
          title: "Settings saved",
          description: "Test email configuration has been updated successfully.",
        });
        router.refresh();
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to save settings");
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="enabledSwitch">Enable Test Emails</Label>
            <p className="text-sm text-muted-foreground">
              Allow specific test emails to be sent in development environment
            </p>
          </div>
          <Switch
            id="enabledSwitch"
            checked={config.enabled}
            onCheckedChange={(checked) => updateConfig("enabled", checked)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="addresses">Test Email Addresses</Label>
          <p className="text-sm text-muted-foreground">
            Enter one email address per line. These addresses will receive actual emails in development.
          </p>
          <Textarea
            id="addresses"
            placeholder="<EMAIL>"
            rows={3}
            value={Array.isArray(config.addresses) ? arrayToString(config.addresses) : config.addresses || ""}
            onChange={(e) => updateConfig("addresses", e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="domains">Test Email Domains</Label>
          <p className="text-sm text-muted-foreground">
            Enter one domain per line (without @). All addresses with these domains will receive actual emails.
          </p>
          <Textarea
            id="domains"
            placeholder="example.com"
            rows={3}
            value={Array.isArray(config.domains) ? arrayToString(config.domains) : config.domains || ""}
            onChange={(e) => updateConfig("domains", e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="streetPatterns">Street Name Patterns</Label>
          <p className="text-sm text-muted-foreground">
            Enter one street name pattern per line. Users with these street names will receive actual emails.
          </p>
          <Textarea
            id="streetPatterns"
            placeholder="Debreceno"
            rows={3}
            value={Array.isArray(config.streetPatterns) ? arrayToString(config.streetPatterns) : config.streetPatterns || ""}
            onChange={(e) => updateConfig("streetPatterns", e.target.value)}
          />
        </div>
      </div>

      <Button type="submit" disabled={isSubmitting}>
        {isSubmitting ? "Saving..." : "Save Settings"}
      </Button>
    </form>
  );
} 