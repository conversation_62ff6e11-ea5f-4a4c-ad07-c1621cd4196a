import { redirect } from "next/navigation";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { getTestEmailConfig } from "@/lib/email-utils";
import { getCurrentUser } from "@/lib/supabase/auth";
import TestEmailForm from "./components/TestEmailForm";

// Mark route as dynamic to prevent build-time static rendering which causes issues with headers
export const dynamic = 'force-dynamic';

export const metadata = {
  title: "Test Email Settings - DNSB Admin",
};

export default async function TestEmailSettingsPage() {
  // Check authentication and permissions
  const user = await getCurrentUser();
  
  if (!user || !["developer", "super_admin", "admin"].includes(user.role as string)) {
    redirect("/dashboard");
  }
  
  // Get current test email configuration
  const testEmailConfig = await getTestEmailConfig();
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Test Email Settings</h1>
      
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Test Email Configuration</CardTitle>
          <CardDescription>
            Configure which email addresses should receive real emails in the development environment.
            This is useful for testing email functionality with specific test users.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <TestEmailForm initialConfig={testEmailConfig} />
        </CardContent>
      </Card>
    </div>
  );
} 