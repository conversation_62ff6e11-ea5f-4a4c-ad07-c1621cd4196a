import { Metadata } from "next";
import { getSettings, getEnvironmentInfo, getEmailSettings } from "@/lib/settings";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { SmtpSettings } from "@/components/settings/smtp-settings";
import { PhoneSettings } from "@/components/settings/phone-settings";
import { Breadcrumb } from "@/components/ui/breadcrumb";

// Mark route as dynamic to prevent build-time static rendering which causes issues with headers
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: "Nustatymai | DNSB Vakarai",
  description: "Sistemos nustatymų valdymas",
};

export default async function SettingsPage() {
  // Get settings from the database
  const emailSettings = await getEmailSettings();
  const phoneSettings = await getSettings('phone');
  const environmentInfo = getEnvironmentInfo();
  
  return (
    <div className="container pb-10">
      <div className="mb-6">
        <Breadcrumb 
          items={[
            { href: "/dashboard", label: "Valdymo skydelis" },
            { href: "/dashboard/admin", label: "Administravimas" },
            { href: "/dashboard/admin/settings", label: "Nustatymai", isLast: true }
          ]}
        />
      </div>
      
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-gradient-to-r from-indigo-50 to-white p-6 rounded-lg border border-indigo-100 shadow-sm mb-6">
        <h1 className="text-3xl font-bold text-indigo-900 tracking-tight">Sistemos nustatymai</h1>
        <div className="text-sm text-indigo-700">
          Valdykite sistemos konfigūraciją
        </div>
      </div>
      
      <Tabs defaultValue="phones" className="space-y-6">
        <TabsList className="bg-indigo-50 p-1 mb-6">
          <TabsTrigger value="phones" className="data-[state=active]:bg-white data-[state=active]:text-indigo-900">
            Telefono numeriai
          </TabsTrigger>
          <TabsTrigger value="email" className="data-[state=active]:bg-white data-[state=active]:text-indigo-900">
            El. pašto konfigūracija
          </TabsTrigger>
          <TabsTrigger value="debug" className="data-[state=active]:bg-white data-[state=active]:text-indigo-900">
            Diagnostika
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="phones" className="space-y-6">
          <PhoneSettings settings={phoneSettings} />
        </TabsContent>
        
        <TabsContent value="email" className="space-y-6">
          <SmtpSettings 
            settings={emailSettings} 
            environmentInfo={environmentInfo} 
          />
        </TabsContent>
        
        <TabsContent value="debug" className="space-y-6">
          <div className="bg-white p-6 rounded-lg border shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Diagnostika</h2>
            <p className="mb-4">Peržiūrėkite visus sistemos nustatymus diagnostikos skydelyje.</p>
            <a 
              href="/dashboard/admin/settings/debug" 
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            >
              Atidaryti diagnostikos skydelį
            </a>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 