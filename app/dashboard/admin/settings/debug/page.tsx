import { redirect } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { getCurrentUser } from "@/lib/supabase/auth";
import { getSettings } from "@/lib/settings";
import { Badge } from "@/components/ui/badge";

// Mark route as dynamic to prevent build-time static rendering which causes issues with headers
export const dynamic = 'force-dynamic';

export const metadata = {
  title: "Debug Settings - DNSB Admin",
};

export default async function DebugSettingsPage() {
  // Check authentication and permissions
  const user = await getCurrentUser();
  
  if (!user || !["developer", "super_admin", "admin"].includes(user.role as string)) {
    redirect("/dashboard");
  }
  
  // Get all settings
  const allSettings = await getSettings();
  
  // Group settings by key prefix
  const groupedSettings: Record<string, any[]> = {};
  
  allSettings.forEach(setting => {
    const prefix = setting.key.split('_')[0] || 'other';
    if (!groupedSettings[prefix]) {
      groupedSettings[prefix] = [];
    }
    groupedSettings[prefix].push(setting);
  });
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Debug Settings</h1>
      
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>All System Settings</CardTitle>
          <CardDescription>
            This page shows all settings in the database for debugging purposes.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {Object.entries(groupedSettings).map(([group, settings]) => (
            <div key={group} className="mb-8">
              <h2 className="text-xl font-semibold mb-3 capitalize">{group} Settings</h2>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Key
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Value
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {settings.map((setting) => (
                      <tr key={setting.key}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {setting.key}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">
                          {setting.key.includes('password') 
                            ? (setting.value ? '••••••••' : '[empty]')
                            : (typeof setting.value === 'string' && setting.value.length > 100 
                              ? `${setting.value.substring(0, 100)}...` 
                              : setting.value || '[empty]')}
                        </td>
                        <td className="px-6 py-4 text-sm">
                          {!setting.value || setting.value === '' ? (
                            <Badge variant="destructive">Missing</Badge>
                          ) : (
                            <Badge variant="success" className="bg-green-100 text-green-800">Set</Badge>
                          )}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">
                          {setting.description || '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ))}
          
          {allSettings.length === 0 && (
            <div className="text-center text-gray-500 py-8">
              No settings found in the database.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 