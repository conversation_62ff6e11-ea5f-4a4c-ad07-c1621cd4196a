import { <PERSON>ada<PERSON> } from "next";
import { redirect } from "next/navigation";
import Link from "next/link";
import { getCurrentUser } from "@/lib/supabase/auth";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, UserPlus } from "lucide-react";
import { UserForm } from "@/components/users/user-form";

export const metadata: Metadata = {
  title: "Naujo vartotojo kūrimas | DNSB Vakarai",
  description: "Sukurkite naują sistemos vartotoją",
};

export default async function NewUserPage() {
  const user = await getCurrentUser();
  
  // Check if user is authorized to add users
  if (!user || !["developer", "super_admin"].includes(user.role)) {
    redirect("/dashboard");
  }
  
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Link href="/dashboard/admin/users" className="text-blue-600 hover:text-blue-800 flex items-center mb-4">
          <ArrowLeft className="h-4 w-4 mr-1" />
          Grįžti į vartotojų sąrašą
        </Link>
        
        <h1 className="text-3xl font-bold flex items-center">
          <UserPlus className="mr-2 h-6 w-6" />
          Naujas vartotojas
        </h1>
        <p className="text-muted-foreground mt-1">
          Sukurkite naują vartotoją sistemoje
        </p>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Vartotojo informacija</CardTitle>
        </CardHeader>
        <CardContent>
          <UserForm />
        </CardContent>
      </Card>
    </div>
  );
} 