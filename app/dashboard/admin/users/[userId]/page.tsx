import { Metadata } from "next";
import { redirect } from "next/navigation";
import Link from "next/link";
import { getCurrentUser } from "@/lib/supabase/auth";
import { formatDateTime } from "@/lib/utils";
import { ArrowLeft, Calendar, Edit, Mail, Phone, Tag, Trash2, User, UserCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { db } from "@/lib/db/supabase-adapter";

export const metadata: Metadata = {
  title: "Vartotojo informacija | DNSB Vakarai",
  description: "Vartotojo informacija ir duomenys",
};

// In Next.js 15, params is a promise that must be awaited
interface PageProps {
  params: Promise<{ userId: string }>;
}

export default async function UserPage({ params }: PageProps) {
  const user = await getCurrentUser();

  // Check if user is authenticated and has necessary permissions
  if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
    redirect("/dashboard");
  }

  // Await the params to get the userId using the new async pattern
  const { userId } = await params;

  // Fetch the user data directly from the database instead of using API
  try {
    // Direct database query to get user data
    const userResult = await db.query(
      `SELECT 
        u.id, u.username, u.name, u.email, u.role, 
        u.created_at, u.updated_at, u.is_profile_updated,
        u.phone, u.flat_id, u.last_login
      FROM users u
      WHERE u.id = $1`,
      [userId]
    );
    
    if (userResult.rows.length === 0) {
      console.error("User not found:", userId);
      redirect("/dashboard/admin/users");
    }

    // Get the user data
    const userData = userResult.rows[0];
    
    // Debug: Log the user data structure
    console.log("User data from database:", JSON.stringify(userData, null, 2));
    
    // The nested data is already included in the query result from Supabase
    // Extract address information from the nested structure
    const user = {
      ...userData,
      street: userData.flat?.house?.street?.name || "",
      house_number: userData.flat?.house?.name || "",
      flat_number: userData.flat?.number || "",
      address: userData.flat?.house?.address || 
        (userData.flat?.house?.street && userData.flat?.house ? 
         `${userData.flat.house.street.name} ${userData.flat.house.name}, ${userData.flat.house.street.city}` : "")
    };
    
    // Debug: Log the processed user object
    console.log("Processed user object:", JSON.stringify(user, null, 2));
    
    const canEdit = user.role === "super_admin";

    function getRoleBadge(role: string) {
      switch (role) {
        case "developer":
          return (
            <Badge variant="destructive" className="bg-purple-600">
              Programuotojas
            </Badge>
          );
        case "super_admin":
          return (
            <Badge variant="destructive">
              Super Administratorius
            </Badge>
          );
        case "editor":
          return (
            <Badge variant="default">
              Redaktorius
            </Badge>
          );
        case "user":
        default:
          return (
            <Badge variant="outline">
              Vartotojas
            </Badge>
          );
      }
    }

    return (
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <Link href="/dashboard/admin/users">
            <Button variant="outline" size="sm" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Grįžti į vartotojų sąrašą
            </Button>
          </Link>
          
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">{user.name}</h1>
            
            <div className="flex gap-2">
              {canEdit && (
                <>
                  <Link href={`/dashboard/admin/users/${userId}/edit`}>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      Redaguoti
                    </Button>
                  </Link>
                  
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" size="sm">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Ištrinti
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Ar tikrai norite ištrinti šį vartotoją?</AlertDialogTitle>
                        <AlertDialogDescription>
                          Ši operacija negrįžtama. Vartotojo duomenys bus ištrinti visam laikui.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Atšaukti</AlertDialogCancel>
                        <form action={async () => {
                          'use server'
                          try {
                            // Delete user directly
                            await db.query('DELETE FROM users WHERE id = $1', [userId]);
                            redirect("/dashboard/admin/users");
                          } catch (error) {
                            console.error("Failed to delete user:", error);
                          }
                        }}>
                          <AlertDialogAction type="submit">
                            Ištrinti
                          </AlertDialogAction>
                        </form>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </>
              )}
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <UserCircle className="h-5 w-5 mr-2" />
                  <span>Pagrindinė informacija</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="grid gap-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Prisijungimo vardas</h3>
                    <p className="flex items-center">
                      <Tag className="h-4 w-4 mr-2 text-muted-foreground" />
                      {user.username}
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Vardas ir pavardė</h3>
                    <p className="flex items-center">
                      <User className="h-4 w-4 mr-2 text-muted-foreground" />
                      {user.name}
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">El. paštas</h3>
                    <p className="flex items-center">
                      <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                      {user.email}
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Telefono numeris</h3>
                    <p className="flex items-center">
                      <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                      {user.phone || "Nenurodytas"}
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Rolė</h3>
                    <div>
                      {getRoleBadge(user.role)}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Profilis atnaujintas</h3>
                    <Badge variant={user.is_profile_updated ? "default" : "outline"}>
                      {user.is_profile_updated ? "Taip" : "Ne"}
                    </Badge>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-3">Adresas</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h4 className="text-xs text-muted-foreground">Gatvė</h4>
                      <p>{user.street || "Nenurodyta"}</p>
                    </div>
                    <div>
                      <h4 className="text-xs text-muted-foreground">Namo nr.</h4>
                      <p>{user.house_number || "Nenurodytas"}</p>
                    </div>
                    <div>
                      <h4 className="text-xs text-muted-foreground">Buto nr.</h4>
                      <p>{user.flat_number || "Nenurodytas"}</p>
                    </div>
                  </div>
                </div>
                
                {user.flat && user.flat.house && (
                  <>
                    <Separator />
                    
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-3">Buto informacija</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="text-xs text-muted-foreground">Namas</h4>
                          <p>{user.flat.house.name || "Nenurodytas"}</p>
                        </div>
                        <div>
                          <h4 className="text-xs text-muted-foreground">Buto numeris</h4>
                          <p>{user.flat.number || "Nenurodytas"}</p>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
          
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  <span>Paskutinė aktivumas</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Sukurta</h3>
                  <p>{user.created_at ? formatDateTime(user.created_at) : "Nenurodyta"}</p>
                </div>
                
                {user.updated_at && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Atnaujinta</h3>
                    <p>{formatDateTime(user.updated_at)}</p>
                  </div>
                )}
                
                {user.last_login && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Paskutinis prisijungimas</h3>
                    <p>{formatDateTime(user.last_login)}</p>
                  </div>
                )}
                
                {user.email_verified && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">El. paštas patvirtintas</h3>
                    <p>{formatDateTime(user.email_verified)}</p>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-end">
                {canEdit && (
                  <Link href={`/dashboard/admin/users/${userId}/activity`}>
                    <Button variant="outline" size="sm">
                      Peržiūrėti aktyvumą
                    </Button>
                  </Link>
                )}
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error fetching user:", error);
    redirect("/dashboard/admin/users");
  }
} 