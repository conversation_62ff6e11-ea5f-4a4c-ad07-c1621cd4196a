import { <PERSON>ada<PERSON> } from "next";
import { redirect } from "next/navigation";
import Link from "next/link";
import { getCurrentUser } from "@/lib/supabase/auth";
import { ArrowLeft, UserCog } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { UserForm } from "@/components/users/user-form";
import { db } from "@/lib/db/supabase-adapter";

export const metadata: Metadata = {
  title: "Redaguoti vartotoją | DNSB Vakarai",
  description: "Redaguoti vartotojo informaciją",
};

// In Next.js 15, params is a promise that must be awaited
interface PageProps {
  params: Promise<{ userId: string }>;
}

export default async function EditUserPage({ params }: PageProps) {
  const user = await getCurrentUser();

  // Check if user is authenticated and has necessary permissions
  if (!user || !["developer", "super_admin"].includes(user.role as string)) {
    redirect("/dashboard");
  }

  // Await the params to get the userId using the new async pattern
  const { userId } = await params;

  // Fetch user data directly from the database instead of using API
  try {
    // Query the database for the user info
    const userResult = await db.query(
      `SELECT 
        id, username, name, email, role, is_profile_updated,
        flat_id, phone, is_test_user
      FROM users 
      WHERE id = $1`,
      [userId]
    );
    
    if (userResult.rows.length === 0) {
      console.error("User not found:", userId);
      redirect("/dashboard/admin/users");
    }

    const user = userResult.rows[0];
    
    // If the user has a flat, get the flat info with house and street
    let flatInfo = null;
    if (user.flat_id) {
      const flatResult = await db.query(
        `SELECT 
          f.id as flat_id, 
          f.number as flat_number, 
          h.id as house_id, 
          h.name as house_number,
          h.street_id as house_street_id,
          s.id as street_id,
          s.name as street_name
        FROM flats f
        LEFT JOIN houses h ON f.house_id = h.id
        LEFT JOIN streets s ON h.street_id = s.id
        WHERE f.id = $1`,
        [user.flat_id]
      );
      
      console.log(`[EditUserPage] Flat query result for user ${userId} with flat_id ${user.flat_id}:`, flatResult.rows);
      
      if (flatResult.rows.length > 0) {
        flatInfo = flatResult.rows[0];
        console.log(`[EditUserPage] Final flatInfo:`, flatInfo);
      }
    }
    
    // Combine user data with flat info
    const userData = {
      id: user.id,
      username: user.username,
      name: user.name,
      email: user.email,
      role: user.role,
      phone: user.phone,
      is_profile_updated: user.is_profile_updated,
      is_test_user: user.is_test_user,
      flat_id: user.flat_id?.toString() || null,
      house_id: flatInfo?.house_id?.toString() || null,
      street_id: flatInfo?.street_id?.toString() || null,
      street: flatInfo?.street_name || "",
      house_number: flatInfo?.house_number || "",
      flat_number: flatInfo?.flat_number || "",
    };

    return (
      <div className="container mx-auto py-10">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-6">
            <Button variant="outline" asChild>
              <Link href="/dashboard/admin/users">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Atgal į vartotojų sąrašą
              </Link>
            </Button>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <UserCog className="h-8 w-8" />
              Redaguoti vartotoją
            </h1>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Vartotojo informacija</CardTitle>
          </CardHeader>
          <CardContent>
            <UserForm user={userData} />
          </CardContent>
        </Card>
      </div>
    );
  } catch (error) {
    console.error("Error fetching user for edit:", error);
    // In case of error, return a simple error message
    return (
      <div className="container mx-auto py-10">
        <h1 className="text-3xl font-bold tracking-tight mb-6">Klaida</h1>
        <p className="text-red-500">Nepavyko užkrauti vartotojo duomenų. Bandykite dar kartą vėliau.</p>
        <Button variant="outline" asChild className="mt-4">
          <Link href="/dashboard/admin/users">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Grįžti į vartotojų sąrašą
          </Link>
        </Button>
      </div>
    );
  }
} 