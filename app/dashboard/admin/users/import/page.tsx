import { Metada<PERSON> } from "next";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import UserImportForm from "@/components/users/user-import-form";
import logger from "@/lib/logger";

export const metadata: Metadata = {
  title: "Vartotojų importas | DNSB Vakarai",
  description: "Importuokite vartotojus iš Excel failo",
};

export default async function UserImportPage() {
  const user = await getCurrentUser();
  const isDevelopment = process.env.NODE_ENV === "development";
  
  // Check if user is authorized to import users
  // Only super_admin in development mode or developers in any mode can access
  const isAuthorized = 
    (user?.role === "developer") || 
    (isDevelopment && user?.role === "super_admin");
  
  if (!user || !isAuthorized) {
    logger.info({
      userId: user?.id,
      action: "access_denied",
      resource: "user_import"
    }, "Unauthorized access attempt to user import page");
    redirect("/dashboard");
  }
  
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Vartotojų importas (Dev Only)</h1>
        <p className="text-muted-foreground mt-2">
          Ši funkcija yra prieinama tik programuotojams. Importuokite vartotojus iš Excel (.xlsx) failo
        </p>
        
        {isDevelopment && (
          <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 my-4">
            <p>⚠️ Ši funkcija veikia tik vystymo aplinkoje ir skirta tik programuotojams!</p>
          </div>
        )}
      </div>
      
      <UserImportForm />
    </div>
  );
} 