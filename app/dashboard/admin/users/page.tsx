import { <PERSON>ada<PERSON> } from "next";
import { redirect } from "next/navigation";
import Link from "next/link";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { PlusCircle, UserCog, Download } from "lucide-react";
import { UserList } from "@/components/users/user-list";

export const metadata: Metadata = {
  title: "Vartotojų valdymas | DNSB Vakarai",
  description: "Peržiūrėkite ir valdykite sistemos vartotojus",
};

export default async function UsersPage() {
  const user = await getCurrentUser();
  
  // Check if user is authorized to manage users
  if (!user || !["developer", "super_admin"].includes(user.role)) {
    redirect("/dashboard");
  }
  
  // Fetch users from Supabase
  let users = [];
  try {
    const supabase = await createServiceClient();
    
    // Fetch users with related data
    const { data, error } = await supabase
      .from('users')
      .select(`
        id,
        username,
        name,
        email,
        role,
        is_profile_updated,
        phone,
        created_at,
        last_login,
        is_test_user,
        flat_id,
        flats!users_flat_id_fkey (
          id,
          number,
          floor,
          house_id,
          houses!flats_house_id_fkey (
            id,
            name,
            address,
            street_id
          )
        )
      `)
      .order('role')
      .order('name');
    
    if (error) {
      console.error('Failed to fetch users:', error);
    } else {
      // Transform the data to match the expected format
      users = (data || []).map(user => ({
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        role: user.role,
        is_profile_updated: user.is_profile_updated,
        phone: user.phone,
        created_at: user.created_at,
        last_login: user.last_login,
        is_test_user: user.is_test_user,
        house_name: user.flats?.houses?.name,
        flat_number_db: user.flats?.number,
        flat_number: user.flats?.number, // Add this for compatibility
        street_name: null, // Streets table may not exist yet
      }));
    }
  } catch (error) {
    console.error('Failed to fetch users:', error);
  }
  
  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold flex items-center">
            <UserCog className="mr-2 h-6 w-6" />
            Vartotojų valdymas
          </h1>
          <p className="text-muted-foreground mt-1">
            Peržiūrėkite, redaguokite ir pridėkite sistemos vartotojus
          </p>
        </div>
        
        <div className="flex gap-2">
          <Link href="/dashboard/admin/users/import">
            <Button variant="outline" className="flex items-center gap-1">
              <Download className="h-4 w-4" />
              Importuoti
            </Button>
          </Link>
          
          <Link href="/dashboard/admin/users/new">
            <Button className="flex items-center gap-1">
              <PlusCircle className="h-4 w-4" />
              Naujas vartotojas
            </Button>
          </Link>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Vartotojų sąrašas</CardTitle>
        </CardHeader>
        <CardContent>
          <UserList users={users} />
        </CardContent>
      </Card>
    </div>
  );
} 