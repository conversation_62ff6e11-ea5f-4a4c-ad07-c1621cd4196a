import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { getCurrentUser } from '@/lib/supabase/auth';
import { db } from '@/lib/db/supabase-adapter';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { format } from 'date-fns';

export const metadata: Metadata = {
  title: 'User Debug - DNSB Vakarai Admin',
  description: 'Debug page for viewing all users in the database',
};

export default async function UserDebugPage() {
  const user = await getCurrentUser();

  // Only super_admin and developer can access this page
  if (!user || !["developer", "super_admin"].includes(user.role)) {
    redirect('/dashboard');
  }

  // Fetch all users from the database
  const result = await db.query(`
    SELECT 
      u.id, 
      u.username, 
      u.name, 
      u.email, 
      u.role, 
      u.is_profile_updated,
      u.created_at,
      u.last_login,
      h.name as house_name,
      f.number as flat_number
    FROM 
      users u
    LEFT JOIN
      flats f ON u.flat_id = f.id
    LEFT JOIN
      houses h ON f.house_id = h.id
    ORDER BY 
      u.role, u.name
  `);

  const users = result.rows;

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-4">User Database Debug</h1>
      <p className="mb-6 text-gray-500">
        This page displays all users currently in the database.
      </p>
      
      <Card>
        <CardHeader>
          <CardTitle>All Users ({users.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableCaption>A list of all users in the database</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Username</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Profile Updated</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead>House</TableHead>
                <TableHead>Flat</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.id}</TableCell>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                      user.role === 'super_admin' 
                        ? 'bg-red-100 text-red-800' 
                        : user.role === 'admin' 
                        ? 'bg-blue-100 text-blue-800' 
                        : user.role === 'editor' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {user.role}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                      user.is_profile_updated 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {user.is_profile_updated ? 'Yes' : 'No'}
                    </span>
                  </TableCell>
                  <TableCell>
                    {user.created_at ? format(new Date(user.created_at), 'yyyy-MM-dd HH:mm') : '-'}
                  </TableCell>
                  <TableCell>
                    {user.last_login ? format(new Date(user.last_login), 'yyyy-MM-dd HH:mm') : '-'}
                  </TableCell>
                  <TableCell>{user.house_name || '-'}</TableCell>
                  <TableCell>{user.flat_number || '-'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
} 