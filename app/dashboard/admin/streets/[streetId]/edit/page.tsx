import { Suspense } from "react";
import { notFound, redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { StreetForm } from "@/components/housing/street-form";
import { CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface PageProps {
  params: Promise<{
    streetId: string;
  }>;
}

async function getStreet(streetId: string) {
  try {
    const supabase = await createServiceClient();
    
    const { data: street, error } = await supabase
      .from('streets')
      .select('*')
      .eq('id', parseInt(streetId))
      .single();

    if (error || !street) {
      return null;
    }

    return street;
  } catch (error) {
    console.error("Error fetching street data:", error);
    return null;
  }
}

export default async function EditStreetPage({ params }: PageProps) {
  const user = await getCurrentUser();
  
  // Check if user is authorized
  if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
    redirect("/dashboard");
  }
  
  // Next.js 15 requires awaiting params before accessing properties
  const { streetId } = await params;
  
  if (!streetId || isNaN(parseInt(streetId))) {
    return notFound();
  }
  
  const street = await getStreet(streetId);
  
  if (!street) {
    return notFound();
  }
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col gap-y-4">
        <h1 className="text-3xl font-bold">Redaguoti gatvę</h1>
        <p className="text-muted-foreground">
          Redaguokite gatvės informaciją žemiau esančioje formoje.
        </p>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Gatvės duomenys</CardTitle>
          <CardDescription>
            Atnaujinkite gatvės pavadinimą, miestą ir kitą informaciją.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<Skeleton className="h-[300px] w-full" />}>
            <StreetForm
              street={{
                id: street.id.toString(),
                name: street.name,
                city: street.city,
              }}
            />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
} 