import { Suspense } from "react";
import { StreetForm } from "@/components/housing/street-form";
import { CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function NewStreetPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col gap-y-4">
        <h1 className="text-3xl font-bold">Pridėti naują gatvę</h1>
        <p className="text-muted-foreground">
          Užpildykite formą žemiau, kad pridėtumėte naują gatvę.
        </p>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Gatvės duomenys</CardTitle>
          <CardDescription>
            Įveskite gatvės pavadinimą ir miestą.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<Skeleton className="h-[300px] w-full" />}>
            <StreetForm />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
} 