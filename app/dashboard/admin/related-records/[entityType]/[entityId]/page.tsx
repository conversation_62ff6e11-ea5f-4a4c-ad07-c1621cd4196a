import { <PERSON><PERSON><PERSON> } from "next";
import { db } from "@/lib/db/supabase-adapter";
import Link from "next/link";
import { Card } from "@/components/ui/card";
import { EmptyState } from "@/components/ui/empty-state";
import { MailIcon, ArrowLeft } from "lucide-react";
import { EmailTableClient } from "@/components/emails/email-table-client";
import ProcessEmailsButton from "@/components/emails/process-emails-button";
import { Button } from "@/components/ui/button";
import { notFound } from "next/navigation";

// Mark route as dynamic to prevent build-time static rendering which causes issues with headers
export const dynamic = 'force-dynamic';

// Helper to get entity details (e.g., title) - could be expanded
async function getEntityDetails(type: string, id: number): Promise<{ title: string } | null> {
  let query = "";
  let params = [id];

  if (type === 'announcement') {
    query = "SELECT title FROM announcements WHERE id = $1";
  } else if (type === 'poll') {
    query = "SELECT title FROM polls WHERE id = $1";
  } else {
    // Add support for other entity types if needed
    console.warn(`getEntityDetails: Unsupported entity type: ${type}`)
    return null;
  }

  try {
    const result = await db.query(query, params);
    if (result.rows.length === 0) {
      return null;
    }
    return { title: result.rows[0].title };
  } catch (error) {
    console.error(`Error fetching details for ${type} #${id}:`, error);
    return null;
  }
}

// Generate dynamic metadata
export async function generateMetadata({
  params,
}: { 
  params: { entityType: string; entityId: string }
}): Promise<Metadata> {
  const type = params.entityType;
  const id = parseInt(params.entityId, 10);

  if (isNaN(id)) {
    return { title: "Nežinomas Įrašas | DNSB Vakarai" };
  }

  const details = await getEntityDetails(type, id);
  const titlePrefix = details ? `${details.title} - ` : `Įrašas (${type} #${id}) - `;

  return {
    title: `${titlePrefix}El. Pašto Eilė | DNSB Vakarai`,
    description: `El. pašto eilė susijusi su įrašu ${type} #${id}`,
  };
}

// Main page component
export default async function RelatedRecordEmailQueuePage({
  params,
  searchParams,
}: {
  params: { entityType: string; entityId: string };
  searchParams: { status?: string; page?: string; limit?: string };
}) {
  const entityType = params.entityType;
  const entityId = parseInt(params.entityId, 10);
  const page = searchParams?.page ? parseInt(searchParams.page, 10) : 1;
  const limit = searchParams?.limit ? parseInt(searchParams.limit, 10) : 10;
  const statusFilter = searchParams?.status || "all";
  
  // Validate entityId
  if (isNaN(entityId)) {
    console.error("Invalid entityId:", params.entityId);
    notFound(); // Or redirect to an error page
  }

  // Fetch entity details for display
  const entityDetails = await getEntityDetails(entityType, entityId);
  if (!entityDetails && (entityType === 'announcement' || entityType === 'poll')) {
    // Only call notFound if we expect details for known types and didn't get them
    console.warn(`Entity details not found for ${entityType} #${entityId}`);
    // Optional: Show a generic page instead of 404 if you prefer
    // notFound();
  }

  const entityTitle = entityDetails?.title || `${entityType} #${entityId}`;

  // Pagination and status filtering
  const offset = (Math.max(1, page) - 1) * limit;

  // Build the query based on entity and filters
  let whereClauses: string[] = [
    // Match based on specific ID columns OR generic entity_type/entity_id
    `(
      (email_queue.announcement_id = $1 AND $2 = 'announcement') OR
      (email_queue.poll_id = $1 AND $2 = 'poll') OR
      (email_queue.entity_id = $1 AND email_queue.entity_type = $2)
     )`, 
    `email_queue.is_archived = FALSE`
  ];
  let queryParams: any[] = [entityId, entityType];
  let paramIndex = 3; // Start parameter index after entityId and entityType

  if (statusFilter && statusFilter !== "all") {
    whereClauses.push(`email_queue.status = $${paramIndex}`);
    queryParams.push(statusFilter);
    paramIndex++;
  }

  const whereCondition = whereClauses.join(" AND ");

  // Get emails with pagination for this specific entity
  const emailsQuery = `
    SELECT *, announcement_id, poll_id 
    FROM email_queue 
    WHERE ${whereCondition} 
    ORDER BY 
      CASE 
        WHEN status = 'pending' THEN 1 
        WHEN status = 'processing' THEN 2
        WHEN status = 'failed' THEN 3 
        ELSE 4 
      END,
      COALESCE(scheduled_for, created_at) DESC
    LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
  `;
  queryParams.push(limit, offset);

  const emailsResult = await db.query(emailsQuery, queryParams);

  // Get total count for pagination for this specific entity
  const countQueryParams = queryParams.slice(0, -2); // Remove limit and offset for count
  const countResult = await db.query(
    `SELECT COUNT(*) as total FROM email_queue WHERE ${whereCondition}`,
    countQueryParams
  );

  const total = parseInt(countResult.rows[0].total, 10);
  const totalPages = Math.ceil(total / limit);

  // Get stats for this specific entity
  const statsQuery = `
    SELECT 
      status, 
      COUNT(*) as count
    FROM email_queue
    WHERE ${whereCondition}
    GROUP BY status
  `;
  const statsResult = await db.query(statsQuery, countQueryParams);

  // Calculate stats including total (only for non-archived)
  const initialStats = { 
    pending: 0, 
    processing: 0,
    sent: 0, 
    failed: 0, 
    cancelled: 0, 
    dev_skipped: 0,
    total: total // Total non-archived for this entity
  };
  const stats = statsResult.rows.reduce((acc, row) => {
    if (acc.hasOwnProperty(row.status) && row.status !== 'archived') { // Ensure archived are excluded
      acc[row.status] = parseInt(row.count, 10);
    }
    return acc;
  }, initialStats);

  // Status labels (same as before)
  const statusLabels: Record<string, string> = {
    pending: "Laukiantys",
    processing: "Apdorojami",
    sent: "Išsiųsti",
    failed: "Nepavykę",
    cancelled: "Atšaukti",
    dev_skipped: "Praleista (DEV)",
  };

  // Format the data for the table (same as before)
  const emails = emailsResult.rows.map((email: any) => ({
    id: email.id,
    type: email.email_type,
    recipient: email.recipient,
    subject: email.subject,
    status: email.status,
    priority: email.priority,
    attempts: email.attempts,
    scheduled_for: email.scheduled_for,
    sent_at: email.sent_at,
    created_at: email.created_at,
    entity_type: email.entity_type, // Still needed for actions
    entity_id: email.entity_id,     // Still needed for actions
    announcement_id: email.announcement_id, // Still needed for actions
    poll_id: email.poll_id,           // Still needed for actions
    error: email.error_message,
    emailData: {
      id: email.id,
      status: email.status,
      entity_type: email.entity_type,
      entity_id: email.entity_id,
      announcement_id: email.announcement_id,
      poll_id: email.poll_id,
      error_message: email.error_message,
      // Add archive action trigger here later
    }
  }));

  // Define columns - REMOVED "source" column
  const columns = [
    { accessorKey: "id", header: "ID" },
    { accessorKey: "type", header: "Tipas", cell: "type-badge" },
    { accessorKey: "recipient", header: "Gavėjas" },
    { accessorKey: "subject", header: "Tema" },
    { accessorKey: "status", header: "Būsena", cell: "status-badge" },
    { accessorKey: "priority", header: "Prioritetas", cell: "priority-badge" },
    { accessorKey: "attempts", header: "Bandymai" },
    { accessorKey: "scheduled_for", header: "Suplanuotas", cell: "scheduled-for-format" },
    { accessorKey: "created_at", header: "Sukurtas", cell: "created-at-format" },
    {
      accessorKey: "actions",
      header: "Veiksmai",
      // Ensure email-actions component knows how to handle archive action
      cell: "email-actions" 
    },
  ];

  // Base URL for status filter links on this page
  const baseUrl = `/dashboard/admin/related-records/${entityType}/${entityId}`;

  return (
    <div className="container py-6 space-y-6">
      {/* Back Button and Title */}
      <div className="flex items-center justify-between gap-4">
        <Link href="/dashboard/admin/related-records" className="inline-block">
          <Button variant="outline" size="sm" className="h-8 px-3">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Grįžti į sąrašą
          </Button>
        </Link>
         <div className="flex-grow text-right">
           {/* Optional: Add ProcessEmailsButton if relevant per-entity */}
           {/* <ProcessEmailsButton entityType={entityType} entityId={entityId} /> */} 
         </div>
      </div>
      
      <div className="flex flex-col gap-1">
        <div className="flex items-center gap-2">
          <MailIcon className="h-5 w-5 text-muted-foreground" />
          <h1 className="text-2xl font-semibold tracking-tight">El. Pašto Eilė: {entityTitle}</h1>
        </div>
        <p className="text-sm text-muted-foreground">
          Laiškai susiję su šiuo įrašu (neįtraukiant archyvuotų).
        </p>
      </div>

      {/* Stats Card - Simplified as it's per-entity now */}
       <Card className="p-4 bg-card shadow-sm border">
        <h3 className="text-lg font-semibold mb-3">Eilės statistika (šiam įrašui)</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold">{stats.total}</div>
            <div className="text-sm text-muted-foreground">Iš viso (nearchyvuotų)</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-600">{stats.pending}</div>
            <div className="text-sm text-muted-foreground">Laukiantys</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">{stats.sent}</div>
            <div className="text-sm text-muted-foreground">Išsiųsti</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
            <div className="text-sm text-muted-foreground">Nepavykę</div>
          </div>
        </div>
        {/* Progress bar might be less relevant here, but kept for consistency */}
        <div className="mt-4">
          <label className="text-sm font-medium text-muted-foreground">Apdorojimo progresas:</label>
          <div className="w-full bg-muted rounded-full h-2.5 mt-1">
            <div 
              className="bg-gradient-to-r from-blue-500 to-green-500 h-2.5 rounded-full"
              style={{ width: `${stats.total > 0 ? ((stats.sent + stats.failed + stats.cancelled) / stats.total * 100) : 0}%` }}
            ></div>
          </div>
          <div className="text-xs text-muted-foreground mt-1 text-right">
            {stats.sent + stats.failed + stats.cancelled} / {stats.total} apdorota
          </div>
        </div>
      </Card>

      {/* Status Filter Buttons */} 
      <div className="flex flex-wrap items-center gap-2">
        <Link href={baseUrl} className="inline-block">
          <Button
            variant={statusFilter === "all" ? "default" : "outline"}
            size="sm" className="h-8 px-3"
          >
            Visi ({total})
          </Button>
        </Link>
        {Object.entries(statusLabels).map(([key, label]) => (
          stats[key] > 0 && (
            <Link href={`${baseUrl}?status=${key}`} key={key} className="inline-block">
              <Button
                variant={statusFilter === key ? "default" : "outline"}
                size="sm" className="h-8 px-3"
              >
                {label} ({stats[key] || 0})
              </Button>
            </Link>
          )
        ))}
      </div>

      {/* Email Table */} 
      {emails.length === 0 ? (
        <EmptyState
          title="Nerasta laiškų šiam įrašui"
          description="Pagal jūsų nurodytus filtrus nerasta jokių laiškų, susijusių su šiuo įrašu."
          icon={<MailIcon className="h-10 w-10 text-muted-foreground" />}
        />
      ) : (
        <Card>
          <EmailTableClient 
            emails={emails}
            columns={columns} // Pass columns without the source link
            pageIndex={page - 1}
            pageSize={limit}
            totalPages={totalPages}
            stats={stats} // Pass stats specific to this entity
          />
        </Card>
      )}
    </div>
  );
} 