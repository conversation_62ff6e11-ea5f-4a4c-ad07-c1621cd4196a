import { Metadata } from "next";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { EmptyState } from "@/components/ui/empty-state";
import { MailIcon, ListChecks, ArchiveIcon } from "lucide-react";
import ProcessEmailsButton from "@/components/emails/process-emails-button";

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ArchivedEmailList from "@/components/emails/archived-email-list";

// Mark route as dynamic to prevent build-time static rendering which causes issues with headers
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: "Susij<PERSON> Įrašai (El. Paš<PERSON>) | DNSB Vakarai",
  description: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> įrašus, su kuriais susiję laukiantys el. laiškai.",
};

// Type for related records
interface RelatedRecord {
  type: 'announcement' | 'poll' | string; // Allow other types
  id: number;
  title: string; 
  pendingEmailCount: number;
}

async function getRelatedRecordsWithPendingEmails(): Promise<RelatedRecord[]> {
  // This query fetches distinct related entities (announcements, polls, etc.) 
  // that have associated emails which are not 'archived', 'sent', or 'cancelled'.
  // It joins with the respective entity tables to get titles and counts the relevant emails.
  try {
    const query = `
      WITH RelevantEmails AS (
        SELECT
          COALESCE(announcement_id, poll_id, entity_id) AS record_id,
          CASE
            WHEN announcement_id IS NOT NULL THEN 'announcement'
            WHEN poll_id IS NOT NULL THEN 'poll'
            WHEN entity_type IS NOT NULL THEN entity_type
            ELSE NULL
          END AS record_type,
          status
        FROM email_queue
        WHERE is_archived = FALSE -- Filter by is_archived = FALSE
          AND status NOT IN ('sent', 'cancelled') -- Still only count pending/processing/failed/dev_skipped for the count
          AND (
            announcement_id IS NOT NULL OR 
            poll_id IS NOT NULL OR 
            (entity_type IS NOT NULL AND entity_id IS NOT NULL)
          )
      )
      SELECT
        re.record_id,
        re.record_type,
        COUNT(*) AS email_count,
        CASE
          WHEN re.record_type = 'announcement' THEN a.title
          WHEN re.record_type = 'poll' THEN p.title
          -- Add cases for other entity types if needed, fetching their titles
          -- WHEN re.record_type = 'feedback' THEN f.title
          -- WHEN re.record_type = 'contact' THEN c.subject 
          ELSE 'Nežinomas Įrašas'
        END AS record_title
      FROM RelevantEmails re
      LEFT JOIN announcements a ON re.record_type = 'announcement' AND re.record_id = a.id
      LEFT JOIN polls p ON re.record_type = 'poll' AND re.record_id = p.id
      -- Add LEFT JOINs for other entity types if needed
      -- LEFT JOIN feedback f ON re.record_type = 'feedback' AND re.record_id = f.id
      -- LEFT JOIN contact_messages c ON re.record_type = 'contact' AND re.record_id = c.id
      WHERE re.record_type IS NOT NULL AND re.record_id IS NOT NULL
      GROUP BY re.record_id, re.record_type, record_title
      ORDER BY re.record_type, re.record_id DESC;
    `;

    const result = await db.query(query);

    return result.rows.map((row: any) => ({
      type: row.record_type as string,
      id: parseInt(row.record_id, 10),
      // Provide a fallback title if the JOIN didn't find one
      title: row.record_title || `${row.record_type === 'announcement' ? 'Pranešimas' : row.record_type === 'poll' ? 'Apklausa' : 'Įrašas'} #${row.record_id}`, 
      pendingEmailCount: parseInt(row.email_count, 10),
    }));

  } catch (error) {
    console.error("Error fetching related records:", error);
    // Consider logging this error to a monitoring service
    return []; // Return empty on error to prevent page crash
  }
}

export default async function RelatedRecordsPage({ searchParams }: { searchParams: any }) {
  // Check authentication first
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/auth/login");
  }
  
  if (!["developer", "super_admin", "editor"].includes(user.role as string)) {
    redirect("/dashboard");
  }
  
  // Fetch data for the default tab (active records)
  const relatedRecords = await getRelatedRecordsWithPendingEmails();

  const entityTypeLabels: Record<string, string> = {
    announcement: "Pranešimas",
    poll: "Apklausa",
    contact: "Kontaktinis Pranešimas", // Example for other types
    feedback: "Atsiliepimas",         // Example for other types
  };

  const getRecordUrl = (record: RelatedRecord): string => {
     // Construct the URL based on the entity type
     // Ensure these target paths match your actual detail page routes
     // This example uses the structure we plan to create: /related-records/[type]/[id]
     return `/dashboard/admin/related-records/${record.type}/${record.id}`;
  }

  const formatEmailCountText = (count: number): string => {
    if (count === 1) return 'laiškas';
    if (count % 10 === 0 || (count % 100 >= 11 && count % 100 <= 19)) return 'laiškų';
    return 'laiškai';
  };

  return (
    <div className="container py-6 space-y-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <MailIcon className="h-5 w-5 text-muted-foreground" />
            <h1 className="text-2xl font-semibold tracking-tight">El. Pašto Eilė</h1>
          </div>
          <p className="text-sm text-muted-foreground">
            Peržiūrėkite susijusius įrašus su aktyviais laiškais arba archyvuotus laiškus.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <ProcessEmailsButton />
        </div>
      </div>

      <Tabs defaultValue="active">
        <TabsList className="grid w-full grid-cols-2 md:w-[400px]">
          <TabsTrigger value="active">
            <ListChecks className="mr-2 h-4 w-4" /> Aktyvūs Įrašai
          </TabsTrigger>
          <TabsTrigger value="archived">
            <ArchiveIcon className="mr-2 h-4 w-4" /> Archyvuoti Laiškai
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          {relatedRecords.length === 0 ? (
            <EmptyState
              title="Nerasta susijusių įrašų"
              description="Nėra įrašų su laukiančiais, apdorojamais ar nepavykusiais el. laiškais."
              icon={<MailIcon className="h-10 w-10 text-muted-foreground" />}
            />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Aktyvių Įrašų Sąrašas</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {relatedRecords.map((record) => (
                    <li key={`${record.type}-${record.id}`} className="border p-4 rounded-md hover:bg-muted/50 transition-colors">
                      <Link href={getRecordUrl(record)} className="block">
                        <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-2">
                          <div className="flex-grow">
                            <span className="font-medium">{record.title}</span>
                            <span className="text-xs text-muted-foreground ml-2">({entityTypeLabels[record.type] || record.type})</span>
                          </div>
                          <div className="flex items-center gap-3 flex-shrink-0">
                            <span className="text-sm text-muted-foreground whitespace-nowrap">
                              {record.pendingEmailCount} {formatEmailCountText(record.pendingEmailCount)} eilėje
                            </span>
                            <Button variant="outline" size="sm" asChild className="h-8 px-3">
                              <span>Peržiūrėti eilę</span>
                            </Button>
                          </div>
                        </div>
                      </Link>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="archived">
          <ArchivedEmailList searchParams={searchParams} />
        </TabsContent>
      </Tabs>
    </div>
  );
} 