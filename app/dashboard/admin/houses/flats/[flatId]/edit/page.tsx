import { Metadata } from "next";
import { notFound, redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { FlatForm } from "@/components/housing/flat-form";
import { Breadcrumb } from "@/components/ui/breadcrumb";

export const metadata: Metadata = {
  title: "Redaguoti butą | DNSB Vakarai",
  description: "Redaguoti buto informaciją",
};

interface PageProps {
  params: Promise<{
    flatId: string;
  }>;
}

export default async function EditFlatPage({ params }: PageProps) {
  const user = await getCurrentUser();
  
  // Check if user is authorized
  if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
    redirect("/dashboard");
  }
  
  const { flatId } = await params;
  
  if (!flatId) {
    notFound();
  }
  
  try {
    const supabase = await createServiceClient();
    
    // Fetch flat data with house and street information
    const { data: flat, error } = await supabase
      .from('flats')
      .select(`
        id,
        number,
        floor,
        house_id,
        created_at,
        updated_at,
        houses (
          id,
          name,
          street_id,
          streets (
            id,
            name
          )
        )
      `)
      .eq('id', flatId)
      .single();
    
    if (error || !flat) {
      console.error("Error fetching flat:", error);
      notFound();
    }
    
    // Get all houses for the form dropdown
    const { data: houses } = await supabase
      .from('houses')
      .select(`
        id,
        name,
        street_id,
        streets (
          id,
          name
        )
      `)
      .order('name');
    
    // Transform houses data for the form
    const housesOptions = houses?.map(house => {
      const street = house.streets as any;
      return {
        id: house.id.toString(),
        name: house.name,
        displayName: street ? `${street.name} ${house.name}` : house.name
      };
    }) || [];
    
    // Prepare initial data for the form
    const initialData = {
      number: flat.number,
      floor: flat.floor || '',
      houseId: flat.house_id.toString()
    };
    
    const flatDisplay = flat.houses ? 
      `${(flat.houses.streets as any)?.name || ''} ${flat.houses.name}, butas ${flat.number}` : 
      `Butas ${flat.number}`;
    
    return (
      <div className="space-y-6">
        <Breadcrumb 
          items={[
            { href: "/dashboard/admin/houses", label: "Namai ir butai" },
            { href: "/dashboard/admin/houses?tab=flats", label: "Butai" },
            { href: `/dashboard/admin/houses/flats/${flatId}/edit`, label: "Redaguoti butą", isLast: true }
          ]}
          className="mb-4"
        />
        
        <div className="bg-gradient-to-r from-indigo-50 to-white p-6 rounded-lg border border-indigo-100 shadow-sm mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Redaguoti butą</h1>
          <p className="text-gray-600">Redaguojate: {flatDisplay}</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <FlatForm 
            flatId={flatId}
            initialData={initialData}
            houses={housesOptions}
          />
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error in edit flat page:", error);
    notFound();
  }
}