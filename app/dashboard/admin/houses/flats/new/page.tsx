"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { success, error } from "@/components/ui/use-toast"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface House {
  id: string
  name: string
  address: string
  displayName: string
  streetName?: string | null
}

export default function NewFlatPage() {
  const [number, setNumber] = useState("")
  const [floor, setFloor] = useState("")
  const [houseId, setHouseId] = useState<string>("")
  const [houses, setHouses] = useState<House[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const router = useRouter()

  // Fetch houses for the dropdown
  useEffect(() => {
    const fetchHouses = async () => {
      try {
        setIsLoading(true)
        const response = await fetch("/api/houses")
        
        if (!response.ok) {
          throw new Error("Failed to fetch houses")
        }
        
        const data: House[] = await response.json()
        setHouses(data)
      } catch (error) {
        console.error("Error fetching houses:", error)
        error("Nepavyko užkrauti namų sąrašo")
      } finally {
        setIsLoading(false)
      }
    }

    fetchHouses()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!houseId) {
      error("Pasirinkite namą")
      return
    }
    
    if (!number.trim()) {
      error("Buto numeris yra privalomas")
      return
    }
    
    try {
      setIsSubmitting(true)
      
      const response = await fetch("/api/flats", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ 
          number: number.trim(),
          floor: floor.trim() || null,
          houseId
        })
      })
      
      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || "Failed to create flat")
      }
      
      success(`Butas numeriu "${number}" sukurtas sėkmingai`)
      
      router.push("/dashboard/admin/houses")
      router.refresh()
    } catch (error) {
      console.error("Failed to create flat:", error)
      error(error instanceof Error ? error.message : "Nepavyko sukurti buto")
    } finally {
      setIsSubmitting(false)
    }
  }

  const selectedHouse = houses.find(h => h.id === houseId)

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Pridėti naują butą"
        description="Sukurkite naują butą sistemoje"
      />
      
      <Button
        variant="outline"
        onClick={() => router.back()}
        className="mb-4 flex items-center gap-2"
      >
        <ArrowLeft className="h-4 w-4" />
        <span>Grįžti atgal</span>
      </Button>
      
      <Card>
        <CardHeader>
          <CardTitle>Buto informacija</CardTitle>
          <CardDescription>Įveskite naujo buto duomenis</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="house">Namas</Label>
              <Select value={houseId} onValueChange={setHouseId}>
                <SelectTrigger id="house" disabled={isLoading}>
                  <SelectValue placeholder="Pasirinkite namą" />
                </SelectTrigger>
                <SelectContent>
                  {houses.map((house) => (
                    <SelectItem key={house.id} value={house.id}>
                      {house.displayName || house.address}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="number">Buto numeris</Label>
              <Input
                id="number"
                value={number}
                onChange={e => setNumber(e.target.value)}
                required
                placeholder="Pvz.: 10"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="floor">Aukštas</Label>
              <Input
                id="floor"
                value={floor}
                onChange={e => setFloor(e.target.value)}
                placeholder="Pvz.: 3"
              />
              <p className="text-sm text-muted-foreground">Neprivalomas laukas</p>
            </div>
          </CardContent>
          <CardFooter>
            <Button 
              type="submit" 
              disabled={isSubmitting || !houseId}
              className="flex items-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Kuriama...
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                  Sukurti butą
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </DashboardShell>
  )
} 