"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import { DataTable } from "@/components/ui/data-table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { PlusCircle, Home, Map, Building, Edit } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { type ColumnDef } from "@tanstack/react-table"
import { useStreets, useHouses, useFlats } from "@/lib/tanstack/queries"
import { useQueryClient } from "@tanstack/react-query"
import { queryKeys } from "@/lib/tanstack/query-client"

// Types for our data
interface Street {
  id: string
  name: string
  city: string
  createdAt: string
}

interface House {
  id: string
  name: string
  address: string
  streetId: string | null
  streetName: string | null
  displayName: string
  createdAt: string
}

interface Flat {
  id: string
  number: string
  floor: string
  houseId: string
  houseName: string
  houseAddress: string
  streetId: string | null
  streetName: string | null
  displayName: string
  createdAt: string
}

export default function HousingManagementPage() {
  // State
  const [activeTab, setActiveTab] = useState<string>("streets")
  const router = useRouter()
  const queryClient = useQueryClient()

  // Use TanStack Query hooks
  const { data: streets = [], isLoading: streetsLoading, error: streetsError } = useStreets()
  const { data: houses = [], isLoading: housesLoading, error: housesError } = useHouses()
  const { data: flats = [], isLoading: flatsLoading, error: flatsError } = useFlats()

  // Determine loading and error states based on active tab
  const isLoading = activeTab === "streets" ? streetsLoading : 
                   activeTab === "houses" ? housesLoading : 
                   flatsLoading
  
  const error = activeTab === "streets" ? streetsError :
                activeTab === "houses" ? housesError :
                flatsError

  // Handlers for deletion
  const handleDeleteStreets = async (selectedRows: Street[]) => {
    try {
      const ids = selectedRows.map(street => street.id);
      const results = await Promise.all(
        ids.map(async (id) => {
          const response = await fetch(`/api/streets/${id}`, { method: 'DELETE' });
          if (!response.ok) {
            const data = await response.json();
            return { success: false, id, error: data.error || "Unknown error" };
          }
          return { success: true, id };
        })
      );
      
      // Check for any failures
      const failures = results.filter(result => !result.success);
      
      if (failures.length > 0) {
        // At least one deletion failed
        if (failures.length === ids.length) {
          // All deletions failed
          toast({
            title: "Error",
            description: failures[0].error || "Failed to delete any streets.",
            variant: "destructive"
          });
        } else {
          // Some deletions succeeded, some failed
          toast({
            title: "Partial Success",
            description: `Deleted ${ids.length - failures.length} out of ${ids.length} streets. Some streets could not be deleted because they are linked to houses.`,
            variant: "destructive"
          });
        }
      } else {
        // All deletions succeeded
        toast({
          title: "Success",
          description: `${ids.length} streets have been deleted.`,
        });
      }
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: queryKeys.housing.streets() });
      queryClient.invalidateQueries({ queryKey: queryKeys.housing.houses() });
      
    } catch (error) {
      console.error('Error deleting streets:', error);
      toast({
        title: "Error",
        description: "Failed to delete streets. Some streets might be linked to houses.",
        variant: "destructive"
      });
    }
  }

  const handleDeleteHouses = async (selectedRows: House[]) => {
    try {
      const ids = selectedRows.map(house => house.id);
      const results = await Promise.all(
        ids.map(async (id) => {
          const response = await fetch(`/api/houses/${id}`, { method: 'DELETE' });
          if (!response.ok) {
            const data = await response.json();
            return { success: false, id, error: data.error || "Unknown error" };
          }
          return { success: true, id };
        })
      );
      
      // Check for any failures
      const failures = results.filter(result => !result.success);
      
      if (failures.length > 0) {
        // At least one deletion failed
        if (failures.length === ids.length) {
          // All deletions failed
          toast({
            title: "Error",
            description: failures[0].error || "Failed to delete any houses.",
            variant: "destructive"
          });
        } else {
          // Some deletions succeeded, some failed
          toast({
            title: "Partial Success",
            description: `Deleted ${ids.length - failures.length} out of ${ids.length} houses. Some houses could not be deleted because they have linked flats.`,
            variant: "destructive"
          });
        }
      } else {
        // All deletions succeeded
        toast({
          title: "Success",
          description: `${ids.length} houses have been deleted.`,
        });
      }
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: queryKeys.housing.houses() });
      queryClient.invalidateQueries({ queryKey: queryKeys.housing.flats() });
      
    } catch (error) {
      console.error('Error deleting houses:', error);
      toast({
        title: "Error",
        description: "Failed to delete houses. Some houses might have flats.",
        variant: "destructive"
      });
    }
  }

  const handleDeleteFlats = async (selectedRows: Flat[]) => {
    try {
      const ids = selectedRows.map(flat => flat.id);
      const results = await Promise.all(
        ids.map(async (id) => {
          const response = await fetch(`/api/flats/${id}`, { method: 'DELETE' });
          if (!response.ok) {
            const data = await response.json();
            return { success: false, id, error: data.error || "Unknown error" };
          }
          return { success: true, id };
        })
      );
      
      // Check for any failures
      const failures = results.filter(result => !result.success);
      
      if (failures.length > 0) {
        // At least one deletion failed
        if (failures.length === ids.length) {
          // All deletions failed
          toast({
            title: "Error",
            description: failures[0].error || "Failed to delete any flats.",
            variant: "destructive"
          });
        } else {
          // Some deletions succeeded, some failed
          toast({
            title: "Partial Success",
            description: `Deleted ${ids.length - failures.length} out of ${ids.length} flats. Some flats could not be deleted because they are linked to users.`,
            variant: "destructive"
          });
        }
      } else {
        // All deletions succeeded
        toast({
          title: "Success",
          description: `${ids.length} flats have been deleted.`,
        });
      }
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: queryKeys.housing.flats() });
      
    } catch (error) {
      console.error('Error deleting flats:', error);
      toast({
        title: "Error",
        description: "Failed to delete flats. Some flats might be linked to users.",
        variant: "destructive"
      });
    }
  }

  // Table column definitions
  const streetColumns: ColumnDef<Street>[] = [
    {
      accessorKey: "name",
      header: "Gatvės pavadinimas",
    },
    {
      accessorKey: "city",
      header: "Miestas",
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center justify-end space-x-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={() => {
              router.push(`/dashboard/admin/streets/${row.original.id}/edit`)
            }}
          >
            <Edit className="h-4 w-4" />
            <span>Redaguoti</span>
          </Button>
        </div>
      ),
    },
  ]

  const houseColumns: ColumnDef<House>[] = [
    {
      accessorKey: "name",
      header: "Namo numeris",
    },
    {
      accessorKey: "streetName",
      header: "Gatvė",
    },
    {
      accessorKey: "displayName",
      header: "Pilnas adresas",
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center justify-end space-x-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={() => {
              router.push(`/dashboard/admin/houses/${row.original.id}/edit`)
            }}
          >
            <Edit className="h-4 w-4" />
            <span>Redaguoti</span>
          </Button>
        </div>
      ),
    },
  ]

  const flatColumns: ColumnDef<Flat>[] = [
    {
      accessorKey: "displayName",
      header: "Butas",
    },
    {
      accessorKey: "number",
      header: "Buto numeris",
    },
    {
      accessorKey: "floor",
      header: "Aukštas",
    },
    {
      accessorKey: "houseName",
      header: "Namas",
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center justify-end space-x-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={() => {
              router.push(`/dashboard/admin/houses/flats/${row.original.id}/edit`)
            }}
          >
            <Edit className="h-4 w-4" />
            <span>Redaguoti</span>
          </Button>
        </div>
      ),
    },
  ]

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Namai ir butai"
        description="Tvarkykite namus, butus ir gatvių informaciją"
      />
      
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Tabs defaultValue="streets" className="w-full" onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="streets" className="flex items-center">
                <Map className="mr-2 h-4 w-4" />
                Gatvės ({streets.length})
              </TabsTrigger>
              <TabsTrigger value="houses" className="flex items-center">
                <Building className="mr-2 h-4 w-4" />
                Namai ({houses.length})
              </TabsTrigger>
              <TabsTrigger value="flats" className="flex items-center">
                <Home className="mr-2 h-4 w-4" />
                Butai ({flats.length})
              </TabsTrigger>
            </TabsList>
            
            {/* Street content */}
            <TabsContent value="streets" className="space-y-4">
              <div className="flex justify-end">
                <Button onClick={() => router.push('/dashboard/admin/streets/new')}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Pridėti gatvę
                </Button>
              </div>
              {isLoading ? (
                <p>Kraunami duomenys...</p>
              ) : error ? (
                <p className="text-red-500">Nepavyko užkrauti duomenų. Bandykite vėliau.</p>
              ) : (
                <DataTable
                  columns={streetColumns}
                  data={streets}
                  searchColumn="name"
                  searchPlaceholder="Ieškoti gatvės..."
                  onDelete={handleDeleteStreets}
                  pageSize={8}
                />
              )}
            </TabsContent>
            
            {/* Houses content */}
            <TabsContent value="houses" className="space-y-4">
              <div className="flex justify-end">
                <Button onClick={() => router.push('/dashboard/admin/houses/new')}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Pridėti namą
                </Button>
              </div>
              {isLoading ? (
                <p>Kraunami duomenys...</p>
              ) : error ? (
                <p className="text-red-500">Nepavyko užkrauti duomenų. Bandykite vėliau.</p>
              ) : (
                <DataTable
                  columns={houseColumns}
                  data={houses}
                  searchColumn="displayName" 
                  searchPlaceholder="Ieškoti namo..."
                  onDelete={handleDeleteHouses}
                  pageSize={8}
                />
              )}
            </TabsContent>
            
            {/* Flats content */}
            <TabsContent value="flats" className="space-y-4">
              <div className="flex justify-end">
                <Button onClick={() => router.push('/dashboard/admin/houses/flats/new')}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Pridėti butą
                </Button>
              </div>
              {isLoading ? (
                <p>Kraunami duomenys...</p>
              ) : error ? (
                <p className="text-red-500">Nepavyko užkrauti duomenų. Bandykite vėliau.</p>
              ) : (
                <DataTable
                  columns={flatColumns}
                  data={flats}
                  searchColumn="displayName"
                  searchPlaceholder="Ieškoti buto..."
                  onDelete={handleDeleteFlats}
                  pageSize={8}
                />
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </DashboardShell>
  )
}