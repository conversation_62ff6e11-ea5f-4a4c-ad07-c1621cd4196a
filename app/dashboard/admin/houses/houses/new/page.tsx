"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Suspense } from "react"
import { HouseForm } from "@/components/housing/house-form"
import { Skeleton } from "@/components/ui/skeleton"

interface Street {
  id: string
  name: string
  city: string
}

export default function NewHousePage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col gap-y-4">
        <h1 className="text-3xl font-bold">Pridėti naują namą</h1>
        <p className="text-muted-foreground">
          Užpildykite formą žemiau, kad pridėtumėte naują namą.
        </p>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Namo duomenys</CardTitle>
          <CardDescription>
            Įveskite namo pavadinimą, adresą ir pasirinkite gatvę.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
            <HouseForm />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
} 