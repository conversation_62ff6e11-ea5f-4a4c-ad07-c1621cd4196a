import { Suspense } from "react";
import { HouseForm } from "@/components/housing/house-form";
import { <PERSON><PERSON>es<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function NewHousePage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col gap-y-4">
        <h1 className="text-3xl font-bold">Pridėti naują namą</h1>
        <p className="text-muted-foreground">
          Užpildykite formą žemiau, kad pridėtumėte naują namą.
        </p>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Namo duomenys</CardTitle>
          <CardDescription>
            Įveskite namo numerį ir pasirinkite gatvę
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
            <HouseForm />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
} 