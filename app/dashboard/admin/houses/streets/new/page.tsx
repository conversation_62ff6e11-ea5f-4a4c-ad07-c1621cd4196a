"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { success, error } from "@/components/ui/use-toast"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft } from "lucide-react"
import { Suspense } from "react"
import { StreetForm } from "@/components/housing/street-form"
import { Skeleton } from "@/components/ui/skeleton"

export default function NewStreetPage() {
  const [name, setName] = useState("")
  const [city, setCity] = useState("Vilnius")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!name.trim()) {
      error("Gatvės pavadinimas yra privalomas")
      return
    }
    
    try {
      setIsSubmitting(true)
      
      const response = await fetch("/api/streets", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ name, city })
      })
      
      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || "Failed to create street")
      }
      
      success(`Gatvė "${name}" sukurta sėkmingai`)
      
      router.push("/dashboard/admin/houses")
      router.refresh()
    } catch (error) {
      console.error("Failed to create street:", error)
      error(error instanceof Error ? error.message : "Nepavyko sukurti gatvės")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-y-4">
          <h1 className="text-3xl font-bold">Pridėti naują gatvę</h1>
          <p className="text-muted-foreground">
            Užpildykite formą žemiau, kad pridėtumėte naują gatvę.
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Grįžti atgal</span>
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Gatvės duomenys</CardTitle>
          <CardDescription>
            Įveskite gatvės pavadinimą ir miestą.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<Skeleton className="h-[300px] w-full" />}>
            <StreetForm />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
} 