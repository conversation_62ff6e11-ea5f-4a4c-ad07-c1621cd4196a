import { Suspense } from "react";
import { notFound, redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { HouseForm } from "@/components/housing/house-form";
import { CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface PageProps {
  params: Promise<{
    houseId: string;
  }>;
}

async function getHouse(houseId: string) {
  try {
    const supabase = await createServiceClient();
    
    const { data: house, error } = await supabase
      .from('houses')
      .select('id, name, street_id')
      .eq('id', parseInt(houseId))
      .single();

    if (error || !house) {
      return null;
    }

    // Transform data to match expected format
    return {
      ...house,
      street_id_string: house.street_id ? house.street_id.toString() : 'none'
    };
  } catch (error) {
    console.error("Error fetching house data:", error);
    return null;
  }
}

export default async function EditHousePage({ params }: PageProps) {
  const user = await getCurrentUser();
  
  // Check if user is authorized
  if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
    redirect("/dashboard");
  }
  
  // Next.js 15 requires awaiting params before accessing properties
  const { houseId } = await params;
  
  if (!houseId || isNaN(parseInt(houseId))) {
    return notFound();
  }
  
  const house = await getHouse(houseId);
  
  if (!house) {
    return notFound();
  }
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col gap-y-4">
        <h1 className="text-3xl font-bold">Redaguoti namą</h1>
        <p className="text-muted-foreground">
          Redaguokite namo informaciją žemiau esančioje formoje.
        </p>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Namo duomenys</CardTitle>
          <CardDescription>
            Atnaujinkite namo numerį ir gatvę
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
            <HouseForm
              house={{
                id: house.id.toString(),
                number: house.name, // Using name field as number until database schema is updated
                streetId: house.street_id_string || "none",
              }}
            />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
} 