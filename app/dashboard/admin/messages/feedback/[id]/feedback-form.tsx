"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter,
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Icons } from "@/components/ui/icons";

// Form validation schema
const formSchema = z.object({
  status: z.string().min(1, { message: "Būsena yra privaloma" }),
  response: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface FeedbackFormProps {
  messageId: string;
  status: string;
  hasResponse: boolean;
}

export function FeedbackForm({ messageId, status, hasResponse }: FeedbackFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      status: status,
      response: "",
    },
  });
  
  async function onSubmit(values: FormValues) {
    setIsSubmitting(true);
    
    try {
      const response = await fetch(`/api/admin/feedback/${messageId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: values.status,
          response: values.response,
        }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Įvyko klaida atnaujinant atsiliepimą");
      }
      
      toast.success("Atsiliepimas sėkmingai atnaujintas");
      router.refresh();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Įvyko klaida atnaujinant atsiliepimą");
    } finally {
      setIsSubmitting(false);
    }
  }
  
  // Get the current status to show appropriate UI
  const currentStatus = form.watch("status");
  const isResolved = currentStatus === "RESOLVED";
  const isResponseRequired = isResolved && !hasResponse;
  
  return (
    <Card className="border-t-4 border-t-blue-600 shadow-md">
      <CardHeader className="bg-gray-50 border-b">
        <CardTitle className="text-xl font-semibold text-blue-800">Atnaujinti atsiliepimo būseną</CardTitle>
        <CardDescription className="text-gray-600">
          Pasirinkite atsiliepimo būseną ir pridėkite atsakymą
        </CardDescription>
      </CardHeader>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardContent className="space-y-6 pt-6">
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem className="bg-white p-4 rounded-md border">
                  <FormLabel className="text-gray-700 font-medium text-base">Būsena</FormLabel>
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                    disabled={isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger className="bg-white border-2 focus:ring-2 focus:ring-blue-200">
                        <SelectValue placeholder="Pasirinkite būseną" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="NEW">Naujas</SelectItem>
                      <SelectItem value="IN_PROGRESS">Nagrinėjamas</SelectItem>
                      <SelectItem value="RESOLVED">Išspręstas</SelectItem>
                      <SelectItem value="CLOSED">Uždarytas</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription className="text-sm text-gray-500 mt-1">
                    Pasirinkite dabartinę atsiliepimo būseną
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {!hasResponse && (
              <FormField
                control={form.control}
                name="response"
                render={({ field }) => (
                  <FormItem className="bg-white p-4 rounded-md border">
                    <FormLabel className="text-gray-700 font-medium text-base">Atsakymas</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Įveskite atsakymą..." 
                        className="min-h-[120px] resize-y bg-white border-2 focus:ring-2 focus:ring-blue-200" 
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    {isResponseRequired && (
                      <FormDescription className="text-destructive font-medium mt-1">
                        Atsakymas privalomas, kai būsena yra "Išspręstas"
                      </FormDescription>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </CardContent>
          
          <CardFooter className="flex justify-end border-t pt-6 bg-gray-50">
            <Button 
              type="submit" 
              className="bg-[#002855] hover:bg-blue-800 py-2 px-6 text-base font-medium"
              disabled={isSubmitting || (isResponseRequired && !form.getValues("response"))}
            >
              {isSubmitting ? (
                <>
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  Siunčiama...
                </>
              ) : (
                "Atnaujinti"
              )}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
} 