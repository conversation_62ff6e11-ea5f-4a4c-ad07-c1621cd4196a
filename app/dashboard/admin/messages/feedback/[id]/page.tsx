import { Metadata } from "next";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import { formatDateTime, translateCategory } from "@/lib/utils";
import { db } from "@/lib/db/supabase-adapter";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Clock, Mail, MessageSquare, Star, StarIcon, User, Edit, Tag } from "lucide-react";
import { FeedbackForm } from "./feedback-form";
import { AdminNotesSection } from "@/components/ui/admin-notes-section";

export const metadata: Metadata = {
  title: "Atsiliepimo informacija | DNSB Vakarai",
  description: "Atsiliepimo informacija ir statusas",
};

// Helper function to format dates
function formatDate(date: string | Date) {
  return formatDateTime(date);
}

// Rating stars component
function RatingStars({ rating }: { rating: number }) {
  return (
    <div className="flex">
      {[1, 2, 3, 4, 5].map((star) => (
        <StarIcon
          key={star}
          className={`h-5 w-5 ${
            star <= rating ? "text-yellow-500 fill-yellow-500" : "text-gray-300"
          }`}
        />
      ))}
    </div>
  );
}

export default async function FeedbackMessagePage({
  params
}: {
  params: Promise<{ id: string }>
}) {
  const user = await getCurrentUser();
  const resolvedParams = await params;
  
  if (!user) {
    redirect("/auth/login");
  }
  
  if (!["developer", "super_admin", "editor"].includes(user.role as string)) {
    redirect("/dashboard");
  }
  
  // Directly get feedback data from the database
  let feedback;
  
  try {
    // Try to get feedback from database
    const { rows } = await db.query(
      'SELECT * FROM feedback WHERE id = $1',
      [resolvedParams.id]
    );
    
    if (rows.length === 0) {
      // Feedback not found, redirect to messages page
      redirect("/dashboard/admin/messages");
    }
    
    feedback = rows[0];
  } catch (error: any) {
    // Check if the error is due to the table not existing
    if (error.message && (
        error.message.includes("relation \"feedback\" does not exist") || 
        error.message.includes("no such table: feedback")
      )) {
      // Redirect to messages page if table doesn't exist
      redirect("/dashboard/admin/messages");
    } else {
      throw new Error(`Database error: ${error.message}`);
    }
  }
  
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6 flex items-center gap-2">
        <Link href="/dashboard/admin/messages" className="text-blue-600 hover:text-blue-800">
          <ArrowLeft className="h-4 w-4 inline" />
          <span className="ml-1">Atgal į atsiliepimų sąrašą</span>
        </Link>
      </div>
      
      <Card className="mb-6">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-xl">{feedback.title}</CardTitle>
              <p className="text-sm text-muted-foreground">Atsiliepimas #{feedback.id}</p>
            </div>
            <Badge 
              className={
                feedback.status === 'NEW' ? 'bg-blue-500' :
                feedback.status === 'IN_PROGRESS' ? 'bg-yellow-500' :
                feedback.status === 'RESOLVED' ? 'bg-green-500' :
                'bg-gray-500'
              }
            >
              {
                feedback.status === 'NEW' ? 'Naujas' :
                feedback.status === 'IN_PROGRESS' ? 'Nagrinėjamas' :
                feedback.status === 'RESOLVED' ? 'Išspręstas' :
                feedback.status === 'CLOSED' ? 'Uždarytas' :
                feedback.status
              }
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center gap-1">
                <User className="h-4 w-4" /> Siuntėjas
              </h3>
              <p>{feedback.is_anonymous ? "Anoniminis" : feedback.name}</p>
              {feedback.is_anonymous && (
                <p className="text-xs text-amber-600 mt-1">
                  <span className="font-semibold">Pastaba:</span> Šis atsiliepimas pateiktas anonimiškai
                </p>
              )}
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center gap-1">
                <Mail className="h-4 w-4" /> El. paštas
              </h3>
              <p>{feedback.is_anonymous ? "⋯⋯⋯⋯⋯⋯" : feedback.email}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center gap-1">
                <Tag className="h-4 w-4" /> Kategorija
              </h3>
              <p>{translateCategory(feedback.category)}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center gap-1">
                <Star className="h-4 w-4" /> Įvertinimas
              </h3>
              <RatingStars rating={feedback.rating} />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center gap-1">
                <Clock className="h-4 w-4" /> Išsiųsta
              </h3>
              <p>{formatDate(feedback.created_at)}</p>
            </div>
          </div>
          
          <div className="bg-white border rounded-lg shadow-sm p-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-1">
              <MessageSquare className="h-4 w-4 text-blue-600" /> Atsiliepimas
            </h3>
            <div className="p-4 border rounded-md whitespace-pre-wrap bg-gray-50">
              {feedback.content}
            </div>
          </div>
          
          {feedback.admin_notes && (
            <AdminNotesSection notes={feedback.admin_notes} />
          )}
          
          <Separator className="my-6" />
          
          <FeedbackForm messageId={feedback.id} status={feedback.status} hasResponse={!!feedback.admin_notes} />
        </CardContent>
      </Card>
    </div>
  );
} 