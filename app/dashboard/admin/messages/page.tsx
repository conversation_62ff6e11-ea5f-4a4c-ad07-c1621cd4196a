import { <PERSON>ada<PERSON> } from "next";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";
import { getApiBaseUrl } from "@/lib/utils";
import Link from "next/link";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CalendarIcon, Clock, Mail, MessageSquare, Star, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import { formatDistanceToNow } from "date-fns";
import { lt } from "date-fns/locale";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Trash2 } from "lucide-react";
import { PaginatedContactMessages, PaginatedFeedbackMessages } from "@/components/dashboard/paginated-message-list";
import { MessageStats } from "@/components/dashboard/message-stats";

export const metadata: Metadata = {
  title: "Pranešimai ir atsiliepimai | DNSB Vakarai",
  description: "Gyventojų pranešimai ir atsiliepimai",
};

// Mock data for when database tables don't exist
const mockContactMessages = [
  {
    id: "mock-contact-1",
    subject: "Vandens nuotėkis laiptinėje",
    name: "Jonas Jonaitis",
    email: "<EMAIL>",
    phone_number: "+37061234567",
    message: "Pastebėjau vandens nuotėkį pirmame aukšte prie lifto. Vanduo laša iš lubų.",
    category: "technical",
    status: "NEW",
    created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: "mock-contact-2",
    subject: "Parkavimo vietos žymėjimas",
    name: "Petras Petraitis",
    email: "<EMAIL>",
    phone_number: "+37062345678",
    message: "Prašau patikslinti, ar planuojama atnaujinti parkavimo vietų žymėjimą šį pavasarį?",
    category: "general",
    status: "IN_PROGRESS",
    created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: "mock-contact-3",
    subject: "Padėka už greitą reagavimą",
    name: "Ona Onaitė",
    email: "<EMAIL>",
    phone_number: "+37063456789",
    message: "Norėjau padėkoti už greitą reagavimą į mano pranešimą apie sugedusį apšvietimą. Viskas sutvarkyta per 24 valandas!",
    category: "feedback",
    status: "RESOLVED",
    created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
  }
];

const mockFeedbackMessages = [
  {
    id: "mock-feedback-1",
    title: "Puikus aptarnavimas",
    name: "Marius Mariulis",
    email: "<EMAIL>",
    content: "Labai patenkinti administracijos darbu. Visi klausimai išsprendžiami greitai ir efektyviai.",
    rating: 5,
    category: "service",
    status: "NEW",
    created_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
    allow_contact: true
  },
  {
    id: "mock-feedback-2",
    title: "Pasiūlymai dėl svetainės",
    name: "Laura Lauraitė",
    email: "<EMAIL>",
    content: "Svetainė labai patogi naudoti, bet būtų gerai pridėti galimybę matyti mokėjimų istoriją.",
    rating: 4,
    category: "website",
    status: "IN_PROGRESS",
    created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    allow_contact: false
  }
];

export default async function MessagesPage() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/auth/login");
  }
  
  if (!["developer", "super_admin", "editor"].includes(user.role)) {
    redirect("/dashboard");
  }
  
  // Fetch contact messages with fallback to mock data - increase limit to 500
  let contactMessages = [];
  let feedbackMessages = [];
  // Track if we are displaying mock data
  let hasMockData = false;
  
  try {
    // Fetch contact messages
    try {
      // Fetch up to 500 messages so we have plenty for pagination
      contactMessages = await db.query(
        `SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT 500`
      ).then(result => result.rows);
      
      // Check if any of the messages are mock data
      hasMockData = hasMockData || contactMessages.some(msg => msg.is_mock);
    } catch (error) {
      console.error("Error fetching contact messages:", error);
      contactMessages = mockContactMessages;
      hasMockData = true;
    }
    
    // Fetch feedback messages
    try {
      // Fetch up to 500 feedback messages
      feedbackMessages = await db.query(
        `SELECT * FROM feedback ORDER BY created_at DESC LIMIT 500`
      ).then(result => result.rows);
      
      // Check if any of the messages are mock data
      hasMockData = hasMockData || feedbackMessages.some(msg => msg.is_mock);
    } catch (error) {
      console.error("Error fetching feedback messages:", error);
      feedbackMessages = mockFeedbackMessages;
      hasMockData = true;
    }
  } catch (error) {
    console.error("Error fetching messages:", error);
    contactMessages = mockContactMessages;
    feedbackMessages = mockFeedbackMessages;
    hasMockData = true;
  }

  // Calculate message counts
  const newContactCount = contactMessages.filter(msg => msg.status === "NEW").length;
  const newFeedbackCount = feedbackMessages.filter(msg => msg.status === "NEW").length;

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Pranešimai ir atsiliepimai</h1>
        
        {hasMockData && user.role === 'super_admin' && (
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" size="sm">
                <Trash2 className="h-4 w-4 mr-2" />
                Ištrinti testavimo duomenis
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Ar tikrai norite ištrinti?</AlertDialogTitle>
                <AlertDialogDescription>
                  Ši operacija ištrins visus testavimo duomenis (pranešimus ir atsiliepimus, pažymėtus kaip "is_mock"). 
                  Šio veiksmo negalima atšaukti.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Atšaukti</AlertDialogCancel>
                <AlertDialogAction asChild>
                  <form action={async () => {
                    'use server'
                    
                    // Delete mock data
                    const apiUrl = getApiBaseUrl();
                    
                    await fetch(`${apiUrl}/api/admin/messages`, {
                      method: 'DELETE',
                    });
                    
                    // Redirect to reload the page
                    redirect('/dashboard/admin/messages');
                  }}>
                    <Button type="submit" variant="destructive">Ištrinti</Button>
                  </form>
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 mb-6">
        <MessageStats messages={contactMessages} type="contact" />
        <MessageStats messages={feedbackMessages} type="feedback" />
      </div>
      
      <Tabs defaultValue="contact" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="contact" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Pranešimai
            {newContactCount > 0 && (
              <Badge variant="secondary" className="ml-1 bg-blue-500 text-white h-5 min-w-5 px-1.5 rounded-full text-xs">
                {newContactCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="feedback" className="flex items-center gap-2">
            <Star className="h-4 w-4" />
            Atsiliepimai
            {newFeedbackCount > 0 && (
              <Badge variant="secondary" className="ml-1 bg-blue-500 text-white h-5 min-w-5 px-1.5 rounded-full text-xs">
                {newFeedbackCount}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="contact">
          <PaginatedContactMessages messages={contactMessages} />
        </TabsContent>
        
        <TabsContent value="feedback">
          <PaginatedFeedbackMessages messages={feedbackMessages} />
        </TabsContent>
      </Tabs>
    </div>
  );
} 