"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const formSchema = z.object({
  status: z.string({
    required_error: "Pasirinkite būseną",
  }),
  notes: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface ContactFormProps {
  messageId: string;
  status: string;
}

export function ContactForm({ messageId, status }: ContactFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      status: status,
      notes: "",
    },
  });

  async function onSubmit(values: FormValues) {
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/admin/contact/${messageId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        throw new Error("Klaida atnaujinant pranešimą");
      }

      toast.success("Pranešimas sėkmingai atnaujintas", {
        description: "Būsena ir pastabos išsaugotos.",
      });
      
      router.refresh();
    } catch (error) {
      console.error("Klaida atnaujinant pranešimą:", error);
      toast.error("Nepavyko atnaujinti pranešimo", {
        description: "Bandykite dar kartą vėliau.",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  const statusOptions = [
    { value: "NEW", label: "Naujas" },
    { value: "IN_PROGRESS", label: "Nagrinėjamas" },
    { value: "RESOLVED", label: "Išspręstas" },
    { value: "CLOSED", label: "Uždarytas" },
  ];

  return (
    <Card className="border-t-4 border-t-blue-600 shadow-md">
      <CardHeader className="bg-gray-50 border-b">
        <CardTitle className="text-xl font-semibold text-blue-800">Atnaujinti pranešimo būseną</CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem className="bg-white p-4 rounded-md border">
                  <FormLabel className="text-gray-700 font-medium text-base">Būsena</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger className="bg-white border-2 focus:ring-2 focus:ring-blue-200">
                        <SelectValue placeholder="Pasirinkite būseną" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {statusOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription className="text-sm text-gray-500 mt-1">
                    Pasirinkite esamą pranešimo būseną
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem className="bg-white p-4 rounded-md border">
                  <FormLabel className="text-gray-700 font-medium text-base">Administratoriaus pastabos</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Įveskite pastabas apie šį pranešimą (matys tik administratoriai)..."
                      className="min-h-[120px] resize-y bg-white border-2 focus:ring-2 focus:ring-blue-200"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormDescription className="text-sm text-gray-500 mt-1">
                    Pastabos bus pridėtos prie esamų pastabų
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end mt-6">
              <Button 
                type="submit" 
                className="bg-[#002855] hover:bg-blue-800 py-2 px-6 text-base font-medium"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Saugoma..." : "Išsaugoti pakeitimus"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
} 