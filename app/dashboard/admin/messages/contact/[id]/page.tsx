import { <PERSON>ada<PERSON> } from "next";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import { formatDateTime, translateCategory } from "@/lib/utils";
import { db } from "@/lib/db/supabase-adapter";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Clock, Edit, Mail, MessageSquare, Phone, User, Tag } from "lucide-react";
import { ContactForm } from "./contact-form";
import { AdminNotesSection } from "@/components/ui/admin-notes-section";

export const metadata: Metadata = {
  title: "Pranešimo informacija | DNSB Vakarai",
  description: "Pranešimo informacija ir statusas",
};

// Helper function to format dates - replace with the one from utils
function formatDate(date: string | Date) {
  return formatDateTime(date);
}

export default async function ContactMessagePage({
  params
}: {
  params: Promise<{ id: string }>
}) {
  const user = await getCurrentUser();
  const resolvedParams = await params;
  
  if (!user) {
    redirect("/auth/login");
  }
  
  if (!["developer", "super_admin", "editor"].includes(user.role as string)) {
    redirect("/dashboard");
  }
  
  // Directly get message data from the database
  let message;
  
  try {
    // Try to get message from database
    const { rows } = await db.query(
      'SELECT * FROM contact_messages WHERE id = $1',
      [resolvedParams.id]
    );
    
    if (rows.length === 0) {
      // Message not found, redirect to messages page
      redirect("/dashboard/admin/messages");
    }
    
    message = rows[0];
  } catch (error: any) {
    // Check if the error is due to the table not existing
    if (error.message && (
        error.message.includes("relation \"contact_messages\" does not exist") || 
        error.message.includes("no such table: contact_messages")
      )) {
      // Try to get mock data
      // Check if it's a mock ID we have data for
      redirect("/dashboard/admin/messages");
    } else {
      throw new Error(`Database error: ${error.message}`);
    }
  }
  
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6 flex items-center gap-2">
        <Link href="/dashboard/admin/messages" className="text-blue-600 hover:text-blue-800">
          <ArrowLeft className="h-4 w-4 inline" />
          <span className="ml-1">Atgal į pranešimų sąrašą</span>
        </Link>
      </div>
      
      <Card className="mb-6">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-xl">{message.subject}</CardTitle>
              <p className="text-sm text-muted-foreground">Pranešimas #{message.id}</p>
            </div>
            <Badge 
              className={
                message.status === 'NEW' ? 'bg-blue-500' :
                message.status === 'IN_PROGRESS' ? 'bg-yellow-500' :
                message.status === 'RESOLVED' ? 'bg-green-500' :
                'bg-gray-500'
              }
            >
              {
                message.status === 'NEW' ? 'Naujas' :
                message.status === 'IN_PROGRESS' ? 'Nagrinėjamas' :
                message.status === 'RESOLVED' ? 'Išspręstas' :
                message.status === 'CLOSED' ? 'Uždarytas' :
                message.status
              }
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center gap-1">
                <User className="h-4 w-4" /> Siuntėjas
              </h3>
              <p>{message.is_anonymous ? "Anoniminis" : message.name}</p>
              {message.is_anonymous && (
                <p className="text-xs text-amber-600 mt-1">
                  <span className="font-semibold">Pastaba:</span> Šis pranešimas pateiktas anonimiškai
                </p>
              )}
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center gap-1">
                <Mail className="h-4 w-4" /> El. paštas
              </h3>
              <p>{message.is_anonymous ? "⋯⋯⋯⋯⋯⋯" : message.email}</p>
            </div>
            {!message.is_anonymous && message.phone_number && (
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center gap-1">
                  <Phone className="h-4 w-4" /> Telefono numeris
                </h3>
                <p>{message.phone_number}</p>
              </div>
            )}
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center gap-1">
                <Tag className="h-4 w-4" /> Kategorija
              </h3>
              <p>{translateCategory(message.category)}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center gap-1">
                <Clock className="h-4 w-4" /> Išsiųsta
              </h3>
              <p>{formatDate(message.created_at)}</p>
            </div>
          </div>
          
          <div className="bg-white border rounded-lg shadow-sm p-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-1">
              <MessageSquare className="h-4 w-4 text-blue-600" /> Pranešimas
            </h3>
            <div className="p-4 border rounded-md whitespace-pre-wrap bg-gray-50">
              {message.message}
            </div>
          </div>
          
          {message.admin_notes && (
            <AdminNotesSection notes={message.admin_notes} />
          )}
          
          <Separator className="my-6" />
          
          <ContactForm messageId={message.id} status={message.status} />
        </CardContent>
      </Card>
    </div>
  );
} 