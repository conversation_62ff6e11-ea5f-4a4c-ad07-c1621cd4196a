import { <PERSON>ada<PERSON> } from "next";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import { PollForm } from "@/components/polls/poll-form";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { DashboardHeader } from "@/components/dashboard/header";
import { DashboardShell } from "@/components/dashboard/shell";

export const metadata: Metadata = {
  title: "Nauja apklausa | DNSB Vakarai",
  description: "Sukurkite naują apklausą bendruomenei",
};

export default async function CreatePollPage() {
  const user = await getCurrentUser();
  
  // Check if user is authorized to create polls
  if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
    redirect("/dashboard"); // Redirect if not authorized
  }
  
  return (
    <DashboardShell>
       <DashboardHeader heading="Nauja apklausa">
           <Breadcrumb 
            items={[
                { href: "/dashboard", label: "Val<PERSON><PERSON>" },
                { href: "/dashboard/admin", label: "Administravimas" },
                { href: "/dashboard/admin/polls", label: "Apklausos" },
                { href: "/dashboard/admin/polls/new", label: "Nauja apklausa", isLast: true }
            ]}
            className="mb-4"
           />
       </DashboardHeader>
       <div className="grid gap-10">
            {/* Render the new PollForm component */}
           <PollForm userId={parseInt(user.id)} />
       </div>
    </DashboardShell>
  );
} 