import { <PERSON>ada<PERSON> } from "next";
import { notFound, redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter"; // Assuming db access for fetching poll
import { PollForm } from "@/components/polls/poll-form";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { DashboardHeader } from "@/components/dashboard/header";
import { DashboardShell } from "@/components/dashboard/shell";

// Define params type
interface EditPollPageParams {
  id: string;
}

// Function to fetch poll data (similar to helper in API route, but adapted for server component)
async function getPollForEdit(pollId: number) {
  try {
    // First, get the poll itself, including audience_type
    const pollQuery = `
      SELECT 
        p.id, 
        p.title, 
        p.description, 
        p.status,
        p.start_date,
        p.end_date,
        p.created_at,
        p.updated_at,
        p.show_results_after_voting,
        p.audience_type
      FROM polls p
      WHERE p.id = $1
    `;
    const pollResult = await db.query(pollQuery, [pollId]);

    if (pollResult.rows.length === 0) {
      return null;
    }
    const poll = pollResult.rows[0];

    // Get options
    const optionsQuery = `
      SELECT id, option_text, display_order
      FROM poll_options
      WHERE poll_id = $1
      ORDER BY display_order
    `;
    const optionsResult = await db.query(optionsQuery, [pollId]);
    poll.options = optionsResult.rows;

    // Get audience IDs
    if (poll.audience_type === 'houses' || poll.audience_type === 'streets') {
        const houseIdsResult = await db.query(`SELECT house_id FROM poll_houses WHERE poll_id = $1`, [pollId]);
        poll.houseIds = houseIdsResult.rows.map(r => r.house_id.toString());
    } else if (poll.audience_type === 'flats') {
        const flatIdsResult = await db.query(`SELECT flat_id FROM poll_flats WHERE poll_id = $1`, [pollId]);
        poll.flatIds = flatIdsResult.rows.map(r => r.flat_id.toString());
    } else if (poll.audience_type === 'users') {
        const userIdsResult = await db.query(`SELECT user_id FROM poll_users WHERE poll_id = $1`, [pollId]);
        poll.userIds = userIdsResult.rows.map(r => r.user_id.toString());
    }

    return poll;

  } catch (error) {
    console.error('Error getting poll for edit:', error);
    return null;
  }
}


export async function generateMetadata({ params }: { params: EditPollPageParams }): Promise<Metadata> {
  // Fetch basic poll info for title, handle potential errors
  try {
    const pollId = parseInt(params.id);
    const poll = await db.query('SELECT title FROM polls WHERE id = $1', [pollId]);
    const title = poll.rows.length > 0 ? poll.rows[0].title : "Redaguoti apklausą";
    return {
      title: `Redaguoti: ${title} | DNSB Vakarai`,
      description: `Redaguoti apklausą "${title}"`,
    };
  } catch {
      return {
          title: "Redaguoti apklausą | DNSB Vakarai",
          description: "Redaguoti apklausą",
      }
  }
}

export default async function EditPollPage({ params }: { params: EditPollPageParams }) {
  const user = await getCurrentUser();
  const pollId = parseInt(params.id);

  // Check authorization
  if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
    redirect("/dashboard");
  }

  // Validate poll ID
  if (isNaN(pollId)) {
      notFound();
  }

  // Fetch the poll data
  const pollData = await getPollForEdit(pollId);

  // If poll not found
  if (!pollData) {
    notFound();
  }

  return (
    <DashboardShell>
       <DashboardHeader heading={`Redaguoti apklausą: ${pollData.title}`}>
           <Breadcrumb 
            items={[
                { href: "/dashboard", label: "Valdymas" },
                { href: "/dashboard/admin", label: "Administravimas" },
                { href: "/dashboard/admin/polls", label: "Apklausos" },
                { href: `/dashboard/admin/polls/${pollId}/edit`, label: "Redaguoti apklausą", isLast: true }
            ]}
            className="mb-4"
           />
       </DashboardHeader>
       <div className="grid gap-10">
           <PollForm 
             editMode={true} 
             existingPoll={pollData} 
             userId={parseInt(user.id)} 
           />
       </div>
    </DashboardShell>
  );
} 