"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { format } from "date-fns";
import { 
  ChevronLeft, 
  Edit, 
  BarChart3, 
  Calendar, 
  Clock, 
  CheckCircle2, 
  User, 
  FileText, 
  Settings,
  Users,
  Eye,
  ArrowLeft,
  CheckCircle,
  XCircle,
  MessageCircle,
  PenSquare,
  HomeIcon,
  UserCheck,
  UserX,
  X as XIcon
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import {
  Pagination,
  PaginationContent,
  Pa<PERSON>ation<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ationI<PERSON>,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Status badges with appropriate colors
const statusBadges = {
  draft: <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100">Juodraštis</Badge>,
  active: <Badge className="bg-green-100 text-green-800 border-green-200 hover:bg-green-200">Aktyvi</Badge>,
  closed: <Badge className="bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200">Uždaryta</Badge>,
};

// Add this function for formatting a date and time
function formatDateTime(dateString: string) {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('lt-LT', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
}

// Add this new type
type PollResponse = {
  id: string;
  optionId: string;
  optionText: string;
  explanation: string | null;
  createdAt: string;
  isAnonymous: boolean;
  userName: string;
  houseInfo: string | null;
  flatInfo: string | null;
};

type PaginatedResponses = {
  responses: PollResponse[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
};

export default function PollDetailPage({ params }: { params: any }) {
  // Unwrap params using React.use()
  const resolvedParams = use(params) as { id: string };
  const pollId = resolvedParams.id;
  
  const router = useRouter();
  const [poll, setPoll] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Pagination state for voter responses
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [voterResponses, setVoterResponses] = useState<any[]>([]);
  const [totalResponses, setTotalResponses] = useState(0);
  const [isLoadingResponses, setIsLoadingResponses] = useState(false);
  const [responses, setResponses] = useState<PollResponse[]>([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: 10,
    totalPages: 1,
    total: 0,
  });

  useEffect(() => {
    const fetchPoll = async () => {
      try {
        const response = await fetch(`/api/polls?id=${pollId}`);
        if (!response.ok) {
          throw new Error("Nepavyko gauti apklausos duomenų");
        }

        const data = await response.json();
        setPoll(data);
        
        // If poll has responses, fetch the voter responses with pagination
        if (data.results?.totalVotes > 0) {
          fetchVoterResponses(1, pageSize);
          setTotalResponses(data.results.totalVotes);
        }
      } catch (error) {
        console.error("Error fetching poll:", error);
        toast.error("Nepavyko įkelti apklausos duomenų");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPoll();
  }, [pollId, pageSize]);

  const fetchVoterResponses = async (page: number, size: number) => {
    setIsLoadingResponses(true);
    try {
      const response = await fetch(`/api/polls/responses?pollId=${pollId}&page=${page}&pageSize=${size}`);
      if (!response.ok) {
        throw new Error("Nepavyko gauti balsavimo duomenų");
      }

      const data = await response.json();
      setVoterResponses(data.responses);
      setTotalResponses(data.total);
      setCurrentPage(page);
      
      // Update the pagination state as well for consistency
      setPagination({
        currentPage: page,
        pageSize: size,
        totalPages: Math.ceil(data.total / size),
        total: data.total
      });
    } catch (error) {
      console.error("Error fetching voter responses:", error);
      toast.error("Nepavyko įkelti balsavimo duomenų");
      // Set empty responses to prevent displaying stale data
      setVoterResponses([]);
    } finally {
      setIsLoadingResponses(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({
      ...prev,
      currentPage: page,
    }));
    fetchVoterResponses(page, pageSize);
  };

  const handlePageSizeChange = (size: string) => {
    const newSize = parseInt(size);
    setPageSize(newSize);
    fetchVoterResponses(1, newSize);
  };

  // Calculate pagination details
  const totalPages = Math.ceil(totalResponses / pageSize);
  
  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5;
    
    if (totalPages <= maxPagesToShow) {
      // Show all pages if there are few
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);
      
      // Calculate range around current page
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);
      
      // Adjust if at edges
      if (currentPage <= 2) {
        endPage = 4;
      } else if (currentPage >= totalPages - 1) {
        startPage = totalPages - 3;
      }
      
      // Add ellipsis if needed before middle pages
      if (startPage > 2) {
        pages.push('ellipsis1');
      }
      
      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
      
      // Add ellipsis if needed after middle pages
      if (endPage < totalPages - 1) {
        pages.push('ellipsis2');
      }
      
      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  // Add this effect to fetch responses
  useEffect(() => {
    async function fetchResponses() {
      if (!poll) return;
      
      try {
        setIsLoadingResponses(true);
        const response = await fetch(
          `/api/polls/responses?pollId=${pollId}&page=${pagination.currentPage}&pageSize=${pagination.pageSize}`
        );
        
        if (!response.ok) {
          throw new Error("Failed to fetch responses");
        }
        
        const data: PaginatedResponses = await response.json();
        setResponses(data.responses);
        setPagination({
          currentPage: data.page,
          pageSize: data.pageSize,
          totalPages: data.totalPages,
          total: data.total,
        });
      } catch (err) {
        console.error("Error fetching responses:", err);
        toast.error("Nepavyko gauti balsavimo duomenų");
        // Don't set the main error state, just log this error to not disrupt the whole page
      } finally {
        setIsLoadingResponses(false);
      }
    }
    
    fetchResponses();
  }, [pollId, pagination.currentPage, pagination.pageSize, poll]);

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="h-6 w-1/4 bg-slate-200 rounded animate-pulse mb-8"></div>
        
        <div className="bg-white p-6 rounded-lg border border-slate-200 shadow-sm mb-8 animate-pulse">
          <div className="h-8 w-1/2 bg-slate-200 rounded mb-4"></div>
          <div className="h-4 w-1/4 bg-slate-200 rounded mb-4"></div>
          <div className="h-4 w-full bg-slate-200 rounded mb-2"></div>
          <div className="h-4 w-full bg-slate-200 rounded mb-2"></div>
          <div className="h-4 w-3/4 bg-slate-200 rounded mb-4"></div>
          
          <div className="grid grid-cols-2 gap-4 mt-6">
            <div className="h-12 w-full bg-slate-200 rounded"></div>
            <div className="h-12 w-full bg-slate-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!poll) {
    return (
      <div className="container mx-auto p-6">
        <div className="bg-red-50 text-red-700 p-6 rounded-lg border border-red-200">
          <h2 className="text-xl font-bold mb-2">
            Apklausa nerasta
          </h2>
          <p>
            Nepavyko rasti apklausos su ID {pollId}. 
            Patikrinkite, ar teisingai nurodėte apklausos ID, arba grįžkite į apklausų sąrašą.
          </p>
          <Button
            variant="outline"
            onClick={() => router.push("/dashboard/admin/polls")}
            className="mt-4"
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Grįžti į apklausų sąrašą
          </Button>
        </div>
      </div>
    );
  }

  const getAudienceText = () => {
    if (poll.audience === "all") return "Visi naudotojai";
    if (poll.audience === "registered") return "Registruoti naudotojai";
    if (poll.audience === "specific") return "Pasirinkti naudotojai";
    return "Nenustatyta";
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Nenustatyta";
    return format(new Date(dateString), "yyyy-MM-dd HH:mm");
  };

  return (
    <>
      <Breadcrumb
        items={[
          { href: "/dashboard", label: "Valdymo skydelis" },
          { href: "/dashboard/admin", label: "Administravimas" },
          { href: "/dashboard/admin/polls", label: "Apklausos" },
          { href: `/dashboard/admin/polls/${pollId}`, label: poll.title, isLast: true },
        ]}
        className="mb-6"
      />

      <div className="space-y-8">
        {/* Header section */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-gradient-to-r from-indigo-50 to-white p-6 rounded-lg border border-indigo-100 shadow-sm">
          <div>
            <h1 className="text-2xl font-bold text-indigo-900 tracking-tight">{poll.title}</h1>
            <div className="flex items-center gap-3 mt-2">
              {statusBadges[poll.status as keyof typeof statusBadges]}
              <p className="text-indigo-700 text-sm">
                <span className="font-medium">Sukurta:</span> {formatDate(poll.created_at)}
              </p>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push("/dashboard/admin/polls")}
              className="border-slate-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Grįžti į sąrašą
            </Button>
            
            {poll.status !== "closed" && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => router.push(`/dashboard/admin/polls/${pollId}/edit`)}
                className="border-indigo-200 text-indigo-700 hover:bg-indigo-50"
              >
                <Edit className="mr-2 h-4 w-4" />
                Redaguoti
              </Button>
            )}
          </div>
        </div>

        {/* Poll Details Card */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2 space-y-8">
            <div className="p-1 bg-gradient-to-br from-slate-100 to-transparent rounded-lg">
              <Card className="border border-slate-200 shadow-sm">
                <CardHeader className="pb-3">
                  <div className="flex items-center">
                    <FileText className="mr-2 h-5 w-5 text-indigo-600" />
                    <CardTitle className="text-xl text-slate-800">Apklausos detalės</CardTitle>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <div className="bg-slate-50 p-4 rounded-lg border border-slate-200">
                    <h3 className="font-medium text-slate-800 mb-3">Aprašymas</h3>
                    <p className="text-slate-700 whitespace-pre-wrap">
                      {poll.description}
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-white p-4 rounded-lg border border-slate-200">
                      <div className="flex items-center mb-3">
                        <Calendar className="h-5 w-5 text-indigo-600 mr-2" />
                        <h3 className="font-medium text-slate-800">Laikotarpis</h3>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-slate-500">Pradžia:</span>
                          <span className="font-medium text-slate-800">{formatDate(poll.start_date)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-500">Pabaiga:</span>
                          <span className="font-medium text-slate-800">{formatDate(poll.end_date)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-500">Statusas:</span>
                          <span>{statusBadges[poll.status as keyof typeof statusBadges]}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white p-4 rounded-lg border border-slate-200">
                      <div className="flex items-center mb-3">
                        <Users className="h-5 w-5 text-indigo-600 mr-2" />
                        <h3 className="font-medium text-slate-800">Auditorija</h3>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-slate-500">Tipas:</span>
                          <span className="font-medium text-slate-800">{getAudienceText()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-500">Autorius:</span>
                          <span className="font-medium text-slate-800">{poll.created_by_name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-500">Rodyti rezultatus:</span>
                          <span className="font-medium text-slate-800 flex items-center">
                            {poll.show_results_after_voting ? (
                              <>
                                <CheckCircle className="h-4 w-4 text-green-600 mr-1" />
                                Po balsavimo
                              </>
                            ) : (
                              <>
                                <XCircle className="h-4 w-4 text-amber-600 mr-1" />
                                Po apklausos pabaigos
                              </>
                            )}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-white p-4 rounded-lg border border-slate-200">
                    <div className="flex items-center mb-3">
                      <MessageCircle className="h-5 w-5 text-indigo-600 mr-2" />
                      <h3 className="font-medium text-slate-800">Apklausos variantai</h3>
                    </div>
                    <div className="space-y-4">
                      {poll.options && poll.options.map((option: any, index: number) => (
                        <div 
                          key={option.id} 
                          className="p-3 rounded-lg border border-slate-200 bg-slate-50"
                        >
                          <div className="flex justify-between gap-4">
                            <div className="flex items-start gap-2">
                              <div className="flex items-center justify-center bg-indigo-100 text-indigo-700 w-6 h-6 rounded-full shrink-0 mt-0.5 font-medium text-sm">
                                {index + 1}
                              </div>
                              <p className="text-slate-700">{option.option_text}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Results summary card */}
          <div className="space-y-8">
            <div className="p-1 bg-gradient-to-br from-blue-100 to-transparent rounded-lg">
              <Card className="border border-blue-200 shadow-sm">
                <CardHeader className="pb-2">
                  <div className="flex items-center">
                    <BarChart3 className="mr-2 h-5 w-5 text-blue-600" />
                    <CardTitle className="text-lg text-blue-800">Rezultatų suvestinė</CardTitle>
                  </div>
                </CardHeader>
                
                <CardContent className="pt-4">
                  <div className="bg-white p-4 rounded-lg border border-blue-100 mb-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-700">
                        {poll.results?.totalVotes || 0}
                      </div>
                      <p className="text-blue-600 text-sm">Iš viso balsų</p>
                    </div>
                  </div>
                  
                  {poll.results && poll.results.totalVotes > 0 ? (
                    <div className="space-y-5">
                      {poll.results.options.map((option: any) => (
                        <div key={option.option_id} className="space-y-2">
                          <div className="flex justify-between items-center text-sm">
                            <div className="font-medium text-slate-700 max-w-[70%] truncate" title={option.option_text}>
                              {option.option_text}
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-blue-700 font-medium">{option.vote_count}</span>
                              <Badge className="bg-blue-50 text-blue-700 border-blue-200">
                                {option.percentage}%
                              </Badge>
                            </div>
                          </div>
                          <div className="relative w-full">
                            <Progress
                              value={option.percentage}
                              className="h-2 bg-blue-100 rounded-full"
                              indicatorClassName="bg-blue-600 rounded-full"
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <div className="bg-blue-50 p-3 rounded-full mb-3">
                        <BarChart3 className="h-6 w-6 text-blue-400" />
                      </div>
                      <h3 className="text-sm font-medium text-blue-700 mb-1">Nėra balsų</h3>
                      <p className="text-blue-600 text-xs">
                        Kol kas niekas nebalsavo šioje apklausoje
                      </p>
                    </div>
                  )}
                  
                  {poll.status === "active" && (
                    <div className="bg-blue-50 p-3 rounded-md mt-6 text-sm text-blue-700">
                      <p>
                        <span className="font-medium">Pastaba:</span> Apklausos rezultatai 
                        atsinaujina realiu laiku, kai naudotojai balsuoja.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
            
            {poll.status === "active" && (
              <div className="p-1 bg-gradient-to-br from-amber-100 to-transparent rounded-lg">
                <Card className="border border-amber-200 shadow-sm">
                  <CardHeader className="pb-2">
                    <div className="flex items-center">
                      <Settings className="mr-2 h-5 w-5 text-amber-600" />
                      <CardTitle className="text-lg text-amber-800">Veiksmai</CardTitle>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-4">
                    <Button 
                      onClick={() => {
                        // Implement poll closing functionality
                        router.push('/dashboard/admin/polls');
                      }}
                      className="w-full bg-amber-600 hover:bg-amber-700 text-white"
                    >
                      Užbaigti apklausą
                    </Button>
                    
                    <p className="text-amber-600 text-sm mt-3">
                      Užbaigus apklausą, gyventojai nebegalės balsuoti, 
                      bet galės peržiūrėti galutinius rezultatus.
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}
            
            {poll.status === "draft" && (
              <div className="p-1 bg-gradient-to-br from-green-100 to-transparent rounded-lg">
                <Card className="border border-green-200 shadow-sm">
                  <CardHeader className="pb-2">
                    <div className="flex items-center">
                      <Settings className="mr-2 h-5 w-5 text-green-600" />
                      <CardTitle className="text-lg text-green-800">Veiksmai</CardTitle>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-4">
                    <Button 
                      onClick={() => {
                        // Implement poll activation functionality
                        router.push('/dashboard/admin/polls');
                      }}
                      className="w-full bg-green-600 hover:bg-green-700 text-white"
                    >
                      Aktyvuoti apklausą
                    </Button>
                    
                    <p className="text-green-600 text-sm mt-3">
                      Aktyvavus apklausą, ji bus matoma gyventojams ir jie galės pradėti balsuoti.
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>

        {/* Voter Responses Section */}
        <div className="p-1 bg-gradient-to-br from-purple-100 to-transparent rounded-lg">
          <Card className="border border-purple-200 shadow-sm">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <User className="mr-2 h-5 w-5 text-purple-600" />
                  <CardTitle className="text-xl text-slate-800">Balsavimo detalės</CardTitle>
                </div>
                <div className="flex items-center gap-2">
                  <p className="text-sm text-slate-600">Įrašai per puslapį:</p>
                  <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>
                    <SelectTrigger className="w-16 h-8">
                      <SelectValue placeholder="10" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <CardDescription>
                Gyventojų balsavimo detalės, viso: {totalResponses}
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              {isLoadingResponses ? (
                <div className="py-8 flex justify-center">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '0s' }}></div>
                    <div className="w-3 h-3 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-3 h-3 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                </div>
              ) : voterResponses.length > 0 ? (
                <>
                  <div className="rounded-md border overflow-hidden">
                    <Table>
                      <TableHeader className="bg-slate-50">
                        <TableRow>
                          <TableHead className="w-12 text-center">#</TableHead>
                          <TableHead>Gyventojas</TableHead>
                          <TableHead>Buto/Namo informacija</TableHead>
                          <TableHead>Pasirinkimas</TableHead>
                          <TableHead className="w-32">Laikas</TableHead>
                          <TableHead>Paaiškinimas</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {voterResponses.map((response, index) => (
                          <TableRow key={response.id} className={index % 2 === 0 ? 'bg-white' : 'bg-slate-50'}>
                            <TableCell className="text-center font-medium text-sm text-slate-500">
                              {(currentPage - 1) * pageSize + index + 1}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <div className="h-8 w-8 rounded-full bg-purple-100 text-purple-700 flex items-center justify-center text-sm font-medium">
                                  {response.userName ? response.userName.charAt(0) : '?'}
                                </div>
                                <span className="font-medium">{response.userName || 'Nežinomas'}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="text-sm">
                                {response.houseInfo ? (
                                  <div className="flex flex-col">
                                    <span>{response.houseInfo}</span>
                                    {response.flatInfo && (
                                      <span className="text-slate-500">Butas: {response.flatInfo}</span>
                                    )}
                                  </div>
                                ) : (
                                  <span className="text-slate-500">Nenurodyta</span>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <div className="h-2 w-2 rounded-full bg-blue-600 mr-2"></div>
                                <span>{response.optionText}</span>
                              </div>
                            </TableCell>
                            <TableCell className="text-sm text-slate-600">
                              {format(new Date(response.createdAt), "yyyy-MM-dd HH:mm")}
                            </TableCell>
                            <TableCell>
                              {response.explanation ? (
                                <div className="max-w-md">
                                  <p className="text-sm text-slate-700 whitespace-pre-wrap bg-slate-50 p-2 rounded-md border border-slate-200">
                                    {response.explanation}
                                  </p>
                                </div>
                              ) : (
                                <span className="text-sm text-slate-400 italic">Nėra</span>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                  
                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="mt-6">
                      <Pagination>
                        <PaginationContent>
                          <PaginationItem>
                            <PaginationPrevious 
                              onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
                              className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                            />
                          </PaginationItem>
                          
                          {getPageNumbers().map((page, i) => (
                            page === 'ellipsis1' || page === 'ellipsis2' ? (
                              <PaginationItem key={`ellipsis-${i}`}>
                                <PaginationEllipsis />
                              </PaginationItem>
                            ) : (
                              <PaginationItem key={page}>
                                <PaginationLink
                                  isActive={page === currentPage}
                                  onClick={() => handlePageChange(page as number)}
                                  className="cursor-pointer"
                                >
                                  {page}
                                </PaginationLink>
                              </PaginationItem>
                            )
                          ))}
                          
                          <PaginationItem>
                            <PaginationNext 
                              onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
                              className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                            />
                          </PaginationItem>
                        </PaginationContent>
                      </Pagination>
                    </div>
                  )}
                </>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="bg-purple-100 p-3 rounded-full mb-3">
                    <User className="h-6 w-6 text-purple-400" />
                  </div>
                  <h3 className="text-lg font-medium text-slate-800 mb-1">Nėra balsavimo duomenų</h3>
                  <p className="text-slate-600 max-w-md">
                    Kol kas niekas nebalsavo šioje apklausoje arba nepavyko gauti balsavimo duomenų.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
} 