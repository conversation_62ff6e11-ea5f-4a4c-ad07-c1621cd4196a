"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, <PERSON>cil, Trash, Eye, ListFilter, Clock, CheckCircle2, BarChart3, Search, Filter, Settings, AlertCircle, Calendar } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import Link from "next/link";
import { toast } from "sonner";
import { formatDate, cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { PollCard } from "@/components/polls/poll-card";

interface Poll {
  id: number;
  title: string;
  description: string;
  status: "draft" | "active" | "closed";
  start_date: string | null;
  end_date: string | null;
  created_at: string;
  created_by_name: string;
  options: Array<{
    id: number;
    option_text: string;
    display_order: number;
  }>;
  results?: {
    totalVotes: number;
    options: Array<{
      option_id: number;
      option_text: string;
      vote_count: number;
      percentage: number;
    }>;
  };
}

export default function AdminPollsPage() {
  const router = useRouter();
  const [polls, setPolls] = useState<Poll[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [pollToDelete, setPollToDelete] = useState<Poll | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [pollToClose, setPollToClose] = useState<Poll | null>(null);
  const [isClosing, setIsClosing] = useState(false);
  const [isActivating, setIsActivating] = useState(false);
  const [activatingPollId, setActivatingPollId] = useState<number | null>(null);

  useEffect(() => {
    fetchPolls();
  }, []);

  useEffect(() => {
    filterPolls();
  }, [activeTab, polls, searchTerm]);

  const fetchPolls = async () => {
    setIsLoading(true);
    try {
      const res = await fetch(`/api/polls`);
      if (!res.ok) throw new Error("Failed to fetch polls");
      const data = await res.json();
      setPolls(data);
    } catch (error) {
      console.error("Error fetching polls:", error);
      toast.error("Failed to load polls");
    } finally {
      setIsLoading(false);
    }
  };

  const filterPolls = () => {
    let filtered = [...polls];

    // Filter by status
    if (activeTab !== "all") {
      filtered = filtered.filter((poll) => poll.status === activeTab);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (poll) =>
          poll.title.toLowerCase().includes(term) ||
          poll.description.toLowerCase().includes(term)
      );
    }

    return filtered;
  };

  const handleDeletePoll = async () => {
    if (!pollToDelete) return;

    setIsDeleting(true);
    try {
      const res = await fetch(`/api/polls?id=${pollToDelete.id}`, {
        method: "DELETE",
      });
      
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.error || "Failed to delete poll");
      }
      
      setPolls(polls.filter((poll) => poll.id !== pollToDelete.id));
      toast.success("Poll deleted successfully");
    } catch (error) {
      console.error("Error deleting poll:", error);
      toast.error("Failed to delete poll");
    } finally {
      setIsDeleting(false);
      setPollToDelete(null);
    }
  };

  const handleClosePoll = async () => {
    if (!pollToClose) return;

    setIsClosing(true);
    try {
      const response = await fetch(`/api/polls/close?id=${pollToClose.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          endDate: pollToClose.end_date || new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to close poll");
      }
      
      // Update the poll in the local state
      const updatedPolls = polls.map(p => 
        p.id === pollToClose.id ? { ...p, status: "closed" as const } : p
      );
      
      setPolls(updatedPolls);
      toast.success("Apklausa uždaryta sėkmingai");
    } catch (error) {
      console.error("Error closing poll:", error);
      toast.error(error instanceof Error ? error.message : "Nepavyko uždaryti apklausos");
    } finally {
      setIsClosing(false);
      setPollToClose(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "draft":
        return <Badge variant="outline" className="bg-slate-100 text-slate-700 hover:bg-slate-200 border-slate-200">Juodraštis</Badge>;
      case "active":
        return <Badge className="bg-green-100 hover:bg-green-200 text-green-800 border-green-200">Aktyvi</Badge>;
      case "closed":
        return <Badge variant="destructive" className="bg-red-100 hover:bg-red-200 text-red-800 border-red-200">Uždaryta</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const activatePoll = async (pollId: number) => {
    try {
      setIsActivating(true);
      setActivatingPollId(pollId);
      
      const response = await fetch(`/api/admin/polls/${pollId}/activate`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Klaida aktyvuojant apklausą');
      }
      
      // Update local polls data with correct typing
      const updatedPolls = polls.map(poll => 
        poll.id === pollId 
          ? { ...poll, status: 'active' as 'draft' | 'active' | 'closed', start_date: new Date().toISOString() } 
          : poll
      );
      
      setPolls(updatedPolls);
      toast.success('Apklausa sėkmingai aktyvuota');
    } catch (error) {
      console.error('Error activating poll:', error);
      toast.error(error instanceof Error ? error.message : 'Nepavyko aktyvuoti apklausos');
    } finally {
      setIsActivating(false);
      setActivatingPollId(null);
    }
  };

  const filteredPolls = filterPolls();

  // Get counts for tabs
  const draftCount = polls.filter(poll => poll.status === "draft").length;
  const activeCount = polls.filter(poll => poll.status === "active").length;
  const closedCount = polls.filter(poll => poll.status === "closed").length;
  const totalCount = polls.length;

  return (
    <>
      <Breadcrumb 
        items={[
          { href: "/dashboard", label: "Valdymo skydelis" },
          { href: "/dashboard/admin", label: "Administravimas" },
          { href: "/dashboard/admin/polls", label: "Apklausos", isLast: true }
        ]}
        className="mb-6"
      />
      
      <div className="space-y-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-gradient-to-r from-indigo-50 to-white p-6 rounded-lg border border-indigo-100 shadow-sm">
          <div>
            <h1 className="text-3xl font-bold text-indigo-900 tracking-tight">Apklausų valdymas</h1>
            <p className="text-indigo-700 mt-1">Sukurkite, redaguokite ir analizuokite gyventojų apklausas</p>
          </div>
          
          <Button 
            onClick={() => router.push("/dashboard/admin/polls/new")}
            className="bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800"
          >
            <Plus className="mr-2 h-4 w-4" />
            Nauja apklausa
          </Button>
        </div>
        
        {searchTerm && (
          <div className="p-1 bg-gradient-to-br from-amber-100 to-transparent rounded-lg">
            <div className="flex items-center justify-between bg-white px-4 py-3 rounded-lg border border-amber-100">
              <div className="flex items-center">
                <Filter className="h-5 w-5 mr-2 text-amber-500" />
                <p className="text-amber-800">
                  Rodomi rezultatai pagal paiešką: <span className="font-medium">"{searchTerm}"</span>
                </p>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setSearchTerm("")}
                className="text-amber-800 border-amber-300 hover:bg-amber-100 hover:text-amber-900"
              >
                Išvalyti
              </Button>
            </div>
          </div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="p-1 bg-gradient-to-br from-slate-100 to-transparent rounded-lg">
            <Card className={cn(
              "border border-slate-200 h-full hover:border-indigo-200 transition-colors",
              activeTab === "all" ? "bg-slate-50" : "bg-white" 
            )}>
              <CardContent className="p-4 flex justify-between items-center cursor-pointer h-full" onClick={() => setActiveTab("all")}>
                <div>
                  <h3 className="font-semibold text-slate-800">Visos apklausos</h3>
                  <p className="text-slate-500 text-sm">Administruokite visas</p>
                </div>
                <div className="flex items-center">
                  <div className="bg-slate-100 w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <ListFilter className="h-5 w-5 text-slate-600" />
                  </div>
                  <span className="text-2xl font-bold text-slate-700">{totalCount}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="p-1 bg-gradient-to-br from-amber-100 to-transparent rounded-lg">
            <Card className={cn(
              "border border-amber-200 h-full hover:border-amber-300 transition-colors",
              activeTab === "draft" ? "bg-amber-50" : "bg-white" 
            )}>
              <CardContent className="p-4 flex justify-between items-center cursor-pointer h-full" onClick={() => setActiveTab("draft")}>
                <div>
                  <h3 className="font-semibold text-amber-800">Juodraščiai</h3>
                  <p className="text-amber-600 text-sm">Ruošiamos apklausos</p>
                </div>
                <div className="flex items-center">
                  <div className="bg-amber-100 w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <Settings className="h-5 w-5 text-amber-600" />
                  </div>
                  <span className="text-2xl font-bold text-amber-600">{draftCount}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="p-1 bg-gradient-to-br from-green-100 to-transparent rounded-lg">
            <Card className={cn(
              "border border-green-200 h-full hover:border-green-300 transition-colors",
              activeTab === "active" ? "bg-green-50" : "bg-white" 
            )}>
              <CardContent className="p-4 flex justify-between items-center cursor-pointer h-full" onClick={() => setActiveTab("active")}>
                <div>
                  <h3 className="font-semibold text-green-800">Aktyvios</h3>
                  <p className="text-green-600 text-sm">Vykstančios apklausos</p>
                </div>
                <div className="flex items-center">
                  <div className="bg-green-100 w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <CheckCircle2 className="h-5 w-5 text-green-600" />
                  </div>
                  <span className="text-2xl font-bold text-green-600">{activeCount}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="p-1 bg-gradient-to-br from-blue-100 to-transparent rounded-lg">
            <Card className={cn(
              "border border-blue-200 h-full hover:border-blue-300 transition-colors",
              activeTab === "closed" ? "bg-blue-50" : "bg-white" 
            )}>
              <CardContent className="p-4 flex justify-between items-center cursor-pointer h-full" onClick={() => setActiveTab("closed")}>
                <div>
                  <h3 className="font-semibold text-blue-800">Užbaigtos</h3>
                  <p className="text-blue-600 text-sm">Archyvuoti rezultatai</p>
                </div>
                <div className="flex items-center">
                  <div className="bg-blue-100 w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                  </div>
                  <span className="text-2xl font-bold text-blue-600">{closedCount}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        <div className="p-1 bg-gradient-to-br from-indigo-100 to-transparent rounded-lg">
          <Card className="border border-slate-200 shadow-sm">
            <CardHeader className="pb-3">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div className="flex items-center">
                  <Settings className="mr-2 h-5 w-5 text-indigo-600" />
                  <CardTitle className="text-xl text-slate-800">
                    {activeTab === "all" ? "Visos apklausos" : 
                     activeTab === "draft" ? "Juodraščiai" :
                     activeTab === "active" ? "Aktyvios apklausos" : "Užbaigtos apklausos"}
                  </CardTitle>
                </div>
                
                <div className="w-full sm:w-auto sm:min-w-[280px]">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-500" />
                    <Input
                      type="search"
                      placeholder="Ieškoti apklausų..."
                      className="pl-9 bg-white focus-visible:ring-indigo-500"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="border rounded-lg p-4 animate-pulse bg-slate-50">
                      <div className="flex items-start justify-between">
                        <div className="space-y-3 w-3/4">
                          <Skeleton className="h-6 w-1/2" />
                          <Skeleton className="h-4 w-3/4" />
                          <div className="flex gap-2 pt-2">
                            <Skeleton className="h-8 w-16 rounded-md" />
                            <Skeleton className="h-8 w-16 rounded-md" />
                          </div>
                        </div>
                        <Skeleton className="h-10 w-24 rounded-md" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : filteredPolls.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="bg-slate-100 p-3 rounded-full mb-3">
                    <AlertCircle className="h-8 w-8 text-slate-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-slate-700 mb-2">Nerasta apklausų</h3>
                  <p className="text-slate-600 max-w-md">
                    {searchTerm 
                      ? "Pagal pasirinktus filtrus nerasta jokių apklausų. Bandykite pakeisti filtrus."
                      : activeTab === "all" 
                        ? "Šiuo metu nėra sukurtų apklausų. Sukurkite naują apklausą norėdami pradėti."
                        : activeTab === "draft" 
                          ? "Nėra apklausų juodraščių. Sukurkite naują apklausą."
                          : activeTab === "active" 
                            ? "Nėra aktyvių apklausų. Aktyvuokite apklausą."
                            : "Nėra užbaigtų apklausų."}
                  </p>
                  {filteredPolls.length === 0 && !searchTerm && activeTab === "all" && (
                    <Button 
                      onClick={() => router.push("/dashboard/admin/polls/new")}
                      className="mt-4 bg-indigo-600 hover:bg-indigo-700"
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Sukurti apklausą
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredPolls.map((poll) => (
                    <Card key={poll.id} className="border border-slate-200 hover:border-indigo-200 transition-colors overflow-hidden">
                      <div className={cn(
                        "h-1.5 w-full",
                        poll.status === "draft" 
                          ? "bg-gradient-to-r from-amber-500 to-amber-600" 
                          : poll.status === "active" 
                            ? "bg-gradient-to-r from-green-500 to-emerald-600" 
                            : "bg-gradient-to-r from-blue-500 to-indigo-600"
                      )}></div>
                      <CardContent className="p-4">
                        <div className="flex flex-col md:flex-row md:items-start justify-between gap-4">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium text-lg">{poll.title}</h3>
                              {getStatusBadge(poll.status)}
                            </div>
                            <p className="text-slate-600 text-sm line-clamp-2">{poll.description}</p>
                            <div className="flex flex-wrap gap-4 text-xs text-slate-500 pt-1">
                              {poll.created_at && (
                                <div className="flex items-center">
                                  <Calendar className="h-3.5 w-3.5 mr-1 text-slate-400" />
                                  Sukurta: {formatDate(poll.created_at)}
                                </div>
                              )}
                              {poll.start_date && (
                                <div className="flex items-center">
                                  <Clock className="h-3.5 w-3.5 mr-1 text-slate-400" />
                                  Pradėta: {formatDate(poll.start_date)}
                                </div>
                              )}
                              {poll.end_date && (
                                <div className="flex items-center">
                                  <Clock className="h-3.5 w-3.5 mr-1 text-slate-400" />
                                  Baigsis: {formatDate(poll.end_date)}
                                </div>
                              )}
                              {poll.results && (
                                <div className="flex items-center">
                                  <BarChart3 className="h-3.5 w-3.5 mr-1 text-slate-400" />
                                  Balsų: {poll.results.totalVotes}
                                </div>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex flex-wrap gap-2 mt-3 md:mt-0">
                            {poll.status === "draft" && (
                              <Button 
                                size="sm" 
                                onClick={() => activatePoll(poll.id)}
                                disabled={isActivating && activatingPollId === poll.id}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                {isActivating && activatingPollId === poll.id ? (
                                  <>
                                    <div className="w-4 h-4 rounded-full border-2 border-white border-t-transparent animate-spin mr-2"></div>
                                    Aktyvuojama...
                                  </>
                                ) : (
                                  <>Aktyvuoti</>
                                )}
                              </Button>
                            )}
                            
                            {poll.status === "active" && (
                              <Button 
                                size="sm" 
                                variant="outline"
                                onClick={() => setPollToClose(poll)}
                                className="border-amber-500 text-amber-700 hover:bg-amber-50"
                              >
                                Užbaigti
                              </Button>
                            )}
                            
                            <Button 
                              size="sm" 
                              variant={poll.status === "closed" ? "default" : "outline"}
                              onClick={() => router.push(`/dashboard/admin/polls/${poll.id}`)}
                              className={poll.status === "closed" ? "bg-indigo-600 hover:bg-indigo-700" : ""}
                            >
                              <Eye className="mr-1 h-4 w-4" />
                              Peržiūrėti
                            </Button>
                            
                            {poll.status !== "closed" && (
                              <Button 
                                size="sm" 
                                variant="outline"
                                onClick={() => router.push(`/dashboard/admin/polls/${poll.id}/edit`)}
                              >
                                <Pencil className="mr-1 h-4 w-4" />
                                Redaguoti
                              </Button>
                            )}
                            
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                              onClick={() => setPollToDelete(poll)}
                            >
                              <Trash className="mr-1 h-4 w-4" />
                              Ištrinti
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Alert Dialog for Delete Confirmation */}
      <AlertDialog open={!!pollToDelete} onOpenChange={(open) => !open && setPollToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Patvirtinkite ištrynimą</AlertDialogTitle>
            <AlertDialogDescription>
              Ar tikrai norite ištrinti apklausą "{pollToDelete?.title}"? 
              Šis veiksmas negali būti atšauktas ir visi apklausos duomenys bus prarasti.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Atšaukti</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeletePoll}
              disabled={isDeleting}
              className="bg-red-600 text-white hover:bg-red-700"
            >
              {isDeleting ? "Trinama..." : "Ištrinti"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Alert Dialog for Close Poll Confirmation */}
      <AlertDialog open={!!pollToClose} onOpenChange={(open) => !open && setPollToClose(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Patvirtinkite apklausos užbaigimą</AlertDialogTitle>
            <AlertDialogDescription>
              Ar tikrai norite užbaigti apklausą "{pollToClose?.title}"? 
              Užbaigta apklausa nebebus prieinama balsavimui, tačiau rezultatai bus matomi.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setPollToClose(null)}>Atšaukti</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleClosePoll}
              disabled={isClosing}
              className="bg-indigo-600 text-white hover:bg-indigo-700"
            >
              {isClosing ? "Užbaigiama..." : "Užbaigti apklausą"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 