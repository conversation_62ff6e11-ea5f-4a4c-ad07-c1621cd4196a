"use client";

import { useSession } from "@/lib/hooks/use-session";
import { EmergencyContactsCard } from "@/components/dashboard/emergency-contacts";
import { LatestAnnouncementsCard } from "@/components/dashboard/latest-announcements";
import { AdminMessagesWidget } from "@/components/dashboard/admin-messages-widget";
import { AdminPollsWidget } from "@/components/dashboard/admin-polls-widget";
import { ActivePollsCard } from "@/components/dashboard/active-polls";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Skeleton } from "@/components/ui/skeleton";

export default function DashboardPage() {
  const { data: session, status } = useSession();
  
  const isAdmin = session?.user?.role === "super_admin" || session?.user?.role === "editor" || session?.user?.role === "developer";
  
  if (status === 'loading') {
    return (
      <div className="space-y-8">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-64 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }
  
  return (
    <>
      <Breadcrumb 
        items={[
          { href: "/dashboard", label: "Valdymo skydelis", isLast: true }
        ]}
        className="mb-6"
      />
      
      <div className="space-y-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-gradient-to-r from-indigo-50 to-white p-6 rounded-lg border border-indigo-100 shadow-sm">
          <h1 className="text-3xl font-bold text-indigo-900 tracking-tight">Valdymo skydelis</h1>
          <div className="flex items-center gap-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-md border border-indigo-100 shadow-sm">
            <span className="text-sm font-medium text-indigo-700">Sveiki{session?.user?.name ? `, ${session.user.name}` : ''}</span>
          </div>
        </div>
        
        {/* Latest Announcements Card */}
        <div className="grid gap-6">
          <div className="p-1 bg-gradient-to-br from-indigo-100 to-transparent rounded-lg">
            <LatestAnnouncementsCard />
          </div>
        </div>
        
        {/* Polls Section */}
        {isAdmin ? (
          /* Admin Polls Widget - Only visible to admins */
          <div className="grid gap-6">
            <div className="p-1 bg-gradient-to-br from-indigo-100 to-transparent rounded-lg">
              <AdminPollsWidget />
            </div>
          </div>
        ) : (
          /* Active Polls Card - Only visible to regular users */
          <div className="grid gap-6">
            <div className="p-1 bg-gradient-to-br from-indigo-100 to-transparent rounded-lg">
              <ActivePollsCard />
            </div>
          </div>
        )}
        
        {/* Admin Messages Widget - Only visible to admins */}
        {isAdmin && (
          <div className="grid gap-6">
            <div className="p-1 bg-gradient-to-br from-indigo-100 to-transparent rounded-lg">
              <AdminMessagesWidget />
            </div>
          </div>
        )}
        
        {/* Emergency Contacts Card */}
        <div className="grid gap-6">
          <EmergencyContactsCard />
        </div>
      </div>
    </>
  );
}