"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { formatDate, cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PollCard } from "@/components/polls/poll-card";
import { DashboardHeader } from "@/components/dashboard/header";
import { DashboardShell } from "@/components/dashboard/shell";
import { Vote, CheckCircle2, ListFilter, BarChart3, Search, AlertCircle, Filter, Clock, Calendar, XCircle } from "lucide-react";

interface Poll {
  id: number;
  title: string;
  description: string;
  status: "draft" | "active" | "closed";
  created_at: string;
  start_date: string | null;
  end_date: string | null;
  hasVoted?: boolean;
  total_votes?: number;
}

export default function PollsPage() {
  const router = useRouter();
  const [polls, setPolls] = useState<Poll[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("active");

  useEffect(() => {
    async function fetchPolls() {
      try {
        setIsLoading(true);
        
        // Real API call for all environments
        const response = await fetch("/api/polls", {
          credentials: 'include', // Include cookies for auth
          headers: {
            'Accept': 'application/json'
          }
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch polls: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        setPolls(data);
      } catch (error) {
        console.error("Error fetching polls:", error);
        // Show error in toast or UI notification
      } finally {
        setIsLoading(false);
      }
    }

    fetchPolls();
  }, []);

  const filterPolls = () => {
    let filtered = [...polls];

    // Filter by status tab
    if (activeTab !== "all") {
      filtered = filtered.filter((poll) => poll.status === activeTab);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (poll) =>
          poll.title.toLowerCase().includes(term) ||
          poll.description.toLowerCase().includes(term)
      );
    }

    return filtered;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100">
            Aktyvi
          </Badge>
        );
      case "closed":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100">
            Užbaigta
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-slate-50 text-slate-700 border-slate-200 hover:bg-slate-100">
            {status}
          </Badge>
        );
    }
  };

  const filteredPolls = filterPolls();
  
  // Get counts for tabs
  const activeCount = polls.filter(poll => poll.status === "active").length;
  const closedCount = polls.filter(poll => poll.status === "closed").length;
  const totalCount = polls.length;
  const votedCount = polls.filter(poll => poll.hasVoted).length;

  return (
    <DashboardShell>
      <DashboardHeader heading="Apklausos" description="Dalyvaukite rajono gyventojų apklausose">
        <Button 
          variant="ghost" 
          className="flex items-center gap-2 bg-indigo-50 text-indigo-700 hover:bg-indigo-100 hover:text-indigo-800">
          <BarChart3 className="h-4 w-4" />
          <span>Jūs balsavote: <span className="font-bold">{votedCount}</span></span>
        </Button>
      </DashboardHeader>
      
      <div className="space-y-8">
        {searchTerm && (
          <div className="p-1 bg-gradient-to-br from-amber-100 to-transparent rounded-lg">
            <div className="flex items-center justify-between bg-white px-4 py-3 rounded-lg border border-amber-100">
              <div className="flex items-center">
                <Filter className="h-5 w-5 mr-2 text-amber-500" />
                <p className="text-amber-800">
                  Rodomi rezultatai pagal paiešką: <span className="font-medium">"{searchTerm}"</span>
                </p>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setSearchTerm("")}
                className="text-amber-800 border-amber-300 hover:bg-amber-100 hover:text-amber-900"
              >
                Išvalyti
              </Button>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-1 bg-gradient-to-br from-slate-100 to-transparent rounded-lg">
            <Card className={cn(
              "border border-slate-200 h-full hover:border-indigo-200 transition-colors",
              activeTab === "all" ? "bg-slate-50" : "bg-white" 
            )}>
              <CardContent className="p-4 flex justify-between items-center cursor-pointer h-full" onClick={() => setActiveTab("all")}>
                <div>
                  <h3 className="font-semibold text-slate-800">Visos apklausos</h3>
                  <p className="text-slate-500 text-sm">Visos prieinamos</p>
                </div>
                <div className="flex items-center">
                  <div className="bg-slate-100 w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <ListFilter className="h-5 w-5 text-slate-600" />
                  </div>
                  <span className="text-2xl font-bold text-slate-700">{totalCount}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="p-1 bg-gradient-to-br from-green-100 to-transparent rounded-lg">
            <Card className={cn(
              "border border-green-200 h-full hover:border-green-300 transition-colors",
              activeTab === "active" ? "bg-green-50" : "bg-white" 
            )}>
              <CardContent className="p-4 flex justify-between items-center cursor-pointer h-full" onClick={() => setActiveTab("active")}>
                <div>
                  <h3 className="font-semibold text-green-800">Aktyvios</h3>
                  <p className="text-green-600 text-sm">Vykstančios apklausos</p>
                </div>
                <div className="flex items-center">
                  <div className="bg-green-100 w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <CheckCircle2 className="h-5 w-5 text-green-600" />
                  </div>
                  <span className="text-2xl font-bold text-green-600">{activeCount}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="p-1 bg-gradient-to-br from-blue-100 to-transparent rounded-lg">
            <Card className={cn(
              "border border-blue-200 h-full hover:border-blue-300 transition-colors",
              activeTab === "closed" ? "bg-blue-50" : "bg-white" 
            )}>
              <CardContent className="p-4 flex justify-between items-center cursor-pointer h-full" onClick={() => setActiveTab("closed")}>
                <div>
                  <h3 className="font-semibold text-blue-800">Užbaigtos</h3>
                  <p className="text-blue-600 text-sm">Archyvuoti rezultatai</p>
                </div>
                <div className="flex items-center">
                  <div className="bg-blue-100 w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                  </div>
                  <span className="text-2xl font-bold text-blue-600">{closedCount}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        <div className="p-1 bg-gradient-to-br from-indigo-100 to-transparent rounded-lg">
          <Card className="border border-slate-200 shadow-sm">
            <CardHeader className="pb-3">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div className="flex items-center">
                  <Vote className="mr-2 h-5 w-5 text-indigo-600" />
                  <CardTitle className="text-xl text-slate-800">
                    {activeTab === "all" ? "Visos apklausos" :
                     activeTab === "active" ? "Aktyvios apklausos" : "Užbaigtos apklausos"}
                  </CardTitle>
                </div>
                
                <div className="w-full sm:w-auto sm:min-w-[280px]">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-500" />
                    <Input
                      type="search"
                      placeholder="Ieškoti apklausų..."
                      className="pl-9 bg-white focus-visible:ring-indigo-500"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="border rounded-lg p-4 animate-pulse bg-slate-50">
                      <div className="flex items-start justify-between">
                        <div className="space-y-3 w-3/4">
                          <Skeleton className="h-6 w-1/2" />
                          <Skeleton className="h-4 w-3/4" />
                          <div className="flex gap-2 pt-2">
                            <Skeleton className="h-8 w-16 rounded-md" />
                          </div>
                        </div>
                        <Skeleton className="h-10 w-24 rounded-md" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : filteredPolls.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="bg-slate-100 p-3 rounded-full mb-3">
                    <AlertCircle className="h-8 w-8 text-slate-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-slate-700 mb-2">Nerasta apklausų</h3>
                  <p className="text-slate-600 max-w-md">
                    {searchTerm 
                      ? "Pagal pasirinktus filtrus nerasta jokių apklausų. Bandykite pakeisti filtrus."
                      : activeTab === "all" 
                        ? "Šiuo metu nėra sukurtų apklausų."
                        : activeTab === "active" 
                          ? "Šiuo metu nėra aktyvių apklausų."
                          : "Nėra užbaigtų apklausų."}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredPolls.map((poll) => (
                    <Card key={poll.id} className={cn(
                      "border transition-colors overflow-hidden",
                      poll.hasVoted 
                        ? "border-indigo-200 hover:border-indigo-300 bg-indigo-50/30" 
                        : poll.status === "closed"
                          ? "border-slate-200 hover:border-slate-300 bg-slate-50/50"
                          : "border-slate-200 hover:border-indigo-200"
                    )}>
                      <div className={cn(
                        "h-1.5 w-full",
                        poll.hasVoted 
                          ? "bg-gradient-to-r from-indigo-500 to-purple-600"
                          : poll.status === "active" 
                            ? "bg-gradient-to-r from-green-500 to-emerald-600"
                            : "bg-gradient-to-r from-slate-500 to-slate-600"
                      )}></div>
                      <CardContent className="p-4">
                        <div className="flex flex-col md:flex-row md:items-start justify-between gap-4">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2 flex-wrap">
                              <h3 className="font-medium text-lg">{poll.title}</h3>
                              {getStatusBadge(poll.status)}
                              {poll.hasVoted ? (
                                <Badge className="bg-indigo-100 text-indigo-700 border-indigo-200 hover:bg-indigo-200">
                                  <CheckCircle2 className="h-3 w-3 mr-1" />
                                  Jūs balsavote
                                </Badge>
                              ) : poll.status === "closed" ? (
                                <Badge variant="outline" className="bg-slate-100 text-slate-700 border-slate-200">
                                  <XCircle className="h-3 w-3 mr-1 text-slate-500" />
                                  Nebalsavote
                                </Badge>
                              ) : null}
                            </div>
                            <p className="text-slate-600 text-sm line-clamp-2">{poll.description}</p>
                            <div className="flex flex-wrap gap-4 text-xs text-slate-500 pt-1">
                              {poll.start_date && (
                                <div className="flex items-center">
                                  <Clock className="h-3.5 w-3.5 mr-1 text-slate-400" />
                                  Pradėta: {formatDate(poll.start_date)}
                                </div>
                              )}
                              {poll.end_date && (
                                <div className="flex items-center">
                                  <Clock className="h-3.5 w-3.5 mr-1 text-slate-400" />
                                  Baigsis: {formatDate(poll.end_date)}
                                </div>
                              )}
                              {poll.total_votes !== undefined && (
                                <div className="flex items-center">
                                  <BarChart3 className="h-3.5 w-3.5 mr-1 text-slate-400" />
                                  Balsų: {poll.total_votes}
                                </div>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex items-center mt-3 md:mt-0">
                            <Button 
                              size="sm"
                              onClick={() => router.push(`/dashboard/polls/${poll.id}`)}
                              variant={poll.status === "active" && !poll.hasVoted ? "default" : "outline"}
                              className={cn(
                                poll.status === "active" && !poll.hasVoted 
                                  ? "bg-indigo-600 hover:bg-indigo-700" 
                                  : "",
                                poll.hasVoted 
                                  ? "border-indigo-300 text-indigo-700 hover:bg-indigo-100"
                                  : poll.status === "closed" && !poll.hasVoted
                                    ? "border-slate-300 text-slate-700"
                                    : ""
                              )}
                            >
                              {poll.status === "active" && !poll.hasVoted 
                                ? "Balsuoti" 
                                : poll.hasVoted
                                  ? "Peržiūrėti balsą"
                                  : "Peržiūrėti rezultatus"}
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardShell>
  );
} 