"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { format } from "date-fns";
import { 
  ChevronLeft, 
  BarChart3, 
  Calendar, 
  Clock, 
  CheckCircle2, 
  User, 
  Info,
  ArrowLeft,
  CheckCircle,
  XCircle,
  MessageCircle,
  Vote
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { RadioGroup, RadioItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { useSession } from "@/lib/hooks/use-session";
import { DashboardHeader } from "@/components/dashboard/header";
import { DashboardShell } from "@/components/dashboard/shell";

// Status badges with appropriate colors
const statusBadges = {
  active: <Badge className="bg-green-100 text-green-800 border-green-200 hover:bg-green-200">Aktyvi</Badge>,
  closed: <Badge className="bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200">Uždaryta</Badge>,
};

export default function PollDetailPage({ params }: { params: any }) {
  // Unwrap params using React.use()
  const resolvedParams = use(params) as { id: string };
  const pollId = resolvedParams.id;
  
  const router = useRouter();
  const { data: session } = useSession();
  const [poll, setPoll] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [explanation, setExplanation] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const fetchPoll = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/polls?id=${pollId}`);
        if (!response.ok) {
          throw new Error("Nepavyko gauti apklausos duomenų");
        }

        const data = await response.json();
        setPoll(data);
        
        // Set selected option if user has already voted
        if (data.userResponse) {
          setSelectedOption(data.userResponse.option_id);
          setExplanation(data.userResponse.explanation || "");
        }
      } catch (error) {
        console.error("Error fetching poll:", error);
        toast.error("Nepavyko įkelti apklausos duomenų");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPoll();
  }, [pollId]);

  const handleVote = async () => {
    if (!selectedOption) {
      toast.error("Pasirinkite variantą");
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/polls/responses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pollId: parseInt(pollId),
          optionId: selectedOption,
          explanation: explanation.trim() || null
        }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Klaida balsuojant");
      }
      
      // Refetch poll data to get updated results
      const updatedPollResponse = await fetch(`/api/polls?id=${pollId}`);
      const updatedPoll = await updatedPollResponse.json();
      setPoll(updatedPoll);
      
      toast.success("Sėkmingai prabalsavote!");
    } catch (error: any) {
      console.error("Error voting:", error);
      toast.error(error.message || "Nepavyko užregistruoti jūsų balso");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <DashboardShell>
        <div className="container mx-auto">
          <div className="h-6 w-1/4 bg-slate-200 rounded animate-pulse mb-8"></div>
          
          <div className="bg-white p-6 rounded-lg border border-slate-200 shadow-sm mb-8 animate-pulse">
            <div className="h-8 w-1/2 bg-slate-200 rounded mb-4"></div>
            <div className="h-4 w-1/4 bg-slate-200 rounded mb-4"></div>
            <div className="h-4 w-full bg-slate-200 rounded mb-2"></div>
            <div className="h-4 w-full bg-slate-200 rounded mb-2"></div>
            <div className="h-4 w-3/4 bg-slate-200 rounded mb-4"></div>
            
            <div className="grid grid-cols-2 gap-4 mt-6">
              <div className="h-12 w-full bg-slate-200 rounded"></div>
              <div className="h-12 w-full bg-slate-200 rounded"></div>
            </div>
          </div>
        </div>
      </DashboardShell>
    );
  }

  if (!poll) {
    return (
      <DashboardShell>
        <div className="container mx-auto">
          <div className="bg-red-50 text-red-700 p-6 rounded-lg border border-red-200">
            <h2 className="text-xl font-bold mb-2">
              Apklausa nerasta
            </h2>
            <p>
              Nepavyko rasti apklausos su ID {pollId}. 
              Patikrinkite, ar teisingai nurodėte apklausos ID, arba grįžkite į apklausų sąrašą.
            </p>
            <Button
              variant="outline"
              onClick={() => router.push("/dashboard/polls")}
              className="mt-4"
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              Grįžti į apklausų sąrašą
            </Button>
          </div>
        </div>
      </DashboardShell>
    );
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return "Nenustatyta";
    return format(new Date(dateString), "yyyy-MM-dd HH:mm");
  };
  
  const hasVoted = !!poll.userResponse;
  const isActive = poll.status === "active";
  const isClosed = poll.status === "closed";
  const canVote = isActive && !hasVoted;
  const showResults = isClosed || (hasVoted && poll.show_results_after_voting);

  return (
    <DashboardShell>
      <Breadcrumb
        items={[
          { href: "/dashboard", label: "Valdymo skydelis" },
          { href: "/dashboard/polls", label: "Apklausos" },
          { href: `/dashboard/polls/${pollId}`, label: poll.title, isLast: true },
        ]}
        className="mb-6"
      />

      <div className="space-y-6">
        {/* Header section */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-gradient-to-r from-indigo-50 to-white p-6 rounded-lg border border-indigo-100 shadow-sm">
          <div>
            <h1 className="text-2xl font-bold text-indigo-900 tracking-tight">{poll.title}</h1>
            <div className="flex items-center gap-3 mt-2">
              {statusBadges[poll.status as keyof typeof statusBadges]}
              <p className="text-indigo-700 text-sm">
                <span className="font-medium">Sukurta:</span> {formatDate(poll.created_at)}
              </p>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push("/dashboard/polls")}
              className="border-slate-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Grįžti į sąrašą
            </Button>
          </div>
        </div>

        {/* Participation status banner */}
        {hasVoted && (
          <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg p-4 flex items-start gap-3">
            <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium">Jūs dalyvavote šioje apklausoje</p>
              <p className="text-sm mt-1">Balsavote {formatDate(poll.userResponse.created_at)}</p>
            </div>
          </div>
        )}

        {isClosed && !hasVoted && (
          <div className="bg-amber-50 border border-amber-200 text-amber-800 rounded-lg p-4 flex items-start gap-3">
            <XCircle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium">Jūs nedalyvavote šioje apklausoje</p>
              <p className="text-sm mt-1">
                Apklausa buvo aktyvi nuo {formatDate(poll.start_date)} iki {formatDate(poll.end_date)}
              </p>
            </div>
          </div>
        )}

        {/* Main Content Grid - Reordered to put results first on mobile */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Left column: Results and vote information */}
          <div className="col-span-1 lg:col-span-5 space-y-6 order-2 lg:order-1">
            {/* User's vote card */}
            {hasVoted && poll.userResponse && (
              <div className="bg-gradient-to-br from-green-100 to-transparent p-1 rounded-lg">
                <Card className="border border-green-200 shadow-sm">
                  <CardHeader className="pb-2">
                    <div className="flex items-center">
                      <CheckCircle2 className="mr-2 h-5 w-5 text-green-600" />
                      <CardTitle className="text-lg text-green-800">Jūsų balsas</CardTitle>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-4">
                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      {poll.options.map((option: any) => {
                        if (option.id === poll.userResponse.option_id) {
                          return (
                            <div key={option.id} className="flex items-start gap-2">
                              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <p className="text-green-700 font-medium break-words">{option.option_text}</p>
                            </div>
                          );
                        }
                        return null;
                      })}
                      
                      {poll.userResponse.explanation && (
                        <div className="mt-3 pt-3 border-t border-green-200">
                          <p className="text-sm text-green-600 font-medium mb-1">Jūsų komentaras:</p>
                          <p className="text-sm text-green-700 whitespace-pre-wrap">{poll.userResponse.explanation}</p>
                        </div>
                      )}
                    </div>
                    
                    <p className="text-green-600 text-sm mt-3">
                      <span className="font-medium">Balsuota:</span> {formatDate(poll.userResponse.created_at)}
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Results card - Now higher in the layout */}
            {showResults && poll.results && (
              <div className="bg-gradient-to-br from-blue-100 to-transparent p-1 rounded-lg">
                <Card className="border border-blue-200 shadow-sm">
                  <CardHeader className="pb-2">
                    <div className="flex items-center">
                      <BarChart3 className="mr-2 h-5 w-5 text-blue-600" />
                      <CardTitle className="text-lg text-blue-800">Rezultatų suvestinė</CardTitle>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-4">
                    <div className="bg-white p-4 rounded-lg border border-blue-100 mb-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-700">
                          {poll.results?.totalVotes || 0}
                        </div>
                        <p className="text-blue-600 text-sm">Iš viso balsų</p>
                      </div>
                    </div>
                    
                    {poll.results.totalVotes > 0 ? (
                      <div className="space-y-5">
                        {poll.results.options.map((option: any) => {
                          const isSelected = option.option_id === poll.userResponse?.option_id;
                          return (
                            <div key={option.option_id} className="space-y-2">
                              <div className="flex justify-between items-center text-sm">
                                <div className={cn(
                                  "font-medium max-w-[70%] truncate flex items-start", 
                                  isSelected ? "text-blue-700" : "text-slate-700"
                                )} title={option.option_text}>
                                  {isSelected && (
                                    <CheckCircle2 className="h-4 w-4 mr-1.5 text-blue-500 flex-shrink-0 mt-0.5" />
                                  )}
                                  <span className="break-words">{option.option_text}</span>
                                </div>
                                <div className="flex items-center gap-2 ml-2 flex-shrink-0">
                                  <span className="text-blue-700 font-medium">{option.vote_count}</span>
                                  <Badge className="bg-blue-50 text-blue-700 border-blue-200">
                                    {option.percentage}%
                                  </Badge>
                                </div>
                              </div>
                              <div className="relative w-full">
                                <Progress
                                  value={option.percentage}
                                  className={cn(
                                    "h-2 rounded-full", 
                                    isSelected ? "bg-blue-100" : "bg-slate-100"
                                  )}
                                  indicatorClassName={cn(
                                    "rounded-full", 
                                    isSelected ? "bg-blue-600" : "bg-blue-400"
                                  )}
                                />
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-8 text-center">
                        <div className="bg-blue-50 p-3 rounded-full mb-3">
                          <BarChart3 className="h-6 w-6 text-blue-400" />
                        </div>
                        <h3 className="text-sm font-medium text-blue-700 mb-1">Nėra balsų</h3>
                        <p className="text-blue-600 text-xs">
                          Kol kas niekas nebalsavo šioje apklausoje
                        </p>
                      </div>
                    )}
                    
                    {poll.status === "active" && (
                      <div className="bg-blue-50 p-3 rounded-md mt-6 text-sm text-blue-700">
                        <p>
                          <span className="font-medium">Pastaba:</span> Apklausos rezultatai 
                          atsinaujina realiu laiku, kai naudotojai balsuoja.
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}
            
            {/* Missed poll notification - small version */}
            {isClosed && !hasVoted && !showResults && (
              <div className="bg-gradient-to-br from-amber-100 to-transparent p-1 rounded-lg">
                <Card className="border border-amber-200 shadow-sm">
                  <CardHeader className="pb-2">
                    <div className="flex items-center">
                      <XCircle className="mr-2 h-5 w-5 text-amber-600" />
                      <CardTitle className="text-lg text-amber-800">Apklausa jau uždaryta</CardTitle>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-4">
                    <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
                      <p className="text-amber-700">
                        Jūs nedalyvavote šioje apklausoje, kuri buvo aktyvi nuo {formatDate(poll.start_date)} iki {formatDate(poll.end_date)}.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>

          {/* Right column: Poll details and voting section */}
          <div className="col-span-1 lg:col-span-7 space-y-6 order-1 lg:order-2">
            <div className="bg-gradient-to-br from-slate-100 to-transparent p-1 rounded-lg">
              <Card className="border border-slate-200 shadow-sm">
                <CardHeader className="pb-3">
                  <div className="flex items-center">
                    <MessageCircle className="mr-2 h-5 w-5 text-indigo-600" />
                    <CardTitle className="text-xl text-slate-800">Apklausa</CardTitle>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <div className="bg-slate-50 p-4 rounded-lg border border-slate-200">
                    <h3 className="font-medium text-slate-800 mb-3">Aprašymas</h3>
                    <p className="text-slate-700 whitespace-pre-wrap">
                      {poll.description}
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-white p-4 rounded-lg border border-slate-200">
                      <div className="flex items-center mb-3">
                        <Calendar className="h-5 w-5 text-indigo-600 mr-2" />
                        <h3 className="font-medium text-slate-800">Laikotarpis</h3>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-slate-500">Pradžia:</span>
                          <span className="font-medium text-slate-800">{formatDate(poll.start_date)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-500">Pabaiga:</span>
                          <span className="font-medium text-slate-800">{formatDate(poll.end_date)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-500">Statusas:</span>
                          <span>{statusBadges[poll.status as keyof typeof statusBadges]}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white p-4 rounded-lg border border-slate-200">
                      <div className="flex items-center mb-3">
                        <User className="h-5 w-5 text-indigo-600 mr-2" />
                        <h3 className="font-medium text-slate-800">Informacija</h3>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-slate-500">Autorius:</span>
                          <span className="font-medium text-slate-800">{poll.created_by_name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-500">Rodyti rezultatus:</span>
                          <span className="font-medium text-slate-800 flex items-center">
                            {poll.show_results_after_voting ? (
                              <>
                                <CheckCircle className="h-4 w-4 text-green-600 mr-1" />
                                Po balsavimo
                              </>
                            ) : (
                              <>
                                <XCircle className="h-4 w-4 text-amber-600 mr-1" />
                                Po apklausos pabaigos
                              </>
                            )}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Voting section */}
                  {canVote && (
                    <div className="bg-white p-5 rounded-lg border border-indigo-200 shadow-sm">
                      <div className="flex items-center mb-4">
                        <Vote className="h-5 w-5 text-indigo-600 mr-2" />
                        <h3 className="font-medium text-slate-800">Jūsų pasirinkimas</h3>
                      </div>
                      
                      <RadioGroup value={selectedOption?.toString()} onValueChange={(value) => setSelectedOption(parseInt(value))}>
                        <div className="space-y-3">
                          {poll.options && poll.options.map((option: any) => (
                            <div 
                              key={option.id} 
                              className="flex items-start space-x-2 p-3 rounded-lg border border-slate-200 hover:border-indigo-200 hover:bg-indigo-50/30 transition-colors"
                            >
                              <RadioItem value={option.id.toString()} id={`option-${option.id}`} />
                              <Label htmlFor={`option-${option.id}`} className="cursor-pointer font-normal text-slate-700">{option.option_text}</Label>
                            </div>
                          ))}
                        </div>
                      </RadioGroup>
                      
                      <div className="mt-5">
                        <Label htmlFor="explanation" className="text-sm mb-2 block font-medium text-slate-700">Komentaras (neprivaloma):</Label>
                        <Textarea 
                          id="explanation" 
                          value={explanation}
                          onChange={(e) => setExplanation(e.target.value)}
                          placeholder="Kodėl pasirinkote šį variantą?"
                          className="resize-none border-slate-200 focus-visible:ring-indigo-500"
                        />
                      </div>
                      
                      <Button 
                        onClick={handleVote} 
                        className="mt-4 bg-indigo-600 hover:bg-indigo-700" 
                        disabled={!selectedOption || isSubmitting}
                      >
                        {isSubmitting ? "Balsuojama..." : "Balsuoti"}
                      </Button>
                    </div>
                  )}
                  
                  {/* Already voted message */}
                  {hasVoted && !showResults && (
                    <div className="flex items-start gap-3 p-4 bg-indigo-50 border border-indigo-100 rounded-lg text-sm">
                      <Info className="h-5 w-5 text-indigo-700 mt-0.5 flex-shrink-0" />
                      <div className="text-indigo-800">
                        <p className="font-medium mb-1">Ačiū už jūsų balsą!</p>
                        <p>Jūs jau balsavote šioje apklausoje. Rezultatai bus matomi tik užbaigus apklausą{poll.end_date ? ` (${formatDate(poll.end_date)})` : ""}. Šis nustatymas padeda užtikrinti, kad ankstesni balsai nedarytų įtakos vėliau balsuojantiems.</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </DashboardShell>
  );
} 