import { Metadata } from "next";
import { getUserEmailPreferences } from "@/lib/email-queue";
import { getCurrentUser } from "@/lib/supabase/auth";
import { redirect } from "next/navigation";
import { EmailPreferencesForm } from "@/components/emails/email-preferences-form";

export const metadata: Metadata = {
  title: "El. pašto nustatymai | DNSB Vakarai",
  description: "Administruokite el. pašto pranešimų nustatymus",
};

export default async function EmailPreferencesPage() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/auth/login");
  }
  
  // Get current email preferences
  const userPreferencesResult = await getUserEmailPreferences(parseInt(user.id));
  
  // Default preferences if not set
  const defaultPreferences = {
    announcement: true,
    poll: true,
    contact_response: true,
    feedback_response: true,
    password_reset: true,
  };
  
  // Merge defaults with user preferences
  const preferences = userPreferencesResult.success 
    ? { ...defaultPreferences, ...userPreferencesResult.preferences }
    : defaultPreferences;
  
  return (
    <div className="container max-w-4xl py-6">
      <h1 className="text-2xl font-bold mb-6">El. pašto pranešimų nustatymai</h1>
      
      <div className="space-y-6">
        <p className="text-muted-foreground">
          Čia galite valdyti, kokio tipo pranešimus norite gauti į savo el. paštą.
          Atkreipkite dėmesį, kad kai kurie pranešimai, pavyzdžiui, slaptažodžio atkūrimo, 
          visada bus siunčiami.
        </p>
        
        <EmailPreferencesForm initialPreferences={preferences} userId={parseInt(user.id)} />
      </div>
    </div>
  );
} 