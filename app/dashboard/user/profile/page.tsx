import { Metadata } from "next";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import ProfileForm from "@/components/user/profile-form";

export const metadata: Metadata = {
  title: "Profilis | DNSB Vakarai",
  description: "Atnaujinkite savo profilio informaciją",
};

export default async function ProfilePage() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/auth/login");
  }

  // Get the user's ID from the session
  const userId = parseInt(user.id);
  
  // Use Supabase client instead of direct database queries
  try {
    const supabase = await createServiceClient();
    
    // Fetch the user data
    const { data: dbUser, error: userError } = await supabase
      .from('users')
      .select(`
        id, 
        username, 
        name, 
        email, 
        phone, 
        is_profile_updated,
        flat_id,
        role
      `)
      .eq('id', userId)
      .single();
    
    if (userError || !dbUser) {
      console.error("User not found in database:", userId, userError);
      return (
        <div className="container mx-auto p-6">
          <div className="p-4 bg-red-50 border border-red-200 rounded-md text-red-700">
            Vartotojo duomenys nerasti sistemoje. Prašome susisiekti su administratoriumi.
          </div>
        </div>
      );
    }
    
    // Initialize address information
    let street = "";
    let houseNumber = "";
    let flatNumber = "";
    
    // If user has a flat_id, fetch address details
    if (dbUser.flat_id) {
      // Get flat information with house and street
      const { data: flatData, error: flatError } = await supabase
        .from('flats')
        .select(`
          number,
          houses!inner (
            name,
            streets!inner (
              name
            )
          )
        `)
        .eq('id', dbUser.flat_id)
        .single();
      
      if (flatData && !flatError) {
        flatNumber = flatData.number || "";
        
        // Type assertion for nested data
        const houses = flatData.houses as any;
        if (houses && houses.name) {
          houseNumber = houses.name;
          
          if (houses.streets && houses.streets.name) {
            street = houses.streets.name;
          }
        }
      }
    }
    
    // Transform the data to match what the form component expects
    const userData = {
      id: dbUser.id,
      username: dbUser.username,
      name: dbUser.name,
      email: dbUser.email,
      phone: dbUser.phone || "",
      street: street,
      houseNumber: houseNumber,
      flatNumber: flatNumber,
      isProfileUpdated: dbUser.is_profile_updated || false
    };
    
    return (
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Profilio informacija</h1>
          <p className="text-muted-foreground mt-2">
            Atnaujinkite savo asmeninę informaciją
          </p>
        </div>
        
        <ProfileForm 
          userId={userId} 
          isNewUser={!dbUser.is_profile_updated} 
          initialData={userData}
        />
      </div>
    );
  } catch (error) {
    console.error("Error fetching user data:", error);
    return (
      <div className="container mx-auto p-6">
        <div className="p-4 bg-red-50 border border-red-200 rounded-md text-red-700">
          Įvyko klaida bandant gauti profilio duomenis. Prašome pabandyti vėliau arba susisiekti su administratoriumi.
        </div>
      </div>
    );
  }
}