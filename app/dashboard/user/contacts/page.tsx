import { Metada<PERSON> } from "next";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/supabase/auth";
import ContactsManager from "@/components/user/contacts-manager";

export const metadata: Metadata = {
  title: "Kontaktai | DNSB Vakarai",
  description: "Tvarkykite savo avarinius kontaktus",
};

export default async function ContactsPage() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/auth/login");
  }
  
  // Admins and editors don't need emergency contacts
  if (user.role === "developer" || user.role === "super_admin" || user.role === "editor") {
    redirect("/dashboard");
  }
  
  // First-time users should complete their profile first
  if (!user.isProfileUpdated) {
    redirect("/dashboard/user/profile");
  }
  
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Kontaktai nelaimės atveju</h1>
        <p className="text-muted-foreground mt-2">
          Pridėkite kontaktinius asmenis, kuriuos galima būtų informuoti nelaimės atveju
        </p>
      </div>
      
      <ContactsManager userId={parseInt(user.id)} />
    </div>
  );
} 