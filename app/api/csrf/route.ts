import { NextRequest, NextResponse } from 'next/server';
import { generateCsrfToken } from '@/lib/csrf';

/**
 * GET /api/csrf - Generates and returns a CSRF token
 * This endpoint is used by client-side code to get a CSRF token
 * for making POST/PUT/DELETE requests to protected endpoints
 */
export async function GET(req: NextRequest) {
  try {
    // Generate a CSRF token
    const token = await generateCsrfToken();
    
    // Return the token
    return NextResponse.json({ token });
  } catch (error) {
    console.error('Error generating CSRF token:', error);
    
    return NextResponse.json(
      { error: 'Failed to generate CSRF token' },
      { status: 500 }
    );
  }
} 