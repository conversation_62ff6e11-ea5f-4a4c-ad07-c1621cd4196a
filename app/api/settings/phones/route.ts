import { NextRequest, NextResponse } from "next/server";
import { getPhoneNumbers } from "@/lib/settings";

/**
 * API endpoint to get phone numbers
 * This is a public API that can be used by client components
 */
export async function GET(req: NextRequest) {
  try {
    const phones = await getPhoneNumbers();
    
    // Transform to key-value map for easier consumption
    const phoneMap: Record<string, string> = {};
    phones.forEach(phone => {
      // Strip the 'phone_' prefix
      const key = phone.key.replace('phone_', '');
      phoneMap[key] = phone.value;
    });
    
    return NextResponse.json(phoneMap);
  } catch (error) {
    console.error("Error getting phone numbers:", error);
    return NextResponse.json(
      { error: "Failed to get phone numbers" },
      { status: 500 }
    );
  }
} 