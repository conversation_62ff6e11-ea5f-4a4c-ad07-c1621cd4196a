import { NextRequest, NextResponse } from "next/server";
import { getContacts, getContactsForDisplay } from "@/lib/contacts";

/**
 * Get all contacts or contacts for a specific display location
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const displayLocation = searchParams.get('display') as 'footer' | 'contacts' | 'emergency' | null;
    
    let contacts;
    if (displayLocation) {
      contacts = await getContactsForDisplay(displayLocation);
    } else {
      contacts = await getContacts();
    }
    
    // Group contacts by category
    const groupedContacts = contacts.reduce((acc, contact) => {
      if (!acc[contact.category]) {
        acc[contact.category] = [];
      }
      acc[contact.category].push(contact);
      return acc;
    }, {} as Record<string, any[]>);
    
    return NextResponse.json({
      contacts: groupedContacts
    });
  } catch (error) {
    console.error("Error fetching contacts:", error);
    return NextResponse.json(
      { error: "Failed to fetch contacts" },
      { status: 500 }
    );
  }
} 