import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";

export async function DELETE(req: NextRequest) {
  try {
    const user = await getCurrentUser();

    // Check if user is authenticated and has admin privileges
    if (!session?.user || user.role !== "super_admin") {
      return NextResponse.json(
        { error: "Neturite teisių atlikti šią operaciją" },
        { status: 403 }
      );
    }

    // Get the list of user IDs to delete from the request body
    const { userIds } = await req.json();

    // Validate that userIds is an array
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        { error: "Nepateiktas vartotojų sąrašas" },
        { status: 400 }
      );
    }

    // Start a transaction for safely deleting multiple users
    const result = await db.transaction(async (tx) => {
      // Delete the users one by one but within a transaction
      for (const userId of userIds) {
        // Delete any related data first (this depends on your database schema)
        // For example, if users have related profiles, delete those first
        // await tx.query(`DELETE FROM user_profiles WHERE user_id = $1`, [userId]);

        // Delete the user
        await tx.query(`DELETE FROM users WHERE id = $1`, [userId]);
      }

      return userIds.length;
    });

    return NextResponse.json({ 
      success: true, 
      message: `${result} vartotojai ištrinti sėkmingai` 
    });
    
  } catch (error) {
    console.error("Error deleting multiple users:", error);
    return NextResponse.json(
      { error: "Įvyko klaida trinant vartotojus" },
      { status: 500 }
    );
  }
} 