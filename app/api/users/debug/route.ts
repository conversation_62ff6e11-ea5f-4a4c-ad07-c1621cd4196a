import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";

// GET /api/users/debug?userId=123
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Only allow admin users
    const isAdmin = ["admin", "super_admin"].includes(user.role);
    if (!isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }
    
    // Get userId from query parameter
    const url = new URL(request.url);
    const userId = url.searchParams.get("userId");
    
    if (!userId) {
      return NextResponse.json({ error: "Missing userId parameter" }, { status: 400 });
    }
    
    console.log(`Debug request for user ID: ${userId}`);
    
    // Get ALL user fields
    const userResult = await db.query(
      `SELECT * FROM users WHERE id = $1`,
      [userId]
    );
    
    if (userResult.rows.length === 0) {
      return NextResponse.json({ 
        error: "User not found" 
      }, { status: 404 });
    }
    
    const userRecord = userResult.rows[0];
    
    // Get flat information if available
    let flat = null;
    let house = null;
    
    if (userRecord.flat_id) {
      const flatResult = await db.query(
        `SELECT * FROM flats WHERE id = $1`,
        [userRecord.flat_id]
      );
      
      if (flatResult.rows.length > 0) {
        flat = flatResult.rows[0];
        
        // Get house information if available
        if (flat.house_id) {
          const houseResult = await db.query(
            `SELECT * FROM houses WHERE id = $1`,
            [flat.house_id]
          );
          
          if (houseResult.rows.length > 0) {
            house = houseResult.rows[0];
          }
        }
      }
    }
    
    return NextResponse.json({
      user: userRecord,
      relationships: {
        flat,
        house
      },
      session: {
        currentUserId: user.id,
        role: user.role
      }
    });
  } catch (error) {
    console.error("Error in debug endpoint:", error);
    return NextResponse.json(
      { error: "Failed to retrieve debug information" },
      { status: 500 }
    );
  }
} 