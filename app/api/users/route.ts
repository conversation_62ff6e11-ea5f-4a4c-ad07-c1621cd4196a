import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { hash } from "bcryptjs";

// Helper function to transform snake_case to camelCase
function snakeToCamel(obj: any) {
  if (obj === null || typeof obj !== "object") return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(snakeToCamel);
  }
  
  return Object.keys(obj).reduce((acc: any, key) => {
    const newKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    acc[newKey] = snakeToCamel(obj[key]);
    return acc;
  }, {});
}

// GET /api/users - List all users
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json(
        { error: "Naudotojas neprisijungęs" },
        { status: 401 }
      );
    }
    
    if (!["developer", "super_admin", "editor"].includes(user.role)) {
      return NextResponse.json(
        { error: "Neturite teisių peržiūrėti naudotojų duomenis" },
        { status: 403 }
      );
    }
    
    // Get query parameters
    const url = new URL(request.url);
    const streetId = url.searchParams.get("streetId");
    const houseId = url.searchParams.get("houseId");
    const flatId = url.searchParams.get("flatId");
    const excludeAdmins = url.searchParams.get("excludeAdmins") === 'true';
    
    try {
      const supabase = await createServiceClient();
      
      // Build query with filters
      let query = supabase
        .from('users')
        .select(`
          id,
          username,
          name,
          email,
          role,
          created_at,
          updated_at,
          is_profile_updated,
          phone,
          flat_id,
          flats!users_flat_id_fkey (
            id,
            number,
            floor,
            house_id,
            houses!flats_house_id_fkey (
              id,
              name,
              address,
              street_id,
              streets!houses_street_id_fkey (
                id,
                name
              )
            )
          )
        `);
      
      // Apply filters
      if (excludeAdmins) {
        query = query.eq('role', 'user');
      }
      
      if (streetId) {
        query = query.eq('flats.houses.street_id', streetId);
      }
      
      if (houseId) {
        query = query.eq('flats.house_id', houseId);
      }
      
      if (flatId) {
        query = query.eq('flat_id', flatId);
      }
      
      // Add ordering
      query = query
        .order('role', { ascending: true })
        .order('name', { ascending: true });
      
      // Execute query
      const { data: usersResult, error } = await query;
      
      if (error) {
        console.error("Error fetching users:", error);
        throw error;
      }
      
      // Transform and enhance the results
      const transformedUsers = (usersResult || []).map(user => {
        const transformed = snakeToCamel(user);
        
        // Add formatted display name
        transformed.displayName = transformed.name;
        
        // Extract nested data
        const flat = user.flats;
        const house = flat?.houses;
        const street = house?.streets;
        
        transformed.flatId = flat?.id || null;
        transformed.flatNumberDb = flat?.number || null;
        transformed.flatNumber = flat?.number || null;
        transformed.floor = flat?.floor || null;
        transformed.houseId = house?.id || null;
        transformed.houseName = house?.name || null;
        transformed.houseAddress = house?.address || null;
        transformed.streetId = street?.id || null;
        transformed.streetName = street?.name || null;
        
        // Create address display string based on available data
        if (transformed.flatId && transformed.houseName && transformed.streetName) {
          transformed.addressDisplay = `${transformed.streetName} ${transformed.houseName} - ${transformed.flatNumberDb}`;
        } else if (transformed.flatId && transformed.houseName) {
          transformed.addressDisplay = `${transformed.houseName} - ${transformed.flatNumberDb}`;
        } else if (transformed.houseName) {
          transformed.addressDisplay = transformed.houseName;
        } else {
          transformed.addressDisplay = "Adresas nepriskirtas";
        }
        
        // Remove nested objects to clean up response
        delete transformed.flats;
        
        return transformed;
      });
      
      return NextResponse.json(transformedUsers);
    } catch (error) {
      console.error("Error fetching users:", error);
      
      // Try simpler query without streets table
      try {
        const supabase = await createServiceClient();
        
        let fallbackQuery = supabase
          .from('users')
          .select(`
            id,
            username,
            name,
            email,
            role,
            created_at,
            updated_at,
            is_profile_updated,
            phone,
            flat_id,
            flats!users_flat_id_fkey (
              id,
              number,
              floor,
              house_id,
              houses!flats_house_id_fkey (
                id,
                name,
                address
              )
            )
          `);
        
        // Apply filters
        if (excludeAdmins) {
          fallbackQuery = fallbackQuery.eq('role', 'user');
        }
        
        if (houseId) {
          fallbackQuery = fallbackQuery.eq('flats.house_id', houseId);
        }
        
        if (flatId) {
          fallbackQuery = fallbackQuery.eq('flat_id', flatId);
        }
        
        // Add ordering
        fallbackQuery = fallbackQuery
          .order('role', { ascending: true })
          .order('name', { ascending: true });
        
        const { data: usersResult, error: fallbackError } = await fallbackQuery;
        
        if (fallbackError) {
          throw fallbackError;
        }
        
        // Simple transform
        const transformedUsers = (usersResult || []).map(user => {
          const transformed = snakeToCamel(user);
          transformed.displayName = transformed.name;
          
          // Extract nested data
          const flat = user.flats;
          const house = flat?.houses;
          
          transformed.flatId = flat?.id || null;
          transformed.flatNumberDb = flat?.number || null;
          transformed.flatNumber = flat?.number || null;
          transformed.houseId = house?.id || null;
          transformed.houseName = house?.name || null;
          
          // Basic address display
          if (transformed.flatId && transformed.houseName) {
            transformed.addressDisplay = `${transformed.houseName} - ${transformed.flatNumberDb}`;
          } else if (transformed.houseName) {
            transformed.addressDisplay = transformed.houseName;
          } else {
            transformed.addressDisplay = "Adresas nepriskirtas";
          }
          
          transformed.streetId = null;
          transformed.streetName = null;
          
          // Remove nested objects
          delete transformed.flats;
          
          return transformed;
        });
        
        return NextResponse.json(transformedUsers);
      } catch (fallbackError) {
        console.error("Fallback query also failed:", fallbackError);
        // Return empty array instead of error to prevent client-side crashes
        return NextResponse.json([]);
      }
    }
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Įvyko klaida gaunant naudotojų duomenis" },
      { status: 500 }
    );
  }
}

// POST /api/users - Create a new user
export async function POST(request: NextRequest) {
  try {
    console.log("====== START: POST /api/users endpoint ======");
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json(
        { error: "Naudotojas neprisijungęs" },
        { status: 401 }
      );
    }
    
    if (user.role !== "super_admin") {
      return NextResponse.json(
        { error: "Tik super administratoriai gali kurti naujus naudotojus" },
        { status: 403 }
      );
    }
    
    // Parse request body and handle potential JSON parsing errors
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error("Failed to parse request body:", parseError);
      return NextResponse.json(
        { error: "Neteisingas užklausos formatas. Pateikite teisingą JSON formatą." },
        { status: 400 }
      );
    }
    
    console.log("POST /api/users received data:", JSON.stringify(body, null, 2));
    
    // Validate required fields
    const requiredFields = ["username", "name", "email", "role", "password"];
    const missingFields = requiredFields.filter(field => !body[field]);
    
    if (missingFields.length > 0) {
      console.error("Missing required fields:", missingFields);
      return NextResponse.json(
        { error: `Trūksta privalomų laukų: ${missingFields.join(", ")}` },
        { status: 400 }
      );
    }
    
    // Log username specifically to ensure it's being processed
    console.log("Username being processed:", body.username);
    
    // EXPLICIT USERNAME VALIDATION - let's make sure it's not empty or just whitespace
    if (!body.username || body.username.trim() === '') {
      console.error("Username is empty or just whitespace");
      return NextResponse.json(
        { error: "Prisijungimo vardas negali būti tuščias" },
        { status: 400 }
      );
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { error: "Neteisingas el. pašto formatas" },
        { status: 400 }
      );
    }
    
    // Validate password length
    if (body.password.length < 8) {
      return NextResponse.json(
        { error: "Slaptažodis turi būti bent 8 simbolių ilgio" },
        { status: 400 }
      );
    }
    
    // Check if user with same email or username already exists
    try {
      const supabase = await createServiceClient();
      
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .or(`email.eq.${body.email},username.eq.${body.username}`)
        .limit(1)
        .single();
      
      if (existingUser) {
        return NextResponse.json(
          { error: "Naudotojas su tokiu el. paštu arba prisijungimo vardu jau egzistuoja" },
          { status: 409 }
        );
      }
    } catch (error) {
      // If error is not 'no rows returned', then it's a real error
      if (error.code !== 'PGRST116') {
        console.error("Database error checking existing user:", error);
        return NextResponse.json(
          { error: "Klaida tikrinant duomenų bazėje, ar vartotojas jau egzistuoja" },
          { status: 500 }
        );
      }
    }
    
    // Hash password
    let hashedPassword;
    try {
      hashedPassword = await hash(body.password, 10);
    } catch (hashError) {
      console.error("Error hashing password:", hashError);
      return NextResponse.json(
        { error: "Klaida užšifruojant slaptažodį" },
        { status: 500 }
      );
    }
    
    // Process boolean values correctly
    const isProfileUpdated = body.is_profile_updated === true || 
                            body.is_profile_updated === "true" || 
                            body.is_profile_updated === "on" ||
                            body.is_profile_updated === 1 ||
                            body.is_profile_updated === "1";
    
    const isTestUser = body.is_test_user === true || 
                       body.is_test_user === "true" || 
                       body.is_test_user === "on" ||
                       body.is_test_user === 1 ||
                       body.is_test_user === "1";
    
    // Validate flat_id if provided
    let flatId = null;
    if (body.flat_id) {
      try {
        const supabase = await createServiceClient();
        
        // Check if flat exists
        const { data: flatCheck } = await supabase
          .from('flats')
          .select('id')
          .eq('id', body.flat_id)
          .single();
        
        if (!flatCheck) {
          return NextResponse.json(
            { error: "Nurodytas butas neegzistuoja" },
            { status: 400 }
          );
        }
        
        flatId = body.flat_id;
      } catch (flatError) {
        console.error("Error checking flat:", flatError);
        // Continue without flat_id if there's an error
      }
    }
    
    // Create user
    try {
      const supabase = await createServiceClient();
      
      // Prepare user data
      const userData = {
        username: body.username,
        name: body.name,
        email: body.email,
        role: body.role,
        password_hash: hashedPassword,
        phone: body.phone || null,
        is_profile_updated: isProfileUpdated,
        is_test_user: isTestUser,
        flat_id: flatId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      console.log("Creating user with data:", { ...userData, password_hash: '[REDACTED]' });
      
      const { data: insertResult, error: insertError } = await supabase
        .from('users')
        .insert(userData)
        .select('id, username, name, email')
        .single();
      
      if (insertError) {
        console.error("Database error creating user:", insertError);
        
        // Check for common database constraint errors
        const errorMessage = insertError.message || '';
        if (errorMessage.includes('unique') || errorMessage.includes('duplicate')) {
          return NextResponse.json(
            { error: "Vartotojas su tokiu el. paštu arba prisijungimo vardu jau egzistuoja" },
            { status: 409 }
          );
        }
        
        return NextResponse.json(
          { error: "Klaida kuriant vartotoją duomenų bazėje" },
          { status: 500 }
        );
      }
      
      console.log("User created successfully:", insertResult);
      
      return NextResponse.json({
        id: insertResult.id,
        username: insertResult.username,
        name: insertResult.name,
        email: insertResult.email,
        message: "Naudotojas sukurtas sėkmingai"
      }, { status: 201 });
    } catch (error) {
      console.error("Unexpected error creating user:", error);
      return NextResponse.json(
        { error: "Klaida kuriant vartotoją duomenų bazėje" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Unexpected error creating user:", error);
    // Ensure we always return a valid JSON response
    return NextResponse.json(
      { error: "Įvyko nenumatyta klaida kuriant naudotoją" },
      { status: 500 }
    );
  }
} 