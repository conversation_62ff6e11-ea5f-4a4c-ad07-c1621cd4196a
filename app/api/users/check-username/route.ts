import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";

// GET /api/users/check-username?userId=123
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Only allow admin users
    const isAdmin = ["admin", "super_admin"].includes(user.role);
    if (!isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }
    
    // Get userId from query parameter
    const url = new URL(request.url);
    const userId = url.searchParams.get("userId");
    
    if (!userId) {
      return NextResponse.json({ error: "Missing userId parameter" }, { status: 400 });
    }
    
    console.log(`Checking username for user ID: ${userId}`);
    
    // Query the database for the user
    const supabase = await createServiceClient();
    
    const { data: userRecord, error } = await supabase
      .from('users')
      .select('id, username, name, email')
      .eq('id', userId)
      .single();
    
    if (error || !userRecord) {
      return NextResponse.json({ 
        exists: false, 
        message: "User not found" 
      });
    }
    
    return NextResponse.json({
      exists: !!userRecord.username,
      username: userRecord.username || null,
      name: userRecord.name,
      email: userRecord.email
    });
  } catch (error) {
    console.error("Error checking username:", error);
    return NextResponse.json(
      { error: "Failed to check username" },
      { status: 500 }
    );
  }
} 