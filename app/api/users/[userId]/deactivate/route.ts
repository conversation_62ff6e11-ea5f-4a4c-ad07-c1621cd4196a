import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db/supabase-adapter";
import { getCurrentUser } from "@/lib/supabase/auth";

// POST /api/users/[userId]/deactivate
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { userId } = await context.params;
    
    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Only allow users to deactivate their own account
    // (or admins to deactivate any account)
    const requestedUserId = parseInt(userId);
    const currentUserId = parseInt(user.id);
    const isAdmin = ["admin", "super_admin"].includes(user.role);
    
    if (requestedUserId !== currentUserId && !isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }
    
    const body = await req.json();
    const reason = body.reason || "User requested account deactivation";
    
    console.log(`Deactivating account for user ID: ${requestedUserId}, reason: ${reason}`);
    
    // Update user account to be disabled
    const result = await db.query(
      `UPDATE users 
       SET account_disabled = true,
           account_disabled_date = NOW(),
           account_disabled_reason = $1,
           updated_at = NOW()
       WHERE id = $2
       RETURNING id, username, account_disabled`,
      [
        reason,
        requestedUserId
      ]
    );
    
    if (result.rows.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    const updatedUser = result.rows[0];
    
    // Log the deactivation in audit logs
    await db.query(
      `INSERT INTO audit_logs (user_id, action, entity_type, entity_id, changes, ip_address, created_at)
       VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
      [
        currentUserId,
        'ACCOUNT_DEACTIVATED',
        'user',
        requestedUserId,
        JSON.stringify({ 
          reason: reason,
          deactivated_by: currentUserId === requestedUserId ? 'self' : 'admin',
          previous_status: 'active'
        }),
        req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
      ]
    );
    
    console.log(`Account deactivated successfully for user ID: ${requestedUserId}`);
    
    return NextResponse.json({
      success: true,
      message: "Account deactivated successfully",
      user: {
        id: updatedUser.id,
        username: updatedUser.username,
        account_disabled: updatedUser.account_disabled
      }
    });
  } catch (error) {
    console.error("Error deactivating user account:", error);
    return NextResponse.json(
      { error: "Failed to deactivate account" },
      { status: 500 }
    );
  }
}