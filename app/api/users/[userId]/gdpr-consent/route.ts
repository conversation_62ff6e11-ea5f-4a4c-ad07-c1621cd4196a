import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createClient, createServiceClient } from "@/lib/supabase/server";

// POST /api/users/[userId]/gdpr-consent
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  console.log("GDPR Consent API: Received request.");
  let user;
  try {
    user = await getCurrentUser();
    console.log("GDPR Consent API: Auth user retrieved:", JSON.stringify(user, null, 2));
  } catch (authError) {
    console.error("GDPR Consent API: Authentication error:", authError);
    return NextResponse.json(
      { error: "Authentication failed.", details: authError instanceof Error ? authError.message : String(authError) },
      { status: 500 }
    );
  }

  const { userId: userIdParam } = await context.params;
  console.log("GDPR Consent API: userIdParam from context:", userIdParam);

  if (!user || !user.id) {
    console.error("GDPR Consent API: Unauthorized - No user.", { userExists: !!user, userIdExists: !!user?.id });
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const requestedUserId = parseInt(userIdParam);
  const currentUserId = parseInt(user.id);
  console.log("GDPR Consent API: Parsed IDs:", { requestedUserId, currentUserId, originalUserId: user.id });


  if (isNaN(requestedUserId) || isNaN(currentUserId)) {
    console.error(
      "GDPR Consent API: Invalid user ID format.",
      { paramUserId: userIdParam, sessionUserId: user.id }
    );
    return NextResponse.json(
      { error: "Invalid user ID format provided." },
      { status: 400 }
    );
  }

  if (requestedUserId !== currentUserId) {
    console.warn("GDPR Consent API: Forbidden - User mismatch.", { requestedUserId, currentUserId });
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  let body;
  try {
    body = await req.json();
    console.log("GDPR Consent API: Request body parsed:", body);
  } catch (parseError) {
    console.error("GDPR Consent API: Error parsing request body:", parseError);
    return NextResponse.json(
      { error: "Invalid request body.", details: parseError instanceof Error ? parseError.message : String(parseError) },
      { status: 400 }
    );
  }
  
  const { consentGiven } = body;

  if (typeof consentGiven !== 'boolean') {
    console.error("GDPR Consent API: Invalid consentGiven value:", consentGiven);
    return NextResponse.json(
      { error: "consentGiven must be a boolean value" },
      { status: 400 }
    );
  }

  try {
    console.log("GDPR Consent API: Attempting to update users table...");
    // Use service client to bypass RLS
    const supabase = await createServiceClient();
    
    const { data: updateUserResult, error: updateError } = await supabase
      .from('users')
      .update({
        gdpr_consent_given: consentGiven,
        gdpr_consent_date: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', requestedUserId)
      .select('id, gdpr_consent_given, gdpr_consent_date');

    if (updateError) {
      console.error("GDPR Consent API: Error updating user:", updateError);
      return NextResponse.json({ error: "Failed to update user consent" }, { status: 500 });
    }

    if (!updateUserResult || updateUserResult.length === 0) {
      console.error("GDPR Consent API: User not found during update.", { requestedUserId });
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    const updatedUser = updateUserResult[0];
    console.log("GDPR Consent API: Users table updated successfully.", updatedUser);

    try {
      console.log("GDPR Consent API: Attempting to insert into audit_logs table...");
      // Also use service client for audit logs
      const { error: auditError } = await supabase
        .from('audit_logs')
        .insert({
          user_id: currentUserId,
          action: consentGiven ? 'gdpr_consent_given' : 'gdpr_consent_revoked',
          entity_type: 'user',
          entity_id: requestedUserId,
          details: {
            consent_given: consentGiven,
            timestamp: new Date().toISOString(),
            user_agent: req.headers.get('user-agent'),
            ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
          }
        });
      
      if (auditError) {
        console.error("GDPR Consent API: Error inserting audit log:", auditError);
      }
      console.log("GDPR Consent API: audit_logs table inserted successfully.");
    } catch (auditLogError) {
      console.error("GDPR Consent API: Error inserting into audit_logs:", auditLogError);
      // Decide if this is a critical failure. For now, we'll log and continue,
      // but you might want to roll back or return a specific error.
      // For this debugging purpose, we'll still return success for the main operation if user update worked.
    }
    
    return NextResponse.json({
      success: true,
      gdprConsentGiven: updatedUser.gdpr_consent_given,
      gdprConsentDate: updatedUser.gdpr_consent_date
    });

  } catch (dbError) {
    console.error("GDPR Consent API: Database error during user update:", dbError);
    return NextResponse.json(
      { error: "Failed to update user consent in database.", details: dbError instanceof Error ? dbError.message : String(dbError) },
      { status: 500 }
    );
  }
}