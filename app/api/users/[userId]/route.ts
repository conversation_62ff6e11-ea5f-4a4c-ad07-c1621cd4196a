"use server";

import { NextRequest, NextResponse } from "next/server";
import { createServiceClient } from "@/lib/supabase/server";
import { hash } from "bcryptjs";
import { userSchema } from "@/lib/validators";
import { sanitizeOutput } from "@/lib/utils";
import { sanitizeHtml } from "@/lib/sanitize";
import { withApiHandler } from "@/lib/api/withApiHandler";
import { ErrorType, createErrorResponse } from "@/lib/error-handler";
import { z } from "zod";

// Helper function to transform snake_case to camelCase
function snakeToCamel(obj: any) {
  if (obj === null || typeof obj !== "object") return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(snakeToCamel);
  }
  
  return Object.keys(obj).reduce((acc: any, key) => {
    const newKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    acc[newKey] = snakeToCamel(obj[key]);
    return acc;
  }, {});
}

// GET /api/users/[userId] - Get user details
export const GET = withApiHandler(
  {
    method: 'GET',
    requireAuth: true,
  },
  async (req: NextRequest, _, token) => {
    // Extract userId from URL
    const userId = req.nextUrl.pathname.split('/').pop();
    
    if (!userId) {
      return createErrorResponse(
        ErrorType.VALIDATION,
        "User ID is required"
      );
    }
    
    // Only super_admin, developer, or self can view user details
    const currentUserId = String(token.id);
    const targetUserId = String(userId);
    
    const isAuthorized = token.role === 'super_admin' || 
                        token.role === 'developer' || 
                        currentUserId === targetUserId;
    
    if (!isAuthorized) {
      return createErrorResponse(
        ErrorType.AUTHORIZATION,
        "You don't have permission to view this user"
      );
    }
    
    const supabase = await createServiceClient();
    
    const { data: user, error } = await supabase
      .from('users')
      .select(`
        id, username, name, email, role, 
        created_at, updated_at, is_profile_updated,
        street, house_number, flat_number, phone, flat_id,
        flats:flat_id (
          id,
          number,
          houses:house_id (
            id,
            name
          )
        )
      `)
      .eq('id', userId)
      .single();
    
    if (error || !user) {
      return createErrorResponse(
        ErrorType.NOT_FOUND,
        "User not found"
      );
    }
    
    // Transform to match expected structure
    const transformedUser = {
      ...user,
      flat_id: user.flat_id,
      flat_number: user.flats?.number || user.flat_number,
      house_id: user.flats?.houses?.id,
      house_name: user.flats?.houses?.name
    };
    
    // Remove nested objects and transform snake_case to camelCase and sanitize output
    delete transformedUser.flats;
    const camelCaseUser = sanitizeOutput(
      snakeToCamel(transformedUser),
      ['password', 'passwordHash']
    );
    
    return NextResponse.json(camelCaseUser);
  }
);

// Define a schema for user updates
const updateUserSchema = userSchema.partial().extend({
  password: z.string().min(8).optional(),
  flat_id: z.string().optional(),
  is_test_user: z.boolean().optional(),
});

// PUT /api/users/[userId] - Update user details
export const PUT = withApiHandler(
  {
    method: 'PUT',
    requireAuth: true,
    schema: updateUserSchema,
    csrfProtected: true,
  },
  async (req: NextRequest, validatedData, token) => {
    // ---> Add Log Here (Validated Data) <---
    console.log(`[PUT /api/users/[id]] Received validatedData:`, JSON.stringify(validatedData, null, 2));

    // Extract userId from URL
    const userId = req.nextUrl.pathname.split('/').pop();
    
    if (!userId) {
      return createErrorResponse(
        ErrorType.VALIDATION,
        "User ID is required"
      );
    }
    
    // Debug authorization details
    console.log(`[AUTHORIZATION_DEBUG] Current user full object:`, token);
    console.log(`[AUTHORIZATION_DEBUG] Target userId:`, userId);

    // Only super_admin or self can update user details
    // The token object is the user from getCurrentUser() which has our database ID
    const currentUserId = String(token.id);  // Use the database user ID
    const targetUserId = String(userId);
    
    const isAuthorized = token.role === 'super_admin' || 
                        token.role === 'developer' || 
                        currentUserId === targetUserId;
    
    if (!isAuthorized) {
      console.log(`[AUTHORIZATION_ERROR] You don't have permission to update this user`, {
        details: {
          userRole: token.role,
          currentUserId,
          targetUserId,
          isSuperAdmin: token.role === 'super_admin',
          isDeveloper: token.role === 'developer',
          isSameUser: currentUserId === targetUserId
        },
        originalError: undefined
      });
      return createErrorResponse(
        ErrorType.AUTHORIZATION,
        "You don't have permission to update this user"
      );
    }
    
    // Check if user exists
    const supabase = await createServiceClient();
    
    const { data: existingUser, error: userError } = await supabase
      .from('users')
      .select('id, role')
      .eq('id', userId)
      .single();
    
    if (userError || !existingUser) {
      return createErrorResponse(
        ErrorType.NOT_FOUND,
        "User not found"
      );
    }
    
    // Only super_admin can change roles, and they can't demote other super_admin
    if (
      validatedData.role && 
      validatedData.role !== existingUser.role && 
      (token.role !== 'super_admin' || 
       (existingUser.role === 'super_admin' && validatedData.role !== 'super_admin'))
    ) {
      return createErrorResponse(
        ErrorType.AUTHORIZATION,
        "You don't have permission to change this user's role"
      );
    }
    
    // Check if email or username already exists for another user
    if (validatedData.email || validatedData.username) {
      let duplicateQuery = supabase
        .from('users')
        .select('id')
        .neq('id', userId);
      
      if (validatedData.email && validatedData.username) {
        duplicateQuery = duplicateQuery.or(`email.eq.${validatedData.email},username.eq.${validatedData.username}`);
      } else if (validatedData.email) {
        duplicateQuery = duplicateQuery.eq('email', validatedData.email);
      } else if (validatedData.username) {
        duplicateQuery = duplicateQuery.eq('username', validatedData.username);
      }
      
      const { data: duplicateUsers } = await duplicateQuery.limit(1);
      
      if (duplicateUsers && duplicateUsers.length > 0) {
        return createErrorResponse(
          ErrorType.CONFLICT,
          "A user with this email or username already exists"
        );
      }
    }
    
    // Validate flat_id if provided
    if (validatedData.flat_id) {
      const { data: flat, error: flatError } = await supabase
        .from('flats')
        .select('id')
        .eq('id', validatedData.flat_id)
        .single();
      
      if (flatError || !flat) {
        return createErrorResponse(
          ErrorType.VALIDATION,
          "The specified flat does not exist"
        );
      }
    }
    
    // Create update data object with properly typed fields
    const updateData: Record<string, any> = {};
    
    // Sanitize any HTML content in user data
    if (validatedData.name) {
      updateData.name = sanitizeHtml(validatedData.name);
    }
    
    // Copy other fields, ensuring flat_id is null if empty string
    ['username', 'email', 'role', 'phone', 'is_profile_updated', 'flat_id', 'is_test_user']
      .forEach(field => {
        const value = validatedData[field as keyof typeof validatedData];
        if (value !== undefined) {
          if (field === 'flat_id' && value === '') {
            updateData[field] = null; // Convert empty string to null for flat_id
          } else {
            updateData[field] = value;
          }
        }
      });
    
    // Hash password if provided
    if (validatedData.password) {
      updateData.password_hash = await hash(validatedData.password, 10);
    }
    
    // Add timestamp
    updateData.updated_at = new Date();
    
    // ---> Add Log Here (Update Data / Params) <---
    console.log(`[PUT /api/users/[id]] Constructed updateData:`, JSON.stringify(updateData, null, 2));
    
    // Only proceed if there are fields to update
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json({
        message: "No fields to update"
      });
    }
    
    // Execute update with Supabase
    const { error: updateError } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', userId);
    
    if (updateError) {
      console.error(`[PUT /api/users/[id]] Update error:`, updateError);
      return createErrorResponse(
        ErrorType.DATABASE,
        "Failed to update user"
      );
    }
    
    // ---> Add Diagnostic Query Here <---
    try {
      const { data: checkResult } = await supabase
        .from('users')
        .select('id, email, is_test_user')
        .eq('id', userId)
        .single();
      
      if (checkResult) {
        console.log(`[PUT /api/users/[id]] DIAGNOSTIC CHECK after update for user ${userId}: is_test_user = ${checkResult.is_test_user}`);
      } else {
        console.log(`[PUT /api/users/[id]] DIAGNOSTIC CHECK: Could not re-fetch user ${userId} after update.`);
      }
    } catch (checkError) {
        console.error(`[PUT /api/users/[id]] DIAGNOSTIC CHECK error:`, checkError);
    }
    // ---> End Diagnostic Query <---

    return NextResponse.json({
      message: "User updated successfully"
    });
  }
);

// DELETE /api/users/[userId] - Delete user (simplified for troubleshooting)
export const DELETE = withApiHandler(
  {
    method: 'DELETE',
    requireAuth: true,
    requiredRole: 'super_admin',
    csrfProtected: true,
  },
  async (req: NextRequest, _, token) => {
    // Extract userId from URL
    const userId = req.nextUrl.pathname.split('/').pop();
    
    // Log auth details for debugging
    console.log(`DEBUG DELETE REQUEST: Auth details:`, {
      isAuthenticated: !!token,
      userRole: token?.role,
      userId: token?.sub,
      targetUserId: userId
    });
    
    if (!userId) {
      return createErrorResponse(
        ErrorType.VALIDATION,
        "User ID is required"
      );
    }
    
    try {    
      const supabase = await createServiceClient();
      
      // Simplified deletion - first check if user exists
      const { data: existingUser, error: userCheckError } = await supabase
        .from('users')
        .select('id, email')
        .eq('id', userId)
        .single();
      
      console.log(`DEBUG: User check result:`, existingUser ? 
                 'User found: ' + existingUser.email : 'User not found');
      
      if (userCheckError || !existingUser) {
        return createErrorResponse(
          ErrorType.NOT_FOUND,
          "User not found"
        );
      }
      
      // Simplified: Just attempt to delete with error handling
      try {
        // Simple DELETE operation
        const { error: deleteError } = await supabase
          .from('users')
          .delete()
          .eq('id', userId);
        
        if (deleteError) {
          throw deleteError;
        }
        
        console.log(`DEBUG: Delete operation completed successfully`);
        
        return NextResponse.json({
          message: "User deleted successfully"
        });
      } catch (error) {
        // Check if it's a foreign key error
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`DEBUG: Error deleting user:`, errorMessage);
        
        if (errorMessage.includes('foreign key constraint') || 
            errorMessage.includes('violates foreign key') ||
            errorMessage.includes('violates row-level security')) {
          // Handle foreign key violation
          return NextResponse.json({
            error: "Cannot delete user because they have related records",
            details: errorMessage
          }, { status: 409 }); // Conflict status
        }
        
        // Generic database error
        return NextResponse.json({
          error: "Database error occurred",
          details: errorMessage
        }, { status: 500 });
      }
    } catch (error) {
      // Catch any other errors
      console.error('Unexpected error in DELETE handler:', error);
      return NextResponse.json({
        error: "Unexpected error occurred",
        details: error instanceof Error ? error.message : String(error)
      }, { status: 500 });
    }
  }
); 