import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db/supabase-adapter";
import { getCurrentUser } from "@/lib/supabase/auth";

// GET /api/users/[userId]/profile
export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    console.log("====== START: GET /api/users/[userId]/profile endpoint ======");
    const user = await getCurrentUser();
    const { userId } = await context.params;
    
    console.log(`GET /api/users/${userId}/profile requested`);
    
    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Only allow users to access their own profile
    // (or admins to access any profile)
    const requestedUserId = parseInt(userId);
    const currentUserId = parseInt(user.id);
    const isAdmin = ["admin", "super_admin"].includes(user.role);
    
    if (requestedUserId !== currentUserId && !isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }
    
    console.log(`Fetching profile data for user ID: ${requestedUserId}`);
    
    // Get user profile using simple SQL query - Get ALL relevant fields
    const result = await db.query(
      `SELECT 
         id, 
         username, 
         name, 
         email, 
         phone, 
         is_profile_updated,
         flat_id,
         role
       FROM users 
       WHERE id = $1`,
      [requestedUserId]
    );
    
    if (result.rows.length === 0) {
      console.log(`User with ID ${requestedUserId} not found`);
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    // Log the raw database result
    console.log("Raw database result:", JSON.stringify(result.rows[0], null, 2));
    
    // Initialize address information
    let street = "";
    let houseNumber = "";
    let flatNumber = "";
    let flatFloor = null;
    
    // If user has a flat_id, fetch address details
    const userProfile = result.rows[0];
    if (userProfile.flat_id) {
      // Get flat information including floor
      const flatResult = await db.query(
        `SELECT f.number, f.floor, h.name as house_number, h.street_id
         FROM flats f
         JOIN houses h ON f.house_id = h.id
         WHERE f.id = $1`,
        [userProfile.flat_id]
      );
      
      if (flatResult.rowCount > 0) {
        const flat = flatResult.rows[0];
        flatNumber = flat.number || "";
        houseNumber = flat.house_number || "";
        flatFloor = flat.floor;
        
        // If the house has a street_id, get the street name
        if (flat.street_id) {
          const streetResult = await db.query(
            `SELECT name
             FROM streets
             WHERE id = $1`,
            [flat.street_id]
          );
          
          if (streetResult.rowCount > 0) {
            street = streetResult.rows[0].name || "";
          }
        }
      }
    }
    
    // Convert snake_case column names to camelCase for API consistency
    const profile = {
      id: userProfile.id,
      username: userProfile.username,
      name: userProfile.name,
      email: userProfile.email,
      phone: userProfile.phone,
      street: street,
      houseNumber: houseNumber,
      flatNumber: flatNumber,
      flatFloor: flatFloor,
      role: userProfile.role,
      isProfileUpdated: userProfile.is_profile_updated
    };
    
    // Log the transformed profile data
    console.log("Transformed profile data:", JSON.stringify(profile, null, 2));
    console.log("====== END: GET /api/users/[userId]/profile endpoint ======");
    
    return NextResponse.json(profile);
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return NextResponse.json(
      { error: "Failed to fetch user profile" },
      { status: 500 }
    );
  }
}

// PUT /api/users/[userId]/profile
export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const user = await getCurrentUser();
    // Await params to get userId
    const { userId } = await context.params;
    
    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Only allow users to update their own profile
    // (or admins to update any profile)
    const requestedUserId = parseInt(userId);
    const currentUserId = parseInt(user.id);
    const isAdmin = ["admin", "super_admin"].includes(user.role);
    
    if (requestedUserId !== currentUserId && !isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }
    
    const body = await req.json();
    console.log("Received profile update data:", JSON.stringify(body, null, 2));
    
    // Check if email is provided (it's a required field in the database)
    if (!body.email || body.email.trim() === '') {
      return NextResponse.json(
        { error: "Email is required and cannot be empty" },
        { status: 400 }
      );
    }
    
    // The data should already be in snake_case format
    // But let's make sure we extract only the fields we want to update
    const updateData = {
      name: body.name,
      email: body.email,
      phone: body.phone || null,
      is_profile_updated: body.is_profile_updated !== undefined ? body.is_profile_updated : true,
    };
    
    console.log("Updating profile with data:", JSON.stringify(updateData, null, 2));
    
    // Update user profile with simple SQL query
    const result = await db.query(
      `UPDATE users 
       SET name = $1, 
           email = $2, 
           phone = $3, 
           is_profile_updated = $4,
           updated_at = $5
       WHERE id = $6
       RETURNING id, name, email, phone, is_profile_updated, username, flat_id, role`,
      [
        updateData.name,
        updateData.email,
        updateData.phone,
        updateData.is_profile_updated,
        new Date(),
        requestedUserId
      ]
    );
    
    if (result.rows.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    // Initialize address information
    let street = "";
    let houseNumber = "";
    let flatNumber = "";
    
    // If user has a flat_id, fetch address details
    const userProfile = result.rows[0];
    if (userProfile.flat_id) {
      // Get flat information
      const flatResult = await db.query(
        `SELECT f.number, h.name as house_number, h.street_id
         FROM flats f
         JOIN houses h ON f.house_id = h.id
         WHERE f.id = $1`,
        [userProfile.flat_id]
      );
      
      if (flatResult.rowCount > 0) {
        const flat = flatResult.rows[0];
        flatNumber = flat.number || "";
        houseNumber = flat.house_number || "";
        
        // If the house has a street_id, get the street name
        if (flat.street_id) {
          const streetResult = await db.query(
            `SELECT name
             FROM streets
             WHERE id = $1`,
            [flat.street_id]
          );
          
          if (streetResult.rowCount > 0) {
            street = streetResult.rows[0].name || "";
          }
        }
      }
    }
    
    // Convert snake_case column names to camelCase for API consistency
    const updatedProfile = {
      id: userProfile.id,
      name: userProfile.name,
      email: userProfile.email,
      phone: userProfile.phone,
      username: userProfile.username,
      street: street,
      houseNumber: houseNumber,
      flatNumber: flatNumber,
      role: userProfile.role,
      isProfileUpdated: userProfile.is_profile_updated
    };
    
    console.log("Profile updated successfully:", JSON.stringify(updatedProfile, null, 2));
    return NextResponse.json(updatedProfile);
  } catch (error) {
    console.error("Error updating user profile:", error);
    return NextResponse.json(
      { error: "Failed to update user profile" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { userId } = await context.params;
    
    // Check if user is authenticated
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Only allow users to update their own profile
    // (or admins to update any profile)
    const requestedUserId = parseInt(userId);
    const currentUserId = parseInt(user.id);
    const isAdmin = ["admin", "super_admin"].includes(user.role);
    
    if (requestedUserId !== currentUserId && !isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }
    
    const body = await req.json();
    
    // Update user profile with simple SQL query
    const result = await db.query(
      `UPDATE users 
       SET name = $1, 
           email = $2, 
           phone = $3, 
           is_profile_updated = $4,
           updated_at = $5
       WHERE id = $6
       RETURNING id, name, email, phone, is_profile_updated, flat_id, role`,
      [
        body.name,
        body.email || null,
        body.phone || null,
        body.isProfileUpdated !== undefined ? body.isProfileUpdated : true,
        new Date(),
        requestedUserId
      ]
    );
    
    if (result.rows.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    // Initialize address information
    let street = "";
    let houseNumber = "";
    let flatNumber = "";
    
    // If user has a flat_id, fetch address details
    const userProfile = result.rows[0];
    if (userProfile.flat_id) {
      // Get flat information
      const flatResult = await db.query(
        `SELECT f.number, h.name as house_number, h.street_id
         FROM flats f
         JOIN houses h ON f.house_id = h.id
         WHERE f.id = $1`,
        [userProfile.flat_id]
      );
      
      if (flatResult.rowCount > 0) {
        const flat = flatResult.rows[0];
        flatNumber = flat.number || "";
        houseNumber = flat.house_number || "";
        
        // If the house has a street_id, get the street name
        if (flat.street_id) {
          const streetResult = await db.query(
            `SELECT name
             FROM streets
             WHERE id = $1`,
            [flat.street_id]
          );
          
          if (streetResult.rowCount > 0) {
            street = streetResult.rows[0].name || "";
          }
        }
      }
    }
    
    // Convert snake_case column names to camelCase for API consistency
    const updatedProfile = {
      id: userProfile.id,
      name: userProfile.name,
      email: userProfile.email,
      phone: userProfile.phone,
      username: userProfile.username || "",
      street: street,
      houseNumber: houseNumber,
      flatNumber: flatNumber,
      role: userProfile.role,
      isProfileUpdated: userProfile.is_profile_updated
    };
    
    return NextResponse.json(updatedProfile);
  } catch (error) {
    console.error("Error updating user profile:", error);
    return NextResponse.json(
      { error: "Failed to update user profile" },
      { status: 500 }
    );
  }
} 