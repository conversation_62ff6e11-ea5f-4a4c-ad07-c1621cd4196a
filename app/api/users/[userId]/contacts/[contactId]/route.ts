import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db/supabase-adapter";
import { getCurrentUser } from "@/lib/supabase/auth";

// GET /api/users/[userId]/contacts/[contactId]
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ userId: string; contactId: string }> }
) {
  const user = await getCurrentUser();
  
  // Check if user is authenticated
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  
  const { userId, contactId } = await context.params;
  const requestedUserId = parseInt(userId);
  const currentUserId = parseInt(user.id);
  const isAdmin = ["admin", "super_admin"].includes(user.role);
  
  if (requestedUserId !== currentUserId && !isAdmin) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }
  
  try {
    // Get contact using our simplified database utility
    const result = await db.query(
      `SELECT * FROM emergency_contacts 
       WHERE id = $1 AND user_id = $2`,
      [contactId, requestedUserId]
    );
    
    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }
    
    return NextResponse.json(result.rows[0]);
  } catch (error) {
    console.error("Error fetching contact:", error);
    return NextResponse.json(
      { error: "Failed to fetch contact" },
      { status: 500 }
    );
  }
}

// PUT /api/users/[userId]/contacts/[contactId]
export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ userId: string; contactId: string }> }
) {
  const user = await getCurrentUser();
  
  // Check if user is authenticated
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  
  const { userId, contactId } = await context.params;
  const requestedUserId = parseInt(userId);
  const currentUserId = parseInt(user.id);
  const isAdmin = ["admin", "super_admin"].includes(user.role);
  
  if (requestedUserId !== currentUserId && !isAdmin) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }
  
  try {
    const body = await req.json();
    
    // Validate required fields
    if (!body.name || !body.phone) {
      return NextResponse.json(
        { error: "Name and phone are required" },
        { status: 400 }
      );
    }
    
    // Check if contact exists
    const checkResult = await db.query(
      `SELECT id FROM emergency_contacts 
       WHERE id = $1 AND user_id = $2`,
      [contactId, requestedUserId]
    );
    
    if (checkResult.rows.length === 0) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }
    
    // Fetch the user's flat_id for updating the contact
    const { rows: userRows } = await db.query("SELECT flat_id FROM users WHERE id = $1", [requestedUserId]);
    if (userRows.length === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    const userFlatId = userRows[0].flat_id;

    // Update contact with only the fields we know exist in the database
    const updateResult = await db.query(
      `UPDATE emergency_contacts 
       SET name = $1, 
           phone = $2, 
           relationship = $3,
           custom_relationship = $4,
           is_emergency_contact = $5,
           receive_notifications = $6,
           flat_id = $7,
           updated_at = $8
       WHERE id = $9 AND user_id = $10
       RETURNING *`,
      [
        body.name,
        body.phone,
        body.relationship || null,
        body.customRelationship || null,
        body.isEmergencyContact !== undefined ? body.isEmergencyContact : true,
        body.receiveNotifications !== undefined ? body.receiveNotifications : false,
        userFlatId,
        new Date(),
        contactId,
        requestedUserId
      ]
    );
    
    const updatedContact = updateResult.rows[0];
    return NextResponse.json(updatedContact);
  } catch (error) {
    console.error("Error updating contact:", error);
    return NextResponse.json(
      { error: "Failed to update contact" },
      { status: 500 }
    );
  }
}

// DELETE /api/users/[userId]/contacts/[contactId]
export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ userId: string; contactId: string }> }
) {
  const user = await getCurrentUser();
  
  // Check if user is authenticated
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  
  const { userId, contactId } = await context.params;
  const requestedUserId = parseInt(userId);
  const currentUserId = parseInt(user.id);
  const isAdmin = ["admin", "super_admin"].includes(user.role);
  
  if (requestedUserId !== currentUserId && !isAdmin) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }
  
  try {
    // Delete contact
    const result = await db.query(
      `DELETE FROM emergency_contacts 
       WHERE id = $1 AND user_id = $2
       RETURNING id`,
      [contactId, requestedUserId]
    );
    
    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting contact:", error);
    return NextResponse.json(
      { error: "Failed to delete contact" },
      { status: 500 }
    );
  }
} 