import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db/supabase-adapter";
import { getCurrentUser } from "@/lib/supabase/auth";

// GET /api/users/[userId]/contacts
export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { userId } = await context.params;
    
    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Get userId from params
    const requestedUserId = parseInt(userId);
    const currentUserId = parseInt(user.id);
    const isAdmin = ["super_admin", "editor"].includes(user.role);
    
    // Only allow users to access their own contacts 
    // (or admins to access any contacts)
    if (requestedUserId !== currentUserId && !isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }
    
    // Get all contacts for the user using our simplified db utility
    const result = await db.query(
      `SELECT id, user_id, name, phone, relationship, 
              custom_relationship, is_emergency_contact,
              created_at, updated_at
       FROM emergency_contacts 
       WHERE user_id = $1
       ORDER BY id`,
      [requestedUserId]
    );
    
    return NextResponse.json(result.rows);
  } catch (error) {
    console.error("Error fetching contacts:", error);
    return NextResponse.json(
      { error: "Failed to fetch contacts" },
      { status: 500 }
    );
  }
}

// POST /api/users/[userId]/contacts
export async function POST(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { userId } = await context.params;
    
    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Get userId from params
    const requestedUserId = parseInt(userId);
    const currentUserId = parseInt(user.id);
    const isAdmin = ["super_admin", "editor"].includes(user.role);
    
    // Only allow users to create their own contacts
    // (or admins to create contacts for any user)
    if (requestedUserId !== currentUserId && !isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }
    
    const body = await req.json();
    
    // Validate required fields
    if (!body.name || !body.phone) {
      return NextResponse.json(
        { error: "Name and phone are required" },
        { status: 400 }
      );
    }
    
    // Get the user's flat_id to associate with the contact
    const userResult = await db.query(
      `SELECT flat_id FROM users WHERE id = $1`,
      [requestedUserId]
    );
    
    const userFlatId = userResult.rows[0]?.flat_id || null;
    
    // Check if the emergency_contacts table has a flat_id column
    const tableInfoResult = await db.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'emergency_contacts' 
      AND column_name = 'flat_id'
    `);
    
    const hasFlatIdColumn = tableInfoResult.rowCount > 0;
    
    // If the flat_id column doesn't exist, add it
    if (!hasFlatIdColumn) {
      await db.query(`
        ALTER TABLE emergency_contacts 
        ADD COLUMN flat_id UUID REFERENCES flats(id)
      `);
      
      console.log("Added flat_id column to emergency_contacts table");
    }
    
    // Check if the receive_notifications column exists
    const receiveNotificationsColumnResult = await db.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'emergency_contacts' 
      AND column_name = 'receive_notifications'
    `);
    
    const hasReceiveNotificationsColumn = receiveNotificationsColumnResult.rowCount > 0;
    
    // If the receive_notifications column doesn't exist, add it
    if (!hasReceiveNotificationsColumn) {
      await db.query(`
        ALTER TABLE emergency_contacts 
        ADD COLUMN receive_notifications BOOLEAN DEFAULT FALSE
      `);
      
      console.log("Added receive_notifications column to emergency_contacts table");
    }
    
    // Use our simplified db utility
    // Include the flat_id and receive_notifications fields
    const result = await db.query(
      `INSERT INTO emergency_contacts 
       (user_id, name, phone, relationship, 
        custom_relationship, is_emergency_contact, 
        receive_notifications, flat_id, created_at, updated_at) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) 
       RETURNING *`,
      [
        requestedUserId, 
        body.name,
        body.phone, 
        body.relationship || null,
        body.customRelationship || null,
        body.isEmergencyContact !== undefined ? body.isEmergencyContact : true,
        body.receiveNotifications !== undefined ? body.receiveNotifications : false,
        userFlatId,
        new Date(),
        new Date()
      ]
    );
    
    // Return the newly created contact
    const newContact = result.rows[0];
    return NextResponse.json(newContact, { status: 201 });
  } catch (error) {
    console.error("Error creating contact:", error);
    return NextResponse.json(
      { error: "Failed to create contact" },
      { status: 500 }
    );
  }
} 