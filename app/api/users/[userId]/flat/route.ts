import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db/supabase-adapter";
import { getCurrentUser } from "@/lib/supabase/auth";

// GET /api/users/[userId]/flat - Get user's flat information
export async function GET(
  req: Request,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { userId } = await context.params;
    
    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Only allow users to access their own flat info (or admins)
    const requestedUserId = parseInt(userId);
    const currentUserId = parseInt(user.id);
    const isAdmin = ["admin", "super_admin"].includes(user.role);
    
    if (requestedUserId !== currentUserId && !isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }
    
    // Get user's flat information
    const result = await db.query(
      `SELECT 
         u.flat_id,
         f.id, f.number, f.floor, f.area,
         h.name as house_name, h.address as house_address,
         s.name as street_name
       FROM users u
       LEFT JOIN flats f ON u.flat_id = f.id
       LEFT JOIN houses h ON f.house_id = h.id
       LEFT JOIN streets s ON h.street_id = s.id
       WHERE u.id = $1`,
      [requestedUserId]
    );
    
    if (result.rows.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    const data = result.rows[0];
    
    if (!data.flat_id) {
      return NextResponse.json({ 
        message: "User has no assigned flat",
        flat: null 
      });
    }
    
    const flatInfo = {
      id: data.id,
      number: data.number,
      floor: data.floor,
      area: data.area,
      houseName: data.house_name,
      houseAddress: data.house_address,
      streetName: data.street_name,
      displayName: data.street_name 
        ? `${data.street_name} ${data.house_name} - ${data.number}`
        : `${data.house_name} - ${data.number}`
    };
    
    return NextResponse.json({ flat: flatInfo });
  } catch (error) {
    console.error("Error fetching user flat:", error);
    return NextResponse.json(
      { error: "Failed to fetch flat information" },
      { status: 500 }
    );
  }
}

// PUT /api/users/[userId]/flat - Update user's flat floor
export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { userId } = await context.params;
    
    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Only allow users to update their own flat (or admins)
    const requestedUserId = parseInt(userId);
    const currentUserId = parseInt(user.id);
    const isAdmin = ["admin", "super_admin"].includes(user.role);
    
    if (requestedUserId !== currentUserId && !isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }
    
    const body = await req.json();
    
    // Validate floor input
    if (body.floor !== null && body.floor !== undefined) {
      const floor = parseInt(body.floor);
      if (isNaN(floor) || floor < 0 || floor > 50) {
        return NextResponse.json(
          { error: "Aukštas turi būti skaičius nuo 0 iki 50" },
          { status: 400 }
        );
      }
    }
    
    // Get user's flat_id
    const userResult = await db.query(
      `SELECT flat_id FROM users WHERE id = $1`,
      [requestedUserId]
    );
    
    if (userResult.rows.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    const flatId = userResult.rows[0].flat_id;
    
    if (!flatId) {
      return NextResponse.json(
        { error: "User has no assigned flat" },
        { status: 400 }
      );
    }
    
    // Update the flat's floor
    const updateResult = await db.query(
      `UPDATE flats 
       SET floor = $1, updated_at = NOW()
       WHERE id = $2
       RETURNING id, number, floor, house_id`,
      [body.floor, flatId]
    );
    
    if (updateResult.rows.length === 0) {
      return NextResponse.json({ error: "Flat not found" }, { status: 404 });
    }
    
    // Get updated flat information with house details
    const flatResult = await db.query(
      `SELECT 
         f.id, f.number, f.floor, f.area,
         h.name as house_name, h.address as house_address,
         s.name as street_name
       FROM flats f
       JOIN houses h ON f.house_id = h.id
       LEFT JOIN streets s ON h.street_id = s.id
       WHERE f.id = $1`,
      [flatId]
    );
    
    const updatedFlat = flatResult.rows[0];
    
    // Log the update in audit logs
    await db.query(
      `INSERT INTO audit_logs (user_id, action, entity_type, entity_id, changes, ip_address, created_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7)`,
      [
        currentUserId,
        'FLAT_FLOOR_UPDATED',
        'flat',
        flatId.toString(),
        JSON.stringify({ 
          field: 'floor',
          new_value: body.floor,
          updated_by: currentUserId === requestedUserId ? 'self' : 'admin'
        }),
        req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown',
        new Date()
      ]
    );
    
    const flatInfo = {
      id: updatedFlat.id,
      number: updatedFlat.number,
      floor: updatedFlat.floor,
      area: updatedFlat.area,
      houseName: updatedFlat.house_name,
      houseAddress: updatedFlat.house_address,
      streetName: updatedFlat.street_name,
      displayName: updatedFlat.street_name 
        ? `${updatedFlat.street_name} ${updatedFlat.house_name} - ${updatedFlat.number}`
        : `${updatedFlat.house_name} - ${updatedFlat.number}`
    };
    
    return NextResponse.json({
      success: true,
      message: "Aukštas sėkmingai atnaujintas",
      flat: flatInfo
    });
  } catch (error) {
    console.error("Error updating flat floor:", error);
    return NextResponse.json(
      { error: "Failed to update flat floor" },
      { status: 500 }
    );
  }
}