import { NextRequest, NextResponse } from "next/server";
import * as ExcelJS from 'exceljs';
import { getCurrentUser } from "@/lib/supabase/auth";
import logger from "@/lib/logger";

// This endpoint inspects an Excel file and returns the column headers
export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    const isDevelopment = process.env.NODE_ENV === "development";
    
    // Check if user is authorized - only developers or super_admins in dev mode
    const isAuthorized = 
      (session?.user?.role === "developer") || 
      (isDevelopment && session?.user?.role === "super_admin");
    
    if (!session?.user || !isAuthorized) {
      logger.warn({
        userId: session?.user?.id,
        role: session?.user?.role,
        action: "access_denied",
        resource: "import_inspect"
      }, "Unauthorized access attempt to inspect import files");
      
      return NextResponse.json({ error: "Unauthorized - only developers can inspect import files" }, { status: 403 });
    }
    
    logger.debug({
      userId: user.id,
      action: "inspect_file",
      resource: "users"
    }, "File inspection started");
    
    // Get the form data
    const formData = await req.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      logger.warn({
        userId: user.id,
        action: "inspect_failed",
        reason: "no_file"
      }, "File inspection failed: No file uploaded");
      
      return NextResponse.json(
        { error: "No file uploaded" },
        { status: 400 }
      );
    }
    
    // Check file type
    if (!file.name.endsWith('.xlsx')) {
      logger.warn({
        userId: user.id,
        action: "inspect_failed",
        reason: "invalid_file_type",
        fileType: file.name.split('.').pop()
      }, "Invalid file format for inspection");
      
      return NextResponse.json(
        { error: "Only .xlsx files are supported" },
        { status: 400 }
      );
    }
    
    // Convert the file to an array buffer
    const buffer = await file.arrayBuffer();
    
    // Load the workbook
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(buffer);
    
    // Get the first worksheet
    const worksheet = workbook.worksheets[0];
    
    if (!worksheet) {
      logger.warn({
        userId: user.id,
        action: "inspect_failed",
        reason: "no_worksheet",
        fileName: file.name
      }, "Worksheet not found in Excel file");
      
      return NextResponse.json(
        { error: "Worksheet not found" },
        { status: 400 }
      );
    }
    
    // Get column headers from the first row
    const headers: string[] = [];
    worksheet.getRow(1).eachCell((cell) => {
      if (cell.value) {
        headers.push(cell.value.toString().trim());
      }
    });
    
    if (headers.length === 0) {
      logger.warn({
        userId: user.id,
        action: "inspect_failed",
        reason: "no_headers",
        fileName: file.name
      }, "No column headers found in Excel file");
      
      return NextResponse.json(
        { error: "No column headers found in the first row" },
        { status: 400 }
      );
    }
    
    logger.info({
      userId: user.id,
      action: "inspect_success",
      fileName: file.name,
      headerCount: headers.length,
      headers
    }, "Successfully inspected Excel file");
    
    // Return the headers
    return NextResponse.json({ headers });
    
  } catch (error: any) {
    logger.error({
      error: error.message,
      stack: error.stack
    }, "Error processing Excel file for inspection");
    
    return NextResponse.json(
      { error: "Failed to process Excel file" },
      { status: 500 }
    );
  }
} 