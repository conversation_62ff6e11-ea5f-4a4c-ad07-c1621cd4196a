import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import * as ExcelJS from "exceljs";
import logger from "@/lib/logger";

export async function GET() {
  try {
    const user = await getCurrentUser();
    const isDevelopment = process.env.NODE_ENV === "development";
    
    // Check if user is authorized - only developers or super_admins in dev mode
    const isAuthorized = 
      (session?.user?.role === "developer") || 
      (isDevelopment && session?.user?.role === "super_admin");
    
    if (!session?.user || !isAuthorized) {
      logger.warn({
        userId: session?.user?.id,
        role: session?.user?.role,
        action: "access_denied",
        resource: "import_template"
      }, "Unauthorized access attempt to download import template");
      
      return NextResponse.json(
        { error: "Unauthorized - only developers can download import templates" },
        { status: 403 }
      );
    }

    logger.info({
      userId: user.id,
      action: "template_download",
      resource: "users"
    }, "User import template download started");

    // Create a new workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Vartotojai");

    // Define columns - match the exact format from the user's screenshot
    worksheet.columns = [
      { header: "MOKĖTOJO KODAS", key: "payerCode", width: 20 },
      { header: "GATVĖ", key: "street", width: 25 },
      { header: "NAMO NR.", key: "houseNumber", width: 15 },
      { header: "BUTAS", key: "flatNumber", width: 15 },
    ];

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    
    // Add a distinctive background color to all headers
    for (let i = 1; i <= 4; i++) {
      const cell = headerRow.getCell(i);
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFD0D0D0" } // Gray background for headers
      };
    }

    // Add sample data (3 rows) matching the user's format
    worksheet.addRow({
      payerCode: "9019164",
      street: "Smiltelės",
      houseNumber: "31",
      flatNumber: "1"
    });
    
    worksheet.addRow({
      payerCode: "0123591",
      street: "Smiltelės",
      houseNumber: "31",
      flatNumber: "2"
    });
    
    worksheet.addRow({
      payerCode: "0123608",
      street: "Smiltelės",
      houseNumber: "31",
      flatNumber: "3"
    });

    // Add notes to columns
    const payerCodeCell = worksheet.getCell("A1");
    payerCodeCell.note = "Mokėtojo kodas bus naudojamas kaip slaptažodis prisijungimui. Privalomas laukas.";

    const streetCell = worksheet.getCell("B1");
    streetCell.note = "Gatvės pavadinimas namo adresui. Privalomas laukas.";

    const houseNumberCell = worksheet.getCell("C1");
    houseNumberCell.note = "Namo numeris bus naudojamas prisijungimo varde (pvz., 31-7). Privalomas laukas.";
    
    const flatNumberCell = worksheet.getCell("D1");
    flatNumberCell.note = "Buto numeris bus naudojamas prisijungimo varde (pvz., 31-7). Privalomas laukas.";
    
    // Add explanation about username creation and passwords
    worksheet.getCell("A6").value = "SVARBU:";
    worksheet.getCell("A6").font = { bold: true, size: 12, color: { argb: "FFFF0000" } };
    
    worksheet.getCell("A7").value = "Visi stulpeliai yra privalomi importuojant vartotojus.";
    worksheet.getCell("A8").value = "Prisijungimo vardas sudaromas iš namo ir buto numerio: [NAMO NR.]-[BUTAS] (pvz., 31-7)";
    worksheet.getCell("A9").value = "MOKĖTOJO KODAS bus naudojamas kaip pradinis slaptažodis.";

    // Write to buffer
    const buffer = await workbook.xlsx.writeBuffer();
    
    logger.info({
      userId: user.id,
      action: "template_download_success",
      resource: "users"
    }, "User import template generated successfully");
    
    // Return as a downloadable file
    const response = new NextResponse(buffer);
    
    response.headers.set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.headers.set("Content-Disposition", "attachment; filename=vartotoju_importavimo_sablonas.xlsx");
    
    return response;
  } catch (error: any) {
    logger.error({
      error: error.message,
      stack: error.stack
    }, "Error generating import template");
    
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 