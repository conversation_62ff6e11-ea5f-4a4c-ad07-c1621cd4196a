import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { addAuditLog, parseExcelFile } from "@/lib/utils";
import { db } from "@/lib/db/supabase-adapter";
import { hash } from "bcryptjs";
import logger from "@/lib/logger";

// =========================================================================
// HELPER FUNCTIONS
// =========================================================================

/**
 * Generates a random password of specified length
 * @param length - The length of the password to generate
 * @returns A random password string
 */
function generateRandomPassword(length = 10) {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let password = "";
  for (let i = 0; i < length; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

/**
 * Validates an email format
 * @param email - The email to validate
 * @returns Boolean indicating if the email is valid
 */
function isValidEmail(email: string) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validates Excel data with column mapping
 * @param data - The Excel data rows
 * @param columnMapping - Object mapping expected fields to Excel columns
 * @returns Validation result with errors if any
 */
function validateExcelData(data: any[], columnMapping: Record<string, string>) {
  const errors = [];
  
  // Required field IDs
  const requiredFieldIds = ["street", "houseNumber", "flatNumber", "payerCode"];
  
  // Check if data is empty
  if (!data || data.length === 0) {
    errors.push({ row: 1, message: "Failas tuščias arba nėra duomenų lentelėje." });
    return { isValid: false, errors };
  }
  
  // For automatic column mapping from our format
  if (Object.keys(columnMapping).length === 0) {
    // Try to auto-map columns based on headers
    const firstRow = data[0];
    const headerRow = Object.keys(firstRow);
    
    headerRow.forEach(header => {
      const headerUpper = header.toUpperCase().trim();
      
      if (headerUpper === "GATVĖ") {
        columnMapping.street = header;
      } else if (headerUpper === "NAMO NR.") {
        columnMapping.houseNumber = header;
      } else if (headerUpper === "BUTAS") {
        columnMapping.flatNumber = header;
      } else if (headerUpper === "MOKĖTOJO KODAS") {
        columnMapping.payerCode = header;
      } else if (headerUpper === "VARDAS") {
        columnMapping.name = header;
      } else if (headerUpper === "PAVARDĖ") {
        columnMapping.surname = header;
      } else if (["EL. PAŠTAS", "EMAIL", "EL.PAŠTAS"].includes(headerUpper)) {
        columnMapping.email = header;
      } else if (["TELEFONAS", "TEL", "TEL."].includes(headerUpper)) {
        columnMapping.phone = header;
      }
    });
  }
  
  // Check if all required fields are mapped
  const missingMappings = requiredFieldIds.filter(fieldId => !columnMapping[fieldId]);
  if (missingMappings.length > 0) {
    errors.push({ 
      row: 1, 
      message: `Trūksta privalomų laukų susiejimo: ${missingMappings.map(field => {
        switch (field) {
          case "street": return "GATVĖ";
          case "houseNumber": return "NAMO NR.";
          case "flatNumber": return "BUTAS";
          case "payerCode": return "MOKĖTOJO KODAS";
          default: return field;
        }
      }).join(", ")}.` 
    });
    return { isValid: false, errors };
  }
  
  // Validate that all mapped columns exist in the data
  const firstRow = data[0];
  const missingColumns = Object.values(columnMapping)
    .filter(colName => colName && !Object.keys(firstRow).includes(colName));
  
  if (missingColumns.length > 0) {
    errors.push({ 
      row: 1, 
      message: `Faile nerasta šių stulpelių: ${missingColumns.join(", ")}.` 
    });
    return { isValid: false, errors };
  }
  
  // Validate each row
  data.forEach((row, index) => {
    const rowNum = index + 2; // +2 because index is 0-based and we skip the header row
    
    // Extended logging for debugging
    if (rowNum > 650) {
      console.log(`[DEBUG] Row ${rowNum} data:`, JSON.stringify(row));
    }
    
    // Check street
    if (!row[columnMapping.street] || row[columnMapping.street].trim().length < 2) {
      errors.push({ 
        row: rowNum, 
        message: `Neteisingas gatvės pavadinimas: ${row[columnMapping.street] || "tuščias"}` 
      });
    }
    
    // Check house number
    if (!row[columnMapping.houseNumber] || row[columnMapping.houseNumber].toString().trim() === "") {
      errors.push({ 
        row: rowNum, 
        message: `Nenurodyta namo numeris` 
      });
    }
    
    // Check flat number
    if (row[columnMapping.flatNumber] === undefined || 
        row[columnMapping.flatNumber] === null || 
        row[columnMapping.flatNumber].toString().trim() === "") {
      errors.push({ 
        row: rowNum, 
        message: `Nenurodytas buto numeris` 
      });
    }
    
    // Check Payer Code
    if (!row[columnMapping.payerCode] || row[columnMapping.payerCode].toString().trim() === "") {
      errors.push({ 
        row: rowNum, 
        message: `Nenurodytas mokėtojo kodas, kuris bus naudojamas kaip slaptažodis` 
      });
    }
    
    // Validate email if it exists
    if (columnMapping.email && row[columnMapping.email] && !isValidEmail(row[columnMapping.email])) {
      errors.push({
        row: rowNum,
        message: `Neteisingas el. pašto formatas: ${row[columnMapping.email]}`
      });
    }
  });
  
  return { isValid: errors.length === 0, errors };
}

// =========================================================================
// DATABASE FUNCTIONS
// =========================================================================

/**
 * Processes users from Excel data and imports them into the database
 * @param data - The Excel data rows
 * @param columnMapping - Object mapping expected fields to Excel columns
 * @returns Import result with counts and errors
 */
async function processUsers(data: any[], columnMapping: Record<string, string>) {
  const results = {
    imported: 0,
    errors: [] as { row: number; message: string }[]
  };
  
  // Track already seen usernames to prevent duplicates
  const seenUsernames = new Set<string>();
  
  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    const rowNum = i + 2; // +2 because index is 0-based and we skip the header row
    
    try {
      // Extract user data using column mapping
      const street = row[columnMapping.street];
      const houseNumber = row[columnMapping.houseNumber];
      const flatNumber = row[columnMapping.flatNumber];
      const payerCode = row[columnMapping.payerCode];
      
      // Get optional fields if they exist
      const name = columnMapping.name ? row[columnMapping.name] || "" : "";
      const surname = columnMapping.surname ? row[columnMapping.surname] || "" : "";
      const fullName = [name, surname].filter(Boolean).join(" ") || "Vartotojas";
      const email = columnMapping.email ? row[columnMapping.email] || "" : "";
      const phone = columnMapping.phone ? row[columnMapping.phone] || "" : "";
      
      // Generate username from house number and flat number (e.g., "31-7")
      const username = `${houseNumber}-${flatNumber}`;
      
      // Check if this username has already been processed in this batch
      if (seenUsernames.has(username)) {
        results.errors.push({
          row: rowNum,
          message: `Duplikuotas prisijungimo vardas: ${username}. Namo ir buto numerio kombinacija turi būti unikali.`
        });
        continue;
      }
      
      // Track this username
      seenUsernames.add(username);
      
      // Check if user with this username already exists in the database
      const existingUser = await db.query(
        "SELECT id FROM users WHERE username = $1", 
        [username]
      );
      
      if (existingUser.rows.length > 0) {
        results.errors.push({
          row: rowNum,
          message: `Vartotojas su prisijungimo vardu ${username} jau egzistuoja.`
        });
        continue;
      }
      
      // Use payer code as password
      const password = payerCode;
      
      // Hash the password
      const hashedPassword = await hash(password, 10);
      
      // Find or create the house record first
      let houseId: number;
      
      // Check if house exists with this address
      const houseLookupResult = await db.query(
        "SELECT id FROM houses WHERE address = $1",
        [`${street} ${houseNumber}`]
      );
      
      if (houseLookupResult.rows.length > 0) {
        // House exists
        houseId = houseLookupResult.rows[0].id;
      } else {
        // Create a new house
        const newHouseResult = await db.query(
          `INSERT INTO houses 
            (name, address, floors, created_at) 
           VALUES 
            ($1, $2, $3, NOW()) 
           RETURNING id`,
          [
            `${street} ${houseNumber}`,
            `${street} ${houseNumber}`,
            1 // Default number of floors
          ]
        );
        houseId = newHouseResult.rows[0].id;
      }
      
      // Begin transaction
      await db.query("BEGIN");
      try {
        // Create flat if not exists
        let flatId: number;
        const flatLookupResult = await db.query(
          "SELECT id FROM flats WHERE house_id = $1 AND number = $2",
          [houseId, flatNumber]
        );
        
        if (flatLookupResult.rows.length > 0) {
          flatId = flatLookupResult.rows[0].id;
        } else {
          // Create the flat
          const newFlatResult = await db.query(
            `INSERT INTO flats 
              (house_id, number, floor, created_at) 
             VALUES 
              ($1, $2, $3, NOW()) 
             RETURNING id`,
            [
              houseId,
              flatNumber,
              1  // Floor as integer
            ]
          );
          flatId = newFlatResult.rows[0].id;
        }
        
        // Create the user
        const userResult = await db.query(
          `INSERT INTO users 
            (username, name, email, password_hash, phone, street, house_number, flat_number, flat_id, role, is_profile_updated, created_at) 
           VALUES 
            ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW()) 
           RETURNING id`,
          [
            username,
            fullName,
            email,
            hashedPassword,
            phone,
            street,
            houseNumber,
            flatNumber,
            flatId,
            "user",
            email && fullName ? true : false
          ]
        );
        
        await db.query("COMMIT");
        results.imported++;
      } catch (error) {
        await db.query("ROLLBACK");
        throw error;
      }
    } catch (error: any) {
      console.error(`Error processing row ${rowNum}:`, error);
      results.errors.push({
        row: rowNum,
        message: error.message || "Klaida kuriant vartotoją"
      });
    }
  }
  
  return results;
}

// =========================================================================
// ANALYTICS FUNCTIONS
// =========================================================================

/**
 * Analyzes and summarizes import errors to provide actionable feedback
 * @param errors - Array of error objects
 * @returns Formatted summary string with analysis and recommendations
 */
function analyzeErrors(errors: { row: number; message: string }[]) {
  // Count error types
  const errorTypes = new Map<string, number>();
  const errorRows = new Map<number, number>();
  
  errors.forEach(error => {
    const errorType = error.message.split(':')[0];
    errorTypes.set(errorType, (errorTypes.get(errorType) || 0) + 1);
    errorRows.set(error.row, (errorRows.get(error.row) || 0) + 1);
  });
  
  // Identify rows with the most errors
  const rowsWithErrors = [...errorRows.entries()]
    .sort((a, b) => b[1] - a[1])
    .slice(0, 3);
  
  // Build summary
  let summary = "Analizė:\n";
  
  // Add common error types
  summary += "\nDažniausi klaidų tipai:\n";
  [...errorTypes.entries()]
    .sort((a, b) => b[1] - a[1])
    .forEach(([type, count]) => {
      summary += `- ${type}: ${count} kartų\n`;
    });
  
  // Add problematic rows
  summary += "\nEilutės su daugiausia klaidų:\n";
  rowsWithErrors.forEach(([row, count]) => {
    summary += `- Eilutė ${row}: ${count} klaidos\n`;
  });
  
  // Add recommendations
  summary += "\nRekomendacijos:\n";
  if (errorTypes.has("Neteisingas gatvės pavadinimas")) {
    summary += "- Įsitikinkite, kad gatvės pavadinimas yra įrašytas visoms eilutėms\n";
  }
  if (errorTypes.has("Nenurodyta namo numeris")) {
    summary += "- Patikrinkite, ar visiems įrašams nurodytas namo numeris\n";
  }
  if (errorTypes.has("Nenurodytas buto numeris")) {
    summary += "- Patikrinkite, ar visiems įrašams nurodytas buto numeris\n";
  }
  if (errorTypes.has("Nenurodytas mokėtojo kodas")) {
    summary += "- Įsitikinkite, kad visiems įrašams yra nurodytas mokėtojo kodas\n";
  }
  
  // Add advice about empty rows
  if (rowsWithErrors.some(([row]) => row > 650)) {
    summary += "- Failo pabaigoje gali būti tuščių eilučių. Pabandykite pašalinti visas tuščias eilutes iš Excel failo\n";
  }
  
  return summary;
}

// =========================================================================
// API HANDLER
// =========================================================================

/**
 * Handles the POST request for user import
 * @param req - The Next.js request object
 * @returns JSON response with import results
 */
export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    const isDevelopment = process.env.NODE_ENV === "development";
    
    // Check if user is authorized - only developers or super_admins in dev mode
    const isAuthorized = 
      (session?.user?.role === "developer") || 
      (isDevelopment && session?.user?.role === "super_admin");
    
    if (!session?.user || !isAuthorized) {
      logger.warn({
        userId: session?.user?.id,
        role: session?.user?.role,
        action: "access_denied",
        resource: "user_import"
      }, "Unauthorized access attempt to user import");
      
      return NextResponse.json(
        { error: "Unauthorized - only developers can import users" },
        { status: 403 }
      );
    }

    logger.info({
      userId: user.id,
      action: "import_started",
      resource: "users"
    }, "User import started");

    // Get the form data from the request
    const formData = await req.formData();
    const file = formData.get("file") as File;
    const columnMappingStr = formData.get("columnMapping") as string;
    
    if (!file) {
      logger.warn({
        userId: user.id,
        action: "import_failed",
        reason: "no_file"
      }, "User import failed: No file provided");
      
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }
    
    // Parse column mapping
    let columnMapping: Record<string, string> = {};
    try {
      if (columnMappingStr) {
        columnMapping = JSON.parse(columnMappingStr);
      }
    } catch (error) {
      logger.error({
        userId: user.id,
        action: "import_failed",
        reason: "invalid_mapping",
        error
      }, "Error parsing column mapping");
      
      return NextResponse.json(
        { error: "Invalid column mapping format" },
        { status: 400 }
      );
    }
    
    // Validate file type
    if (!file.name.endsWith(".xlsx")) {
      logger.warn({
        userId: user.id,
        action: "import_failed",
        reason: "invalid_file_type",
        fileType: file.name.split('.').pop()
      }, "Invalid file format for user import");
      
      return NextResponse.json(
        { error: "Invalid file format. Only .xlsx files are allowed" },
        { status: 400 }
      );
    }
    
    // Parse the Excel file
    let excelData = await parseExcelFile(file);
    
    // Filter out empty rows (rows where all required fields are empty)
    const originalRowCount = excelData.length;
    excelData = excelData.filter(row => {
      if (!columnMapping.street || !columnMapping.houseNumber || 
          !columnMapping.flatNumber || !columnMapping.payerCode) {
        return true; // Keep all rows if mapping is not complete (validation will catch this)
      }
      
      // Check if all required fields are empty
      const hasStreet = row[columnMapping.street] && row[columnMapping.street].toString().trim() !== "";
      const hasHouseNumber = row[columnMapping.houseNumber] && row[columnMapping.houseNumber].toString().trim() !== "";
      const hasFlatNumber = row[columnMapping.flatNumber] && row[columnMapping.flatNumber].toString().trim() !== "";
      const hasPayerCode = row[columnMapping.payerCode] && row[columnMapping.payerCode].toString().trim() !== "";
      
      return hasStreet || hasHouseNumber || hasFlatNumber || hasPayerCode;
    });
    
    logger.debug({
      userId: user.id,
      totalRows: originalRowCount,
      filteredRows: excelData.length,
      emptyRowsRemoved: originalRowCount - excelData.length
    }, "Filtered out empty rows from Excel file");
    
    // Validate the Excel data with column mapping
    const validationResult = validateExcelData(excelData, columnMapping);
    if (!validationResult.isValid) {
      logger.warn({
        userId: user.id,
        action: "import_validation_failed",
        errorCount: validationResult.errors.length,
        firstErrors: validationResult.errors.slice(0, 3)
      }, "User import validation failed");
      
      return NextResponse.json(
        {
          success: false,
          message: "Importavimas nepavyko dėl netinkamo failo formato.",
          details: {
            total: excelData.length,
            imported: 0,
            errors: validationResult.errors
          }
        },
        { status: 400 }
      );
    }
    
    // Process users for import with column mapping
    logger.info({
      userId: user.id,
      action: "processing_users",
      rowCount: excelData.length
    }, "Processing users for import");
    
    const importResult = await processUsers(excelData, columnMapping);
    
    // Add audit log
    await addAuditLog({
      userId: parseInt(user.id),
      action: "USERS_IMPORTED",
      entityType: "users",
      changes: {
        totalAttempted: excelData.length,
        totalImported: importResult.imported,
        totalErrors: importResult.errors.length,
      },
    });
    
    // Return response based on import results
    if (importResult.imported > 0) {
      logger.info({
        userId: user.id,
        action: "import_succeeded",
        totalAttempted: excelData.length,
        imported: importResult.imported,
        errors: importResult.errors.length
      }, "User import completed with some success");
      
      return NextResponse.json({
        success: true,
        message: importResult.errors.length > 0 
          ? `Importuota ${importResult.imported} vartotojų su ${importResult.errors.length} klaidomis.`
          : `Sėkmingai importuota ${importResult.imported} vartotojų.`,
        details: {
          total: excelData.length,
          imported: importResult.imported,
          errors: importResult.errors
        }
      });
    } else {
      // Analyze the errors to provide better feedback
      const errorSummary = analyzeErrors(importResult.errors);
      
      logger.warn({
        userId: user.id,
        action: "import_failed",
        reason: "all_rows_failed",
        totalAttempted: excelData.length,
        errorCount: importResult.errors.length,
        errorSummary
      }, "User import failed completely - no users were created");
      
      return NextResponse.json(
        {
          success: false,
          message: "Importavimas nepavyko, nepavyko sukurti nei vieno vartotojo.",
          summary: errorSummary,
          details: {
            total: excelData.length,
            filtered: originalRowCount - excelData.length,
            imported: 0,
            errors: importResult.errors
          }
        },
        { status: 400 }
      );
    }
  } catch (error: any) {
    logger.error({
      error: error.message,
      stack: error.stack
    }, "Unexpected error in user import API");
    
    return NextResponse.json(
      { 
        success: false,
        message: "Įvyko sistemos klaida bandant importuoti vartotojus",
        error: error.message || "Unknown error" 
      },
      { status: 500 }
    );
  }
}
