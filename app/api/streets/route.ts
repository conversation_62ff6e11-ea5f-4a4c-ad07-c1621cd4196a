import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { NextRequest } from "next/server";

export async function GET() {
  console.log("[STREETS_GET] Starting request");
  try {
    const user = await getCurrentUser();
    console.log("[STREETS_GET] User:", user?.username, user?.role);
    
    // Allow all authenticated users to access streets data
    if (!user) {
      console.log("[STREETS_GET] No user found, returning 401");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    try {
      const supabase = await createServiceClient();
      
      // Get streets data from database
      const { data: streets, error } = await supabase
        .from('streets')
        .select('id, name, city, created_at, updated_at')
        .order('name');
      
      if (error) {
        console.error("[STREETS_GET] DB error:", error);
        // Return empty array instead of error for better UX
        return NextResponse.json([]);
      }
      
      console.log("[STREETS_GET] Found streets:", streets?.length);
      
      // Transform data to match expected format
      const transformedStreets = streets?.map(street => ({
        id: street.id.toString(),
        name: street.name,
        city: street.city || 'Vilnius', // Use actual city from database, fallback to Vilnius
        createdAt: street.created_at
      })) || [];
      
      console.log("[STREETS_GET] Returning transformed streets:", transformedStreets.length);
      return NextResponse.json(transformedStreets);
    } catch (dbError) {
      console.error("[STREETS_GET] DB error:", dbError);
      // Return empty array instead of error
      return NextResponse.json([]);
    }
  } catch (error) {
    console.error("[STREETS_GET]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Only admin users can create streets
    if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Parse request body
    const body = await request.json();
    
    // Validate required fields
    if (!body.name || typeof body.name !== "string" || body.name.trim() === "") {
      return NextResponse.json(
        { error: "Street name is required" }, 
        { status: 400 }
      );
    }
    
    if (!body.city || typeof body.city !== "string" || body.city.trim() === "") {
      return NextResponse.json(
        { error: "City is required" }, 
        { status: 400 }
      );
    }
    
    const supabase = await createServiceClient();
    
    // Check if street already exists
    const { data: existingStreet } = await supabase
      .from('streets')
      .select('id')
      .eq('name', body.name.trim())
      .single();
    
    if (existingStreet) {
      return NextResponse.json(
        { error: "Street with this name already exists" }, 
        { status: 409 }
      );
    }
    
    // Insert new street
    const { data: newStreet, error } = await supabase
      .from('streets')
      .insert({
        name: body.name.trim(),
        city: body.city.trim(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) {
      console.error("[STREETS_POST] Insert error:", error);
      return NextResponse.json(
        { error: "Failed to create street" }, 
        { status: 500 }
      );
    }
    
    // Transform to match expected format
    const transformedStreet = {
      id: newStreet.id.toString(),
      name: newStreet.name,
      city: newStreet.city,
      createdAt: newStreet.created_at
    };
    
    return NextResponse.json(transformedStreet, { status: 201 });
  } catch (error) {
    console.error("[STREETS_POST]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}