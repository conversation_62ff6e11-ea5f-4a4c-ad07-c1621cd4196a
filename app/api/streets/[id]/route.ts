import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";

// GET /api/streets/[id] - Get a single street
export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const params = await context.params;
    const id = params.id;
    
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const supabase = await createServiceClient();
    
    const { data: street, error } = await supabase
      .from('streets')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error || !street) {
      return NextResponse.json({ error: "Street not found" }, { status: 404 });
    }
    
    return NextResponse.json(street);
  } catch (error) {
    console.error("[STREET_GET]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}

// Next.js 15 requires route handlers to define their params as the first parameter
// and await them before use
export async function DELETE(
  request: NextRequest,
  // Type definition based on Next.js 15 requirements
  context: { params: { id: string } }
) {
  try {
    // In Next.js 15, both the pathname and params are async
    const params = await context.params;
    const id = params.id;

    const user = await getCurrentUser();
    
    // Only admin users can delete streets
    if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const supabase = await createServiceClient();
    
    // First check if street is linked to houses
    const { count, error: countError } = await supabase
      .from('houses')
      .select('*', { count: 'exact', head: true })
      .eq('street_id', id);
    
    if (countError) {
      console.error("[STREET_DELETE] Count error:", countError);
      return NextResponse.json({ error: "Failed to check linked houses" }, { status: 500 });
    }
    
    const linkedHousesCount = count || 0;
    
    if (linkedHousesCount > 0) {
      return NextResponse.json(
        { error: `Cannot delete street: it is linked to ${linkedHousesCount} houses` },
        { status: 400 }
      );
    }
    
    // If no linked houses, delete the street
    const { error: deleteError } = await supabase
      .from('streets')
      .delete()
      .eq('id', id);
    
    if (deleteError) {
      console.error("[STREET_DELETE] Delete error:", deleteError);
      return NextResponse.json({ error: "Failed to delete street" }, { status: 500 });
    }
    
    return NextResponse.json({ message: "Street deleted successfully" });
  } catch (error) {
    console.error("[STREET_DELETE]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  context: { params: { id: string } }
) {
  // Get and await params first (Next.js 15 requirement)
  const params = await context.params;
  const id = params.id;
  
  try {
    const user = await getCurrentUser();
    
    // Only admin users can update streets
    if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Parse request body
    const body = await request.json();
    const { name, city } = body;
    
    // Validate required fields
    if (!name || !city) {
      return NextResponse.json(
        { error: "Name and city are required fields" }, 
        { status: 400 }
      );
    }
    
    const supabase = await createServiceClient();
    
    // Check if street exists
    const { data: existingStreet } = await supabase
      .from('streets')
      .select('id')
      .eq('id', id)
      .single();
    
    if (!existingStreet) {
      return NextResponse.json({ error: "Street not found" }, { status: 404 });
    }
    
    // Update street in database
    const { data: updatedStreet, error: updateError } = await supabase
      .from('streets')
      .update({ 
        name, 
        city, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', id)
      .select()
      .single();
    
    if (updateError) {
      console.error("[STREET_UPDATE] Update error:", updateError);
      return NextResponse.json({ error: "Failed to update street" }, { status: 500 });
    }
    
    return NextResponse.json({ 
      message: "Street updated successfully",
      street: updatedStreet
    });
  } catch (error) {
    console.error("[STREET_UPDATE]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
} 