import { NextRequest, NextResponse } from 'next/server';
import { signInWithUsername } from '@/lib/supabase/auth';
import { createClient } from '@/lib/supabase/server';

export async function POST(req: NextRequest) {
  try {
    const { username, password } = await req.json();

    if (!username || !password) {
      return NextResponse.json(
        { error: 'Username and password are required' },
        { status: 400 }
      );
    }

    // Use our custom username-based authentication
    const result = await signInWithUsername(username, password);

    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 401 }
      );
    }

    // Set session cookies for SSR using the server client
    const supabase = await createClient();
    
    if (result.data?.session) {
      // The signInWithUsername should have already set the session,
      // but we can ensure it's properly set by calling setSession
      await supabase.auth.setSession({
        access_token: result.data.session.access_token,
        refresh_token: result.data.session.refresh_token
      });
    }

    // Return success response
    const response = NextResponse.json(
      { 
        success: true,
        user: result.data.user,
        session: result.data.session 
      },
      { status: 200 }
    );

    return response;
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    );
  }
}