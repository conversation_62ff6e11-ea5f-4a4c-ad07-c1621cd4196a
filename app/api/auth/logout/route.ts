import { NextRequest, NextResponse } from 'next/server';
import { signOut } from '@/lib/supabase/auth';

export async function POST(req: NextRequest) {
  try {
    await signOut();
    
    return NextResponse.json(
      { success: true },
      { status: 200 }
    );
  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { error: 'Logout failed' },
      { status: 500 }
    );
  }
}