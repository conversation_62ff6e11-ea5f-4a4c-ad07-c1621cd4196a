import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";

export async function GET() {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ 
        authenticated: false,
        role: null
      });
    }
    
    return NextResponse.json({
      authenticated: true,
      role: user.role,
      id: user.id
    });
  } catch (error) {
    console.error("Error checking authentication:", error);
    return NextResponse.json(
      { error: "Failed to check authentication" },
      { status: 500 }
    );
  }
} 