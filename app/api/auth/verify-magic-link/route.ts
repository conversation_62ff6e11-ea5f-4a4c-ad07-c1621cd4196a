import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient, createClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Nepateikta prisijungimo nuoroda' },
        { status: 400 }
      );
    }

    const supabaseAdmin = await createServiceClient();

    // 1. Find user with this token
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('magic_link_token', token)
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Neteisinga arba pasenusi prisijungimo nuoroda' },
        { status: 404 }
      );
    }

    // 2. Check if token is expired
    const expires = new Date(user.magic_link_expires);
    const now = new Date();

    if (now > expires) {
      // Clear expired token
      await supabaseAdmin
        .from('users')
        .update({
          magic_link_token: null,
          magic_link_expires: null
        })
        .eq('id', user.id);

      return NextResponse.json(
        { error: 'Prisijungimo nuoroda nebegalioja. Prašykite naujos.' },
        { status: 401 }
      );
    }

    // 3. Check if account is disabled
    if (user.account_disabled) {
      return NextResponse.json(
        { error: 'Ši paskyra yra išjungta' },
        { status: 403 }
      );
    }

    // 4. Clear the magic link token (one-time use)
    await supabaseAdmin
      .from('users')
      .update({
        magic_link_token: null,
        magic_link_expires: null,
        last_login: new Date().toISOString()
      })
      .eq('id', user.id);

    // 5. Create Supabase session
    const supabase = await createClient();
    
    // Since we can't directly create a session with magic link,
    // we need to sign in with the user's email and a temporary password
    // First, ensure the user has a Supabase auth account
    
    if (!user.auth_user_id) {
      return NextResponse.json(
        { error: 'Vartotojas neturi aktyvuotos el. pašto autentifikacijos' },
        { status: 500 }
      );
    }

    // Generate a secure temporary password
    const tempPassword = `ML_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

    // Update the Supabase auth user's password
    const { error: passwordError } = await supabaseAdmin.auth.admin.updateUserById(
      user.auth_user_id,
      { password: tempPassword }
    );

    if (passwordError) {
      console.error('Error updating password for magic link:', passwordError);
      return NextResponse.json(
        { error: 'Nepavyko sukurti sesijos' },
        { status: 500 }
      );
    }

    // Sign in with the temporary password
    const { data: session, error: signInError } = await supabase.auth.signInWithPassword({
      email: user.email,
      password: tempPassword
    });

    if (signInError || !session) {
      console.error('Error signing in with magic link:', signInError);
      return NextResponse.json(
        { error: 'Nepavyko prisijungti' },
        { status: 500 }
      );
    }

    // 6. Log the magic link login
    try {
      await supabaseAdmin
        .from('audit_logs')
        .insert({
          user_id: user.id,
          action: 'magic_link_login',
          entity_type: 'auth',
          entity_id: user.id,
          details: {
            email: user.email,
            timestamp: new Date().toISOString(),
            user_agent: request.headers.get('user-agent')
          }
        });
    } catch (auditError) {
      console.error('Error logging magic link login:', auditError);
      // Continue anyway
    }

    return NextResponse.json({
      success: true,
      message: 'Sėkmingai prisijungta'
    });

  } catch (error) {
    console.error('Magic link verification error:', error);
    return NextResponse.json(
      { error: 'Įvyko klaida. Bandykite vėliau.' },
      { status: 500 }
    );
  }
}