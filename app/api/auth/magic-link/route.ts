import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase/server';
import { nanoid } from 'nanoid';
import { sendEmail } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email || !email.includes('@')) {
      return NextResponse.json(
        { error: 'Prašome įvesti galiojantį el. pašto adresą' },
        { status: 400 }
      );
    }

    const supabase = await createServiceClient();

    // 1. Check if user exists with this email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, username, email, is_profile_updated, gdpr_consent_given, account_disabled, magic_link_token, magic_link_expires')
      .eq('email', email.toLowerCase())
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { 
          error: 'Vartotojas su šiuo el. paštu nerastas',
          hint: 'Pirmą kartą prisijunkite naudodami savo vartotojo vardą (pvz., 44-18)'
        },
        { status: 404 }
      );
    }

    // 2. Check if account is disabled
    if (user.account_disabled) {
      return NextResponse.json(
        { error: 'Ši paskyra yra išjungta. Susisiekite su administratoriumi.' },
        { status: 403 }
      );
    }

    // 3. Check if profile is updated and GDPR consent given
    if (!user.is_profile_updated || !user.gdpr_consent_given) {
      return NextResponse.json(
        { 
          error: 'Prisijungimas el. paštu dar neaktyvuotas',
          hint: 'Pirmiausia prisijunkite naudodami vartotojo vardą (pvz., ' + user.username + ') ir užpildykite savo profilį'
        },
        { status: 403 }
      );
    }

    // 4. Check if there's an unexpired token
    if (user.magic_link_token && user.magic_link_expires) {
      const expiresAt = new Date(user.magic_link_expires);
      const now = new Date();
      
      if (expiresAt > now) {
        // Token still valid, don't send a new one
        const minutesLeft = Math.ceil((expiresAt.getTime() - now.getTime()) / 1000 / 60);
        return NextResponse.json(
          { 
            error: `Prisijungimo nuoroda jau išsiųsta. Patikrinkite savo el. paštą.`,
            hint: `Nuoroda galioja dar ${minutesLeft} min.`
          },
          { status: 429 }
        );
      }
    }

    // 5. Generate magic link token
    const token = nanoid(32);
    const expires = new Date();
    expires.setMinutes(expires.getMinutes() + 15); // 15 minutes expiry

    // 6. Save token to database
    const { error: updateError } = await supabase
      .from('users')
      .update({
        magic_link_token: token,
        magic_link_expires: expires.toISOString()
      })
      .eq('id', user.id);

    if (updateError) {
      console.error('Error saving magic link token:', updateError);
      return NextResponse.json(
        { error: 'Nepavyko sukurti prisijungimo nuorodos' },
        { status: 500 }
      );
    }

    // 7. Create magic link URL
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const magicLink = `${baseUrl}/auth/verify-magic-link?token=${token}`;

    // 8. Send email
    try {
      await sendEmail({
        to: user.email,
        subject: 'Prisijungimo nuoroda - DNSB Vakarai',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Prisijungimas prie DNSB Vakarai</h2>
            <p>Sveiki,</p>
            <p>Gavome prašymą prisijungti prie DNSB Vakarai platformos.</p>
            <p>Paspauskite žemiau esančią nuorodą, kad prisijungtumėte:</p>
            <div style="margin: 30px 0;">
              <a href="${magicLink}" 
                 style="background-color: #4F46E5; color: white; padding: 12px 24px; 
                        text-decoration: none; border-radius: 6px; display: inline-block;">
                Prisijungti
              </a>
            </div>
            <p>Arba nukopijuokite šią nuorodą į naršyklę:</p>
            <p style="background-color: #f3f4f6; padding: 12px; border-radius: 4px; 
                      word-break: break-all; font-size: 14px;">
              ${magicLink}
            </p>
            <p><strong>Svarbu:</strong> Ši nuoroda galioja 15 minučių.</p>
            <p>Jei neprašėte prisijungimo nuorodos, galite ignoruoti šį laišką.</p>
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 14px;">
              DNSB "Vakarai"<br>
              Žirmūnų g. 70, Vilnius
            </p>
          </div>
        `,
        text: `
          Prisijungimas prie DNSB Vakarai
          
          Sveiki,
          
          Gavome prašymą prisijungti prie DNSB Vakarai platformos.
          
          Paspauskite šią nuorodą, kad prisijungtumėte:
          ${magicLink}
          
          Svarbu: Ši nuoroda galioja 15 minučių.
          
          Jei neprašėte prisijungimo nuorodos, galite ignoruoti šį laišką.
          
          DNSB "Vakarai"
          Žirmūnų g. 70, Vilnius
        `
      });

      return NextResponse.json({
        success: true,
        message: 'Prisijungimo nuoroda išsiųsta į ' + email
      });

    } catch (emailError) {
      console.error('Error sending magic link email:', emailError);
      
      // Revert the token
      await supabase
        .from('users')
        .update({
          magic_link_token: null,
          magic_link_expires: null
        })
        .eq('id', user.id);

      return NextResponse.json(
        { error: 'Nepavyko išsiųsti el. laiško. Bandykite vėliau.' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Magic link error:', error);
    return NextResponse.json(
      { error: 'Įvyko klaida. Bandykite vėliau.' },
      { status: 500 }
    );
  }
}