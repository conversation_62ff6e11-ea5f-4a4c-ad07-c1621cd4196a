import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { sendEmail } from "@/lib/email";
import { createServiceClient } from "@/lib/supabase/server";

export async function POST(req: Request) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json(
        { error: "Turite būti <PERSON>, kad <PERSON> si<PERSON>" },
        { status: 401 }
      );
    }

    // Log incoming request for debugging
    const body = await req.json();
    console.log("Received contact message request:", {
      userId: user.id,
      sessionEmail: user.email,
      bodyData: body
    });

    const { category, subject, message, phoneNumber, email, name, isAnonymous } = body;

    // More detailed validation
    const missingFields = [];
    if (!category) missingFields.push('category');
    if (!subject) missingFields.push('subject');
    if (!message) missingFields.push('message');

    if (missingFields.length > 0) {
      console.error(`Validation failed, missing fields: ${missingFields.join(', ')}`);
      return NextResponse.json(
        { error: `Trūksta būtinų laukų: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Use session IDs if not provided explicitly
    const userId = user.id;
    const userEmail = email || user.email;
    const userName = name || user.name;

    console.log("Creating contact message with:", {
      userId, 
      email: userEmail, 
      name: userName,
      category,
      subject
    });

    // Save to database
    try {
      const supabase = await createServiceClient();
      
      const { data: contactMessage, error } = await supabase
        .from('contact_messages')
        .insert({
          user_id: userId,
          email: userEmail,
          name: userName,
          category,
          subject,
          message,
          phone_number: phoneNumber || null,
          is_anonymous: isAnonymous === "true",
          status: "NEW",
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error("Database error:", error);
        throw error;
      }

      // Log successful creation
      console.log("Message created successfully with ID:", contactMessage.id);

      // Categories in Lithuanian for the email
      const categoryLabels: Record<string, string> = {
        general: "Bendras klausimas",
        technical: "Techninis gedimas",
        financial: "Finansai ir mokėjimai",
        noise: "Triukšmas ir konfliktai",
        suggestion: "Pasiūlymas",
        other: "Kita",
      };

      // Get admin email from settings or use default
      let adminEmail = "<EMAIL>"; // Default
      
      try {
        const { data: adminSettings } = await supabase
          .from('admin_settings')
          .select('value')
          .eq('key', 'contactFormEmail')
          .single();
        
        if (adminSettings?.value) {
          adminEmail = adminSettings.value;
        }
      } catch (error) {
        console.error("Error fetching admin settings:", error);
        // Continue with default email
      }

      // Send email notification
      const emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #002855;">Naujas pranešimas iš gyventojo</h2>
          <p>Gavote naują pranešimą nuo gyventojo:</p>
          
          <div style="border-left: 3px solid #002855; padding-left: 10px; margin: 15px 0;">
            <p><strong>Tema:</strong> ${contactMessage.subject}</p>
            <p><strong>Siuntėjas:</strong> ${contactMessage.is_anonymous ? "Anoniminis" : contactMessage.name}</p>
            ${!contactMessage.is_anonymous ? `<p><strong>El. paštas:</strong> ${contactMessage.email}</p>` : ''}
            ${!contactMessage.is_anonymous && contactMessage.phone_number ? `<p><strong>Telefono nr.:</strong> ${contactMessage.phone_number}</p>` : ''}
            <p><strong>Kategorija:</strong> ${categoryLabels[contactMessage.category] || contactMessage.category}</p>
            <p><strong>Žinutė:</strong><br>${contactMessage.message.replace(/\n/g, '<br>')}</p>
          </div>
          
          <p>Norėdami peržiūrėti ir atnaujinti šį pranešimą, spauskite žemiau esantį mygtuką:</p>
          
          <div style="text-align: center; margin: 20px 0;">
            <a href="/dashboard/admin/messages/contact/${contactMessage.id}" style="background-color: #002855; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Peržiūrėti pranešimą
            </a>
          </div>
          
          <p style="font-size: 12px; color: #666; margin-top: 20px;">
            Šis laiškas sugeneruotas automatiškai. Prašome neatsakyti į šį laišką.
          </p>
        </div>
      `;

      await sendEmail({
        to: adminEmail,
        subject: `Naujas pranešimas: ${subject}`,
        html: emailHtml,
      });

      return NextResponse.json({ success: true, id: contactMessage.id });
    } catch (dbError) {
      console.error("Database error when creating contact message:", dbError);
      return NextResponse.json(
        { error: "Nepavyko išsaugoti jūsų pranešimo duomenų bazėje" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error processing contact message:", error);
    return NextResponse.json(
      { error: "Įvyko klaida apdorojant jūsų pranešimą" },
      { status: 500 }
    );
  }
} 