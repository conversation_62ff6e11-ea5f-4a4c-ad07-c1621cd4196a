import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { sendEmail } from "@/lib/email";
import { createServiceClient } from "@/lib/supabase/server";

export async function POST(req: Request) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json(
        { error: "Turite būti <PERSON>, kad <PERSON>te pateikti atsiliepim<PERSON>" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { category, title, feedback, rating, allowContact, email, name, isAnonymous } = body;

    // Basic validation
    if (!category || !title || !feedback || !rating) {
      return NextResponse.json(
        { error: "Prašome užpildyti visus būtinus laukus" },
        { status: 400 }
      );
    }

    // Save to database
    const supabase = await createServiceClient();
    
    const { data: feedbackMessage, error } = await supabase
      .from('feedback')
      .insert({
        user_id: user.id,
        email: email || user.email,
        name: name || user.name,
        category,
        title,
        content: feedback,
        rating: parseInt(rating, 10),
        allow_contact: isAnonymous ? false : allowContact,
        is_anonymous: isAnonymous === "true",
        status: "NEW",
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error("Database error:", error);
      throw error;
    }

    // Categories in Lithuanian for the email
    const categoryLabels: Record<string, string> = {
      administration: "Administracijos darbas",
      maintenance: "Pastatų priežiūra",
      accounting: "Apskaita ir mokėjimai",
      communication: "Komunikacija",
      service: "Aptarnavimo kokybė",
      website: "Svetainės patogumas",
      other: "Kita",
    };

    // Ratings in Lithuanian
    const ratingLabels: Record<string, string> = {
      "1": "Blogai (1 žvaigždutė)",
      "2": "Patenkinamai (2 žvaigždutės)",
      "3": "Gerai (3 žvaigždutės)",
      "4": "Labai gerai (4 žvaigždutės)",
      "5": "Puikiai (5 žvaigždutės)"
    };

    // Get admin email from settings or use default
    let adminEmail = "<EMAIL>"; // Default
    
    try {
      const adminSettings = await db.adminSettings.findFirst({
        where: { key: "feedbackFormEmail" }
      });
      
      if (adminSettings?.value) {
        adminEmail = adminSettings.value;
      }
    } catch (error) {
      console.error("Error fetching admin settings:", error);
      // Continue with default email
    }

    // Send email notification
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #002855;">Naujas atsiliepimas iš gyventojo</h2>
        <p>Gavote naują atsiliepimą nuo gyventojo:</p>
        
        <div style="border-left: 3px solid #002855; padding-left: 10px; margin: 15px 0;">
          <p><strong>Tema:</strong> ${feedbackMessage.title}</p>
          <p><strong>Siuntėjas:</strong> ${feedbackMessage.isAnonymous ? "Anoniminis" : feedbackMessage.name}</p>
          ${!feedbackMessage.isAnonymous ? `<p><strong>El. paštas:</strong> ${feedbackMessage.email}</p>` : ''}
          <p><strong>Įvertinimas:</strong> ${feedbackMessage.rating}/5 - ${ratingLabels[feedbackMessage.rating] || feedbackMessage.rating}</p>
          <p><strong>Kategorija:</strong> ${categoryLabels[feedbackMessage.category] || feedbackMessage.category}</p>
          <p><strong>Atsiliepimas:</strong><br>${feedbackMessage.content.replace(/\n/g, '<br>')}</p>
        </div>
        
        <p>Norėdami peržiūrėti ir atnaujinti šį atsiliepimą, spauskite žemiau esantį mygtuką:</p>
        
        <div style="text-align: center; margin: 20px 0;">
          <a href="/dashboard/admin/messages/feedback/${feedbackMessage.id}" style="background-color: #002855; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Peržiūrėti atsiliepimą
          </a>
        </div>
        
        <p style="font-size: 12px; color: #666; margin-top: 20px;">
          Šis laiškas sugeneruotas automatiškai. Prašome neatsakyti į šį laišką.
        </p>
      </div>
    `;

    await sendEmail({
      to: adminEmail,
      subject: `Naujas atsiliepimas: ${title}`,
      html: emailHtml,
    });

    return NextResponse.json({ success: true, id: feedbackMessage.id });
  } catch (error) {
    console.error("Error processing feedback:", error);
    return NextResponse.json(
      { error: "Įvyko klaida apdorojant jūsų atsiliepimą" },
      { status: 500 }
    );
  }
} 