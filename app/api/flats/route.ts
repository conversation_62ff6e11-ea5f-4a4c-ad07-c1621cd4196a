import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";

export async function GET(request: NextRequest) {
  console.log("[FLATS_GET] Starting request");
  try {
    const user = await getCurrentUser();
    
    // Check if user is authorized
    if (!user) {
      console.log("[FLATS_GET] No user found, returning 401");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Get query parameters
    const url = new URL(request.url);
    const houseId = url.searchParams.get("houseId");
    const streetId = url.searchParams.get("streetId");
    
    try {
      const supabase = await createServiceClient();
      
      // Build query with joins
      let query = supabase
        .from('flats')
        .select(`
          id,
          house_id,
          number,
          floor,
          created_at,
          updated_at,
          houses (
            id,
            name,
            address,
            street_id,
            streets (
              id,
              name
            )
          )
        `)
        .order('number');
      
      // Apply filters
      if (houseId) {
        query = query.eq('house_id', houseId);
      }
      
      if (streetId) {
        // To filter by street_id, we need to use a different approach
        // First get houses in that street, then filter flats
        const { data: housesInStreet } = await supabase
          .from('houses')
          .select('id')
          .eq('street_id', streetId);
        
        if (housesInStreet && housesInStreet.length > 0) {
          const houseIds = housesInStreet.map(h => h.id);
          query = query.in('house_id', houseIds);
        } else {
          // No houses in that street, return empty
          return NextResponse.json([]);
        }
      }
      
      const { data: flats, error } = await query;
      
      if (error) {
        console.error("[FLATS_GET] DB error:", error);
        // Return empty array instead of error for better UX
        return NextResponse.json([]);
      }
      
      // Transform data to match expected format
      const transformedFlats = flats?.map(flat => {
        const house = flat.houses as any;
        const street = house?.streets as any;
        
        return {
          id: flat.id.toString(),
          houseId: flat.house_id.toString(),
          number: flat.number,
          floor: flat.floor,
          houseName: house?.name || '',
          houseAddress: house?.address || '',
          streetId: street?.id?.toString() || null,
          streetName: street?.name || null,
          displayName: street?.name 
            ? `${street.name} ${house.name} - ${flat.number}`
            : `${house?.name || ''} - ${flat.number}`,
          createdAt: flat.created_at,
          updatedAt: flat.updated_at
        };
      }) || [];
      
      // Sort by street name, house name, then flat number
      transformedFlats.sort((a, b) => {
        // First sort by street name
        if (a.streetName && b.streetName) {
          const streetCompare = a.streetName.localeCompare(b.streetName);
          if (streetCompare !== 0) return streetCompare;
        } else if (a.streetName && !b.streetName) {
          return -1;
        } else if (!a.streetName && b.streetName) {
          return 1;
        }
        
        // Then by house name
        const houseCompare = a.houseName.localeCompare(b.houseName);
        if (houseCompare !== 0) return houseCompare;
        
        // Finally by flat number (numeric sort)
        const aNum = parseInt(a.number) || 0;
        const bNum = parseInt(b.number) || 0;
        return aNum - bNum;
      });
      
      return NextResponse.json(transformedFlats);
    } catch (dbError) {
      console.error("[FLATS_GET] DB error:", dbError);
      // Return empty array instead of error
      return NextResponse.json([]);
    }
  } catch (error) {
    console.error("[FLATS_GET]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Only admin users can create flats
    if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Parse request body
    const body = await request.json();
    
    // Validate required fields
    if (!body.number || typeof body.number !== "string" || body.number.trim() === "") {
      return NextResponse.json(
        { error: "Flat number is required" }, 
        { status: 400 }
      );
    }
    
    if (!body.houseId) {
      return NextResponse.json(
        { error: "House ID is required" }, 
        { status: 400 }
      );
    }
    
    const supabase = await createServiceClient();
    
    // Validate if house exists and get its data with street info
    const { data: house, error: houseError } = await supabase
      .from('houses')
      .select(`
        id,
        name,
        address,
        street_id,
        streets (
          id,
          name,
          city
        )
      `)
      .eq('id', body.houseId)
      .single();
    
    if (houseError || !house) {
      return NextResponse.json(
        { error: "House not found" },
        { status: 404 }
      );
    }
    
    // Check if flat with same number already exists in this house
    const { data: existingFlat } = await supabase
      .from('flats')
      .select('id')
      .eq('house_id', body.houseId)
      .eq('number', body.number.trim())
      .single();
    
    if (existingFlat) {
      return NextResponse.json(
        { error: `Flat with number ${body.number} already exists in this house` },
        { status: 409 }
      );
    }
    
    // Process floor (optional)
    const floor = body.floor ? body.floor.toString().trim() : null;
    
    // Insert new flat
    const { data: newFlat, error: insertError } = await supabase
      .from('flats')
      .insert({
        house_id: body.houseId,
        number: body.number.trim(),
        floor: floor,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (insertError) {
      console.error("[FLATS_POST] Insert error:", insertError);
      return NextResponse.json(
        { error: "Failed to create flat" },
        { status: 500 }
      );
    }
    
    // Get street information from the house data
    const street = house.streets as any;
    
    // Format the response
    const response = {
      id: newFlat.id.toString(),
      number: newFlat.number,
      floor: newFlat.floor,
      houseId: newFlat.house_id.toString(),
      houseName: house.name,
      houseAddress: house.address,
      streetId: street?.id?.toString() || null,
      streetName: street?.name || null,
      displayName: street?.name 
        ? `${street.name} ${house.name} - ${newFlat.number}`
        : `${house.name} - ${newFlat.number}`,
      createdAt: newFlat.created_at,
      updatedAt: newFlat.updated_at
    };
    
    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error("[FLATS_POST]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
} 