import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/flats/[id] - Get a single flat
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const { id: flatId } = await params;
    
    const supabase = await createServiceClient();
    
    const { data: flat, error } = await supabase
      .from('flats')
      .select(`
        id,
        number,
        floor,
        house_id,
        created_at,
        updated_at,
        houses (
          id,
          name,
          street_id,
          streets (
            id,
            name
          )
        )
      `)
      .eq('id', flatId)
      .single();
    
    if (error || !flat) {
      return NextResponse.json({ error: "Flat not found" }, { status: 404 });
    }
    
    return NextResponse.json(flat);
  } catch (error) {
    console.error("[FLAT_GET]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}

// PATCH /api/flats/[id] - Update a flat
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const user = await getCurrentUser();
    
    // Only admin users can update flats
    if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const { id: flatId } = await params;
    const body = await request.json();
    
    // Validate required fields
    if (!body.number || !body.house_id) {
      return NextResponse.json(
        { error: "Flat number and house are required" }, 
        { status: 400 }
      );
    }
    
    const supabase = await createServiceClient();
    
    // Check if flat exists
    const { data: existingFlat } = await supabase
      .from('flats')
      .select('id')
      .eq('id', flatId)
      .single();
    
    if (!existingFlat) {
      return NextResponse.json({ error: "Flat not found" }, { status: 404 });
    }
    
    // Check if another flat with the same number exists in the same house
    const { data: duplicateFlat } = await supabase
      .from('flats')
      .select('id')
      .eq('house_id', body.house_id)
      .eq('number', body.number)
      .neq('id', flatId)
      .single();
    
    if (duplicateFlat) {
      return NextResponse.json(
        { error: "A flat with this number already exists in this house" }, 
        { status: 409 }
      );
    }
    
    // Update the flat
    const { data: updatedFlat, error } = await supabase
      .from('flats')
      .update({
        number: body.number.trim(),
        floor: body.floor || null,
        house_id: body.house_id,
        updated_at: new Date().toISOString()
      })
      .eq('id', flatId)
      .select()
      .single();
    
    if (error) {
      console.error("[FLAT_PATCH] Update error:", error);
      return NextResponse.json(
        { error: "Failed to update flat" }, 
        { status: 500 }
      );
    }
    
    return NextResponse.json(updatedFlat);
  } catch (error) {
    console.error("[FLAT_PATCH]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}

// DELETE /api/flats/[id] - Delete a flat
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const user = await getCurrentUser();
    
    // Only admin users can delete flats
    if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const { id: flatId } = await params;
    
    const supabase = await createServiceClient();
    
    // Check if flat is linked to any users
    const { data: linkedUsers } = await supabase
      .from('users')
      .select('id')
      .eq('flat_id', flatId)
      .limit(1);
    
    if (linkedUsers && linkedUsers.length > 0) {
      return NextResponse.json(
        { error: "Cannot delete flat that has associated users" }, 
        { status: 409 }
      );
    }
    
    // Delete the flat
    const { error } = await supabase
      .from('flats')
      .delete()
      .eq('id', flatId);
    
    if (error) {
      console.error("[FLAT_DELETE] Delete error:", error);
      return NextResponse.json(
        { error: "Failed to delete flat" }, 
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[FLAT_DELETE]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}