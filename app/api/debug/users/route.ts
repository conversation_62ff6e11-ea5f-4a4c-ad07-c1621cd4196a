import { NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/supabase/auth';
import { db } from '@/lib/db/supabase-adapter';

export async function GET() {
  try {
    const user = await getCurrentUser();
    
    // Only allow super_admin to access this debug endpoint
    if (!session || user.role !== 'super_admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const result = await db.query(`
      SELECT 
        u.id, 
        u.username, 
        u.name, 
        u.email, 
        u.role, 
        u.is_profile_updated,
        u.created_at,
        h.name as house_name,
        f.number as flat_number
      FROM 
        users u
      LEFT JOIN
        flats f ON u.flat_id = f.id
      LEFT JOIN
        houses h ON f.house_id = h.id
      ORDER BY 
        u.role, u.name
    `);
    
    return NextResponse.json({
      count: result.rows.length,
      users: result.rows
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
} 