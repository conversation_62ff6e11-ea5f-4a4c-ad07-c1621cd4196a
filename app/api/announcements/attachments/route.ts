import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { getStorageService, generateCDNPath } from "@/lib/services/storage-factory";
import { validateFile, generateFileHash } from "@/lib/utils/file-validation";
import { cdnMonitor } from "@/lib/monitoring/cdn-monitor";


export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    if (!["developer", "super_admin", "editor"].includes(user.role)) {
      return NextResponse.json(
        { error: "Only admins and editors can upload attachments" },
        { status: 403 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const announcementId = formData.get('announcementId') as string;
    
    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }
    
    if (!announcementId) {
      return NextResponse.json({ error: "Announcement ID is required" }, { status: 400 });
    }

    // Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
      return NextResponse.json({ 
        error: validation.error 
      }, { status: 400 });
    }

    const supabase = await createServiceClient();
    
    // Verify announcement exists and user can edit it
    const { data: announcement, error: announcementError } = await supabase
      .from('announcements')
      .select('id, author_id')
      .eq('id', announcementId)
      .single();
    
    if (announcementError || !announcement) {
      return NextResponse.json({ error: "Announcement not found" }, { status: 404 });
    }

    // Generate file hash for deduplication
    const fileHash = await generateFileHash(file);
    
    // Check if file already exists
    const { data: existingFile } = await supabase
      .from('announcement_attachments')
      .select('*')
      .eq('file_hash', fileHash)
      .eq('announcement_id', parseInt(announcementId))
      .single();
    
    if (existingFile) {
      return NextResponse.json({
        success: true,
        attachment: {
          id: existingFile.id,
          file_name: existingFile.file_name,
          file_size: existingFile.file_size,
          file_type: existingFile.file_type,
          is_image: existingFile.is_image,
          created_at: existingFile.created_at,
          cdn_url: existingFile.cdn_url
        }
      });
    }

    // Generate CDN path
    const cdnPath = generateCDNPath(announcementId, validation.sanitizedFileName!);
    
    // Upload file to CDN
    const fileBuffer = Buffer.from(await file.arrayBuffer());
    const storageService = getStorageService();
    
    const uploadResult = await cdnMonitor.trackUpload(async () => {
      return await storageService.uploadFile(fileBuffer, cdnPath, {
        contentType: file.type,
        overwrite: false
      });
    });

    // Save attachment metadata to database
    const { data: attachment, error: dbError } = await supabase
      .from('announcement_attachments')
      .insert({
        announcement_id: parseInt(announcementId),
        file_name: file.name,
        file_path: cdnPath,
        file_size: file.size,
        file_type: file.type,
        is_image: validation.category === 'images',
        cdn_path: cdnPath,
        cdn_url: uploadResult.url,
        file_hash: fileHash,
        processing_status: 'completed'
      })
      .select()
      .single();

    if (dbError) {
      console.error('Database insert error:', dbError);
      
      // Clean up uploaded file if database insert fails
      await storageService.deleteFile(cdnPath);
      
      return NextResponse.json({ 
        error: "Nepavyko išsaugoti failo informacijos" 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      attachment: {
        id: attachment.id,
        file_name: attachment.file_name,
        file_size: attachment.file_size,
        file_type: attachment.file_type,
        is_image: attachment.is_image,
        created_at: attachment.created_at,
        cdn_url: attachment.cdn_url
      }
    });

  } catch (error) {
    console.error('File upload error:', error);
    return NextResponse.json(
      { error: "Nepavyko įkelti failo" },
      { status: 500 }
    );
  }
}

// GET endpoint to fetch attachments for an announcement
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const announcementId = searchParams.get('announcementId');
    
    if (!announcementId) {
      return NextResponse.json({ error: "Announcement ID is required" }, { status: 400 });
    }

    const supabase = await createServiceClient();
    
    // Get attachments for the announcement
    const { data: attachments, error } = await supabase
      .from('announcement_attachments')
      .select('*')
      .eq('announcement_id', announcementId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching attachments:', error);
      return NextResponse.json({ 
        error: "Nepavyko gauti priedų" 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      attachments: attachments || []
    });

  } catch (error) {
    console.error('Error fetching attachments:', error);
    return NextResponse.json(
      { error: "Nepavyko gauti priedų" },
      { status: 500 }
    );
  }
}
