import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { getStorageService } from "@/lib/services/storage-factory";
import { cdnMonitor } from "@/lib/monitoring/cdn-monitor";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    
    if (!id) {
      return NextResponse.json({ error: "Attachment ID is required" }, { status: 400 });
    }

    const supabase = await createServiceClient();
    
    // Get attachment metadata
    const { data: attachment, error: attachmentError } = await supabase
      .from('announcement_attachments')
      .select(`
        *,
        announcements!inner(id, is_draft, author_id)
      `)
      .eq('id', id)
      .single();

    if (attachmentError || !attachment) {
      return NextResponse.json({ error: "Attachment not found" }, { status: 404 });
    }

    // Check if user can access this attachment
    const announcement = attachment.announcements;
    const isAdmin = ["developer", "super_admin", "editor"].includes(user.role);
    
    // Non-admins can't access attachments from draft announcements
    if (announcement.is_draft && !isAdmin) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Track download in database
    await supabase
      .from('attachment_operations')
      .insert({
        attachment_id: parseInt(id),
        operation_type: 'download',
        user_id: user.id,
        ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
        user_agent: request.headers.get('user-agent'),
        metadata: { source: 'api' }
      });

    // Update download count and last accessed
    await supabase
      .from('announcement_attachments')
      .update({
        download_count: (attachment.download_count || 0) + 1,
        last_accessed_at: new Date().toISOString()
      })
      .eq('id', id);

    // Get CDN URL
    const storageService = getStorageService();
    let downloadUrl: string;

    if (attachment.cdn_url) {
      // Use existing CDN URL
      downloadUrl = attachment.cdn_url;
    } else {
      // Generate URL from storage service
      downloadUrl = await cdnMonitor.trackDownload(async () => {
        return await storageService.getFileUrl(attachment.file_path || attachment.cdn_path, {
          download: true,
          fileName: attachment.file_name
        });
      });
    }

    // Return file info and download URL
    return NextResponse.json({
      success: true,
      attachment: {
        id: attachment.id,
        file_name: attachment.file_name,
        file_size: attachment.file_size,
        file_type: attachment.file_type,
        is_image: attachment.is_image,
        download_url: downloadUrl
      }
    });

  } catch (error) {
    console.error('Error accessing attachment:', error);
    return NextResponse.json(
      { error: "Nepavyko gauti failo" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    if (!["developer", "super_admin", "editor"].includes(user.role)) {
      return NextResponse.json(
        { error: "Only admins and editors can delete attachments" },
        { status: 403 }
      );
    }

    const { id } = await params;
    
    if (!id) {
      return NextResponse.json({ error: "Attachment ID is required" }, { status: 400 });
    }

    const supabase = await createServiceClient();
    
    // Get attachment metadata before deletion
    const { data: attachment, error: attachmentError } = await supabase
      .from('announcement_attachments')
      .select('*')
      .eq('id', id)
      .single();

    if (attachmentError || !attachment) {
      return NextResponse.json({ error: "Attachment not found" }, { status: 404 });
    }

    // Delete from CDN/storage
    const storageService = getStorageService();
    const filePath = attachment.cdn_path || attachment.file_path;
    
    if (filePath) {
      try {
        await storageService.deleteFile(filePath);
      } catch (error) {
        console.error('Storage deletion error:', error);
        // Continue with database deletion even if storage deletion fails
      }
    }

    // Delete from database
    const { error: dbError } = await supabase
      .from('announcement_attachments')
      .delete()
      .eq('id', id);

    if (dbError) {
      console.error('Database deletion error:', dbError);
      return NextResponse.json({ 
        error: "Nepavyko ištrinti failo informacijos" 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: "Failas sėkmingai ištrintas"
    });

  } catch (error) {
    console.error('Error deleting attachment:', error);
    return NextResponse.json(
      { error: "Nepavyko ištrinti failo" },
      { status: 500 }
    );
  }
}
