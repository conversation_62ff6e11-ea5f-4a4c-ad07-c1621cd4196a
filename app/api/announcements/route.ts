import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { addAuditLog } from "@/lib/utils";

export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Allow unauthenticated access to public announcements
    const { searchParams } = new URL(req.url);
    const houseId = searchParams.get("houseId");
    const flatId = searchParams.get("flatId");
    const isAdmin = user ? ["developer", "super_admin", "editor"].includes(user.role) : false;
    
    const supabase = await createServiceClient();
    
    let query = supabase
      .from('announcements')
      .select(`
        id,
        title,
        content,
        importance,
        recipient_type,
        is_draft,
        created_at,
        updated_at,
        sent_at,
        sender_id,
        author_id
      `);
    
    // For non-admin users (including unauthenticated), only show non-draft announcements
    if (!isAdmin) {
      query = query.eq('is_draft', false);
      
      // If user is logged in, filter by their flat/house access
      if (user) {
        const userId = parseInt(user.id);
        
        // Get user's flat and house info
        const { data: userFlat } = await supabase
          .from('users')
          .select(`
            flat_id,
            flats:flat_id (
              id,
              house_id,
              houses:house_id (
                id,
                street_id
              )
            )
          `)
          .eq('id', userId)
          .single();
        
        if (userFlat?.flat_id) {
          const flatId = userFlat.flat_id;
          const houseId = userFlat.flats?.house_id;
          
          // Filter announcements based on recipient type and user's location
          query = query.or(`
            recipient_type.eq.all,
            and(recipient_type.eq.flats,announcement_flats.flat_id.eq.${flatId}),
            and(recipient_type.eq.houses,announcement_houses.house_id.eq.${houseId}),
            and(recipient_type.eq.users,announcement_users.user_id.eq.${userId})
          `);
        } else {
          // User without flat can only see 'all' announcements
          query = query.eq('recipient_type', 'all');
        }
      } else {
        // Unauthenticated users can only see 'all' announcements
        query = query.eq('recipient_type', 'all');
      }
    }
    
    // Add filtering by houseId or flatId if provided (admin use)
    if (houseId) {
      query = query
        .eq('recipient_type', 'houses')
        .in('id', 
          supabase
            .from('announcement_houses')
            .select('announcement_id')
            .eq('house_id', houseId)
        );
    } else if (flatId) {
      query = query
        .eq('recipient_type', 'flats')
        .in('id',
          supabase
            .from('announcement_flats')
            .select('announcement_id')
            .eq('flat_id', flatId)
        );
    }
    
    // Order by importance and date
    query = query.order('sent_at', { ascending: false, nullsFirst: false })
                 .order('created_at', { ascending: false });
    
    const { data: announcements, error } = await query;
    
    if (error) {
      console.error("Error fetching announcements:", error);
      throw error;
    }
    
    // Get author names for announcements
    const authorIds = [...new Set((announcements || []).map(a => a.author_id || a.sender_id).filter(Boolean))];
    let authorNames: Record<string, string> = {};
    
    if (authorIds.length > 0) {
      const { data: authors } = await supabase
        .from('users')
        .select('id, name')
        .in('id', authorIds);
      
      authorNames = (authors || []).reduce((acc, author) => {
        acc[author.id] = author.name;
        return acc;
      }, {} as Record<string, string>);
    }
    
    // Fetch tags for announcements using entity_tags table
    const announcementIds = (announcements || []).map(a => a.id);
    let announcementTags: Record<number, any[]> = {};
    
    if (announcementIds.length > 0) {
      // Fetch all entity_tags for announcements
      const { data: tagRelations } = await supabase
        .from('entity_tags')
        .select('entity_id, tag_id')
        .eq('entity_type', 'announcement')
        .in('entity_id', announcementIds.map(id => id.toString()));
      
      if (tagRelations && tagRelations.length > 0) {
        // Get unique tag IDs
        const tagIds = [...new Set(tagRelations.map(r => r.tag_id))];
        
        // Fetch all tags
        const { data: tags } = await supabase
          .from('tags')
          .select('id, name, color, category')
          .in('id', tagIds);
        
        // Create a map of tag_id to tag data
        const tagMap = (tags || []).reduce((acc, tag) => {
          acc[tag.id] = tag;
          return acc;
        }, {} as Record<number, any>);
        
        // Group tags by entity_id (announcement_id)
        tagRelations.forEach(relation => {
          const entityId = parseInt(relation.entity_id);
          if (!announcementTags[entityId]) {
            announcementTags[entityId] = [];
          }
          if (tagMap[relation.tag_id]) {
            announcementTags[entityId].push(tagMap[relation.tag_id]);
          }
        });
      }
    }
    
    // Transform announcements to match expected format
    const transformedAnnouncements = (announcements || []).map(announcement => ({
      ...announcement,
      author_name: authorNames[announcement.author_id || announcement.sender_id] || 'System',
      author_id: announcement.author_id || announcement.sender_id,
      tags: announcementTags[announcement.id] || []
    }));
    
    return NextResponse.json(transformedAnnouncements);
  } catch (error) {
    console.error("Error fetching announcements:", error);
    return NextResponse.json(
      { error: "Failed to fetch announcements" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    if (user.role !== "developer" && user.role !== "super_admin" && user.role !== "editor") {
      return NextResponse.json(
        { error: "Only admins and editors can create announcements" },
        { status: 403 }
      );
    }
    
    const { 
      title, 
      content, 
      importance = 'normal',
      isDraft = false,
      recipientType = 'all',
      streetIds = [],
      houseIds = [], 
      flatIds = [], 
      userIds = [],
      tagIds = [],
      sendEmails = true
    } = await req.json();
    
    // Convert string IDs to numbers
    const numericStreetIds = streetIds.map(id => parseInt(id));
    const numericHouseIds = houseIds.map(id => parseInt(id));
    const numericFlatIds = flatIds.map(id => parseInt(id));
    const numericUserIds = userIds.map(id => parseInt(id));
    const numericTagIds = tagIds.map(id => parseInt(id));
    
    if (!title || !content) {
      return NextResponse.json(
        { error: "Title and content are required" },
        { status: 400 }
      );
    }
    
    // Make sure we have valid targeting data based on recipientType
    if (recipientType === 'streets' && (!streetIds || streetIds.length === 0)) {
      return NextResponse.json(
        { error: "Street IDs are required when recipient type is 'streets'" },
        { status: 400 }
      );
    }
    
    if (recipientType === 'houses' && (!houseIds || houseIds.length === 0)) {
      return NextResponse.json(
        { error: "House IDs are required when recipient type is 'houses'" },
        { status: 400 }
      );
    }
    
    if (recipientType === 'flats' && (!flatIds || flatIds.length === 0)) {
      return NextResponse.json(
        { error: "Flat IDs are required when recipient type is 'flats'" },
        { status: 400 }
      );
    }
    
    if (recipientType === 'users' && (!userIds || userIds.length === 0)) {
      return NextResponse.json(
        { error: "User IDs are required when recipient type is 'users'" },
        { status: 400 }
      );
    }
    
    const supabase = await createServiceClient();
    
    try {
      // Create the announcement
      const { data: announcement, error: insertError } = await supabase
        .from('announcements')
        .insert({
          title, 
          content, 
          author_id: parseInt(user.id),
          sender_id: parseInt(user.id), 
          importance, 
          is_draft: isDraft, 
          recipient_type: recipientType,
          created_at: new Date().toISOString(),
          sent_at: isDraft ? null : new Date().toISOString(),
          send_emails: sendEmails
        })
        .select('id, title, content, importance, recipient_type, is_draft, created_at, sent_at')
        .single();
      
      if (insertError) {
        console.error("Error creating announcement:", insertError);
        throw insertError;
      }
      
      // If targeting streets, handle street associations and also add associated houses
      if (recipientType === 'streets' && numericStreetIds.length > 0) {
        // Find all houses in these streets
        const { data: houses, error: housesError } = await supabase
          .from('houses')
          .select('id')
          .in('street_id', numericStreetIds);
        
        if (housesError) {
          console.error("Error fetching houses:", housesError);
          throw housesError;
        }
        
        if (houses && houses.length > 0) {
          const houseAssociations = houses.map(h => ({
            announcement_id: announcement.id,
            house_id: h.id
          }));
          
          const { error: houseInsertError } = await supabase
            .from('announcement_houses')
            .insert(houseAssociations);
          
          if (houseInsertError) {
            console.error("Error inserting announcement_houses:", houseInsertError);
            throw houseInsertError;
          }
        }
      }
      
      // Handle house associations
      if ((recipientType === 'houses' || recipientType === 'streets') && numericHouseIds.length > 0) {
        const houseAssociations = numericHouseIds.map(houseId => ({
          announcement_id: announcement.id,
          house_id: houseId
        }));
        
        const { error: houseInsertError } = await supabase
          .from('announcement_houses')
          .insert(houseAssociations);
        
        if (houseInsertError) {
          console.error("Error inserting announcement_houses:", houseInsertError);
          throw houseInsertError;
        }
      }
      
      // Handle flat associations
      if (recipientType === 'flats' && numericFlatIds.length > 0) {
        const flatAssociations = numericFlatIds.map(flatId => ({
          announcement_id: announcement.id,
          flat_id: flatId
        }));
        
        const { error: flatInsertError } = await supabase
          .from('announcement_flats')
          .insert(flatAssociations);
        
        if (flatInsertError) {
          console.error("Error inserting announcement_flats:", flatInsertError);
          throw flatInsertError;
        }
      }
      
      // Handle user associations
      if (recipientType === 'users' && numericUserIds.length > 0) {
        console.log(`[POST /api/announcements] Inserting announcement_users for announcement ${announcement.id}:`, numericUserIds);
        const userAssociations = numericUserIds.map(userId => ({
          announcement_id: announcement.id,
          user_id: userId
        }));
        
        const { error: userInsertError } = await supabase
          .from('announcement_users')
          .insert(userAssociations);
        
        if (userInsertError) {
          console.error("Error inserting announcement_users:", userInsertError);
          throw userInsertError;
        }
      }
      
      // Handle tag associations
      if (numericTagIds.length > 0) {
        const tagAssociations = numericTagIds.map(tagId => ({
          announcement_id: announcement.id,
          tag_id: tagId
        }));
        
        const { error: tagInsertError } = await supabase
          .from('announcement_tags')
          .insert(tagAssociations);
        
        if (tagInsertError) {
          console.error("Error inserting announcement_tags:", tagInsertError);
          throw tagInsertError;
        }
      }
      
      // Add audit log
      await addAuditLog({
        action: isDraft ? 'create_announcement_draft' : 'create_announcement',
        userId: parseInt(user.id),
        entityType: 'announcement',
        entityId: announcement.id,
        changes: {
          title,
          recipientType,
          importance,
          tagIds: numericTagIds,
          sendEmails
        },
      });
      
      return NextResponse.json(announcement);
    } catch (error) {
      // Note: Supabase doesn't have traditional transactions in the same way
      // If any insert fails, we might need to clean up manually
      console.error("Error in announcement creation:", error);
      throw error;
    }
  } catch (error) {
    console.error("Error creating announcement:", error);
    return NextResponse.json(
      { error: "Failed to create announcement" },
      { status: 500 }
    );
  }
} 