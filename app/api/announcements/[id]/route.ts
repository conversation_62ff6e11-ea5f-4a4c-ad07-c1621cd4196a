import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";
import { createServiceClient } from "@/lib/supabase/server";

export async function GET(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const { id } = await context.params;
    const isAdmin = ["developer", "super_admin", "editor"].includes(user.role);
    const supabase = await createServiceClient();
    
    // Get the announcement details first
    const { data: announcement, error: announcementError } = await supabase
      .from('announcements')
      .select(`
        id,
        title,
        content,
        importance,
        recipient_type,
        is_draft,
        created_at,
        updated_at,
        sent_at,
        author_id
      `)
      .eq('id', id)
      .single();
    
    if (announcementError || !announcement) {
      return NextResponse.json({ error: "Announcement not found" }, { status: 404 });
    }
    
    // If not admin, check if user has access to this announcement
    if (!isAdmin) {
      const userId = parseInt(user.id);
      
      // Get user's flat and house
      const { data: userData } = await supabase
        .from('users')
        .select('flat_id')
        .eq('id', userId)
        .single();
      
      if (!userData?.flat_id) {
        // User without a flat can only see announcements targeted to all
        if (announcement.recipient_type !== 'all' || announcement.is_draft) {
          return NextResponse.json(
            { error: "You don't have access to this announcement" },
            { status: 403 }
          );
        }
      } else {
        // Get house ID
        const { data: flatData } = await supabase
          .from('flats')
          .select('house_id')
          .eq('id', userData.flat_id)
          .single();
        
        if (flatData?.house_id) {
          const userHouseId = flatData.house_id;
          
          // Check access based on recipient type
          let hasAccess = false;
          
          if (announcement.recipient_type === 'all' && !announcement.is_draft) {
            hasAccess = true;
          } else if (announcement.recipient_type === 'houses') {
            const { data: houseAccess } = await supabase
              .from('announcement_houses')
              .select('id')
              .eq('announcement_id', id)
              .eq('house_id', userHouseId)
              .single();
            hasAccess = !!houseAccess;
          } else if (announcement.recipient_type === 'flats') {
            const { data: flatAccess } = await supabase
              .from('announcement_flats')
              .select('id')
              .eq('announcement_id', id)
              .eq('flat_id', userData.flat_id)
              .single();
            hasAccess = !!flatAccess;
          } else if (announcement.recipient_type === 'users') {
            const { data: userAccess } = await supabase
              .from('announcement_users')
              .select('id')
              .eq('announcement_id', id)
              .eq('user_id', userId)
              .single();
            hasAccess = !!userAccess;
          }
          
          if (!hasAccess && !announcement.is_draft) {
            return NextResponse.json(
              { error: "You don't have access to this announcement" },
              { status: 403 }
            );
          }
        } else {
          // If we can't determine the house, only allow access to 'all' announcements
          if (announcement.recipient_type !== 'all' || announcement.is_draft) {
            return NextResponse.json(
              { error: "You don't have access to this announcement" },
              { status: 403 }
            );
          }
        }
      }
    }
    
    // Get author name
    const { data: author } = await supabase
      .from('users')
      .select('id, name')
      .eq('id', announcement.author_id)
      .single();
    
    const announcementWithAuthor = {
      ...announcement,
      author_name: author?.name || 'Unknown',
      author_id: announcement.author_id
    };
    
    // Get associated houses if this targets houses
    if (announcement.recipient_type === 'houses' || isAdmin) {
      const { data: houseRelations } = await supabase
        .from('announcement_houses')
        .select('house_id')
        .eq('announcement_id', id);
      
      if (houseRelations && houseRelations.length > 0) {
        const houseIds = houseRelations.map(r => r.house_id);
        const { data: houses } = await supabase
          .from('houses')
          .select('id, name, address, street_id')
          .in('id', houseIds);
        
        // Get street names
        if (houses && houses.length > 0) {
          const streetIds = [...new Set(houses.map(h => h.street_id))];
          const { data: streets } = await supabase
            .from('streets')
            .select('id, name')
            .in('id', streetIds);
          
          const streetMap = (streets || []).reduce((acc, street) => {
            acc[street.id] = street.name;
            return acc;
          }, {} as Record<number, string>);
          
          announcementWithAuthor.houses = houses.map(house => ({
            ...house,
            street_name: streetMap[house.street_id] || ''
          }));
          
          announcementWithAuthor.street_ids = streetIds;
        }
      }
    }
    
    // Get associated flats if this targets flats
    if (announcement.recipient_type === 'flats' || isAdmin) {
      const { data: flatRelations } = await supabase
        .from('announcement_flats')
        .select('flat_id')
        .eq('announcement_id', id);
      
      if (flatRelations && flatRelations.length > 0) {
        const flatIds = flatRelations.map(r => r.flat_id);
        const { data: flats } = await supabase
          .from('flats')
          .select('id, number, floor, house_id')
          .in('id', flatIds);
        
        if (flats && flats.length > 0) {
          const houseIds = [...new Set(flats.map(f => f.house_id))];
          const { data: houses } = await supabase
            .from('houses')
            .select('id, name, street_id')
            .in('id', houseIds);
          
          const houseMap = (houses || []).reduce((acc, house) => {
            acc[house.id] = house;
            return acc;
          }, {} as Record<number, any>);
          
          // Get street names
          const streetIds = [...new Set(houses?.map(h => h.street_id) || [])];
          const { data: streets } = await supabase
            .from('streets')
            .select('id, name')
            .in('id', streetIds);
          
          const streetMap = (streets || []).reduce((acc, street) => {
            acc[street.id] = street.name;
            return acc;
          }, {} as Record<number, string>);
          
          announcementWithAuthor.flats = flats.map(flat => {
            const house = houseMap[flat.house_id];
            return {
              ...flat,
              house_name: house?.name || '',
              street_id: house?.street_id,
              street_name: house ? streetMap[house.street_id] || '' : ''
            };
          });
          
          announcementWithAuthor.house_ids = houseIds;
          announcementWithAuthor.street_ids = streetIds;
        }
      }
    }
    
    // Get associated users if this targets users
    if (announcement.recipient_type === 'users' || isAdmin) {
      const { data: userRelations } = await supabase
        .from('announcement_users')
        .select('user_id')
        .eq('announcement_id', id);
      
      if (userRelations && userRelations.length > 0) {
        const userIds = userRelations.map(r => r.user_id);
        const { data: users } = await supabase
          .from('users')
          .select('id, name, email')
          .in('id', userIds);
        
        announcementWithAuthor.users = users || [];
        announcementWithAuthor.user_ids = userIds.map(id => id.toString());
      }
    }
    
    // Fetch tags for the announcement
    const { data: tagRelations } = await supabase
      .from('announcement_tags')
      .select('tag_id')
      .eq('announcement_id', id);
    
    if (tagRelations && tagRelations.length > 0) {
      const tagIds = tagRelations.map(r => r.tag_id);
      const { data: tags } = await supabase
        .from('tags')
        .select('id, name, color, category, created_at, updated_at')
        .in('id', tagIds);
      
      announcementWithAuthor.tags = tags || [];
    } else {
      announcementWithAuthor.tags = [];
    }
    
    return NextResponse.json(announcementWithAuthor);
  } catch (error) {
    console.error("Error fetching announcement:", error);
    return NextResponse.json(
      { error: "Failed to fetch announcement" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    if (user.role !== "super_admin" && user.role !== "editor") {
      return NextResponse.json(
        { error: "Only admins and editors can update announcements" },
        { status: 403 }
      );
    }
    
    const { id } = await context.params;
    
    // Verify announcement exists
    const checkResult = await db.query(
      `SELECT id FROM announcements WHERE id = $1`,
      [id]
    );
    
    if (checkResult.rows.length === 0) {
      return NextResponse.json({ error: "Announcement not found" }, { status: 404 });
    }
    
    const { 
      title, 
      content, 
      importance = 'normal',
      isDraft = false,
      recipientType = 'all',
      streetIds = [],
      houseIds = [], 
      flatIds = [], 
      userIds = [],
      tagIds = [],
      sendEmails = true
    } = await request.json();
    
    if (!title || !content) {
      return NextResponse.json(
        { error: "Title and content are required" },
        { status: 400 }
      );
    }
    
    // Make sure we have valid targeting data based on recipientType
    if (recipientType === 'streets' && (!streetIds || streetIds.length === 0)) {
      return NextResponse.json(
        { error: "Street IDs are required when recipient type is 'streets'" },
        { status: 400 }
      );
    }
    
    if (recipientType === 'houses' && (!houseIds || houseIds.length === 0)) {
      return NextResponse.json(
        { error: "House IDs are required when recipient type is 'houses'" },
        { status: 400 }
      );
    }
    
    if (recipientType === 'flats' && (!flatIds || flatIds.length === 0)) {
      return NextResponse.json(
        { error: "Flat IDs are required when recipient type is 'flats'" },
        { status: 400 }
      );
    }
    
    if (recipientType === 'users' && (!userIds || userIds.length === 0)) {
      return NextResponse.json(
        { error: "User IDs are required when recipient type is 'users'" },
        { status: 400 }
      );
    }
    
    // Start a transaction
    await db.query('BEGIN');
    
    try {
      // Update the announcement
      const announcementResult = await db.query(
        `
        UPDATE announcements
        SET 
          title = $1, 
          content = $2, 
          importance = $3,
          is_draft = $4,
          recipient_type = $5,
          updated_at = CURRENT_TIMESTAMP,
          sent_at = $6,
          send_emails = $7
        WHERE id = $8
        RETURNING id, title, content, importance, recipient_type, is_draft, created_at, updated_at, sent_at, send_emails
        `,
        [
          title, 
          content, 
          importance, 
          isDraft, 
          recipientType,
          isDraft ? null : new Date(),
          sendEmails,
          id
        ]
      );
      
      const announcement = announcementResult.rows[0];
      
      // Remove existing house associations
      await db.query(
        `DELETE FROM announcement_houses WHERE announcement_id = $1`,
        [id]
      );
      
      // If targeting streets, handle street associations and also add associated houses
      if (recipientType === 'streets' && streetIds && streetIds.length > 0) {
        // Find all houses in these streets and add them to announcement_houses
        const housesResult = await db.query(
          `
          SELECT id 
          FROM houses 
          WHERE street_id IN (${streetIds.map((_, i) => `$${i + 1}`).join(',')})
          `,
          streetIds
        );
        
        if (housesResult.rows.length > 0) {
          const houseIds = housesResult.rows.map(h => h.id);
          const houseValues = houseIds.map((_, index) => {
            return `($1, $${index + 2})`;
          }).join(', ');
          
          const houseParams = [id, ...houseIds];
          
          await db.query(
            `
            INSERT INTO announcement_houses (announcement_id, house_id)
            VALUES ${houseValues}
            `,
            houseParams
          );
        }
      }
      // Add new house associations
      else if (recipientType === 'houses' && houseIds && houseIds.length > 0) {
        const houseValues = houseIds.map((_, index) => {
          return `($1, $${index + 2})`;
        }).join(', ');
        
        const houseParams = [id, ...houseIds];
        
        await db.query(
          `
          INSERT INTO announcement_houses (announcement_id, house_id)
          VALUES ${houseValues}
          `,
          houseParams
        );
      }
      
      // Remove existing flat associations
      await db.query(
        `DELETE FROM announcement_flats WHERE announcement_id = $1`,
        [id]
      );
      
      // Add new flat associations
      if (recipientType === 'flats' && flatIds && flatIds.length > 0) {
        const flatValues = flatIds.map((_, index) => {
          return `($1, $${index + 2})`;
        }).join(', ');
        
        const flatParams = [id, ...flatIds];
        
        await db.query(
          `
          INSERT INTO announcement_flats (announcement_id, flat_id)
          VALUES ${flatValues}
          `,
          flatParams
        );
      }
      
      // Remove existing user associations
      await db.query(
        `DELETE FROM announcement_users WHERE announcement_id = $1`,
        [id]
      );
      
      // Add new user associations
      if (recipientType === 'users' && userIds && userIds.length > 0) {
        const userValues = userIds.map((_, index) => {
          return `($1, $${index + 2})`;
        }).join(', ');
        
        const userParams = [id, ...userIds];
        
        console.log(`[PUT /api/announcements/[id]] Inserting announcement_users for announcement ${id}:`, userIds);
        await db.query(
          `
          INSERT INTO announcement_users (announcement_id, user_id)
          VALUES ${userValues}
          `,
          userParams
        );
      }
      
      // Commit the transaction
      await db.query('COMMIT');
      
      return NextResponse.json(announcement);
    } catch (error) {
      // Rollback in case of error
      await db.query('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error("Error updating announcement:", error);
    return NextResponse.json(
      { error: "Failed to update announcement" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    if (user.role !== "super_admin" && user.role !== "editor") {
      return NextResponse.json(
        { error: "Only admins and editors can delete announcements" },
        { status: 403 }
      );
    }
    
    const { id } = await context.params;
    
    // Start a transaction
    await db.query('BEGIN');
    
    try {
      // Delete associated house records first (foreign key constraints)
      await db.query(
        `DELETE FROM announcement_houses WHERE announcement_id = $1`,
        [id]
      );
      
      // Delete associated user records
      await db.query(
        `DELETE FROM announcement_users WHERE announcement_id = $1`,
        [id]
      );
      
      // Delete the announcement
      const result = await db.query(
        `DELETE FROM announcements WHERE id = $1 RETURNING id`,
        [id]
      );
      
      if (result.rows.length === 0) {
        await db.query('ROLLBACK');
        return NextResponse.json({ error: "Announcement not found" }, { status: 404 });
      }
      
      // Commit the transaction
      await db.query('COMMIT');
      
      return NextResponse.json({ success: true, message: "Announcement deleted successfully" });
    } catch (error) {
      // Rollback in case of error
      await db.query('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error("Error deleting announcement:", error);
    return NextResponse.json(
      { error: "Failed to delete announcement" },
      { status: 500 }
    );
  }
} 