import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const { id } = await params;
    
    const result = await db.query(
      `
      SELECT 
        a.id, 
        a.title, 
        a.content, 
        a.created_at, 
        a.updated_at,
        u.name as author_name,
        u.id as author_id
      FROM announcements a
      JOIN users u ON a.author_id = u.id
      WHERE a.id = $1
      `,
      [id]
    );
    
    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Announcement not found" }, { status: 404 });
    }
    
    const announcement = result.rows[0];
    
    // Get associated houses
    const housesResult = await db.query(
      `
      SELECT h.id, h.address, h.name
      FROM houses h
      JOIN announcement_houses ah ON h.id = ah.house_id
      WHERE ah.announcement_id = $1
      `,
      [id]
    );
    
    // Get associated users
    const usersResult = await db.query(
      `
      SELECT u.id, u.name
      FROM users u
      JOIN announcement_users au ON u.id = au.user_id
      WHERE au.announcement_id = $1
      `,
      [id]
    );
    
    announcement.houses = housesResult.rows;
    announcement.users = usersResult.rows;
    
    return NextResponse.json(announcement);
  } catch (error) {
    console.error("Error fetching announcement:", error);
    return NextResponse.json(
      { error: "Failed to fetch announcement" },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    if (user.role !== "super_admin" && user.role !== "editor") {
      return NextResponse.json(
        { error: "Only admins and editors can update announcements" },
        { status: 403 }
      );
    }
    
    const { id } = await params;
    const { title, content, houseIds, userIds } = await req.json();
    
    if (!title || !content) {
      return NextResponse.json(
        { error: "Title and content are required" },
        { status: 400 }
      );
    }
    
    // Check if announcement exists
    const checkResult = await db.query(
      `SELECT id FROM announcements WHERE id = $1`,
      [id]
    );
    
    if (checkResult.rows.length === 0) {
      return NextResponse.json({ error: "Announcement not found" }, { status: 404 });
    }
    
    // Start a transaction
    await db.query('BEGIN');
    
    try {
      // Update the announcement
      const announcementResult = await db.query(
        `
        UPDATE announcements
        SET title = $1, content = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = $3
        RETURNING id, title, content, created_at, updated_at
        `,
        [title, content, id]
      );
      
      const announcement = announcementResult.rows[0];
      
      // Remove existing house associations
      await db.query(
        `DELETE FROM announcement_houses WHERE announcement_id = $1`,
        [id]
      );
      
      // Add new house associations
      if (houseIds && houseIds.length > 0) {
        const houseValues = houseIds.map((houseId: string, index: number) => {
          return `($1, $${index + 2})`;
        }).join(', ');
        
        const houseParams = [id, ...houseIds];
        
        await db.query(
          `
          INSERT INTO announcement_houses (announcement_id, house_id)
          VALUES ${houseValues}
          `,
          houseParams
        );
      }
      
      // Remove existing user associations
      await db.query(
        `DELETE FROM announcement_users WHERE announcement_id = $1`,
        [id]
      );
      
      // Add new user associations
      if (userIds && userIds.length > 0) {
        const userValues = userIds.map((userId: string, index: number) => {
          return `($1, $${index + 2})`;
        }).join(', ');
        
        const userParams = [id, ...userIds];
        
        await db.query(
          `
          INSERT INTO announcement_users (announcement_id, user_id)
          VALUES ${userValues}
          `,
          userParams
        );
      }
      
      // Commit the transaction
      await db.query('COMMIT');
      
      return NextResponse.json(announcement);
    } catch (error) {
      // Rollback in case of error
      await db.query('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error("Error updating announcement:", error);
    return NextResponse.json(
      { error: "Failed to update announcement" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    if (user.role !== "super_admin" && user.role !== "editor") {
      return NextResponse.json(
        { error: "Only admins and editors can delete announcements" },
        { status: 403 }
      );
    }
    
    const { id } = await params;
    
    // Start a transaction
    await db.query('BEGIN');
    
    try {
      // Delete associated house records first (foreign key constraints)
      await db.query(
        `DELETE FROM announcement_houses WHERE announcement_id = $1`,
        [id]
      );
      
      // Delete associated user records
      await db.query(
        `DELETE FROM announcement_users WHERE announcement_id = $1`,
        [id]
      );
      
      // Delete the announcement
      const result = await db.query(
        `DELETE FROM announcements WHERE id = $1 RETURNING id`,
        [id]
      );
      
      if (result.rows.length === 0) {
        await db.query('ROLLBACK');
        return NextResponse.json({ error: "Announcement not found" }, { status: 404 });
      }
      
      // Commit the transaction
      await db.query('COMMIT');
      
      return NextResponse.json({ success: true, message: "Announcement deleted successfully" });
    } catch (error) {
      // Rollback in case of error
      await db.query('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error("Error deleting announcement:", error);
    return NextResponse.json(
      { error: "Failed to delete announcement" },
      { status: 500 }
    );
  }
} 