import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";
import { createServiceClient } from "@/lib/supabase/server";
import { queueEmail, getEmailTemplate } from "@/lib/supabase/email-queue";
import { addAuditLog } from "@/lib/utils";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role
    if (!user || (user.role !== "developer" && user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // For Next.js 15, params need to be awaited
    const { id } = await params;
    
    // Parse the request body to check if emails should be sent
    const { sendEmails = true } = await request.json().catch(() => ({}));
    
    // Check if announcement exists using Supabase
    const supabase = await createServiceClient();
    
    const { data: announcement, error: fetchError } = await supabase
      .from('announcements')
      .select('*')
      .eq('id', id)
      .single();
    
    if (fetchError || !announcement) {
      console.error("Error fetching announcement:", fetchError);
      return NextResponse.json(
        { error: "Announcement not found" },
        { status: 404 }
      );
    }
    const isAlreadyPublished = !announcement.is_draft;
    
    try {
      // Process the announcement sending

      let updatedAnnouncement;
      
      if (isAlreadyPublished) {
        // For already published announcements, just get the current data
        updatedAnnouncement = announcement;
      } else {
        // For drafts, update to published state using Supabase
        const { data: updated, error: updateError } = await supabase
          .from('announcements')
          .update({ 
            is_draft: false, 
            sent_at: new Date().toISOString() 
          })
          .eq('id', id)
          .eq('is_draft', true)
          .select('*')
          .single();

        if (updateError || !updated) {
          console.error("Error updating announcement:", updateError);
          throw new Error("Failed to update announcement status");
        }
        
        updatedAnnouncement = updated;
      }

      // 2. Get recipients based on recipient_type using Supabase
      let recipients = [];
      
      if (announcement.recipient_type === 'all') {
        // Get all users
        const { data: users, error: usersError } = await supabase
          .from('users')
          .select('id, email, name')
          .eq('role', 'user');
        
        if (usersError) {
          console.error("Error fetching all users:", usersError);
          throw new Error("Failed to fetch recipients");
        }
        recipients = users || [];
      }
      else if (announcement.recipient_type === 'houses') {
        // Get users from specific houses
        const { data: houseRelations } = await supabase
          .from('announcement_houses')
          .select('house_id')
          .eq('announcement_id', id);
        
        if (houseRelations && houseRelations.length > 0) {
          const houseIds = houseRelations.map(r => r.house_id);
          
          const { data: users, error: usersError } = await supabase
            .from('users')
            .select(`
              id, email, name,
              flat_id,
              flats!inner(house_id)
            `)
            .in('flats.house_id', houseIds);
          
          if (usersError) {
            console.error("Error fetching house users:", usersError);
            throw new Error("Failed to fetch house recipients");
          }
          recipients = users || [];
        }
      } 
      else if (announcement.recipient_type === 'flats') {
        // Get users from specific flats
        const { data: flatRelations } = await supabase
          .from('announcement_flats')
          .select('flat_id')
          .eq('announcement_id', id);
        
        if (flatRelations && flatRelations.length > 0) {
          const flatIds = flatRelations.map(r => r.flat_id);
          
          const { data: users, error: usersError } = await supabase
            .from('users')
            .select('id, email, name')
            .in('flat_id', flatIds);
          
          if (usersError) {
            console.error("Error fetching flat users:", usersError);
            throw new Error("Failed to fetch flat recipients");
          }
          recipients = users || [];
        }
      } 
      else if (announcement.recipient_type === 'users') {
        // Get specific users
        const { data: userRelations } = await supabase
          .from('announcement_users')
          .select('user_id')
          .eq('announcement_id', id);
        
        if (userRelations && userRelations.length > 0) {
          const userIds = userRelations.map(r => r.user_id);
          
          const { data: users, error: usersError } = await supabase
            .from('users')
            .select('id, email, name')
            .in('id', userIds);
          
          if (usersError) {
            console.error("Error fetching specific users:", usersError);
            throw new Error("Failed to fetch user recipients");
          }
          recipients = users || [];

          // ---> Add Log Here <---
          console.log(`[sendAnnouncement] User IDs refetched from announcement_users for announcement ${id}:`, recipients.map(r => r.id));
        }
      }

      // Add logging for raw recipients
      console.log(`[sendAnnouncement] Raw recipients fetched (${recipients.length}):`, JSON.stringify(recipients.map(r => ({ id: r.id, email: r.email }))));

      // 3. Deduplicate recipients list based on user ID before proceeding
      const uniqueRecipientsMap = new Map();
      recipients.forEach(recipient => {
        if (!uniqueRecipientsMap.has(recipient.id)) {
          uniqueRecipientsMap.set(recipient.id, recipient);
        }
      });
      const uniqueRecipients = Array.from(uniqueRecipientsMap.values());
      console.log(`[sendAnnouncement] Fetched ${recipients.length} potential recipients, deduplicated to ${uniqueRecipients.length}`);
      // Add logging for unique recipients
      console.log(`[sendAnnouncement] Unique recipients (${uniqueRecipients.length}):`, JSON.stringify(uniqueRecipients.map(r => ({ id: r.id, email: r.email }))));

      // 4. Skip notification system for now - tables not migrated to Supabase yet
      // TODO: Migrate notifications and notification_delivery tables to Supabase
      let notificationId = null; // Placeholder for when notifications are migrated
      
      // 6. Check for attachments
      const supabase = await createServiceClient();
      const { data: attachments } = await supabase
        .from('announcement_attachments')
        .select('id')
        .eq('announcement_id', id);

      const hasAttachments = attachments && attachments.length > 0;

      // 7. Get email template for announcements, but only if sendEmails is true
      let emailTemplate = null;
      let emailSubject = '';
      let emailContent = '';

      if (sendEmails) {
        emailTemplate = await getEmailTemplate('announcement', {
          title: updatedAnnouncement.title,
          content: updatedAnnouncement.content,
          id: updatedAnnouncement.id,
          hasAttachments: hasAttachments ? 'true' : 'false'
        });

        // If no template exists, create a basic one
        emailSubject = emailTemplate?.subject || `Naujas pranešimas: ${updatedAnnouncement.title}`;
        emailContent = emailTemplate?.content || `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #002855;">${updatedAnnouncement.title}</h2>
            <div style="border-left: 3px solid #002855; padding-left: 10px; margin: 15px 0;">
              ${updatedAnnouncement.content}
            </div>
            ${hasAttachments ? `
              <div style="background-color: #f3f4f6; border: 1px solid #d1d5db; border-radius: 6px; padding: 12px; margin: 20px 0;">
                <p style="margin: 0; color: #374151; font-size: 14px;">
                  📎 <strong>Šis pranešimas turi priedų.</strong><br>
                  Prisijunkite prie sistemos, kad galėtumėte juos peržiūrėti ir atsisiųsti.
                </p>
              </div>
            ` : ''}
            <p style="font-size: 12px; color: #666; margin-top: 20px;">
              Šis laiškas sugeneruotas automatiškai. Prašome neatsakyti į šį laišką.
            </p>
          </div>
        `;
      }
      
      // 5. Queue emails for each UNIQUE recipient if sendEmails is true  
      let emailsQueued = 0;
      
      for (const recipient of uniqueRecipients) {
        // Skip notification delivery tracking for now since tables aren't migrated
        
        // Queue email for this recipient only if sendEmails is true
        if (sendEmails && recipient.email) {
          // Set priority based on importance
          let priority = 3; // default
          if (updatedAnnouncement.importance === 'important') priority = 2;
          if (updatedAnnouncement.importance === 'urgent') priority = 1;
          
          const queueResult = await queueEmail({
            emailType: 'announcement',
            recipient: recipient.email,
            subject: emailSubject,
            content: emailContent,
            entityType: 'announcement',
            entityId: parseInt(updatedAnnouncement.id),
            announcementId: parseInt(updatedAnnouncement.id),
            priority,
            userId: recipient.id,
            metadata: {
              announcementId: updatedAnnouncement.id,
              recipientId: recipient.id,
              recipientType: announcement.recipient_type
            }
          });
          
          if (queueResult.success) {
            emailsQueued++;
          }
        }
      }

      // 8. Add audit log
      await addAuditLog({
        userId: parseInt(user.id),
        action: isAlreadyPublished 
          ? (sendEmails ? "resent_announcement_with_email" : "resent_announcement_without_email") 
          : (sendEmails ? "sent_announcement_with_email" : "sent_announcement_without_email"),
        entityType: "announcement",
        entityId: parseInt(id),
        changes: {
          recipients: recipients.length,
          emailsSent: emailsQueued,
          recipientType: announcement.recipient_type,
          resent: isAlreadyPublished
        },
      });

      // 6. Processing complete

      return NextResponse.json({
        success: true,
        message: isAlreadyPublished ? "Announcement resent successfully" : "Announcement sent successfully",
        data: {
          recipients: recipients.length,
          emailsQueued: sendEmails ? emailsQueued : 0
        }
      });
    } catch (error) {
      // Log the error and propagate
      console.error("Error in announcement sending process:", error);
      throw error;
    }
  } catch (error) {
    console.error("Error sending announcement:", error);
    return NextResponse.json(
      { error: "Failed to send announcement" },
      { status: 500 }
    );
  }
}
