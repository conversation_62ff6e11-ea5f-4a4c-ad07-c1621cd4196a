import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";
import { addAuditLog } from "@/lib/utils";

// GET /api/announcements/[id]/tags - Get tags for an announcement
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const announcementId = params.id;
    
    if (!announcementId) {
      return NextResponse.json(
        { error: "Announcement ID is required" },
        { status: 400 }
      );
    }
    
    // Get tags for this announcement
    const result = await db.query(`
      SELECT t.id, t.name, t.color, t.category
      FROM tags t
      JOIN announcement_tags at ON t.id = at.tag_id
      WHERE at.announcement_id = $1
      ORDER BY t.name ASC
    `, [announcementId]);
    
    return NextResponse.json(result.rows);
  } catch (error) {
    console.error("Error fetching announcement tags:", error);
    return NextResponse.json(
      { error: "Failed to fetch announcement tags" },
      { status: 500 }
    );
  }
}

// POST /api/announcements/[id]/tags - Add tags to an announcement
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Only admins or editors can add tags to announcements
    if (user.role !== "super_admin" && user.role !== "editor") {
      return NextResponse.json(
        { error: "Only admins and editors can add tags to announcements" },
        { status: 403 }
      );
    }
    
    const announcementId = params.id;
    
    if (!announcementId) {
      return NextResponse.json(
        { error: "Announcement ID is required" },
        { status: 400 }
      );
    }
    
    const { tagIds } = await req.json();
    
    if (!tagIds || !Array.isArray(tagIds) || tagIds.length === 0) {
      return NextResponse.json(
        { error: "At least one tag ID is required" },
        { status: 400 }
      );
    }
    
    // Check if announcement exists
    const announcementResult = await db.query(
      `SELECT id, title FROM announcements WHERE id = $1`,
      [announcementId]
    );
    
    if (announcementResult.rows.length === 0) {
      return NextResponse.json(
        { error: "Announcement not found" },
        { status: 404 }
      );
    }
    
    // Start a transaction
    await db.query('BEGIN');
    
    try {
      // First, delete existing tags for this announcement (if replacing all)
      await db.query(
        `DELETE FROM announcement_tags WHERE announcement_id = $1`,
        [announcementId]
      );
      
      // Then add all the new tags
      await db.query(
        `
        INSERT INTO announcement_tags (announcement_id, tag_id)
        SELECT $1, UNNEST($2::integer[])
        `,
        [announcementId, tagIds]
      );
      
      await db.query('COMMIT');
      
      // Get the updated tags
      const updatedTags = await db.query(`
        SELECT t.id, t.name, t.color, t.category
        FROM tags t
        JOIN announcement_tags at ON t.id = at.tag_id
        WHERE at.announcement_id = $1
        ORDER BY t.name ASC
      `, [announcementId]);
      
      // Add audit log
      await addAuditLog({
        action: 'update_announcement_tags',
        userId: parseInt(user.id),
        entityType: 'announcement',
        entityId: parseInt(announcementId),
        changes: { tagIds }
      });
      
      return NextResponse.json(updatedTags.rows);
    } catch (error) {
      await db.query('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error("Error adding tags to announcement:", error);
    return NextResponse.json(
      { error: "Failed to add tags to announcement" },
      { status: 500 }
    );
  }
}

// DELETE /api/announcements/[id]/tags?tagId=123 - Remove a tag from an announcement
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Only admins or editors can remove tags from announcements
    if (user.role !== "super_admin" && user.role !== "editor") {
      return NextResponse.json(
        { error: "Only admins and editors can remove tags from announcements" },
        { status: 403 }
      );
    }
    
    const announcementId = params.id;
    
    if (!announcementId) {
      return NextResponse.json(
        { error: "Announcement ID is required" },
        { status: 400 }
      );
    }
    
    const { searchParams } = new URL(req.url);
    const tagId = searchParams.get("tagId");
    
    if (!tagId) {
      return NextResponse.json(
        { error: "Tag ID is required" },
        { status: 400 }
      );
    }
    
    // Check if the tag is associated with this announcement
    const result = await db.query(
      `
      SELECT at.announcement_id, at.tag_id, t.name 
      FROM announcement_tags at
      JOIN tags t ON at.tag_id = t.id
      WHERE at.announcement_id = $1 AND at.tag_id = $2
      `,
      [announcementId, tagId]
    );
    
    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: "Tag is not associated with this announcement" },
        { status: 404 }
      );
    }
    
    const tag = result.rows[0];
    
    // Remove the tag association
    await db.query(
      `DELETE FROM announcement_tags WHERE announcement_id = $1 AND tag_id = $2`,
      [announcementId, tagId]
    );
    
    // Add audit log
    await addAuditLog({
      action: 'remove_announcement_tag',
      userId: parseInt(user.id),
      entityType: 'announcement',
      entityId: parseInt(announcementId),
      changes: { tagId, tagName: tag.name }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error removing tag from announcement:", error);
    return NextResponse.json(
      { error: "Failed to remove tag from announcement" },
      { status: 500 }
    );
  }
} 