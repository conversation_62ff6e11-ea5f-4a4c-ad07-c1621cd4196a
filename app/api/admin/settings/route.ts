import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";

/**
 * Get all settings or settings by group
 */
export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role
    if (!user || user.role !== "super_admin") {
      return NextResponse.json(
        { error: "Unauthorized - only super_admin can access settings" },
        { status: 401 }
      );
    }
    
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const group = searchParams.get("group");
    
    const supabase = await createServiceClient();
    
    // Build the query based on filters
    let query = supabase
      .from('admin_settings')
      .select('*');
    
    if (group) {
      query = query.like('key', `${group}_%`);
    }
    
    query = query.order('key', { ascending: true });
    
    // Get settings from database
    const { data: settings, error } = await query;
    
    if (error) {
      console.error("Error fetching settings:", error);
      throw error;
    }
    
    // Transform to key-value object with metadata
    const transformedSettings = (settings || []).map(row => ({
      key: row.key,
      value: row.value,
      description: row.description
    }));
    
    // Add environment info
    const environmentInfo = {
      nodeEnv: process.env.NODE_ENV || 'development',
      isDevelopment: process.env.NODE_ENV !== 'production',
      isProduction: process.env.NODE_ENV === 'production',
    };
    
    return NextResponse.json({ 
      settings: transformedSettings,
      environment: environmentInfo
    });
  } catch (error) {
    console.error("Error getting settings:", error);
    return NextResponse.json(
      { error: "Failed to get settings" },
      { status: 500 }
    );
  }
}

/**
 * Update settings
 */
export async function PUT(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role
    if (!user || user.role !== "super_admin") {
      return NextResponse.json(
        { error: "Unauthorized - only super_admin can update settings" },
        { status: 401 }
      );
    }
    
    let body;
    try {
      body = await req.json();
      console.log("Settings update request body:", body);
    } catch (parseError) {
      console.error("Error parsing request body:", parseError);
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }
    
    if (!body.settings || !Array.isArray(body.settings)) {
      return NextResponse.json(
        { error: "Invalid request format - expected settings array" },
        { status: 400 }
      );
    }
    
    const updatedSettings = [];
    const createdSettings = [];
    
    const supabase = await createServiceClient();
    
    try {
      // Update each setting
      for (const setting of body.settings) {
        if (!setting.key) {
          throw new Error("Invalid setting object: must have key");
        }
        
        // Convert undefined or null values to empty string
        if (setting.value === undefined || setting.value === null) {
          setting.value = "";
        }
        
        console.log(`Processing setting update: ${setting.key} = ${setting.key.includes('password') ? '[HIDDEN]' : JSON.stringify(setting.value)}`);
        
        // Special handling for password fields - don't update if empty
        if (setting.key === 'smtp_password' && setting.value === '') {
          // Skip empty password updates
          console.log("Skipping empty password update");
          continue;
        }
        
        // Check if setting exists
        const { data: existingSettings, error: checkError } = await supabase
          .from('admin_settings')
          .select('key')
          .eq('key', setting.key)
          .single();
        
        if (existingSettings && !checkError) {
          // Update existing setting
          console.log(`Updating existing setting: ${setting.key}`);
          const { error: updateError } = await supabase
            .from('admin_settings')
            .update({
              value: setting.value,
              updated_at: new Date().toISOString()
            })
            .eq('key', setting.key);
          
          if (updateError) {
            console.error(`Error updating setting ${setting.key}:`, updateError);
            throw updateError;
          }
          
          updatedSettings.push(setting.key);
        } else if (checkError && checkError.code === 'PGRST116') {
          // Insert new setting (PGRST116 means no rows found)
          console.log(`Creating new setting: ${setting.key}`);
          const { error: insertError } = await supabase
            .from('admin_settings')
            .insert({
              key: setting.key,
              value: setting.value,
              description: setting.description || `Setting for ${setting.key}`,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
          
          if (insertError) {
            console.error(`Error creating setting ${setting.key}:`, insertError);
            throw insertError;
          }
          
          createdSettings.push(setting.key);
        } else if (checkError) {
          // Other errors
          console.error(`Error checking setting ${setting.key}:`, checkError);
          throw checkError;
        }
      }
      
      return NextResponse.json({ 
        success: true, 
        message: "Settings updated successfully",
        updated: updatedSettings,
        created: createdSettings
      });
    } catch (error) {
      console.error("Error in settings update:", error);
      throw error;
    }
  } catch (error) {
    console.error("Error updating settings:", error);
    return NextResponse.json(
      { error: "Failed to update settings", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 