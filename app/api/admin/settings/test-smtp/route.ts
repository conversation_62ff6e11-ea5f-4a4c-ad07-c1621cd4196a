import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import nodemailer from "nodemailer";

export async function POST(request: NextRequest) {
  try {
    // Check authentication and authorization
    const user = await getCurrentUser();
    if (!user || user.role !== "super_admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const data = await request.json();
    const {
      smtp_host,
      smtp_port,
      smtp_user,
      smtp_password,
      smtp_from,
      smtp_reply_to,
      smtp_connection_type,
      test_recipient
    } = data;

    console.log("Testing SMTP with config:", {
      host: smtp_host,
      port: smtp_port,
      user: smtp_user,
      password: smtp_password ? "[REDACTED]" : "[EMPTY]",
      from: smtp_from,
      replyTo: smtp_reply_to || "[NOT SET]",
      connection_type: smtp_connection_type,
      recipient: test_recipient
    });

    // Validate required fields
    if (!smtp_host || !smtp_port || !smtp_user || !test_recipient) {
      return NextResponse.json(
        { error: "Missing required SMTP settings" },
        { status: 400 }
      );
    }

    // If password is empty, try to get it from the database
    let password = smtp_password;
    if (!password) {
      try {
        const supabase = await createServiceClient();
        const { data: passwordSetting, error } = await supabase
          .from('admin_settings')
          .select('value')
          .eq('key', 'smtp_password')
          .single();

        if (!error && passwordSetting) {
          password = passwordSetting.value;
        }
      } catch (dbError) {
        console.error("Error fetching SMTP password from database:", dbError);
      }
    }

    // Convert connection type to nodemailer config
    const smtpConfig = getSmtpConfig(smtp_connection_type, {
      host: smtp_host,
      port: parseInt(smtp_port, 10),
      auth: {
        user: smtp_user,
        pass: password || "",
      }
    });

    // Initial connection test
    const connectionResult = await testConnection(smtpConfig);

    if (!connectionResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          connection: false,
          error: connectionResult.error
        },
        { status: 200 }
      );
    }

    // If connection successful, send test email
    // Use the same configuration that worked for connection test
    const emailConfig = {
      ...smtpConfig,
      from: smtp_from || "<EMAIL>",
      replyTo: smtp_reply_to || undefined,
      to: test_recipient
    };

    const emailResult = await sendTestEmail(emailConfig);

    return NextResponse.json({
      success: emailResult.success,
      connection: true,
      sent: emailResult.success,
      messageId: emailResult.messageId,
      error: emailResult.error
    });
  } catch (error) {
    console.error("Error testing SMTP:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : "An unexpected error occurred while testing SMTP settings." 
      },
      { status: 500 }
    );
  }
}

// Convert connection type to proper SMTP configuration
function getSmtpConfig(connectionType: string, baseConfig: any) {
  const config = { ...baseConfig };

  switch (connectionType) {
    case 'ssl':
      // Direct SSL connection (typically port 465)
      config.secure = true;
      break;
    case 'tls':
      // STARTTLS connection (typically port 587)
      config.secure = false;
      config.requireTLS = true;
      config.tls = {
        rejectUnauthorized: false // Allow self-signed certificates in development
      };
      break;
    case 'none':
      // No encryption (typically port 25)
      config.secure = false;
      break;
    default:
      // Default to TLS
      config.secure = false;
      config.requireTLS = true;
      config.tls = {
        rejectUnauthorized: false
      };
  }

  return config;
}

// Test SMTP connection without sending email
async function testConnection(config: any) {
  try {
    const transporter = nodemailer.createTransport(config);

    // Verify connection configuration
    await transporter.verify();

    return { success: true };
  } catch (error) {
    console.error("SMTP connection test failed:", error);
    const errorMessage = error instanceof Error ?
      error.message :
      "Failed to connect to SMTP server";

    return {
      success: false,
      error: errorMessage
    };
  }
}

// Send test email
async function sendTestEmail(config: any) {
  try {
    // Create transporter with the provided config (already properly formatted)
    const { from, replyTo, to, ...transporterConfig } = config;
    const transporter = nodemailer.createTransport(transporterConfig);
    
    const emailOptions = {
      from: {
        name: "DNSB Vakarai",
        address: from,
      },
      to: to,
      subject: "SMTP Testavimo laiškas",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
          <h2 style="color: #4338ca;">SMTP Konfigūracijos testas pavyko!</h2>
          <p>Šis laiškas patvirtina, kad jūsų SMTP serverio konfigūracija yra teisinga ir sistema gali sėkmingai siųsti el. laiškus.</p>
          <hr style="border: none; border-top: 1px solid #eaeaea; margin: 20px 0;" />
          <p style="color: #666; font-size: 12px;">Šis laiškas yra tik testavimo tikslais ir buvo išsiųstas iš DNSB Vakarai administravimo sistemos.</p>
          <p style="color: #666; font-size: 12px;">Siuntimo laikas: ${new Date().toLocaleString()}</p>
        </div>
      `,
      text: 
        `SMTP Konfigūracijos testas pavyko!\n\n` +
        `Šis laiškas patvirtina, kad jūsų SMTP serverio konfigūracija yra teisinga ir sistema gali sėkmingai siųsti el. laiškus.\n\n` +
        `Šis laiškas yra tik testavimo tikslais ir buvo išsiųstas iš DNSB Vakarai administravimo sistemos.\n` +
        `Siuntimo laikas: ${new Date().toLocaleString()}`
    };
    
    // Add reply-to if specified
    if (replyTo) {
      console.log(`Adding replyTo: ${replyTo}`);
      emailOptions['replyTo'] = replyTo;
    }

    const info = await transporter.sendMail(emailOptions);

    return { 
      success: true, 
      messageId: info.messageId 
    };
  } catch (error) {
    console.error("Test email sending failed:", error);
    const errorMessage = error instanceof Error ? 
      error.message : 
      "Failed to send test email";
    
    return { 
      success: false, 
      error: errorMessage
    };
  }
} 