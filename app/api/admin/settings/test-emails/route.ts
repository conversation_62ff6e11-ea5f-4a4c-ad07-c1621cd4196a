import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { TestEmailConfig } from "@/lib/email-utils";
import { addAuditLog } from "@/lib/utils";

export async function POST(request: NextRequest) {
  try {
    // Check authentication and authorization
    const user = await getCurrentUser();
    // Ensure user exists before accessing role
    if (!user || !["super_admin", "admin", "developer"].includes(user.role)) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    // Parse request body
    const data = await request.json();
    const { config } = data as { config: TestEmailConfig };

    // Validate config
    if (!config) {
      return NextResponse.json(
        { error: "Invalid configuration" },
        { status: 400 }
      );
    }

    // Ensure arrays are properly formed
    const sanitizedConfig: TestEmailConfig = {
      enabled: <PERSON><PERSON><PERSON>(config.enabled),
      addresses: Array.isArray(config.addresses) ? config.addresses : [],
      domains: Array.isArray(config.domains) ? config.domains : [],
      streetPatterns: Array.isArray(config.streetPatterns) ? config.streetPatterns : [],
    };

    // Save to database
    const supabase = await createServiceClient();
    const { error } = await supabase
      .from('admin_settings')
      .upsert({
        key: 'test_email_config',
        value: JSON.stringify(sanitizedConfig),
        description: 'Configuration for test emails in development environment',
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'key'
      });

    if (error) {
      console.error("Error saving test email config:", error);
      throw error;
    }

    // Log the change
    await addAuditLog({
      action: 'update_test_email_config',
      entityType: 'admin_settings',
      changes: sanitizedConfig,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error updating test email config:", error);
    return NextResponse.json(
      { error: "Failed to update configuration" },
      { status: 500 }
    );
  }
} 