import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { initializeDefaultSettings, areSettingsInitialized } from "@/lib/init-settings";

export async function POST(request: NextRequest) {
  try {
    // Check authentication and authorization
    const user = await getCurrentUser();
    if (!user || user.role !== "super_admin") {
      return NextResponse.json(
        { error: "Unauthorized - only super_admin can initialize settings" },
        { status: 401 }
      );
    }

    // Check if settings are already initialized
    const alreadyInitialized = await areSettingsInitialized();
    if (alreadyInitialized) {
      return NextResponse.json({
        success: true,
        message: "Settings are already initialized",
        alreadyInitialized: true
      });
    }

    // Initialize default settings
    const success = await initializeDefaultSettings();
    
    if (success) {
      return NextResponse.json({
        success: true,
        message: "Default settings initialized successfully"
      });
    } else {
      return NextResponse.json(
        { error: "Failed to initialize settings" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error initializing settings:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication and authorization
    const user = await getCurrentUser();
    if (!user || user.role !== "super_admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if settings are initialized
    const initialized = await areSettingsInitialized();
    
    return NextResponse.json({
      initialized
    });
  } catch (error) {
    console.error("Error checking settings initialization:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
