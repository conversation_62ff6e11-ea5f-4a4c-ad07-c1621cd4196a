import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";
import { sendEmail } from "@/lib/email";

// Mock data for when the database table doesn't exist
const mockFeedbackMessages = {
  "1": {
    id: "1",
    title: "<PERSON><PERSON><PERSON> aptarnavi<PERSON>",
    name: "<PERSON>",
    email: "<EMAIL>",
    content: "Labai patenkinti administracijos darbu. Visi klausimai išsprendžiami greitai ir efektyviai.",
    rating: 5,
    category: "service",
    status: "NEW",
    created_at: "2023-11-15T08:30:00.000Z",
    updated_at: "2023-11-15T08:30:00.000Z",
    resolved_at: null,
    admin_notes: null,
    assigned_to: null,
    user_id: "user_abc123",
    allow_contact: true
  },
  "2": {
    id: "2",
    title: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>vetain<PERSON>",
    name: "<PERSON>",
    email: "<EMAIL>",
    content: "<PERSON><PERSON>ain<PERSON> labai patogi naudoti, bet būtų gerai pridėti galimybę matyti mokėjimų istoriją.",
    rating: 4,
    category: "website",
    status: "IN_PROGRESS",
    created_at: "2023-11-10T10:15:00.000Z",
    updated_at: "2023-11-11T14:22:00.000Z",
    resolved_at: null,
    admin_notes: "2023-11-11T14:22:00.000Z - Admin: Išsiųstas užklausimas dėl galimybės pridėti mokėjimų istoriją.",
    assigned_to: "admin_user_123",
    user_id: "user_def456",
    allow_contact: false
  }
};

export async function GET(
  req: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id } = await context.params;
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Turite būti prisijungęs, kad galėtumėte atnaujinti atsiliepimą" },
        { status: 401 }
      );
    }
    
    if (user.role !== 'super_admin' && user.role !== 'editor') {
      return NextResponse.json(
        { error: "Neturite teisių atlikti šio veiksmo" },
        { status: 403 }
      );
    }

    try {
      // Try to get feedback from database
      const { rows } = await db.query(
        'SELECT * FROM feedback WHERE id = $1',
        [id]
      );
      
      if (rows.length === 0) {
        return NextResponse.json(
          { error: "Atsiliepimas nerastas" },
          { status: 404 }
        );
      }
      
      return NextResponse.json(rows[0]);
    } catch (error: any) {
      // Check if the error is due to the table not existing
      if (error.message && (
          error.message.includes("relation \"feedback\" does not exist") || 
          error.message.includes("no such table: feedback")
        )) {
        // Use mock data instead
        const mockMessage = mockFeedbackMessages[id];
        
        if (!mockMessage) {
          return NextResponse.json(
            { error: "Atsiliepimas nerastas" },
            { status: 404 }
          );
        }
        
        return NextResponse.json(mockMessage);
      } else {
        throw error; // Re-throw any other database errors
      }
    }
  } catch (error) {
    console.error("Klaida apdorojant užklausą:", error);
    return NextResponse.json(
      { error: "Įvyko klaida apdorojant jūsų užklausą" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  req: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id } = await context.params;
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Turite būti prisijungęs, kad galėtumėte atnaujinti atsiliepimą" },
        { status: 401 }
      );
    }
    
    if (user.role !== 'super_admin' && user.role !== 'editor') {
      return NextResponse.json(
        { error: "Neturite teisių atlikti šio veiksmo" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { status, response } = body;

    // Validate status
    const validStatuses = ['NEW', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: "Neteisinga būsena" },
        { status: 400 }
      );
    }

    // Get existing feedback
    const { rows: existingRows } = await db.query(
      'SELECT * FROM feedback_messages WHERE id = $1',
      [id]
    );
    
    if (existingRows.length === 0) {
      return NextResponse.json(
        { error: "Atsiliepimas nerastas" },
        { status: 404 }
      );
    }
    
    const existingFeedback = existingRows[0];
    
    // Check if a response is required but not provided
    if (status === 'RESOLVED' && !existingFeedback.admin_response && (!response || response.trim() === '')) {
      return NextResponse.json(
        { error: "Būtina pridėti atsakymą prieš pakeičiant būseną į 'Išspręstas'" },
        { status: 400 }
      );
    }
    
    // Update feedback
    let query = 'UPDATE feedback_messages SET status = $1, updated_at = CURRENT_TIMESTAMP';
    const queryParams: any[] = [status];
    let index = 2;
    
    // Add response if provided
    if (response && response.trim() !== '') {
      query += `, admin_response = $${index}, responded_at = CURRENT_TIMESTAMP`;
      queryParams.push(response);
      index++;
    }
    
    query += ` WHERE id = $${index} RETURNING *`;
    queryParams.push(id);
    
    const { rows } = await db.query(query, queryParams);
    const updatedFeedback = rows[0];
    
    // If status is RESOLVED and there's a response and the user allows contact, send email
    if (status === 'RESOLVED' && response && updatedFeedback.allow_contact) {
      try {
        await sendEmail({
          to: updatedFeedback.email,
          subject: `Atsakymas į jūsų atsiliepimą - ${updatedFeedback.title}`,
          html: `
            <h1>Atsakymas į jūsų atsiliepimą</h1>
            <p>Sveiki, ${updatedFeedback.name},</p>
            <p>Dėkojame už jūsų atsiliepimą "<strong>${updatedFeedback.title}</strong>".</p>
            <p>Mūsų atsakymas:</p>
            <div style="padding: 10px; border-left: 4px solid #ccc; margin: 15px 0;">
              <p>${updatedFeedback.admin_response.replace(/\n/g, '<br/>')}</p>
            </div>
            <p>Jei turite kitų klausimų ar pastebėjimų, galite susisiekti su mumis naudodami kontaktinę formą.</p>
            <p>Pagarbiai,<br/>DNSB Vakarai administracija</p>
          `,
        });
      } catch (error) {
        console.error("Klaida siunčiant el. laišką:", error);
        // Continue even if email fails
      }
    }

    return NextResponse.json(updatedFeedback);
  } catch (error) {
    console.error("Klaida apdorojant užklausą:", error);
    return NextResponse.json(
      { error: "Įvyko klaida apdorojant jūsų užklausą" },
      { status: 500 }
    );
  }
} 