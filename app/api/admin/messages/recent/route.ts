import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";

// Mock data to return when database is not available
const mockContactMessages = [
  {
    id: "mock-contact-1",
    subject: "<PERSON><PERSON><PERSON>",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "NEW",
    is_anonymous: false,
    created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() // 2 hours ago
  },
  {
    id: "mock-contact-2",
    subject: "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    status: "IN_PROGRESS",
    is_anonymous: false,
    created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 1 day ago
  },
  {
    id: "mock-contact-3",
    subject: "Automobilio parkavimas",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    status: "RESOLVED",
    is_anonymous: true,
    created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString() // 3 days ago
  }
];

const mockFeedbackMessages = [
  {
    id: "mock-feedback-1",
    title: "Puikus aptarnavimas",
    name: "Marius Mariulis",
    rating: 5,
    status: "NEW",
    is_anonymous: false,
    created_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString() // 12 hours ago
  },
  {
    id: "mock-feedback-2",
    title: "Pasiūlymai dėl svetainės",
    name: "Laura Lauraitė",
    rating: 4,
    status: "IN_PROGRESS",
    is_anonymous: true,
    created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() // 2 days ago
  }
];

export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and is an admin
    if (!user || (user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }
    
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const type = searchParams.get("type") || "all";
    const limit = parseInt(searchParams.get("limit") || "5", 10);
    
    // Validate limit to prevent excessive queries
    const validLimit = Math.min(Math.max(1, limit), 20);
    
    let results = [];
    
    try {
      if (type === "contact" || type === "all") {
        // Get contact messages
        const contactResult = await db.query(
          `SELECT id, subject, name, email, status, created_at, is_anonymous 
           FROM contact_messages 
           ORDER BY 
             CASE WHEN status = 'NEW' THEN 0
                  WHEN status = 'IN_PROGRESS' THEN 1
                  ELSE 2
             END,
             created_at DESC
           LIMIT $1`,
          [validLimit]
        );
        
        if (type === "contact") {
          results = contactResult.rows;
        } else {
          results = [...results, ...contactResult.rows.map(row => ({ ...row, type: "contact" }))];
        }
      }
      
      if (type === "feedback" || type === "all") {
        // Get feedback messages
        const feedbackResult = await db.query(
          `SELECT id, title, name, rating, status, created_at, is_anonymous 
           FROM feedback 
           ORDER BY 
             CASE WHEN status = 'NEW' THEN 0
                  WHEN status = 'IN_PROGRESS' THEN 1
                  ELSE 2
             END,
             created_at DESC
           LIMIT $1`,
          [validLimit]
        );
        
        if (type === "feedback") {
          results = feedbackResult.rows;
        } else {
          results = [...results, ...feedbackResult.rows.map(row => ({ ...row, type: "feedback" }))];
        }
        
        // If type is "all", sort combined results
        if (type === "all") {
          results.sort((a, b) => {
            // Sort by status priority
            const statusPriority = {
              NEW: 0,
              IN_PROGRESS: 1,
              RESOLVED: 2,
              CLOSED: 3
            };
            
            const aPriority = statusPriority[a.status] || 99;
            const bPriority = statusPriority[b.status] || 99;
            
            if (aPriority !== bPriority) {
              return aPriority - bPriority;
            }
            
            // Then sort by date
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          });
          
          // Limit the combined results
          results = results.slice(0, validLimit);
        }
      }
    } catch (dbError) {
      console.error("[RECENT_MESSAGES_DB_ERROR]", dbError);
      
      // Return mock data instead of failing
      if (type === "contact") {
        results = mockContactMessages.slice(0, validLimit);
      } else if (type === "feedback") {
        results = mockFeedbackMessages.slice(0, validLimit);
      } else {
        // For "all" type, combine and sort mock data
        results = [...mockContactMessages.map(msg => ({ ...msg, type: "contact" })), 
                  ...mockFeedbackMessages.map(msg => ({ ...msg, type: "feedback" }))];
        
        results.sort((a, b) => {
          // Sort by status priority
          const statusPriority = {
            NEW: 0,
            IN_PROGRESS: 1,
            RESOLVED: 2,
            CLOSED: 3
          };
          
          const aPriority = statusPriority[a.status] || 99;
          const bPriority = statusPriority[b.status] || 99;
          
          if (aPriority !== bPriority) {
            return aPriority - bPriority;
          }
          
          // Then sort by date
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
        
        results = results.slice(0, validLimit);
      }
    }
    
    return NextResponse.json(results);
  } catch (error) {
    console.error("[RECENT_MESSAGES_GET]", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
} 