import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";

// Get all messages (contact and feedback)
export async function GET() {
  try {
    const user = await getCurrentUser();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Turite būti p<PERSON>, kad <PERSON> perž<PERSON>ti prane<PERSON>" },
        { status: 401 }
      );
    }
    
    if (user.role !== 'super_admin' && user.role !== 'editor') {
      return NextResponse.json(
        { error: "Neturite teisių atlikti šio veiksmo" },
        { status: 403 }
      );
    }

    // Get contact messages
    const contactMessages = await db.query(
      'SELECT * FROM contact_messages ORDER BY created_at DESC'
    ).then(res => res.rows).catch(() => []);
    
    // Get feedback messages
    const feedbackMessages = await db.query(
      'SELECT * FROM feedback ORDER BY created_at DESC'
    ).then(res => res.rows).catch(() => []);
    
    return NextResponse.json({
      contactMessages,
      feedbackMessages
    });
  } catch (error) {
    console.error("Klaida gaunant pranešimus:", error);
    return NextResponse.json(
      { error: "Įvyko klaida gaunant pranešimus" },
      { status: 500 }
    );
  }
}

// Delete all mock messages
export async function DELETE() {
  try {
    const user = await getCurrentUser();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Turite būti prisijungęs, kad galėtumėte ištrinti pranešimus" },
        { status: 401 }
      );
    }
    
    if (user.role !== 'super_admin') {
      return NextResponse.json(
        { error: "Tik super administratorius gali ištrinti testavimo duomenis" },
        { status: 403 }
      );
    }

    // Delete mock contact messages
    const contactResult = await db.query(
      'DELETE FROM contact_messages WHERE is_mock = TRUE RETURNING id'
    ).then(res => res.rows).catch(err => {
      console.error("Klaida trinant kontaktų pranešimus:", err);
      return [];
    });
    
    // Delete mock feedback messages
    const feedbackResult = await db.query(
      'DELETE FROM feedback WHERE is_mock = TRUE RETURNING id'
    ).then(res => res.rows).catch(err => {
      console.error("Klaida trinant atsiliepimus:", err);
      return [];
    });
    
    return NextResponse.json({
      success: true,
      deleted: {
        contactMessages: contactResult.length,
        feedbackMessages: feedbackResult.length
      }
    });
  } catch (error) {
    console.error("Klaida trinant testavimo duomenis:", error);
    return NextResponse.json(
      { error: "Įvyko klaida trinant testavimo duomenis" },
      { status: 500 }
    );
  }
} 