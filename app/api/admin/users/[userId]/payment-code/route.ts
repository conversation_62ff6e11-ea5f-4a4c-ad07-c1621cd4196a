"use server";

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db/supabase-adapter";
import { getCurrentUser } from "@/lib/supabase/auth";
import { with<PERSON><PERSON><PERSON>and<PERSON> } from "@/lib/api/withA<PERSON>Handler";
import { ErrorType, createErrorResponse } from "@/lib/error-handler";
import logger from "@/lib/logger";

// GET /api/admin/users/[userId]/payment-code
export const GET = withApiHandler(
  {
    method: 'GET',
    requireAuth: true,
    requiredRole: 'super_admin',
    csrfProtected: true,
  },
  async (req: NextRequest, _, token) => {
    try {
      // Extract userId from URL
      const userId = req.nextUrl.pathname.split('/')[4]; // /api/admin/users/[userId]/payment-code
      
      logger.info({ userId, adminId: token.sub }, "Payment code request by admin");
      
      if (!userId) {
        return createErrorResponse(
          ErrorType.VALIDATION,
          "Naudotojo ID yra privalomas"
        );
      }
      
      // Check if user exists and has a flat
      const userExistsResult = await db.query(
        "SELECT id, flat_id FROM users WHERE id = $1",
        [userId]
      );
      
      if (userExistsResult.rows.length === 0) {
        logger.warn({ userId }, "Payment code request failed: User not found");
        return createErrorResponse(
          ErrorType.NOT_FOUND,
          "Naudotojas nerastas"
        );
      }
      
      const user = userExistsResult.rows[0];
      const flatId = user.flat_id;
      
      if (!flatId) {
        logger.warn({ userId, flatId }, "Payment code request failed: User has no flat assigned");
        return createErrorResponse(
          ErrorType.VALIDATION,
          "Naudotojas neturi priskirto buto, negalima gauti mokėtojo kodo"
        );
      }
      
      // Get payment code from flat_payment_codes table
      const paymentCodeResult = await db.query(
        "SELECT payment_code FROM flat_payment_codes WHERE flat_id = $1",
        [flatId]
      );
      
      if (paymentCodeResult.rows.length === 0) {
        // No payment code found, try to generate one
        try {
          // Get flat and house information
          const flatResult = await db.query(
            `SELECT f.number as flat_number, h.name as house_name 
             FROM flats f 
             JOIN houses h ON f.house_id = h.id 
             WHERE f.id = $1`,
            [flatId]
          );
          
          if (flatResult.rows.length === 0) {
            logger.warn({ userId, flatId }, "Payment code request failed: Flat not found");
            return createErrorResponse(
              ErrorType.NOT_FOUND,
              "Butas nerastas"
            );
          }
          
          const flat = flatResult.rows[0];
          const houseName = flat.house_name;
          const flatNumber = flat.flat_number;
          
          // Generate payment code using house-flat pattern
          const paymentCode = `${houseName}-${flatNumber}`;
          
          logger.info({ userId, flatId, houseName, flatNumber }, "Generated new payment code");
          
          return NextResponse.json({
            payment_code: paymentCode,
            generated: true,
            message: "Mokėtojo kodas sugeneruotas"
          });
        } catch (error) {
          logger.error({ userId, flatId, error }, "Error generating payment code");
          return createErrorResponse(
            ErrorType.SERVER,
            "Nepavyko sugeneruoti mokėtojo kodo",
            undefined,
            error
          );
        }
      } else {
        // Return the existing payment code
        const paymentCode = paymentCodeResult.rows[0].payment_code;
        
        logger.info({ userId, flatId }, "Payment code retrieved successfully");
        
        return NextResponse.json({
          payment_code: paymentCode,
          generated: false,
          message: "Mokėtojo kodas rastas"
        });
      }
    } catch (error) {
      logger.error({ error }, "Unexpected error during payment code request");
      return createErrorResponse(
        ErrorType.SERVER,
        "Įvyko klaida gaunant mokėtojo kodą",
        undefined,
        error
      );
    }
  }
);