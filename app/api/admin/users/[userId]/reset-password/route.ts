"use server";

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db/supabase-adapter";
import { hash } from "bcryptjs";
import { getCurrentUser } from "@/lib/supabase/auth";
import { with<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/api/withA<PERSON>Handler";
import { ErrorType, createErrorResponse } from "@/lib/error-handler";
import logger from "@/lib/logger";

// POST /api/admin/users/[userId]/reset-password
export const POST = withApiHandler(
  {
    method: 'POST',
    requireAuth: true,
    requiredRole: 'super_admin',
    csrfProtected: true,
  },
  async (req: NextRequest, _, token) => {
    try {
      // Extract userId from URL
      const userId = req.nextUrl.pathname.split('/')[4]; // /api/admin/users/[userId]/reset-password
      
      logger.info({ userId, adminId: token.sub }, "Password reset requested by admin");
      
      if (!userId) {
        return createErrorResponse(
          ErrorType.VALIDATION,
          "Naudotojo ID yra privalomas"
        );
      }
      
      // Check if user exists
      const userExistsResult = await db.query(
        "SELECT id, flat_id, name, email FROM users WHERE id = $1",
        [userId]
      );
      
      if (userExistsResult.rows.length === 0) {
        logger.warn({ userId }, "Password reset failed: User not found");
        return createErrorResponse(
          ErrorType.NOT_FOUND,
          "Naudotojas nerastas"
        );
      }
      
      const user = userExistsResult.rows[0];
      const flatId = user.flat_id;
      
      if (!flatId) {
        logger.warn({ userId, flatId }, "Password reset failed: User has no flat assigned");
        return createErrorResponse(
          ErrorType.VALIDATION,
          "Naudotojas neturi priskirto buto, negalima atstatyti slaptažodžio"
        );
      }
      
      // Get payment code from flat_payment_codes table
      let foundPaymentCode = false;
      let paymentCode = '';
      let paymentCodeHash = '';
      
      try {
        const paymentCodeResult = await db.query(
          "SELECT payment_code, payment_code_hash FROM flat_payment_codes WHERE flat_id = $1",
          [flatId]
        );
        
        if (paymentCodeResult.rows.length > 0) {
          foundPaymentCode = true;
          paymentCode = paymentCodeResult.rows[0].payment_code;
          paymentCodeHash = paymentCodeResult.rows[0].payment_code_hash;
          logger.info({ userId, flatId }, "Found existing payment code");
        }
      } catch (error) {
        logger.error({ userId, flatId, error }, "Error checking for payment code");
      }
      
      // If no payment code found, generate one based on flat details
      if (!foundPaymentCode) {
        try {
          // Get flat and house information
          const flatResult = await db.query(
            `SELECT f.number as flat_number, h.name as house_name 
             FROM flats f 
             JOIN houses h ON f.house_id = h.id 
             WHERE f.id = $1`,
            [flatId]
          );
          
          if (flatResult.rows.length === 0) {
            logger.warn({ userId, flatId }, "Password reset failed: Flat not found");
            return createErrorResponse(
              ErrorType.NOT_FOUND,
              "Butas nerastas"
            );
          }
          
          const flat = flatResult.rows[0];
          const houseName = flat.house_name;
          const flatNumber = flat.flat_number;
          
          // Generate payment code using house-flat pattern
          paymentCode = `${houseName}-${flatNumber}`;
          
          // Hash payment code
          paymentCodeHash = await hash(paymentCode, 10);
          
          logger.info({ userId, flatId, houseName, flatNumber }, "Generated new payment code");
          
          // Store in flat_payment_codes table
          await db.query(
            `INSERT INTO flat_payment_codes (flat_id, payment_code, payment_code_hash) 
             VALUES ($1, $2, $3) 
             ON CONFLICT (flat_id) DO UPDATE SET 
             payment_code = EXCLUDED.payment_code,
             payment_code_hash = EXCLUDED.payment_code_hash,
             updated_at = NOW()`,
            [flatId, paymentCode, paymentCodeHash]
          );
          
          logger.info({ userId, flatId }, "Stored new payment code");
        } catch (error) {
          logger.error({ userId, flatId, error }, "Error generating payment code");
          return createErrorResponse(
            ErrorType.SERVER,
            "Nepavyko sugeneruoti mokėtojo kodo",
            undefined,
            error
          );
        }
      }
      
      // Update user's password to payment code hash
      try {
        await db.query(
          `UPDATE users 
           SET password_hash = $1, 
               has_custom_password = false, 
               updated_at = NOW() 
           WHERE id = $2`,
          [paymentCodeHash, userId]
        );
        
        logger.info({ userId, adminId: token.sub }, "Password reset successful");
        
        // Log admin action
        await db.query(
          `INSERT INTO auth_attempts (email, type, ip_address) 
           VALUES ($1, $2, $3)`,
          [user.email, "admin_password_reset", req.headers.get("x-forwarded-for") || req.ip || ""]
        );
        
        // Prepare response
        return NextResponse.json({
          message: "Slaptažodis sėkmingai atstatytas į mokėtojo kodą",
          payment_code: paymentCode
        });
      } catch (error) {
        logger.error({ userId, error }, "Error updating user password");
        return createErrorResponse(
          ErrorType.DATABASE,
          "Nepavyko atnaujinti vartotojo slaptažodžio",
          undefined,
          error
        );
      }
    } catch (error) {
      logger.error({ error }, "Unexpected error during password reset");
      return createErrorResponse(
        ErrorType.SERVER,
        "Įvyko klaida atstatant slaptažodį",
        undefined,
        error
      );
    }
  }
);