import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";
import { addAuditLog } from "@/lib/utils";

export async function DELETE(request: Request) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role
    if (!session || (user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Delete emails with status 'dev_skipped'
    const result = await db.query(
      `DELETE FROM email_queue WHERE status = 'dev_skipped' RETURNING id`
    );
    
    const deletedCount = result.rowCount || 0;
    
    // Add audit log
    await addAuditLog({
      action: 'purge_dev_skipped_emails',
      userId: parseInt(user.id),
      entityType: 'email_queue',
      entityId: null, // Bulk action
      changes: {
        count: deletedCount,
      },
    });
    
    return NextResponse.json({ 
      success: true,
      deleted: deletedCount,
    });
  } catch (error) {
    console.error("Error purging dev_skipped emails:", error);
    return NextResponse.json(
      { error: "Failed to purge dev_skipped emails" },
      { status: 500 }
    );
  }
} 