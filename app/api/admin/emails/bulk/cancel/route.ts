import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";
import { addAuditLog } from "@/lib/utils";

export async function POST(request: Request) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role
    if (!session || (user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get email IDs from request
    const data = await request.json();
    const { ids } = data;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: "No email IDs provided" },
        { status: 400 }
      );
    }
    
    // Get emails to be cancelled
    const emailsResult = await db.query(
      `SELECT id, status FROM email_queue WHERE id = ANY($1::int[])`,
      [ids]
    );
    
    const pendingEmails = emailsResult.rows.filter(email => email.status === 'pending');
    
    if (pendingEmails.length === 0) {
      return NextResponse.json(
        { error: "No pending emails found" },
        { status: 400 }
      );
    }
    
    const pendingIds = pendingEmails.map(email => email.id);
    
    // Cancel the emails
    const result = await db.query(
      `UPDATE email_queue 
       SET status = 'cancelled', 
           updated_at = NOW(),
           cancelled_by = $2
       WHERE id = ANY($1::int[]) AND status = 'pending'
       RETURNING id`,
      [pendingIds, parseInt(user.id)]
    );
    
    // Add audit log
    await addAuditLog({
      action: 'cancel_emails_bulk',
      userId: parseInt(user.id),
      entityType: 'email_queue',
      entityId: null,
      changes: {
        emailIds: pendingIds,
        count: result.rowCount,
      },
    });
    
    return NextResponse.json({ 
      success: true,
      cancelled: result.rowCount,
      requested: ids.length
    });
  } catch (error) {
    console.error("Error cancelling emails:", error);
    return NextResponse.json(
      { error: "Failed to cancel emails" },
      { status: 500 }
    );
  }
} 