import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";
import { addAuditLog } from "@/lib/utils";

export async function POST(request: Request) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role
    if (!session || (user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get email IDs from request
    const data = await request.json();
    const { ids } = data;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: "No email IDs provided" },
        { status: 400 }
      );
    }
    
    // Get emails to be resent
    const emailsResult = await db.query(
      `SELECT id, status FROM email_queue WHERE id = ANY($1::int[])`,
      [ids]
    );
    
    const failedEmails = emailsResult.rows.filter(email => email.status === 'failed');
    
    if (failedEmails.length === 0) {
      return NextResponse.json(
        { error: "No failed emails found" },
        { status: 400 }
      );
    }
    
    const failedIds = failedEmails.map(email => email.id);
    
    // Reset emails for resending
    const result = await db.query(
      `UPDATE email_queue 
       SET status = 'pending', 
           attempts = 0, 
           error_message = NULL,
           updated_at = NOW(),
           scheduled_for = NULL
       WHERE id = ANY($1::int[]) AND status = 'failed'
       RETURNING id`,
      [failedIds]
    );
    
    // Add audit log
    await addAuditLog({
      action: 'resend_emails_bulk',
      userId: parseInt(user.id),
      entityType: 'email_queue',
      entityId: null,
      changes: {
        emailIds: failedIds,
        count: result.rowCount,
      },
    });
    
    return NextResponse.json({ 
      success: true,
      resent: result.rowCount,
      requested: ids.length
    });
  } catch (error) {
    console.error("Error resending emails:", error);
    return NextResponse.json(
      { error: "Failed to resend emails" },
      { status: 500 }
    );
  }
} 