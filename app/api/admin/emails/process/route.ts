import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { processEmailQueue } from "@/lib/email-queue";

export async function POST(request: NextRequest) {
  try {
    // Check authentication and authorization
    const user = await getCurrentUser();
    if (!session || (user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Process the email queue
    const result = await processEmailQueue();
    
    return NextResponse.json({
      success: result.success,
      processed: result.processed,
      error: result.error,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("Error processing email queue:", error);
    return NextResponse.json(
      { error: "Failed to process email queue", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 