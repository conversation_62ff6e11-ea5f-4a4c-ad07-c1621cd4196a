import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";
import { addAuditLog } from "@/lib/utils";

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role
    if (!session || (user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const { id } = params;
    const emailId = parseInt(id, 10);
    
    if (isNaN(emailId)) {
      return NextResponse.json(
        { error: "Invalid email ID" },
        { status: 400 }
      );
    }
    
    // Get the email from the queue
    const emailResult = await db.query(
      `SELECT * FROM email_queue WHERE id = $1`,
      [emailId]
    );
    
    if (emailResult.rows.length === 0) {
      return NextResponse.json(
        { error: "Email not found" },
        { status: 404 }
      );
    }
    
    const email = emailResult.rows[0];
    
    // Reset the email status and attempt count
    const result = await db.query(
      `UPDATE email_queue 
       SET status = 'pending', 
           attempts = 0, 
           error_message = NULL,
           updated_at = NOW(),
           scheduled_for = NULL
       WHERE id = $1
       RETURNING *`,
      [emailId]
    );
    
    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: "Failed to update email status" },
        { status: 500 }
      );
    }
    
    // Add audit log
    await addAuditLog({
      action: 'resend_email',
      userId: parseInt(user.id),
      entityType: 'email_queue',
      entityId: emailId,
      changes: {
        status: 'pending',
        previousStatus: email.status,
        attemptsReset: true,
      },
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error resending email:", error);
    return NextResponse.json(
      { error: "Failed to resend email" },
      { status: 500 }
    );
  }
} 