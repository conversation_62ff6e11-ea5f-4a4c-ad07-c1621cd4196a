import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { cancelQueuedEmail } from "@/lib/email-queue";

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role
    if (!session || (user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const { id } = params;
    const emailId = parseInt(id, 10);
    
    if (isNaN(emailId)) {
      return NextResponse.json(
        { error: "Invalid email ID" },
        { status: 400 }
      );
    }
    
    const result = await cancelQueuedEmail(emailId, parseInt(user.id));
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error || "Failed to cancel email" },
        { status: 400 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error cancelling email:", error);
    return NextResponse.json(
      { error: "Failed to cancel email" },
      { status: 500 }
    );
  }
} 