import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";
import { addAuditLog } from "@/lib/utils";

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role (e.g., editor or super_admin)
    if (!session || (user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const { id } = params;
    const emailId = parseInt(id, 10);
    
    if (isNaN(emailId)) {
      return NextResponse.json(
        { error: "Invalid email ID" },
        { status: 400 }
      );
    }
    
    // Check if email exists and its current status
    const checkResult = await db.query(
      // Also select is_archived to prevent re-archiving
      `SELECT status, is_archived FROM email_queue WHERE id = $1`,
      [emailId]
    );
    
    if (checkResult.rows.length === 0) {
      return NextResponse.json({ error: "Email not found" }, { status: 404 });
    }
    
    const { status: currentStatus, is_archived: alreadyArchived } = checkResult.rows[0];
    
    // Prevent archiving already archived emails
    if (alreadyArchived) {
      return NextResponse.json({ error: "Email is already archived" }, { status: 400 });
    }

    // Only allow archiving if status is 'sent' or 'cancelled'
    if (currentStatus !== 'sent' && currentStatus !== 'cancelled') {
        return NextResponse.json(
            { error: `Cannot archive email with status: ${currentStatus}` },
            { status: 400 } // Bad Request
        );
    }
    
    // Update the is_archived flag to TRUE
    const result = await db.query(
      `UPDATE email_queue 
       SET is_archived = TRUE, updated_at = NOW() 
       WHERE id = $1 
       RETURNING id`,
      [emailId]
    );

    if (result.rows.length === 0) {
        throw new Error("Failed to update email is_archived status");
    }
    
    // Add audit log
    await addAuditLog({
      action: 'archive_email',
      userId: parseInt(user.id),
      entityType: 'email_queue',
      entityId: emailId,
      changes: {
        is_archived: true, // Log the change
        status_when_archived: currentStatus,
      },
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error archiving email:", error);
    return NextResponse.json(
      { error: "Failed to archive email" },
      { status: 500 }
    );
  }
} 