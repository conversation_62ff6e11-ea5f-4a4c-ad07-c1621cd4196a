import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";
import { sendEmail } from "@/lib/email";

// Mock data for when the database table doesn't exist
const mockContactMessages = {
  "1": {
    id: "1",
    subject: "Vandens n<PERSON>",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone_number: "+37061234567",
    message: "Pasteb<PERSON><PERSON><PERSON> vandens nuotėkį pirmame aukšte prie lifto. Vanduo laša iš lubų.",
    category: "ISSUE",
    status: "NEW",
    created_at: "2023-11-15T08:30:00.000Z",
    updated_at: "2023-11-15T08:30:00.000Z",
    resolved_at: null,
    admin_notes: null,
    assigned_to: null,
    user_id: "user_abc123"
  },
  "2": {
    id: "2",
    subject: "Parkavimo viet<PERSON> ž<PERSON>",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone_number: "+37062345678",
    message: "<PERSON><PERSON><PERSON><PERSON>, ar planuojama atnaujinti parkavimo vietų žymėjimą šį pavasarį?",
    category: "QUESTION",
    status: "IN_PROGRESS",
    created_at: "2023-11-10T10:15:00.000Z",
    updated_at: "2023-11-11T14:22:00.000Z",
    resolved_at: null,
    admin_notes: "2023-11-11T14:22:00.000Z - Admin: Išsiųstas užklausimas dėl darbų grafiko.",
    assigned_to: "admin_user_123",
    user_id: "user_def456"
  },
  "3": {
    id: "3",
    subject: "Padėka už greitą reagavimą",
    name: "Ona Onaitė",
    email: "<EMAIL>",
    phone_number: "+37063456789",
    message: "Norėjau padėkoti už greitą reagavimą į mano pranešimą apie sugedusį apšvietimą. Viskas sutvarkyta per 24 valandas!",
    category: "FEEDBACK",
    status: "RESOLVED",
    created_at: "2023-11-05T16:45:00.000Z",
    updated_at: "2023-11-06T09:10:00.000Z",
    resolved_at: "2023-11-06T09:10:00.000Z",
    admin_notes: "2023-11-06T09:10:00.000Z - Admin: Padėkota už teigiamą atsiliepimą.",
    assigned_to: "admin_user_456",
    user_id: "user_ghi789"
  }
};

export async function GET(
  req: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id } = await context.params;
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Turite būti prisijungęs, kad galėtumėte peržiūrėti pranešimą" },
        { status: 401 }
      );
    }
    
    if (user.role !== 'super_admin' && user.role !== 'editor') {
      return NextResponse.json(
        { error: "Neturite teisių atlikti šio veiksmo" },
        { status: 403 }
      );
    }

    try {
      // Try to get message from database
      const { rows } = await db.query(
        'SELECT * FROM contact_messages WHERE id = $1',
        [id]
      );
      
      if (rows.length === 0) {
        return NextResponse.json(
          { error: "Pranešimas nerastas" },
          { status: 404 }
        );
      }
      
      return NextResponse.json(rows[0]);
    } catch (error: any) {
      // Check if the error is due to the table not existing
      if (error.message && (
          error.message.includes("relation \"contact_messages\" does not exist") || 
          error.message.includes("no such table: contact_messages")
        )) {
        // Use mock data instead
        const mockMessage = mockContactMessages[id];
        
        if (!mockMessage) {
          return NextResponse.json(
            { error: "Pranešimas nerastas" },
            { status: 404 }
          );
        }
        
        return NextResponse.json(mockMessage);
      } else {
        throw error; // Re-throw any other database errors
      }
    }
  } catch (error) {
    console.error("Klaida gaunant pranešimą:", error);
    return NextResponse.json(
      { error: "Įvyko klaida gaunant pranešimą" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  req: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id } = await context.params;
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Turite būti prisijungęs, kad galėtumėte atnaujinti pranešimą" },
        { status: 401 }
      );
    }
    
    if (user.role !== 'super_admin' && user.role !== 'editor') {
      return NextResponse.json(
        { error: "Neturite teisių atlikti šio veiksmo" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { status, notes } = body;

    // Validate status
    const validStatuses = ['NEW', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: "Neteisinga būsena" },
        { status: 400 }
      );
    }

    try {
      // Get existing message
      const { rows: existingRows } = await db.query(
        'SELECT * FROM contact_messages WHERE id = $1',
        [id]
      );
      
      if (existingRows.length === 0) {
        return NextResponse.json(
          { error: "Pranešimas nerastas" },
          { status: 404 }
        );
      }
      
      const existingMessage = existingRows[0];
      
      // Update message
      let query = 'UPDATE contact_messages SET status = $1, updated_at = CURRENT_TIMESTAMP';
      const queryParams: any[] = [status];
      
      // Add resolved_at timestamp if status changed to RESOLVED
      if (status === 'RESOLVED' && existingMessage.status !== 'RESOLVED') {
        query += ', resolved_at = CURRENT_TIMESTAMP';
      }
      
      // Add admin notes if provided
      if (notes && notes.trim() !== '') {
        const updatedNotes = existingMessage.admin_notes 
          ? `${existingMessage.admin_notes}\n\n${new Date().toISOString()} - ${user.name}:\n${notes}`
          : `${new Date().toISOString()} - ${user.name}:\n${notes}`;
        
        query += ', admin_notes = $2';
        queryParams.push(updatedNotes);
      }
      
      query += ' WHERE id = $' + (queryParams.length + 1) + ' RETURNING *';
      queryParams.push(id);
      
      const { rows } = await db.query(query, queryParams);
      const updatedMessage = rows[0];
      
      // If status is RESOLVED, send email to user
      if (status === 'RESOLVED' && existingMessage.status !== 'RESOLVED') {
        try {
          await sendEmail({
            to: updatedMessage.email,
            subject: `Jūsų pranešimas buvo išspręstas - ${updatedMessage.subject}`,
            html: `
              <h1>Jūsų pranešimas buvo išspręstas</h1>
              <p>Sveiki, ${updatedMessage.name},</p>
              <p>Jūsų pranešimas "<strong>${updatedMessage.subject}</strong>" buvo išspręstas.</p>
              <p>Jei turite kitų klausimų ar pastebėjimų, galite susisiekti su mumis naudodami kontaktinę formą.</p>
              <p>Pagarbiai,<br/>DNSB Vakarai administracija</p>
            `,
          });
        } catch (error) {
          console.error("Klaida siunčiant el. laišką:", error);
          // Continue even if email fails
        }
      }

      return NextResponse.json(updatedMessage);
    } catch (error: any) {
      // Check if the error is due to the table not existing
      if (error.message && (
          error.message.includes("relation \"contact_messages\" does not exist") || 
          error.message.includes("no such table: contact_messages")
        )) {
        // Use mock data instead
        const mockMessage = mockContactMessages[id];
        
        if (!mockMessage) {
          return NextResponse.json(
            { error: "Pranešimas nerastas" },
            { status: 404 }
          );
        }
        
        // Update the mock message
        const updatedMockMessage = {
          ...mockMessage,
          status,
          updated_at: new Date().toISOString()
        };
        
        // Add resolved_at if status changed to RESOLVED
        if (status === 'RESOLVED' && mockMessage.status !== 'RESOLVED') {
          updatedMockMessage.resolved_at = new Date().toISOString();
        }
        
        // Add admin notes if provided
        if (notes && notes.trim() !== '') {
          const updatedNotes = mockMessage.admin_notes 
            ? `${mockMessage.admin_notes}\n\n${new Date().toISOString()} - ${user.name}:\n${notes}`
            : `${new Date().toISOString()} - ${user.name}:\n${notes}`;
          
          updatedMockMessage.admin_notes = updatedNotes;
        }
        
        // Update the mock data
        mockContactMessages[id] = updatedMockMessage;
        
        // If status is RESOLVED, pretend to send email to user
        if (status === 'RESOLVED' && mockMessage.status !== 'RESOLVED') {
          console.log(`Mock email would be sent to ${mockMessage.email} for resolved message`);
        }
        
        return NextResponse.json(updatedMockMessage);
      } else {
        throw error; // Re-throw any other database errors
      }
    }
  } catch (error) {
    console.error("Klaida apdorojant užklausą:", error);
    return NextResponse.json(
      { error: "Įvyko klaida apdorojant jūsų užklausą" },
      { status: 500 }
    );
  }
} 