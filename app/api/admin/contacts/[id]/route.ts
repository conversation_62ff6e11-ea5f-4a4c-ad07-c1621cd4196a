import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { getContactById, updateContact, deleteContact } from "@/lib/contacts";
import { addAuditLog } from "@/lib/utils";

/**
 * Get a single contact by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role
    if (!session || (user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const { id } = params;
    const contactId = parseInt(id, 10);
    
    if (isNaN(contactId)) {
      return NextResponse.json(
        { error: "Invalid contact ID" },
        { status: 400 }
      );
    }
    
    const contact = await getContactById(contactId);
    
    if (!contact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ contact });
  } catch (error) {
    console.error("Error fetching contact:", error);
    return NextResponse.json(
      { error: "Failed to fetch contact" },
      { status: 500 }
    );
  }
}

/**
 * Update a contact
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role
    if (!session || (user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const { id } = params;
    const contactId = parseInt(id, 10);
    
    if (isNaN(contactId)) {
      return NextResponse.json(
        { error: "Invalid contact ID" },
        { status: 400 }
      );
    }
    
    const body = await req.json();
    
    // Get the current contact to track changes
    const existingContact = await getContactById(contactId);
    if (!existingContact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }
    
    // Update the contact
    const updatedContact = await updateContact(contactId, body);
    
    if (!updatedContact) {
      throw new Error("Failed to update contact");
    }
    
    // Add audit log
    await addAuditLog({
      action: 'update_contact',
      userId: parseInt(user.id),
      entityType: 'contacts',
      entityId: contactId,
      changes: {
        before: existingContact,
        after: updatedContact
      }
    });
    
    return NextResponse.json({ contact: updatedContact });
  } catch (error) {
    console.error("Error updating contact:", error);
    return NextResponse.json(
      { error: "Failed to update contact" },
      { status: 500 }
    );
  }
}

/**
 * Delete a contact
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role
    if (!session || (user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const { id } = params;
    const contactId = parseInt(id, 10);
    
    if (isNaN(contactId)) {
      return NextResponse.json(
        { error: "Invalid contact ID" },
        { status: 400 }
      );
    }
    
    // Get the contact before deletion for the audit log
    const contact = await getContactById(contactId);
    
    if (!contact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }
    
    // Delete the contact
    const success = await deleteContact(contactId);
    
    if (!success) {
      throw new Error("Failed to delete contact");
    }
    
    // Add audit log
    await addAuditLog({
      action: 'delete_contact',
      userId: parseInt(user.id),
      entityType: 'contacts',
      entityId: contactId,
      changes: { deletedContact: contact }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting contact:", error);
    return NextResponse.json(
      { error: "Failed to delete contact" },
      { status: 500 }
    );
  }
} 