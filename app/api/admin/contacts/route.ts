import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { 
  getContacts, 
  createContact, 
  updateContact, 
  deleteContact,
  reorderContacts,
  CreateContactData,
  UpdateContactData
} from "@/lib/contacts";
import { addAuditLog } from "@/lib/utils";
import { z } from 'zod'; // Import Zod

// Define a schema for contact creation
const createContactSchema = z.object({
  label: z.string().min(1, "Label is required").max(100),
  phone_number: z.string().min(1, "Phone number is required").max(50),
  description: z.string().max(500).optional(),
  company_name: z.string().max(100).optional(),
  category: z.string().min(1, "Category is required").max(50),
  display_in_footer: z.boolean().optional().default(false),
  display_in_contacts: z.boolean().optional().default(false),
  display_in_emergency: z.boolean().optional().default(false),
  icon: z.string().max(50).optional(),
});

/**
 * Get all contacts
 */
export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role
    if (!session || (user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const contacts = await getContacts();
    
    return NextResponse.json({ contacts });
  } catch (error) {
    console.error("Error fetching contacts:", error);
    return NextResponse.json(
      { error: "Failed to fetch contacts" },
      { status: 500 }
    );
  }
}

/**
 * Create a new contact
 */
export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role
    if (!session || (user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const body = await req.json();
    
    // Validate the request body using Zod
    const validationResult = createContactSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid input data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Use validated data
    const contactData = validationResult.data;
    
    // Create the contact
    const contact = await createContact(contactData);
    
    if (!contact) {
      throw new Error("Failed to create contact");
    }
    
    // Add audit log
    await addAuditLog({
      action: 'create_contact',
      userId: parseInt(user.id),
      entityType: 'contacts',
      entityId: contact.id,
      changes: contactData
    });
    
    return NextResponse.json({ contact });
  } catch (error) {
    console.error("Error creating contact:", error);
    return NextResponse.json(
      { error: "Failed to create contact" },
      { status: 500 }
    );
  }
}

/**
 * Update contact order
 */
export async function PUT(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Check if user is authenticated and has proper role
    if (!session || (user.role !== "super_admin" && user.role !== "editor")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const body = await req.json();
    
    if (body.action === 'reorder' && body.category && Array.isArray(body.orderedIds)) {
      const success = await reorderContacts(body.category, body.orderedIds);
      
      if (!success) {
        throw new Error("Failed to reorder contacts");
      }
      
      // Add audit log
      await addAuditLog({
        action: 'reorder_contacts',
        userId: parseInt(user.id),
        entityType: 'contacts',
        entityId: 0,
        changes: {
          category: body.category,
          orderedIds: body.orderedIds
        }
      });
      
      return NextResponse.json({ success: true });
    }
    
    return NextResponse.json(
      { error: "Invalid request" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error updating contacts:", error);
    return NextResponse.json(
      { error: "Failed to update contacts" },
      { status: 500 }
    );
  }
} 