import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { isBunnyCDNConfigured } from '@/lib/config/bunnycdn';
import { getStorageService } from '@/lib/services/storage-factory';

/**
 * Health check endpoint for deployment verification
 * Tests basic application functionality and dependencies
 */
export async function GET() {
  const checks = {
    timestamp: new Date().toISOString(),
    status: 'healthy',
    checks: {
      application: true,
      supabase: false,
      environment: false,
      cdn: false
    }
  };

  try {
    // Check environment variables
    const requiredEnvVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY'
    ];

    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    
    if (missingEnvVars.length === 0) {
      checks.checks.environment = true;
    } else {
      checks.checks.environment = false;
      console.warn('Missing environment variables:', missingEnvVars);
    }

    // Check Supabase connection if environment is configured
    if (checks.checks.environment) {
      try {
        const supabase = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
        );

        // Simple query to test connection
        const { error } = await supabase
          .from('users')
          .select('count', { count: 'exact', head: true });

        if (!error) {
          checks.checks.supabase = true;
        } else {
          checks.checks.supabase = false;
          console.warn('Supabase connection error:', error.message);
        }
      } catch (error) {
        checks.checks.supabase = false;
        console.warn('Supabase connection failed:', error);
      }
    }

    // Check CDN/Storage connection
    try {
      const storageService = getStorageService();
      
      // Try to get storage stats to verify connection
      await storageService.getStorageStats();
      checks.checks.cdn = true;
      
      // Add CDN info to response if in production
      if (process.env.NODE_ENV === 'production' && isBunnyCDNConfigured()) {
        (checks as Record<string, any>).cdnProvider = 'BunnyCDN';
      } else {
        (checks as Record<string, any>).cdnProvider = 'Local Storage';
      }
    } catch (error) {
      checks.checks.cdn = false;
      console.warn('CDN/Storage connection failed:', error);
    }

    // Determine overall status
    const allChecksPass = Object.values(checks.checks).every(check => check === true);
    checks.status = allChecksPass ? 'healthy' : 'degraded';

    // Return appropriate HTTP status
    const httpStatus = allChecksPass ? 200 : 503;

    return NextResponse.json(checks, { status: httpStatus });

  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      status: 'unhealthy',
      error: 'Health check failed',
      checks: {
        application: false,
        supabase: false,
        environment: false,
        cdn: false
      }
    }, { status: 503 });
  }
}
