import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { updateUserEmailPreferences } from "@/lib/email-queue";
import { addAuditLog } from "@/lib/utils";

export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const body = await req.json();
    const { preferences } = body;
    
    if (!preferences || typeof preferences !== 'object') {
      return NextResponse.json(
        { error: "Invalid preferences data" },
        { status: 400 }
      );
    }
    
    // Ensure all preferences are boolean values
    const validPreferences: Record<string, boolean> = {};
    for (const [key, value] of Object.entries(preferences)) {
      validPreferences[key] = Boolean(value);
    }
    
    const userId = parseInt(user.id);
    const result = await updateUserEmailPreferences(userId, validPreferences);
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error || "Failed to update preferences" },
        { status: 500 }
      );
    }
    
    // Add audit log
    await addAuditLog({
      action: 'update_email_preferences',
      userId,
      entityType: 'user',
      entityId: userId,
      changes: validPreferences,
    });
    
    return NextResponse.json({
      success: true,
      message: "Email preferences updated successfully",
    });
  } catch (error) {
    console.error("Error updating email preferences:", error);
    return NextResponse.json(
      { error: "Failed to update email preferences" },
      { status: 500 }
    );
  }
} 