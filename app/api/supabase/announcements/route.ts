import { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { 
  withAuth, 
  withRole, 
  successResponse, 
  errorResponse, 
  parsePaginationParams, 
  applyPagination,
  handleOptions
} from '@/lib/supabase/api-helpers'

// Handle CORS preflight
export async function OPTIONS(req: NextRequest) {
  return handleOptions(req)
}

// GET /api/supabase/announcements - Get announcements
export const GET = withAuth(async (req: NextRequest, user) => {
  try {
    const supabase = createClient()
    const { searchParams } = new URL(req.url)
    const pagination = parsePaginationParams(searchParams)
    
    // Build query
    let query = supabase
      .from('announcements')
      .select(`
        *,
        author:users!author_id(username, name)
      `)
      .eq('status', 'active')
    
    // Apply pagination
    query = applyPagination(query, pagination)
    
    const { data: announcements, error, count } = await query
    
    if (error) {
      console.error('Error fetching announcements:', error)
      return errorResponse('Failed to fetch announcements', 500)
    }
    
    return successResponse({
      announcements,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total: count
      }
    })
  } catch (error) {
    console.error('Unexpected error:', error)
    return errorResponse('Internal server error', 500)
  }
})

// POST /api/supabase/announcements - Create announcement
export const POST = withRole('editor', async (req: NextRequest, user, profile) => {
  try {
    const supabase = createClient()
    const body = await req.json()
    
    // Validate required fields
    const { title, content, importance = 'normal', audience = 'all', expires_at } = body
    
    if (!title || !content) {
      return errorResponse('Title and content are required')
    }
    
    // Create announcement
    const { data: announcement, error } = await supabase
      .from('announcements')
      .insert({
        title,
        content,
        author_id: user.id,
        importance,
        audience,
        expires_at,
        status: 'active'
      })
      .select(`
        *,
        author:users!author_id(username, name)
      `)
      .single()
    
    if (error) {
      console.error('Error creating announcement:', error)
      return errorResponse('Failed to create announcement', 500)
    }
    
    // TODO: Trigger email notifications here
    // This could be done via a Supabase Edge Function or database trigger
    
    return successResponse(announcement, 201)
  } catch (error) {
    console.error('Unexpected error:', error)
    return errorResponse('Internal server error', 500)
  }
})

// PUT /api/supabase/announcements/[id] - Update announcement
export const PUT = withAuth(async (req: NextRequest, user) => {
  try {
    const supabase = createClient()
    const { searchParams } = new URL(req.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return errorResponse('Announcement ID is required')
    }
    
    const body = await req.json()
    const { title, content, importance, audience, expires_at, status } = body
    
    // Check if user owns the announcement or is admin
    const { data: existingAnnouncement } = await supabase
      .from('announcements')
      .select('author_id')
      .eq('id', id)
      .single()
    
    if (!existingAnnouncement) {
      return errorResponse('Announcement not found', 404)
    }
    
    // Check permissions
    const { data: userProfile } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()
    
    const isOwner = existingAnnouncement.author_id === user.id
    const isAdmin = userProfile?.role && ['super_admin', 'developer'].includes(userProfile.role)
    
    if (!isOwner && !isAdmin) {
      return errorResponse('Insufficient permissions', 403)
    }
    
    // Update announcement
    const { data: announcement, error } = await supabase
      .from('announcements')
      .update({
        title,
        content,
        importance,
        audience,
        expires_at,
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        author:users!author_id(username, name)
      `)
      .single()
    
    if (error) {
      console.error('Error updating announcement:', error)
      return errorResponse('Failed to update announcement', 500)
    }
    
    return successResponse(announcement)
  } catch (error) {
    console.error('Unexpected error:', error)
    return errorResponse('Internal server error', 500)
  }
})

// DELETE /api/supabase/announcements/[id] - Delete announcement
export const DELETE = withRole('super_admin', async (req: NextRequest, user, profile) => {
  try {
    const supabase = createClient()
    const { searchParams } = new URL(req.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return errorResponse('Announcement ID is required')
    }
    
    const { error } = await supabase
      .from('announcements')
      .delete()
      .eq('id', id)
    
    if (error) {
      console.error('Error deleting announcement:', error)
      return errorResponse('Failed to delete announcement', 500)
    }
    
    return successResponse({ message: 'Announcement deleted successfully' })
  } catch (error) {
    console.error('Unexpected error:', error)
    return errorResponse('Internal server error', 500)
  }
})
