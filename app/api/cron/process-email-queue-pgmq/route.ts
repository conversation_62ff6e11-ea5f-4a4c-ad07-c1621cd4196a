import { NextRequest, NextResponse } from "next/server";
import { createServiceClient } from "@/lib/supabase/server";
import { sendEmail } from "@/lib/email";
import { addAuditLog } from "@/lib/utils";
import { headers } from "next/headers";

// Verify the request is from a trusted source
async function verifyCronRequest(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  
  // In production, use a secure token
  const cronSecret = process.env.CRON_SECRET;
  if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
    return false;
  }
  
  return true;
}

interface EmailMessage {
  emailType: string;
  recipient: string;
  subject: string;
  content: string;
  entityType?: string;
  entityId?: number;
  metadata?: any;
  replyTo?: string;
  announcementId?: number;
  pollId?: number;
  testMode?: boolean;
  priority?: number;
  scheduledFor?: string;
  userId?: number;
  retryCount?: number;
}

export async function GET(request: NextRequest) {
  try {
    // Verify the request is authorized
    if (!(await verifyCronRequest(request))) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const supabase = await createServiceClient();
    const batchSize = parseInt(process.env.EMAIL_BATCH_SIZE || '10');
    
    console.log(`[Email Queue API] Processing email queue (batch size: ${batchSize})...`);
    
    // Read messages from PGMQ
    const { data: messages, error: readError } = await supabase
      .rpc('pgmq_read', {
        queue_name: 'email_queue',
        visibility_timeout: 30, // 30 second visibility timeout
        quantity: batchSize
      });

    if (readError) {
      console.error('[Email Queue API] Error reading from queue:', readError);
      return NextResponse.json({ 
        error: 'Failed to read from queue',
        details: readError.message 
      }, { status: 500 });
    }

    if (!messages || messages.length === 0) {
      return NextResponse.json({ 
        success: true,
        processed: 0,
        message: 'No messages to process' 
      });
    }

    let processed = 0;
    let failed = 0;
    const results: any[] = [];

    // Process each message
    for (const msg of messages) {
      const startTime = Date.now();
      const emailData: EmailMessage = msg.message;
      
      try {
        // Check if scheduled for future
        if (emailData.scheduledFor) {
          const scheduledTime = new Date(emailData.scheduledFor).getTime();
          if (scheduledTime > Date.now()) {
            console.log(`[Email Queue API] Message ${msg.msg_id} scheduled for future, skipping`);
            continue;
          }
        }

        // Send the actual email
        const result = await sendEmail({
          to: emailData.recipient,
          subject: emailData.subject,
          html: emailData.content,
          replyTo: emailData.replyTo,
        });

        if (result.success) {
          // Delete the message from queue
          await supabase.rpc('pgmq_delete', {
            queue_name: 'email_queue',
            msg_id: msg.msg_id
          });

          // Update status in database
          await supabase
            .from('email_queue')
            .update({ 
              status: 'sent',
              sent_at: new Date().toISOString(),
              processing_time_ms: Date.now() - startTime
            })
            .eq('pgmq_msg_id', msg.msg_id);

          // Add audit log if userId is present
          if (emailData.userId) {
            await addAuditLog({
              action: 'send_email',
              userId: emailData.userId,
              entityType: 'email_queue',
              entityId: msg.msg_id,
              changes: {
                emailType: emailData.emailType,
                recipient: emailData.recipient,
                testMode: emailData.testMode
              }
            });
          }

          processed++;
          results.push({
            msgId: msg.msg_id,
            status: 'sent',
            recipient: emailData.recipient
          });
        } else {
          throw new Error(result.error || 'Failed to send email');
        }
      } catch (error) {
        console.error(`[Email Queue API] Failed to process message ${msg.msg_id}:`, error);
        
        const retryCount = (emailData.retryCount || 0) + 1;
        const maxRetries = 5;
        
        if (retryCount >= maxRetries) {
          // Archive the message as permanently failed
          await supabase.rpc('pgmq_archive', {
            queue_name: 'email_queue',
            msg_id: msg.msg_id
          });
          
          // Update status in database
          await supabase
            .from('email_queue')
            .update({ 
              status: 'failed',
              last_error: error instanceof Error ? error.message : String(error),
              retry_count: retryCount,
              failed_at: new Date().toISOString()
            })
            .eq('pgmq_msg_id', msg.msg_id);
          
          failed++;
          results.push({
            msgId: msg.msg_id,
            status: 'failed',
            error: error instanceof Error ? error.message : String(error),
            retries: retryCount
          });
        } else {
          // Message will automatically reappear after visibility timeout
          // Update retry count
          emailData.retryCount = retryCount;
          
          // Update the message in PGMQ with new retry count
          await supabase.rpc('pgmq_send', {
            queue_name: 'email_queue',
            message: emailData
          });
          
          // Archive the old message
          await supabase.rpc('pgmq_archive', {
            queue_name: 'email_queue',
            msg_id: msg.msg_id
          });
          
          // Update retry count in database
          await supabase
            .from('email_queue')
            .update({ 
              status: 'retrying',
              last_error: error instanceof Error ? error.message : String(error),
              retry_count: retryCount,
              last_retry_at: new Date().toISOString()
            })
            .eq('pgmq_msg_id', msg.msg_id);
          
          results.push({
            msgId: msg.msg_id,
            status: 'retrying',
            retryCount: retryCount,
            maxRetries: maxRetries
          });
        }
      }
    }
    
    return NextResponse.json({
      success: true,
      processed,
      failed,
      total: messages.length,
      results
    });
    
  } catch (error) {
    console.error('[Email Queue API] Fatal error:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

// Also support POST for flexibility
export async function POST(request: NextRequest) {
  return GET(request);
}