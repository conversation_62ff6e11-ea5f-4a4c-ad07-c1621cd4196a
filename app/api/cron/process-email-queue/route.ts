import { NextRequest, NextResponse } from "next/server";
import { processEmailQueue } from "@/lib/email-queue";

/**
 * This endpoint is designed to be called by a cron job service
 * to process the email queue at regular intervals
 * 
 * It should be secured with either:
 * 1. A shared secret key in the Authorization header
 * 2. IP-based restrictions to only allow calls from trusted sources
 */
export async function POST(req: NextRequest) {
  try {
    // Verify the authorization header for security
    const authHeader = req.headers.get('authorization');
    const cronKey = process.env.CRON_API_KEY;
    
    if (!cronKey) {
      console.warn("CRON_API_KEY is not set in environment variables. This endpoint should be secured.");
    }
    
    // If a CRON_API_KEY is set, validate against the Authorization header
    if (cronKey && (!authHeader || !authHeader.startsWith('Bearer ') || authHeader.split(' ')[1] !== cronKey)) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Process the email queue
    const result = await processEmailQueue();
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        processed: result.processed,
        timestamp: new Date().toISOString()
      });
    } else {
      throw new Error(result.error);
    }
  } catch (error) {
    console.error("Error processing email queue:", error);
    return NextResponse.json(
      { error: "Failed to process email queue", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 