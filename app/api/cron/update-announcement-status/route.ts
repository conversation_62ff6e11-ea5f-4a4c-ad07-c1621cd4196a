import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db/supabase-adapter";
import { addAuditLog } from "@/lib/utils";

/**
 * This endpoint is designed to be called by a cron job service
 * to automatically downgrade announcement importance levels after 7 days
 * 
 * It should be secured with either:
 * 1. A shared secret key in the Authorization header
 * 2. IP-based restrictions to only allow calls from trusted sources
 */
export async function POST(req: NextRequest) {
  try {
    // Verify the authorization header for security
    const authHeader = req.headers.get('authorization');
    const cronKey = process.env.CRON_API_KEY;
    
    if (!cronKey) {
      console.warn("CRON_API_KEY is not set in environment variables. This endpoint should be secured.");
    }
    
    // If a CRON_API_KEY is set, validate against the Authorization header
    if (cronKey && (!authHeader || !authHeader.startsWith('Bearer ') || authHeader.split(' ')[1] !== cronKey)) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Update announcements that are older than 7 days
    // Change 'urgent' and 'important' status to 'normal'
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    await db.query('BEGIN');
    
    try {
      // Get announcements that need to be updated
      const result = await db.query(
        `
        SELECT id, title, importance, author_id
        FROM announcements
        WHERE (importance = 'urgent' OR importance = 'important')
        AND is_draft = false
        AND sent_at < $1
        `,
        [sevenDaysAgo.toISOString()]
      );
      
      const updatedCount = result.rows.length;
      
      // Update each announcement and log the change
      for (const announcement of result.rows) {
        await db.query(
          `
          UPDATE announcements
          SET importance = 'normal', updated_at = CURRENT_TIMESTAMP
          WHERE id = $1
          `,
          [announcement.id]
        );
        
        // Add audit log
        await addAuditLog({
          action: 'update_announcement_importance',
          userId: announcement.author_id, // Using original author as the actor
          entityType: 'announcement',
          entityId: announcement.id,
          changes: {
            previous_importance: announcement.importance,
            new_importance: 'normal',
            reason: 'Automatic downgrade after 7 days'
          },
        });
      }
      
      await db.query('COMMIT');
      
      return NextResponse.json({
        success: true,
        updated: updatedCount,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      await db.query('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error("Error updating announcement status:", error);
    return NextResponse.json(
      { error: "Failed to update announcement status", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 