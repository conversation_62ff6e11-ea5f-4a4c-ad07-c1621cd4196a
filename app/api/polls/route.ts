import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { addAuditLog } from "@/lib/utils";

// GET: Fetch polls with filtering options
export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const status = searchParams.get("status");
    const pollId = searchParams.get("id");
    const isAdmin = ["developer", "super_admin", "editor"].includes(user.role);
    
    const supabase = await createServiceClient();
    
    // If a specific poll ID is requested
    if (pollId) {
      const { data: poll, error } = await supabase
        .from('polls')
        .select(`
          id,
          title,
          description,
          status,
          start_date,
          end_date,
          created_at,
          updated_at,
          show_results,
          creator_id,
          created_by
        `)
        .eq('id', pollId)
        .single();
      
      if (error || !poll) {
        return NextResponse.json({ error: "Poll not found" }, { status: 404 });
      }
      
      // Get author name separately
      let authorName = 'Unknown';
      const authorId = poll.created_by || poll.creator_id;
      if (authorId) {
        const { data: author } = await supabase
          .from('users')
          .select('name')
          .eq('id', authorId)
          .single();
        authorName = author?.name || 'Unknown';
      }
      
      // Transform data to match expected format
      const transformedPoll = {
        ...poll,
        created_by_name: authorName,
        show_results_after_voting: poll.show_results,
        audience_type: 'all', // Default value since this column might not exist
        has_explanations: false // Will be calculated separately if needed
      };
      
      return NextResponse.json(transformedPoll);
    }
    
    // For listing polls
    let query = supabase
      .from('polls')
      .select(`
        id,
        title,
        description,
        status,
        start_date,
        end_date,
        created_at,
        updated_at,
        show_results,
        creator_id,
        created_by
      `);
    
    // Apply status filter
    if (status) {
      query = query.eq('status', status);
    } else if (!isAdmin) {
      // For regular users, show active and closed polls
      query = query.in('status', ['active', 'closed']);
    }
    
    // Order by created_at
    query = query.order('created_at', { ascending: false });
    
    const { data: polls, error } = await query;
    
    if (error) {
      throw error;
    }
    
    // Get author names for polls
    const authorIds = [...new Set((polls || []).map(p => p.created_by || p.creator_id).filter(Boolean))];
    let authorNames: Record<string, string> = {};
    
    if (authorIds.length > 0) {
      const { data: authors } = await supabase
        .from('users')
        .select('id, name')
        .in('id', authorIds);
      
      authorNames = (authors || []).reduce((acc, author) => {
        acc[author.id] = author.name;
        return acc;
      }, {} as Record<string, string>);
    }
    
    // Transform polls
    const transformedPolls = (polls || []).map(poll => ({
      ...poll,
      created_by_name: authorNames[poll.created_by || poll.creator_id] || 'Unknown',
      show_results_after_voting: poll.show_results,
      audience_type: 'all', // Default value
      has_explanations: false
    }));
    
    console.log(`Found ${transformedPolls.length} poll(s) for user ${user.id} with role ${user.role}`);
    return NextResponse.json(transformedPolls);
  } catch (error) {
    console.error("Error fetching polls:", error);
    return NextResponse.json(
      { error: "Failed to fetch polls" },
      { status: 500 }
    );
  }
}

// POST: Create a new poll
export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Check authentication
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check authorization (only admins can create polls)
    const isAdmin = ["developer", "super_admin", "editor"].includes(user.role);
    if (!isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }
    
    const {
      title,
      description,
      status,
      startDate,
      endDate,
      audienceType,
      options,
      showResultsAfterVoting,
      
      // Audience-specific fields
      streetIds,
      houseIds,
      flatIds,
      userIds,
    } = await req.json();
    
    // Basic validation
    if (!title || title.length < 5) {
      return NextResponse.json({ error: "Title must be at least 5 characters long" }, { status: 400 });
    }
    
    if (!description || description.length < 10) {
      return NextResponse.json({ error: "Description must be at least 10 characters long" }, { status: 400 });
    }
    
    if (!options || !Array.isArray(options) || options.length < 2) {
      return NextResponse.json({ error: "At least 2 options are required" }, { status: 400 });
    }
    
    // Handle both string and object formats for options
    const optionTexts = options.map((opt: any) => {
      if (typeof opt === 'string') {
        return opt;
      } else if (opt && typeof opt === 'object' && opt.text) {
        return opt.text;
      }
      return '';
    }).filter(text => text && text.trim());
    
    if (optionTexts.length < 2) {
      return NextResponse.json({ error: "At least 2 valid, non-empty options are required" }, { status: 400 });
    }
    
    if (!['all', 'streets', 'houses', 'flats', 'users'].includes(audienceType)) {
      return NextResponse.json({ error: "Invalid audience type" }, { status: 400 });
    }
    
    // Validate audience specific fields
    if (audienceType === 'streets' && (!streetIds || !Array.isArray(streetIds) || streetIds.length === 0)) {
      return NextResponse.json({ error: "Street selection is required for 'streets' audience type" }, { status: 400 });
    }
    
    if (audienceType === 'houses' && (!houseIds || !Array.isArray(houseIds) || houseIds.length === 0)) {
      return NextResponse.json({ error: "House selection is required for 'houses' audience type" }, { status: 400 });
    }
    
    if (audienceType === 'flats' && (!flatIds || !Array.isArray(flatIds) || flatIds.length === 0)) {
      return NextResponse.json({ error: "Flat selection is required for 'flats' audience type" }, { status: 400 });
    }
    
    if (audienceType === 'users' && (!userIds || !Array.isArray(userIds) || userIds.length === 0)) {
      return NextResponse.json({ error: "User selection is required for 'users' audience type" }, { status: 400 });
    }
    
    // Create poll using Supabase
    const createdBy = parseInt(user.id);
    let createdPollId: number | null = null;

    try {
      const supabase = await createServiceClient();
      
      // Insert into polls table
      const { data: poll, error: pollError } = await supabase
        .from('polls')
        .insert({
          title, 
          description, 
          status: status || 'draft',
          start_date: startDate || null, 
          end_date: endDate || null, 
          created_by: createdBy,
          show_results_after_voting: showResultsAfterVoting !== undefined ? showResultsAfterVoting : true,
          audience_type: audienceType,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single();
      
      if (pollError) {
        console.error('Error creating poll:', pollError);
        throw pollError;
      }
      
      createdPollId = poll.id;

      // Insert options using the extracted option texts
      const optionsData = optionTexts.map((optionText: string, index: number) => ({
        poll_id: createdPollId,
        option_text: optionText,
        display_order: index
      }));
      
      const { error: optionsError } = await supabase
        .from('poll_options')
        .insert(optionsData);
      
      if (optionsError) {
        console.error('Error creating poll options:', optionsError);
        throw optionsError;
      }
      
      // Handle audience targeting using new linking tables
      if (audienceType === 'streets' && streetIds && streetIds.length > 0) {
        // Find all houses in these streets (same as announcements)
        const { data: houses, error: housesError } = await supabase
          .from('houses')
          .select('id')
          .in('street_id', streetIds.map(id => parseInt(id)));
        
        if (housesError) {
          console.error('Error fetching houses for streets:', housesError);
          throw housesError;
        }
        
        if (houses && houses.length > 0) {
          const houseLinks = houses.map(house => ({
            poll_id: createdPollId,
            house_id: house.id
          }));
          
          const { error: houseError } = await supabase
            .from('poll_houses')
            .insert(houseLinks);
          
          if (houseError) {
            console.error('Error creating poll house links for streets:', houseError);
            throw houseError;
          }
        }
      } else if (audienceType === 'houses' && houseIds && houseIds.length > 0) {
        const houseLinks = houseIds.map((houseId: string) => ({
          poll_id: createdPollId,
          house_id: parseInt(houseId)
        }));
        
        const { error: houseError } = await supabase
          .from('poll_houses')
          .insert(houseLinks);
        
        if (houseError) {
          console.error('Error creating poll house links:', houseError);
          throw houseError;
        }
      } else if (audienceType === 'flats' && flatIds && flatIds.length > 0) {
        const flatLinks = flatIds.map((flatId: string) => ({
          poll_id: createdPollId,
          flat_id: parseInt(flatId)
        }));
        
        const { error: flatError } = await supabase
          .from('poll_flats')
          .insert(flatLinks);
        
        if (flatError) {
          console.error('Error creating poll flat links:', flatError);
          throw flatError;
        }
      } else if (audienceType === 'users' && userIds && userIds.length > 0) {
        const userLinks = userIds.map((userId: string) => ({
          poll_id: createdPollId,
          user_id: parseInt(userId)
        }));
        
        const { error: userError } = await supabase
          .from('poll_users')
          .insert(userLinks);
        
        if (userError) {
          console.error('Error creating poll user links:', userError);
          throw userError;
        }
      }
      // Note: 'streets' type doesn't have a direct linking table.
      // Eligibility for 'streets' will be checked based on user's house/flat in the GET request.
      // 'all' type requires no specific links.

      // Record audit log
      await addAuditLog({
        userId: createdBy,
        action: 'poll_created',
        entityType: 'poll',
        entityId: createdPollId,
        changes: { title, status: status || 'draft', audienceType }
      });
      
      return NextResponse.json({ message: "Poll created successfully", pollId: createdPollId });
      
    } catch (error) {
      console.error('Error creating poll:', error);
      // Note: Supabase doesn't have built-in transactions like PostgreSQL
      // If any insert fails, we might need to clean up manually
      return NextResponse.json({ error: "Error creating poll" }, { status: 500 });
    }
  } catch (error) {
    console.error('Error in POST /api/polls:', error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}


// PATCH: Update a poll
export async function PATCH(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Naudotojas neprisijungęs" }, { status: 401 });
    }

    // Check if user is admin
    const userRole = user.role;
    if (userRole !== "developer" && userRole !== "super_admin" && userRole !== "editor") {
      return NextResponse.json({ error: "Neturite teisių atlikti šį veiksmą" }, { status: 403 });
    }

    const searchParams = new URL(request.url).searchParams;
    const pollIdParam = searchParams.get("id");

    if (!pollIdParam) {
      return NextResponse.json({ error: "Nenurodytas apklausos ID" }, { status: 400 });
    }
    const pollId = parseInt(pollIdParam);

    const data = await request.json();
    const { 
      title, 
      description, 
      status, 
      startDate, 
      endDate, 
      audienceType, 
      showResultsAfterVoting,
      streetIds,
      houseIds, 
      flatIds, 
      userIds, 
      options
    } = data;

    // Validate required fields
    if (!title || !description || !status) {
      return NextResponse.json({ error: "Trūksta privalomų laukų (pavadinimas, aprašymas, būsena)" }, { status: 400 });
    }

    // Validate status
    if (!["draft", "active", "closed"].includes(status)) {
      return NextResponse.json({ error: "Neteisinga būsena" }, { status: 400 });
    }

    // Validate dates for active polls (start date optional on update, but if provided...)
    if (status === "active" && startDate && endDate && new Date(startDate) > new Date(endDate)) {
        return NextResponse.json(
            { error: "Pradžios data turi būti ankstesnė nei pabaigos data" },
            { status: 400 }
        );
    }

    // Validate audience type
    if (!["all", "streets", "houses", "flats", "users"].includes(audienceType)) {
      return NextResponse.json({ error: "Neteisinga auditorijos tipas" }, { status: 400 });
    }

    // Validate audience selection
    if (audienceType === "streets" && (!streetIds || !Array.isArray(streetIds) || streetIds.length === 0)) {
      return NextResponse.json({ error: "Pasirinkite bent vieną gatvę, jei auditorijos tipas 'gatvės'" }, { status: 400 });
    }
    
    if (audienceType === "houses" && (!houseIds || !Array.isArray(houseIds) || houseIds.length === 0)) {
      return NextResponse.json({ error: "Pasirinkite bent vieną namą, jei auditorijos tipas 'namai'" }, { status: 400 });
    }

    if (audienceType === "flats" && (!flatIds || !Array.isArray(flatIds) || flatIds.length === 0)) {
      return NextResponse.json({ error: "Pasirinkite bent vieną butą, jei auditorijos tipas 'butai'" }, { status: 400 });
    }

    if (audienceType === "users" && (!userIds || !Array.isArray(userIds) || userIds.length === 0)) {
      return NextResponse.json({ error: "Pasirinkite bent vieną vartotoją, jei auditorijos tipas 'vartotojai'" }, { status: 400 });
    }

    // Validate options
    const activeOptions = options ? options.filter((opt: any) => !opt.deleted) : [];
    if (activeOptions.length < 2) {
      return NextResponse.json({ error: "Reikia bent 2 aktyvių pasirinkimų" }, { status: 400 });
    }

    // Check if any active option is empty
    if (activeOptions.some((opt: any) => !opt.text || !opt.text.trim())) {
      return NextResponse.json({ error: "Visi aktyvūs pasirinkimai turi turėti turinį" }, { status: 400 });
    }

    const supabase = await createServiceClient();

    try {
      // Update poll details using Supabase
      const { data: updatedPoll, error: pollError } = await supabase
        .from('polls')
        .update({
          title,
          description,
          status,
          start_date: startDate ? new Date(startDate).toISOString() : null,
          end_date: endDate ? new Date(endDate).toISOString() : null,
          audience_type: audienceType,
          show_results_after_voting: showResultsAfterVoting !== undefined ? showResultsAfterVoting : true,
          updated_at: new Date().toISOString()
        })
        .eq('id', pollId)
        .select()
        .single();

      if (pollError || !updatedPoll) {
        console.error('Error updating poll:', pollError);
        return NextResponse.json({ error: "Apklausa nerasta arba nepavyko atnaujinti" }, { status: 404 });
      }

      // Handle options: Get existing, delete marked, update/insert others
      const { data: existingOptions } = await supabase
        .from('poll_options')
        .select('id')
        .eq('poll_id', pollId);

      const existingOptionIds = (existingOptions || []).map(opt => opt.id);
      const incomingOptionIds = options.map((opt: any) => opt.id).filter((id: any) => id);
      
      // Delete options that are not in the incoming list or marked deleted
      const optionsToDelete = existingOptionIds.filter(id => !incomingOptionIds.includes(id));
      for (const opt of options) {
          if(opt.id && opt.deleted) {
              optionsToDelete.push(opt.id);
          }
      }
      
      if(optionsToDelete.length > 0) {
          await supabase
            .from('poll_options')
            .delete()
            .in('id', optionsToDelete)
            .eq('poll_id', pollId);
      }

      // Update or Insert options
      let displayOrder = 0;
      for (const option of options) {
          if (option.deleted) continue; // Skip deleted

          if (option.id && !optionsToDelete.includes(option.id)) {
              // Update existing option
              await supabase
                .from('poll_options')
                .update({
                  option_text: option.text,
                  display_order: displayOrder
                })
                .eq('id', option.id)
                .eq('poll_id', pollId);
          } else if (!option.id) {
               // Insert new option
              await supabase
                .from('poll_options')
                .insert({
                  poll_id: pollId,
                  option_text: option.text,
                  display_order: displayOrder
                });
          }
          displayOrder++;
      }

      // Handle audience: Delete all existing links and re-insert based on current data
      await supabase.from('poll_houses').delete().eq('poll_id', pollId);
      await supabase.from('poll_flats').delete().eq('poll_id', pollId);
      await supabase.from('poll_users').delete().eq('poll_id', pollId);

      if (audienceType === 'streets' && streetIds && streetIds.length > 0) {
        // Find all houses in these streets
        const { data: houses } = await supabase
          .from('houses')
          .select('id')
          .in('street_id', streetIds.map(id => parseInt(id)));
        
        if (houses && houses.length > 0) {
          const houseLinks = houses.map(house => ({
            poll_id: pollId,
            house_id: house.id
          }));
          
          await supabase
            .from('poll_houses')
            .insert(houseLinks);
        }
      } else if (audienceType === 'houses' && houseIds && houseIds.length > 0) {
        const houseLinks = houseIds.map((houseId: string) => ({
          poll_id: pollId,
          house_id: parseInt(houseId)
        }));
        
        await supabase
          .from('poll_houses')
          .insert(houseLinks);
      } else if (audienceType === 'flats' && flatIds && flatIds.length > 0) {
        const flatLinks = flatIds.map((flatId: string) => ({
          poll_id: pollId,
          flat_id: parseInt(flatId)
        }));
        
        await supabase
          .from('poll_flats')
          .insert(flatLinks);
      } else if (audienceType === 'users' && userIds && userIds.length > 0) {
        const userLinks = userIds.map((userId: string) => ({
          poll_id: pollId,
          user_id: parseInt(userId)
        }));
        
        await supabase
          .from('poll_users')
          .insert(userLinks);
      }

      // Record audit log
      await addAuditLog({
          userId: parseInt(user.id),
          action: 'poll_updated',
          entityType: 'poll',
          entityId: pollId,
          changes: { title, status, audienceType }
      });

      return NextResponse.json({ message: "Apklausa sėkmingai atnaujinta" });

    } catch (error) {
      console.error('Error updating poll:', error);
      return NextResponse.json({ error: "Klaida atnaujinant apklausą" }, { status: 500 });
    }
  } catch (error) {
    console.error('Error in PATCH /api/polls:', error);
    return NextResponse.json({ error: "Vidinė serverio klaida" }, { status: 500 });
  }
}


// DELETE: Delete a poll
export async function DELETE(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    if (user.role !== "developer" && user.role !== "super_admin" && user.role !== "editor") {
      return NextResponse.json(
        { error: "Only admins and editors can delete polls" },
        { status: 403 }
      );
    }
    
    const { searchParams } = new URL(req.url);
    const pollId = searchParams.get("id");
    
    if (!pollId) {
      return NextResponse.json(
        { error: "Poll ID is required" },
        { status: 400 }
      );
    }
    
    const pollIdInt = parseInt(pollId);
    const supabase = await createServiceClient();
    
    // Check if poll exists and get title for audit log
    const { data: existingPoll, error: fetchError } = await supabase
      .from('polls')
      .select('id, title')
      .eq('id', pollIdInt)
      .single();
    
    if (fetchError || !existingPoll) {
      return NextResponse.json(
        { error: "Poll not found" },
        { status: 404 }
      );
    }
    
    const pollTitle = existingPoll.title;

    // Delete the poll (cascade will handle related records in options/responses/links)
    const { error: deleteError } = await supabase
      .from('polls')
      .delete()
      .eq('id', pollIdInt);
    
    if (deleteError) {
      console.error("Error deleting poll:", deleteError);
      return NextResponse.json(
        { error: "Failed to delete poll" },
        { status: 500 }
      );
    }
    
    // Add audit log
    await addAuditLog({
      userId: parseInt(user.id),
      action: 'poll_deleted',
      entityType: 'poll',
      entityId: pollIdInt,
      changes: { title: pollTitle }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting poll:", error);
    return NextResponse.json(
      { error: "Failed to delete poll" },
      { status: 500 }
    );
  }
}