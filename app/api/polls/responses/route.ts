import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";
import { addAuditLog } from "@/lib/utils";

// POST: Submit a response to a poll
export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if user is a regular user (not an admin)
    if (user.role !== "user") {
      return NextResponse.json(
        { error: "Only regular users can vote in polls" },
        { status: 403 }
      );
    }
    
    const { pollId, optionId, explanation } = await req.json();
    
    if (!pollId) {
      return NextResponse.json(
        { error: "Poll ID is required" },
        { status: 400 }
      );
    }
    
    if (!optionId) {
      return NextResponse.json(
        { error: "Option ID is required" },
        { status: 400 }
      );
    }
    
    // Check if poll exists and is active
    const pollQuery = await db.query(
      `
      SELECT id, status, start_date, end_date
      FROM polls
      WHERE id = $1
      `,
      [pollId]
    );
    
    if (pollQuery.rows.length === 0) {
      return NextResponse.json(
        { error: "Poll not found" },
        { status: 404 }
      );
    }
    
    const poll = pollQuery.rows[0];
    
    if (poll.status !== 'active') {
      return NextResponse.json(
        { error: "Poll is not active" },
        { status: 400 }
      );
    }
    
    // Check if poll has end_date and if it has passed
    if (poll.end_date && new Date(poll.end_date) < new Date()) {
      return NextResponse.json(
        { error: "Poll has ended" },
        { status: 400 }
      );
    }
    
    // Check if option exists for this poll
    const optionQuery = await db.query(
      `
      SELECT id
      FROM poll_options
      WHERE id = $1 AND poll_id = $2
      `,
      [optionId, pollId]
    );
    
    if (optionQuery.rows.length === 0) {
      return NextResponse.json(
        { error: "Invalid option" },
        { status: 400 }
      );
    }
    
    // Check if user is eligible to vote on this poll
    const userId = parseInt(user.id);
    
    // Get user's flat and house IDs
    const userResult = await db.query(
      `SELECT flat_id FROM users WHERE id = $1`,
      [userId]
    );
    
    let userFlatId = null;
    let userHouseId = null;
    
    if (userResult.rows.length > 0 && userResult.rows[0].flat_id) {
      userFlatId = userResult.rows[0].flat_id;
      
      // Get the house_id for this flat
      const flatResult = await db.query(
        `SELECT house_id FROM flats WHERE id = $1`,
        [userFlatId]
      );
      
      if (flatResult.rows.length > 0) {
        userHouseId = flatResult.rows[0].house_id;
      }
    }
    
    // Check poll audience
    const audienceQuery = await db.query(
      `
      SELECT audience_type, reference_id
      FROM poll_audience
      WHERE poll_id = $1
      `,
      [pollId]
    );
    
    let isEligible = false;
    
    // If no audience records, assume it's for everyone
    if (audienceQuery.rows.length === 0) {
      isEligible = true;
    } else {
      // Get user's street ID if they have a house
      let userStreetId = null;
      if (userHouseId) {
        const streetResult = await db.query(
          `SELECT street_id FROM houses WHERE id = $1`,
          [userHouseId]
        );
        
        if (streetResult.rows.length > 0 && streetResult.rows[0].street_id) {
          userStreetId = streetResult.rows[0].street_id;
        }
      }
      
      for (const audience of audienceQuery.rows) {
        if (
          audience.audience_type === 'all' ||
          (audience.audience_type === 'users' && audience.reference_id === userId.toString()) ||
          (audience.audience_type === 'flats' && userFlatId && audience.reference_id === userFlatId.toString()) ||
          (audience.audience_type === 'houses' && userHouseId && audience.reference_id === userHouseId.toString()) ||
          (audience.audience_type === 'streets' && userStreetId && audience.reference_id === userStreetId.toString())
        ) {
          isEligible = true;
          break;
        }
      }
    }
    
    if (!isEligible) {
      return NextResponse.json(
        { error: "You are not eligible to vote on this poll" },
        { status: 403 }
      );
    }
    
    // Check if user has already voted
    const responseCheck = await db.query(
      `
      SELECT id
      FROM poll_responses
      WHERE poll_id = $1 AND user_id = $2
      `,
      [pollId, userId]
    );
    
    if (responseCheck.rows.length > 0) {
      return NextResponse.json(
        { error: "You have already voted on this poll" },
        { status: 400 }
      );
    }
    
    // Submit the response
    const responseResult = await db.query(
      `
      INSERT INTO poll_responses (poll_id, option_id, user_id, explanation, created_at)
      VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
      RETURNING id
      `,
      [pollId, optionId, userId, explanation || null]
    );
    
    const responseId = responseResult.rows[0].id;
    
    // Add audit log
    await addAuditLog({
      userId: parseInt(user.id),
      action: 'poll_response_submitted',
      entityType: 'poll_response',
      entityId: responseId,
      changes: { pollId }
    });
    
    // Get the updated poll with options and user's response
    const updatedPoll = await getPollWithUserResponse(pollId, userId);
    
    return NextResponse.json(updatedPoll);
  } catch (error) {
    console.error("Error submitting poll response:", error);
    return NextResponse.json(
      { error: "Failed to submit poll response" },
      { status: 500 }
    );
  }
}

// Helper function to get a poll with the user's response
async function getPollWithUserResponse(pollId: number, userId: number) {
  // Get the poll
  const pollResult = await db.query(
    `
    SELECT 
      p.id, 
      p.title, 
      p.description, 
      p.status,
      p.start_date,
      p.end_date,
      p.created_at,
      u.name as created_by_name
    FROM polls p
    JOIN users u ON p.created_by = u.id
    WHERE p.id = $1
    `,
    [pollId]
  );
  
  if (pollResult.rows.length === 0) {
    return null;
  }
  
  const poll = pollResult.rows[0];
  
  // Get the options
  const optionsResult = await db.query(
    `
    SELECT id, option_text, display_order
    FROM poll_options
    WHERE poll_id = $1
    ORDER BY display_order ASC
    `,
    [pollId]
  );
  
  // Get the user's response
  const responseResult = await db.query(
    `
    SELECT id, option_id, explanation, created_at
    FROM poll_responses
    WHERE poll_id = $1 AND user_id = $2
    LIMIT 1
    `,
    [pollId, userId]
  );
  
  const userResponse = responseResult.rows.length > 0 ? responseResult.rows[0] : null;
  
  // If poll is closed or the user has voted, show results
  let results = null;
  if (poll.status === 'closed' || userResponse) {
    const resultsQuery = await db.query(
      `
      SELECT 
        po.id as option_id, 
        po.option_text,
        COUNT(pr.id) as vote_count
      FROM poll_options po
      LEFT JOIN poll_responses pr ON po.id = pr.option_id
      WHERE po.poll_id = $1
      GROUP BY po.id, po.option_text
      ORDER BY po.display_order
      `,
      [pollId]
    );
    
    // Get total votes
    const totalVotesQuery = await db.query(
      `
      SELECT COUNT(DISTINCT user_id) as total_votes
      FROM poll_responses
      WHERE poll_id = $1
      `,
      [pollId]
    );
    
    const totalVotes = parseInt(totalVotesQuery.rows[0]?.total_votes || '0');
    
    results = {
      options: resultsQuery.rows.map(option => ({
        ...option,
        percentage: totalVotes > 0 ? Math.round((option.vote_count / totalVotes) * 100) : 0
      })),
      totalVotes
    };
  }
  
  return {
    ...poll,
    options: optionsResult.rows,
    userResponse,
    results
  };
}

// PATCH: Update a response (only if poll allows updates)
export async function PATCH(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const { responseId, optionId, explanation } = await req.json();
    
    if (!responseId) {
      return NextResponse.json(
        { error: "Response ID is required" },
        { status: 400 }
      );
    }
    
    if (!optionId) {
      return NextResponse.json(
        { error: "Option ID is required" },
        { status: 400 }
      );
    }
    
    const userId = parseInt(user.id);
    
    // Check if the response exists and belongs to the user
    const responseResult = await db.query(
      `
      SELECT r.id, r.poll_id, p.status, p.end_date
      FROM poll_responses r
      JOIN polls p ON r.poll_id = p.id
      WHERE r.id = $1 AND r.user_id = $2
      `,
      [responseId, userId]
    );
    
    if (responseResult.rows.length === 0) {
      return NextResponse.json(
        { error: "Response not found or does not belong to you" },
        { status: 404 }
      );
    }
    
    const response = responseResult.rows[0];
    
    // Check if poll is still active
    if (response.status !== 'active') {
      return NextResponse.json(
        { error: "Poll is no longer active" },
        { status: 400 }
      );
    }
    
    // Check if poll has end_date and if it has passed
    if (response.end_date && new Date(response.end_date) < new Date()) {
      return NextResponse.json(
        { error: "Poll has ended" },
        { status: 400 }
      );
    }
    
    // Check if option exists for this poll
    const optionQuery = await db.query(
      `
      SELECT id
      FROM poll_options
      WHERE id = $1 AND poll_id = $2
      `,
      [optionId, response.poll_id]
    );
    
    if (optionQuery.rows.length === 0) {
      return NextResponse.json(
        { error: "Invalid option" },
        { status: 400 }
      );
    }
    
    // Update the response
    await db.query(
      `
      UPDATE poll_responses
      SET option_id = $1, explanation = $2
      WHERE id = $3
      `,
      [optionId, explanation || null, responseId]
    );
    
    // Add audit log
    await addAuditLog({
      userId: parseInt(user.id),
      action: 'poll_response_updated',
      entityType: 'poll_response',
      entityId: responseId,
      changes: { pollId: response.poll_id }
    });
    
    // Get the updated poll with options and user's response
    const updatedPoll = await getPollWithUserResponse(response.poll_id, userId);
    
    return NextResponse.json(updatedPoll);
  } catch (error) {
    console.error("Error updating poll response:", error);
    return NextResponse.json(
      { error: "Failed to update poll response" },
      { status: 500 }
    );
  }
}

// GET: Fetch paginated poll responses with voter details
export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Check authentication
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if user is admin
    const isAdmin = ["super_admin", "editor"].includes(user.role);
    if (!isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }
    
    const { searchParams } = new URL(req.url);
    const pollId = searchParams.get("pollId");
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    
    if (!pollId) {
      return NextResponse.json({ error: "Poll ID is required" }, { status: 400 });
    }
    
    // Validate page and pageSize
    if (isNaN(page) || page < 1) {
      return NextResponse.json({ error: "Invalid page parameter" }, { status: 400 });
    }
    
    if (isNaN(pageSize) || pageSize < 1 || pageSize > 100) {
      return NextResponse.json({ error: "Invalid pageSize parameter" }, { status: 400 });
    }
    
    // Calculate offset
    const offset = (page - 1) * pageSize;
    
    // Get total count of responses for pagination
    const countResult = await db.query(
      `SELECT COUNT(*) as total FROM poll_responses WHERE poll_id = $1`,
      [pollId]
    );
    
    const total = parseInt(countResult.rows[0].total);
    
    // Fetch the poll responses with user and option details
    const query = `
      SELECT 
        pr.id,
        pr.option_id,
        pr.explanation,
        pr.created_at as "createdAt",
        u.id as user_id,
        u.name as "userName",
        po.option_text as "optionText",
        h.address as "houseInfo",
        f.number as "flatInfo"
      FROM poll_responses pr
      JOIN users u ON pr.user_id = u.id
      JOIN poll_options po ON pr.option_id = po.id
      LEFT JOIN flats f ON u.flat_id = f.id
      LEFT JOIN houses h ON f.house_id = h.id
      WHERE pr.poll_id = $1
      ORDER BY pr.created_at DESC
      LIMIT $2 OFFSET $3
    `;
    
    const result = await db.query(query, [pollId, pageSize, offset]);
    
    // Format the responses - assume all users are not anonymous as we don't have that column
    const responses = result.rows.map(row => {
      return {
        id: row.id,
        optionId: row.option_id,
        optionText: row.optionText,
        explanation: row.explanation,
        createdAt: row.createdAt,
        isAnonymous: false, // Since we don't have this column, assume all are not anonymous
        userName: row.userName,
        houseInfo: row.houseInfo,
        flatInfo: row.flatInfo
      };
    });
    
    return NextResponse.json({
      responses,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    });
    
  } catch (error) {
    console.error("Error fetching poll responses:", error);
    return NextResponse.json(
      { error: "Failed to fetch poll responses" },
      { status: 500 }
    );
  }
} 