import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";

export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if user is admin
    const isAdmin = ["super_admin", "editor"].includes(user.role);
    if (!isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }
    
    const { searchParams } = new URL(req.url);
    const pollId = searchParams.get("id");
    
    // Get all users with role="user" (who are eligible to vote)
    const eligibleUsersResult = await db.query(
      `SELECT COUNT(*) as total FROM users WHERE role = 'user'`
    );
    const totalEligibleUsers = parseInt(eligibleUsersResult.rows[0].total);
    
    let votedUsers = 0;
    let notVotedUsers = 0;
    
    if (pollId) {
      // Get voting stats for a specific poll
      
      // First check if poll exists
      const pollCheckResult = await db.query(
        `SELECT id, status FROM polls WHERE id = $1`,
        [pollId]
      );
      
      if (pollCheckResult.rows.length === 0) {
        return NextResponse.json(
          { error: "Poll not found" },
          { status: 404 }
        );
      }
      
      // Get users who voted on this poll
      const votedUsersResult = await db.query(
        `SELECT COUNT(DISTINCT user_id) as voted 
         FROM poll_responses 
         WHERE poll_id = $1`,
        [pollId]
      );
      
      votedUsers = parseInt(votedUsersResult.rows[0].voted);
      notVotedUsers = totalEligibleUsers - votedUsers;
      
      // Get poll audience information to check eligibility
      const audienceQuery = await db.query(
        `SELECT audience_type, reference_id
         FROM poll_audience
         WHERE poll_id = $1`,
        [pollId]
      );
      
      // If poll has specific audience targeting, we need more complex calculations
      if (audienceQuery.rows.length > 0) {
        // This is a targeted poll, so we need to determine exactly which users are eligible
        
        // First, check if it's targeted to specific users
        const userTargeting = audienceQuery.rows.find(a => a.audience_type === 'users');
        if (userTargeting) {
          // Poll is targeted to specific users
          const targetedUserIds = userTargeting.reference_id.split(',').map(id => parseInt(id.trim()));
          
          const eligibleResult = await db.query(
            `SELECT COUNT(*) as eligible 
             FROM users 
             WHERE role = 'user' AND id = ANY($1::int[])`,
            [targetedUserIds]
          );
          
          const targetedEligibleUsers = parseInt(eligibleResult.rows[0].eligible);
          
          const votedTargetedResult = await db.query(
            `SELECT COUNT(DISTINCT user_id) as voted 
             FROM poll_responses 
             WHERE poll_id = $1 AND user_id = ANY($2::int[])`,
            [pollId, targetedUserIds]
          );
          
          votedUsers = parseInt(votedTargetedResult.rows[0].voted);
          notVotedUsers = targetedEligibleUsers - votedUsers;
          
          return NextResponse.json({
            totalEligibleUsers: targetedEligibleUsers,
            votedUsers,
            notVotedUsers,
            votingPercentage: targetedEligibleUsers > 0 ? Math.round((votedUsers / targetedEligibleUsers) * 100) : 0
          });
        }
        
        // For simplicity in this implementation, if the poll uses more complex targeting 
        // (flats, houses, streets), we'll return approximate statistics
        // In a real implementation, you would add more complex logic to determine exactly
        // which users are eligible based on those criteria
      }
    } else {
      // Get overall statistics for all active polls
      const votedUsersResult = await db.query(
        `SELECT COUNT(DISTINCT user_id) as voted 
         FROM poll_responses 
         JOIN polls ON poll_responses.poll_id = polls.id
         WHERE polls.status = 'active'`
      );
      
      votedUsers = parseInt(votedUsersResult.rows[0].voted);
      notVotedUsers = totalEligibleUsers - votedUsers;
    }
    
    return NextResponse.json({
      totalEligibleUsers,
      votedUsers,
      notVotedUsers,
      votingPercentage: totalEligibleUsers > 0 ? Math.round((votedUsers / totalEligibleUsers) * 100) : 0
    });
    
  } catch (error) {
    console.error("Error fetching poll statistics:", error);
    return NextResponse.json(
      { error: "Failed to fetch poll statistics" },
      { status: 500 }
    );
  }
} 