import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";
import { addAuditLog } from "@/lib/utils";

// PATCH: Close an existing poll
export async function PATCH(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Check authentication
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check authorization (only admins can close polls)
    const isAdmin = ["super_admin", "editor"].includes(user.role);
    if (!isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }
    
    const { searchParams } = new URL(req.url);
    const pollId = searchParams.get("id");
    
    if (!pollId) {
      return NextResponse.json({ error: "Poll ID is required" }, { status: 400 });
    }
    
    // Get the existing poll
    const pollCheck = await db.query(
      `SELECT * FROM polls WHERE id = $1`,
      [pollId]
    );
    
    if (pollCheck.rows.length === 0) {
      return NextResponse.json({ error: "Poll not found" }, { status: 404 });
    }
    
    const existingPoll = pollCheck.rows[0];
    
    // Check if poll is already closed
    if (existingPoll.status === 'closed') {
      return NextResponse.json({ error: "Poll is already closed" }, { status: 400 });
    }
    
    // Get end date from request body or use current date
    const { endDate } = await req.json();
    const finalEndDate = endDate || new Date().toISOString();
    
    // Update poll status to closed
    await db.query(
      `UPDATE polls SET status = 'closed', end_date = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2`,
      [finalEndDate, pollId]
    );
    
    // Add audit log
    await addAuditLog({
      userId: parseInt(user.id),
      action: 'poll_closed',
      entityType: 'poll',
      entityId: parseInt(pollId),
      changes: {
        status: 'closed',
        end_date: finalEndDate
      }
    });
    
    return NextResponse.json({ success: true, message: "Poll closed successfully" });
  } catch (error) {
    console.error("Error closing poll:", error);
    return NextResponse.json(
      { error: "Failed to close poll" },
      { status: 500 }
    );
  }
} 