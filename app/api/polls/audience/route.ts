import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { db } from "@/lib/db/supabase-adapter";

// GET: Fetch poll audience data
export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!session?.user) {
      return NextResponse.json({ error: "Naudotojas neprisijungęs" }, { status: 401 });
    }

    // Get the poll ID from the query parameters
    const { searchParams } = new URL(req.url);
    const pollId = searchParams.get("pollId");

    if (!pollId) {
      return NextResponse.json({ error: "Nenurodytas apklausos ID" }, { status: 400 });
    }

    // Check if the user has access to this poll
    const userRole = user.role;
    const userId = user.id;

    // If not an admin, check if the poll is targeted to this user
    if (userRole !== "super_admin" && userRole !== "editor") {
      const accessCheck = await db.query(
        `SELECT COUNT(*) FROM polls p
         LEFT JOIN poll_audience pa ON p.id = pa.poll_id
         WHERE p.id = $1 AND (
           p.status = 'active' AND (
             pa.audience_type = 'all' OR
             (pa.audience_type = 'users' AND pa.reference_id = $2) OR
             (pa.audience_type = 'streets' AND pa.reference_id IN (
               SELECT street_id FROM user_addresses WHERE user_id = $2
             )) OR
             (pa.audience_type = 'houses' AND pa.reference_id IN (
               SELECT house_id FROM user_addresses WHERE user_id = $2
             )) OR
             (pa.audience_type = 'flats' AND pa.reference_id IN (
               SELECT flat_id FROM user_addresses WHERE user_id = $2
             ))
           )
         )`,
        [pollId, userId]
      );

      if (parseInt(accessCheck.rows[0].count) === 0) {
        return NextResponse.json({ error: "Neturite teisių peržiūrėti šią apklausą" }, { status: 403 });
      }
    }

    // Get the audience type for this poll
    const audienceTypeResult = await db.query(
      `SELECT DISTINCT audience_type FROM poll_audience WHERE poll_id = $1`,
      [pollId]
    );

    let audienceType = "all";
    if (audienceTypeResult.rows.length > 0) {
      audienceType = audienceTypeResult.rows[0].audience_type;
    }

    // Get the audience items
    const audienceItemsResult = await db.query(
      `SELECT * FROM poll_audience WHERE poll_id = $1`,
      [pollId]
    );

    return NextResponse.json({
      type: audienceType,
      items: audienceItemsResult.rows
    });
  } catch (error) {
    console.error("Error fetching poll audience:", error);
    return NextResponse.json(
      { error: "Nepavyko gauti apklausos auditorijos duomenų" },
      { status: 500 }
    );
  }
} 