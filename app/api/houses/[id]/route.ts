import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";

// GET /api/houses/[id] - Get a single house
export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const params = await context.params;
    const id = params.id;
    
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const supabase = await createServiceClient();
    
    const { data: house, error } = await supabase
      .from('houses')
      .select(`
        *,
        streets (
          name,
          city
        )
      `)
      .eq('id', id)
      .single();
    
    if (error || !house) {
      return NextResponse.json({ error: "House not found" }, { status: 404 });
    }
    
    // Flatten the response to match expected format
    const response = {
      ...house,
      street_name: house.streets?.name || null,
      street_city: house.streets?.city || null
    };
    delete response.streets;
    
    return NextResponse.json(response);
  } catch (error) {
    console.error("[HOUSE_GET]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}

// Next.js 15 requires route handlers to define their params as the first parameter
// and await them before use
export async function DELETE(
  request: NextRequest,
  // Type definition based on Next.js 15 requirements
  context: { params: { id: string } }
) {
  try {
    // Updated:
    const params = await context.params;
    const id = params.id;

    const user = await getCurrentUser();
    
    // Only admin users can delete houses
    if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const supabase = await createServiceClient();
    
    // First check if house is linked to flats
    const { count, error: countError } = await supabase
      .from('flats')
      .select('*', { count: 'exact', head: true })
      .eq('house_id', id);
    
    if (countError) {
      console.error("[HOUSE_DELETE] Count error:", countError);
      return NextResponse.json({ error: "Failed to check linked flats" }, { status: 500 });
    }
    
    const linkedFlatsCount = count || 0;
    
    if (linkedFlatsCount > 0) {
      return NextResponse.json(
        { error: `Cannot delete house: it is linked to ${linkedFlatsCount} flats` },
        { status: 400 }
      );
    }
    
    // If no linked flats, delete the house
    const { error: deleteError } = await supabase
      .from('houses')
      .delete()
      .eq('id', id);
    
    if (deleteError) {
      console.error("[HOUSE_DELETE] Delete error:", deleteError);
      return NextResponse.json({ error: "Failed to delete house" }, { status: 500 });
    }
    
    return NextResponse.json({ message: "House deleted successfully" });
  } catch (error) {
    console.error("[HOUSE_DELETE]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  context: { params: { id: string } }
) {
  // Get and await params first (Next.js 15 requirement)
  try {
    const params = await context.params;
    const id = params.id;
  
    const user = await getCurrentUser();
    
    // Only admin users can update houses
    if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Parse request body
    const body = await request.json();
    const { number, streetId } = body;
    
    // Validate required fields
    if (!number) {
      return NextResponse.json(
        { error: "House number is required" }, 
        { status: 400 }
      );
    }
    
    const supabase = await createServiceClient();
    
    // Check if house exists
    const { data: existingHouse } = await supabase
      .from('houses')
      .select('id')
      .eq('id', id)
      .single();
    
    if (!existingHouse) {
      return NextResponse.json({ error: "House not found" }, { status: 404 });
    }
    
    // Get street info if streetId is provided
    let address = number;
    if (streetId && streetId !== "none") {
      const { data: street } = await supabase
        .from('streets')
        .select('name, city')
        .eq('id', streetId)
        .single();
      
      if (street) {
        address = `${street.name} ${number}, ${street.city}`;
      }
    }
    
    // Update house in database
    const { data: updatedHouse, error: updateError } = await supabase
      .from('houses')
      .update({ 
        name: number,
        address,
        street_id: (streetId && streetId !== "none") ? parseInt(streetId) : null,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();
    
    if (updateError) {
      console.error("[HOUSE_UPDATE] Update error:", updateError);
      return NextResponse.json({ error: "Failed to update house" }, { status: 500 });
    }
    
    return NextResponse.json({ 
      message: "House updated successfully",
      house: updatedHouse
    });
  } catch (error) {
    console.error("[HOUSE_UPDATE]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
} 