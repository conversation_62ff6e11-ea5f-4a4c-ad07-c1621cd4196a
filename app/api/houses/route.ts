import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";

export async function GET(request: NextRequest) {
  console.log("[HOUSES_GET] Starting request");
  try {
    const user = await getCurrentUser();
    
    // Check if user is authorized
    if (!user) {
      console.log("[HOUSES_GET] No user found, returning 401");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Get query parameters if available
    const url = new URL(request.url);
    const streetId = url.searchParams.get("streetId");
    
    try {
      const supabase = await createServiceClient();
      
      // Build query with optional street filter
      let query = supabase
        .from('houses')
        .select(`
          id, 
          name, 
          address, 
          street_id,
          created_at,
          updated_at,
          streets (
            id,
            name
          )
        `)
        .order('name');
      
      // Filter by street if provided
      if (streetId) {
        query = query.eq('street_id', streetId);
      }
      
      const { data: houses, error } = await query;
      
      if (error) {
        console.error("[HOUSES_GET] DB error:", error);
        // Return empty array instead of error for better UX
        return NextResponse.json([]);
      }
      
      console.log("[HOUSES_GET] Found houses:", houses?.length);
      
      // Transform data to match expected format
      const transformedHouses = houses?.map(house => {
        const street = house.streets as any;
        
        return {
          id: house.id.toString(),
          name: house.name,
          address: house.address,
          streetId: house.street_id?.toString() || null,
          streetName: street?.name || null,
          city: street?.city || null,
          displayName: street?.name 
            ? `${street.name} ${house.name}`
            : house.name,
          createdAt: house.created_at,
          updatedAt: house.updated_at
        };
      }) || [];
      
      // Sort by street name first, then house name
      transformedHouses.sort((a, b) => {
        if (a.streetName && b.streetName) {
          const streetCompare = a.streetName.localeCompare(b.streetName);
          if (streetCompare !== 0) return streetCompare;
        }
        return a.name.localeCompare(b.name);
      });
      
      return NextResponse.json(transformedHouses);
    } catch (dbError) {
      console.error("[HOUSES_GET] DB error:", dbError);
      // Return empty array instead of error
      return NextResponse.json([]);
    }
  } catch (error) {
    console.error("[HOUSES_GET]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    // Only admin users can create houses
    if (!user || !["developer", "super_admin", "editor"].includes(user.role as string)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Parse request body
    const body = await request.json();
    
    // Validate required fields
    if (!body.number || typeof body.number !== "string" || body.number.trim() === "") {
      return NextResponse.json(
        { error: "House number is required" }, 
        { status: 400 }
      );
    }
    
    const supabase = await createServiceClient();
    
    // Validate streetId if provided
    let streetId = null;
    let address = body.number.trim(); // Default address is just the number
    let streetData = null;
    
    if (body.streetId && body.streetId !== "none") {
      if (typeof body.streetId !== "string" && typeof body.streetId !== "number") {
        return NextResponse.json(
          { error: "Invalid street ID format" },
          { status: 400 }
        );
      }
      
      // Check if street exists
      const { data: street, error } = await supabase
        .from('streets')
        .select('id, name, city')
        .eq('id', body.streetId)
        .single();
      
      if (error || !street) {
        return NextResponse.json(
          { error: "Street not found" },
          { status: 404 }
        );
      }
      
      streetId = body.streetId;
      streetData = street;
      
      // Create full address with street info
      address = `${street.name} ${body.number.trim()}, ${street.city}`;
    }
    
    // Insert new house
    const { data: newHouse, error: insertError } = await supabase
      .from('houses')
      .insert({
        name: body.number.trim(),
        address: address,
        street_id: streetId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (insertError) {
      console.error("[HOUSES_POST] Insert error:", insertError);
      return NextResponse.json(
        { error: "Failed to create house" }, 
        { status: 500 }
      );
    }
    
    // Format the response
    const responseData = {
      id: newHouse.id.toString(),
      name: newHouse.name,
      address: newHouse.address,
      streetId: newHouse.street_id?.toString() || null,
      streetName: streetData?.name || null,
      city: streetData?.city || null,
      displayName: streetData?.name 
        ? `${streetData.name} ${newHouse.name}`
        : newHouse.name,
      createdAt: newHouse.created_at,
      updatedAt: newHouse.updated_at
    };
    
    return NextResponse.json(responseData, { status: 201 });
  } catch (error) {
    console.error("[HOUSES_POST]", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
} 