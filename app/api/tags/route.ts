import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/supabase/auth";
import { createServiceClient } from "@/lib/supabase/server";
import { addAuditLog } from "@/lib/utils";

// GET /api/tags - Get all tags
export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Use Supabase client to query tags
    const supabase = await createServiceClient();
    const { data, error } = await supabase
      .from('tags')
      .select('id, name, color, category, created_at, updated_at')
      .order('name', { ascending: true });
    
    if (error) {
      console.error("Error fetching tags from Supabase:", error);
      throw error;
    }
    
    return NextResponse.json(data || []);
  } catch (error) {
    console.error("Error fetching tags:", error);
    return NextResponse.json(
      { error: "Failed to fetch tags" },
      { status: 500 }
    );
  }
}

// POST /api/tags - Create a new tag
export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Only admins or editors can create tags
    if (user.role !== "developer" && user.role !== "super_admin" && user.role !== "editor") {
      return NextResponse.json(
        { error: "Only admins and editors can create tags" },
        { status: 403 }
      );
    }
    
    const { name, color = 'gray', category = 'general' } = await req.json();
    
    if (!name) {
      return NextResponse.json(
        { error: "Tag name is required" },
        { status: 400 }
      );
    }
    
    const supabase = await createServiceClient();
    
    // Check if tag with this name already exists
    const { data: existingTag, error: checkError } = await supabase
      .from('tags')
      .select('id')
      .eq('name', name)
      .single();
    
    if (existingTag) {
      return NextResponse.json(
        { error: "A tag with this name already exists" },
        { status: 409 }
      );
    }
    
    // Insert the new tag
    const { data: tag, error: insertError } = await supabase
      .from('tags')
      .insert({
        name,
        color,
        category,
        created_at: new Date().toISOString()
      })
      .select('id, name, color, category, created_at')
      .single();
    
    if (insertError) {
      console.error("Error inserting tag:", insertError);
      throw insertError;
    }
    
    // Add audit log
    await addAuditLog({
      action: 'create_tag',
      userId: parseInt(user.id),
      entityType: 'tag',
      entityId: parseInt(tag.id),
      changes: { name, color, category }
    });
    
    return NextResponse.json(tag, { status: 201 });
  } catch (error) {
    console.error("Error creating tag:", error);
    return NextResponse.json(
      { error: "Failed to create tag" },
      { status: 500 }
    );
  }
}

// DELETE /api/tags?id=123 - Delete a tag
export async function DELETE(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Only admins or editors can delete tags
    if (user.role !== "developer" && user.role !== "super_admin" && user.role !== "editor") {
      return NextResponse.json(
        { error: "Only admins and editors can delete tags" },
        { status: 403 }
      );
    }
    
    const { searchParams } = new URL(req.url);
    const tagId = searchParams.get("id");
    
    if (!tagId) {
      return NextResponse.json(
        { error: "Tag ID is required" },
        { status: 400 }
      );
    }
    
    const supabase = await createServiceClient();
    
    // Check if tag exists
    const { data: existingTag, error: checkError } = await supabase
      .from('tags')
      .select('id, name')
      .eq('id', tagId)
      .single();
    
    if (!existingTag || checkError) {
      return NextResponse.json(
        { error: "Tag not found" },
        { status: 404 }
      );
    }
    
    // Delete the tag
    const { error: deleteError } = await supabase
      .from('tags')
      .delete()
      .eq('id', tagId);
    
    if (deleteError) {
      console.error("Error deleting tag:", deleteError);
      throw deleteError;
    }
    
    // Add audit log
    await addAuditLog({
      action: 'delete_tag',
      userId: parseInt(user.id),
      entityType: 'tag',
      entityId: parseInt(tagId),
      changes: { name: existingTag.name }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting tag:", error);
    return NextResponse.json(
      { error: "Failed to delete tag" },
      { status: 500 }
    );
  }
}

// PATCH /api/tags?id=123 - Update a tag
export async function PATCH(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Only admins or editors can update tags
    if (user.role !== "developer" && user.role !== "super_admin" && user.role !== "editor") {
      return NextResponse.json(
        { error: "Only admins and editors can update tags" },
        { status: 403 }
      );
    }
    
    const { searchParams } = new URL(req.url);
    const tagId = searchParams.get("id");
    
    if (!tagId) {
      return NextResponse.json(
        { error: "Tag ID is required" },
        { status: 400 }
      );
    }
    
    const { name, color, category } = await req.json();
    
    if (!name && !color && !category) {
      return NextResponse.json(
        { error: "At least one field to update is required" },
        { status: 400 }
      );
    }
    
    const supabase = await createServiceClient();
    
    // Check if tag exists
    const { data: existingTag, error: checkError } = await supabase
      .from('tags')
      .select('id, name, color, category')
      .eq('id', tagId)
      .single();
    
    if (!existingTag || checkError) {
      return NextResponse.json(
        { error: "Tag not found" },
        { status: 404 }
      );
    }
    
    // If we're updating the name, make sure it's not a duplicate
    if (name && name !== existingTag.name) {
      const { data: duplicateCheck } = await supabase
        .from('tags')
        .select('id')
        .eq('name', name)
        .neq('id', tagId)
        .single();
      
      if (duplicateCheck) {
        return NextResponse.json(
          { error: "A tag with this name already exists" },
          { status: 409 }
        );
      }
    }
    
    // Build the update object
    const updateData: any = {
      updated_at: new Date().toISOString()
    };
    
    if (name) updateData.name = name;
    if (color) updateData.color = color;
    if (category) updateData.category = category;
    
    // Update the tag
    const { data: updatedTag, error: updateError } = await supabase
      .from('tags')
      .update(updateData)
      .eq('id', tagId)
      .select('id, name, color, category, created_at, updated_at')
      .single();
    
    if (updateError) {
      console.error("Error updating tag:", updateError);
      throw updateError;
    }
    
    // Add audit log
    await addAuditLog({
      action: 'update_tag',
      userId: parseInt(user.id),
      entityType: 'tag',
      entityId: parseInt(tagId),
      changes: {
        old: {
          name: existingTag.name,
          color: existingTag.color,
          category: existingTag.category
        },
        new: {
          name: updatedTag.name,
          color: updatedTag.color,
          category: updatedTag.category
        }
      }
    });
    
    return NextResponse.json(updatedTag);
  } catch (error) {
    console.error("Error updating tag:", error);
    return NextResponse.json(
      { error: "Failed to update tag" },
      { status: 500 }
    );
  }
} 