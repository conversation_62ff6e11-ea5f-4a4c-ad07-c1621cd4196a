/* Emergency contacts page styles */

.pageContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 1.5rem;
}

.pageTitle {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #111827;
  text-align: center;
}

.pageSubtitle {
  font-size: 1.125rem;
  color: #6b7280;
  text-align: center;
  margin-bottom: 2rem;
}

/* Main layout with two sections */
.mainLayout {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Association contacts - highlighted section */
.associationSection {
  background-color: #f8fafc;
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.associationTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 1.25rem;
  text-align: center;
  border-bottom: 2px solid #dbeafe;
  padding-bottom: 0.75rem;
}

.associationContacts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.contact<PERSON>erson {
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-left: 3px solid #3b82f6;
  column-gap: 0.75rem;
}

.personRole {
  font-weight: 600;
  color: #4b5563;
  white-space: nowrap;
}

.personPhone {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e40af;
  text-align: right;
  white-space: nowrap;
}

.contactsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.contactCard {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.contactCard.water {
  background-color: #f0f9ff;
  border-left: 4px solid #3b82f6;
}

.contactCard.service {
  background-color: #fef2f2;
  border-left: 4px solid #ef4444;
}

.contactCard.lift {
  background-color: #fffbeb;
  border-left: 4px solid #f59e0b;
}

.contactCard.admin {
  background-color: #f0f7ff;
  border-left: 4px solid #0369a1;
}

.contactName {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #111827;
}

.contactDescription {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
}

.contactRow {
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  padding: 0.5rem 0;
  column-gap: 0.75rem;
}

.contactLabel {
  font-weight: 500;
  color: #4b5563;
  white-space: nowrap;
}

.contactPhone {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e40af;
  text-align: right;
  white-space: nowrap;
}

.contactIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 9999px;
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  margin-bottom: 0.75rem;
}

.emergencyNumber {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ef4444;
  color: white;
  padding: 1rem;
  border-radius: 0.375rem;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.footer {
  margin-top: 2rem;
  text-align: center;
  font-size: 0.875rem;
  color: #6b7280;
} 