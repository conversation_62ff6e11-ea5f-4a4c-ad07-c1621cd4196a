# Manual Steps Required for DNSB Vakarai Supabase Migration

This document outlines the manual steps that cannot be automated and require human intervention during the migration process.

## 1. Pre-Migration Manual Steps

### 1.1 Supabase Instance Setup
**Why manual**: Requires account access and billing setup

**Steps**:
1. Create new Supabase project (cloud or self-hosted)
2. Note down the project reference ID
3. Generate and save service role key
4. Configure database password
5. Ensure PGMQ extension is available (contact support if needed)

### 1.2 Environment Configuration
**Why manual**: Requires access to deployment platform and security credentials

**Steps**:
1. Update `migration/.env` with actual credentials
2. Verify network connectivity between migration machine and both instances
3. Ensure sufficient disk space for exports/imports
4. Backup current environment variables from Coolify

### 1.3 Source Instance Documentation
**Why manual**: Requires knowledge of current setup and manual inspection

**Steps**:
1. Document current Supabase dashboard settings:
   - Auth provider configurations
   - Email template customizations
   - Storage bucket settings
   - Webhook configurations
   - API rate limits
2. Export current environment variables
3. Take screenshots of important configurations

## 2. During Migration Manual Steps

### 2.1 PGMQ Extension Installation
**Why manual**: May require database admin privileges or support request

**If PGMQ is not available**:
1. Contact your Supabase provider/admin
2. Request PGMQ extension installation
3. Verify installation with: `SELECT * FROM pg_extension WHERE extname = 'pgmq';`

**Alternative approach**:
```sql
-- If PGMQ is not available, you can create a simple table-based queue
CREATE TABLE simple_email_queue (
  id SERIAL PRIMARY KEY,
  message JSONB NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  processed_at TIMESTAMP,
  status TEXT DEFAULT 'pending'
);
```

### 2.2 Authentication Password Handling
**Why manual**: Security requirement - passwords need to be reset

**Steps**:
1. After auth migration, all users will have temporary passwords
2. Implement password reset flow for all users
3. Send password reset emails to all migrated users
4. Consider implementing a "first login after migration" flow

**Code to add to your app**:
```typescript
// Add to your auth flow
const checkMigrationStatus = async (user) => {
  if (user.user_metadata?.migrated && !user.user_metadata?.password_reset_after_migration) {
    // Redirect to password reset
    return { requiresPasswordReset: true };
  }
  return { requiresPasswordReset: false };
};
```

### 2.3 Edge Functions Deployment
**Why manual**: Requires Supabase CLI and project access

**Steps**:
1. Install Supabase CLI if not already installed
2. Login to Supabase CLI: `supabase login`
3. Link to new project: `supabase link --project-ref YOUR_NEW_PROJECT_REF`
4. Deploy functions: `supabase functions deploy`
5. Test function execution in Supabase dashboard

## 3. Post-Migration Manual Steps

### 3.1 Supabase Dashboard Configuration
**Why manual**: UI-based settings that cannot be automated

**Auth Settings**:
1. Go to Authentication → Settings
2. Configure site URL: `https://your-domain.com`
3. Add redirect URLs for your application
4. Set JWT expiry time (currently 3600 seconds)
5. Configure email templates if customized
6. Set up any external auth providers (Google, GitHub, etc.)

**Storage Settings** (if used):
1. Go to Storage → Settings
2. Create buckets if needed
3. Configure bucket policies
4. Set file size limits

**API Settings**:
1. Go to Settings → API
2. Review and configure rate limits
3. Set up any custom CORS settings

### 3.2 Environment Variables Update
**Why manual**: Requires access to deployment platform

**Coolify Steps**:
1. Log into Coolify dashboard
2. Navigate to your DNSB Vakarai project
3. Go to Environment Variables
4. Update the following:
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://your-new-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-new-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-new-service-key
   ```
5. Save and trigger new deployment

### 3.3 DNS Configuration (if using custom domain)
**Why manual**: Requires domain registrar access

**Steps**:
1. Update DNS records to point to new Supabase instance
2. If using custom domain for Supabase, update CNAME records
3. Wait for DNS propagation (up to 48 hours)
4. Update SSL certificates if needed

### 3.4 SMTP Configuration Verification
**Why manual**: Requires testing email delivery

**Steps**:
1. Log into your application as admin
2. Go to Admin → Settings → SMTP Settings
3. Verify SMTP configuration is correct
4. Send test email to verify connectivity
5. Check email delivery and spam folders

## 4. Testing Manual Steps

### 4.1 User Authentication Testing
**Why manual**: Requires testing different user scenarios

**Test Cases**:
1. Login with username (e.g., "31-7")
2. Login with email address
3. Test admin user access
4. Test editor user access
5. Test regular user access
6. Verify user profile data is complete
7. Test password reset functionality

### 4.2 Application Functionality Testing
**Why manual**: Requires business logic validation

**Test Scenarios**:
1. **Announcements**:
   - Create new announcement
   - Send to specific houses/flats
   - Verify email delivery
   - Check announcement history

2. **Contact Messages**:
   - Submit contact form
   - Verify admin can see messages
   - Test message assignment
   - Test status updates

3. **User Management**:
   - Create new user
   - Edit user profile
   - Assign user to flat
   - Test user permissions

4. **Email System**:
   - Check email queue processing
   - Verify PGMQ functionality
   - Test email templates
   - Check email delivery logs

### 4.3 Data Integrity Verification
**Why manual**: Requires business knowledge to validate data

**Verification Steps**:
1. **User Data**:
   - Check user count matches source
   - Verify user profiles are complete
   - Check flat assignments
   - Verify emergency contacts

2. **Property Data**:
   - Verify all houses exist
   - Check flat numbers and details
   - Verify house-flat relationships

3. **Communication Data**:
   - Check announcement history
   - Verify contact message history
   - Check email queue status

## 5. Troubleshooting Manual Steps

### 5.1 Common Issues and Solutions

**Issue**: PGMQ extension not available
**Solution**: 
1. Contact Supabase support
2. Use alternative table-based queue (see section 2.1)
3. Implement custom email processing

**Issue**: Auth users not linking correctly
**Solution**:
1. Check auth_user_id values in users table
2. Manually link users using Supabase dashboard
3. Reset passwords for affected users

**Issue**: Email delivery not working
**Solution**:
1. Check SMTP settings in database
2. Verify SMTP server connectivity
3. Check email queue processing
4. Review email templates

**Issue**: RLS policies blocking access
**Solution**:
1. Check RLS policies in Supabase dashboard
2. Verify auth.uid() is correctly set
3. Test with service role key
4. Review policy conditions

### 5.2 Emergency Rollback Procedures

**Immediate Rollback** (5 minutes):
1. Revert environment variables in Coolify
2. Trigger new deployment
3. Verify application is working
4. Monitor for any issues

**DNS Rollback** (if applicable):
1. Revert DNS changes
2. Wait for propagation
3. Test domain access
4. Update SSL certificates if needed

## 6. Post-Migration Monitoring

### 6.1 Manual Monitoring Tasks

**First 24 Hours**:
- [ ] Check application logs every 2 hours
- [ ] Monitor user login attempts
- [ ] Verify email delivery
- [ ] Check database performance
- [ ] Monitor error rates

**First Week**:
- [ ] Daily application health checks
- [ ] User feedback monitoring
- [ ] Email delivery verification
- [ ] Performance monitoring
- [ ] Data consistency checks

### 6.2 User Communication

**Before Migration**:
1. Notify users of planned maintenance window
2. Inform about potential password reset requirement
3. Provide support contact information

**After Migration**:
1. Send password reset instructions to all users
2. Provide updated login instructions if needed
3. Monitor support requests
4. Send confirmation when migration is complete

## 7. Documentation Updates

### 7.1 Required Documentation Updates

**Technical Documentation**:
- [ ] Update deployment guides with new Supabase URLs
- [ ] Update API documentation
- [ ] Update database schema documentation
- [ ] Update backup procedures

**User Documentation**:
- [ ] Update login instructions if changed
- [ ] Update any references to old URLs
- [ ] Update support contact information

**Operations Documentation**:
- [ ] Update monitoring procedures
- [ ] Update backup procedures
- [ ] Update disaster recovery plans
- [ ] Document new instance details

## 8. Final Checklist

Before considering migration complete:

- [ ] All automated migration scripts completed successfully
- [ ] All manual configuration steps completed
- [ ] Authentication working for all user types
- [ ] Email system fully functional
- [ ] All application features tested
- [ ] Performance is acceptable
- [ ] Users can access the system
- [ ] Documentation updated
- [ ] Monitoring in place
- [ ] Rollback plan tested and ready

## Support Information

If you encounter issues during manual steps:

1. **Check migration logs**: `migration/logs/migration-YYYY-MM-DD.log`
2. **Review verification report**: `migration/reports/verification-YYYY-MM-DD.json`
3. **Test database connectivity**: Use provided connection test scripts
4. **Contact support**: Have project details and error messages ready

Remember: Keep the source instance running until you're completely satisfied with the migration!
