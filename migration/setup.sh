#!/bin/bash

# DNSB Vakarai Supabase Migration Setup Script
# This script helps set up the migration environment

set -e

echo "🚀 DNSB Vakarai Supabase Migration Setup"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -f "config.js" ]; then
    print_error "Please run this script from the migration directory"
    exit 1
fi

print_step "1. Checking Node.js version..."
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

print_status "Node.js version: $(node -v) ✓"

print_step "2. Installing dependencies..."
if npm install; then
    print_status "Dependencies installed successfully ✓"
else
    print_error "Failed to install dependencies"
    exit 1
fi

print_step "3. Setting up environment configuration..."
if [ ! -f ".env" ]; then
    if [ -f ".env.template" ]; then
        cp .env.template .env
        print_status "Created .env file from template ✓"
        print_warning "Please edit .env file with your actual Supabase credentials"
    else
        print_error ".env.template file not found"
        exit 1
    fi
else
    print_status ".env file already exists ✓"
fi

print_step "4. Creating required directories..."
mkdir -p exports
mkdir -p logs
mkdir -p reports
print_status "Created migration directories ✓"

print_step "5. Checking configuration..."
if npm run migrate:dry-run > /dev/null 2>&1; then
    print_status "Configuration validation passed ✓"
else
    print_warning "Configuration validation failed. Please update .env file with correct values."
    print_warning "Run 'npm run migrate:dry-run' to see specific errors."
fi

print_step "6. Testing database connectivity..."

# Test source database connection
print_status "Testing source database connection..."
if node -e "
const config = require('./config');
const { Client } = require('pg');
const client = new Client(config.source.database);
client.connect()
  .then(() => {
    console.log('Source database connection: OK');
    return client.end();
  })
  .catch(err => {
    console.log('Source database connection: FAILED -', err.message);
    process.exit(1);
  });
" 2>/dev/null; then
    print_status "Source database connection: OK ✓"
else
    print_warning "Source database connection failed. Check your source database credentials."
fi

# Test target database connection
print_status "Testing target database connection..."
if node -e "
const config = require('./config');
const { Client } = require('pg');
const client = new Client(config.target.database);
client.connect()
  .then(() => {
    console.log('Target database connection: OK');
    return client.end();
  })
  .catch(err => {
    console.log('Target database connection: FAILED -', err.message);
    process.exit(1);
  });
" 2>/dev/null; then
    print_status "Target database connection: OK ✓"
else
    print_warning "Target database connection failed. Check your target database credentials."
fi

print_step "7. Setup complete!"
echo ""
echo "📋 Next Steps:"
echo "=============="
echo "1. Edit .env file with your actual Supabase credentials"
echo "2. Review and update migration/config.js if needed"
echo "3. Read migration/README.md for detailed instructions"
echo "4. Read migration/MIGRATION_CHECKLIST.md for step-by-step guide"
echo "5. Read migration/MANUAL_STEPS.md for manual configuration steps"
echo ""
echo "💾 Create backup of source instance:"
echo "  npm run backup"
echo ""
echo "🧪 Test your configuration:"
echo "  npm run migrate:dry-run"
echo ""
echo "🚀 Run the migration:"
echo "  npm run migrate"
echo ""
echo "🔍 Verify migration (after completion):"
echo "  npm run verify"
echo ""
echo "📚 Available commands:"
echo "  npm run export:schema    - Export database schema only"
echo "  npm run export:data      - Export application data only"
echo "  npm run import:schema    - Import schema to target"
echo "  npm run import:data      - Import data to target"
echo "  npm run import:auth      - Import authentication data"
echo "  npm run migrate:pgmq     - Setup PGMQ queues"
echo "  npm run verify           - Verify migration completeness"
echo ""
print_warning "⚠️  Important: Always backup your source instance before migration!"
print_warning "⚠️  Keep your source instance running until migration is verified!"
echo ""
print_status "Setup completed successfully! 🎉"
