# DNSB Vakarai Supabase Migration Guide

This guide provides step-by-step instructions for migrating your DNSB Vakarai project from one self-hosted Supabase instance to another.

## Migration Overview

The migration includes:
- Database schema and data
- Authentication users and metadata
- PGMQ queue configuration and pending jobs
- Edge functions
- Application configuration updates
- RLS policies and database functions

## Prerequisites

1. **Source Supabase Instance**: Your current working instance
2. **Target Supabase Instance**: New instance (freshly installed)
3. **Database Access**: Direct PostgreSQL access to both instances
4. **Supabase CLI**: Latest version installed
5. **Node.js**: For running migration scripts

## Migration Steps

### Phase 1: Preparation
1. [Backup Current Instance](#backup-current-instance)
2. [Prepare Target Instance](#prepare-target-instance)
3. [Verify Prerequisites](#verify-prerequisites)

### Phase 2: Schema Migration
1. [Export Database Schema](#export-database-schema)
2. [Import Schema to Target](#import-schema-to-target)
3. [Verify Schema Migration](#verify-schema-migration)

### Phase 3: Data Migration
1. [Export Application Data](#export-application-data)
2. [Export Authentication Data](#export-authentication-data)
3. [Import Data to Target](#import-data-to-target)

### Phase 4: Configuration Migration
1. [Migrate PGMQ Configuration](#migrate-pgmq-configuration)
2. [Migrate Edge Functions](#migrate-edge-functions)
3. [Update Application Configuration](#update-application-configuration)

### Phase 5: Verification & Cutover
1. [Verify Migration](#verify-migration)
2. [Update DNS/Environment](#update-environment)
3. [Test Application](#test-application)

## Detailed Instructions

### Backup Current Instance

**Option 1: Use the automated backup script (Recommended)**
```bash
# Run the automated backup script
./migration/scripts/backup-source.sh
```

**Option 2: Manual backup commands**
```bash
# 1. Create backup directory
mkdir -p migration/backups/$(date +%Y%m%d_%H%M%S)
cd migration/backups/$(date +%Y%m%d_%H%M%S)

# 2. Export database schema and data
# Note: You'll be prompted for the postgres password
pg_dump -h *********** -p 54322 -U postgres -d postgres \
  --schema-only --no-owner --no-privileges > schema_backup.sql

pg_dump -h *********** -p 54322 -U postgres -d postgres \
  --data-only --no-owner --no-privileges \
  --exclude-table=auth.* --exclude-table=storage.* \
  --exclude-table=realtime.* --exclude-table=supabase_functions.* > data_backup.sql

# 3. Export auth users separately
pg_dump -h *********** -p 54322 -U postgres -d postgres \
  --data-only --no-owner --no-privileges \
  --table=auth.users --table=auth.identities > auth_backup.sql

# Alternative: Set PGPASSWORD environment variable to avoid password prompts
# export PGPASSWORD='your-postgres-password'
# Then run the commands above without being prompted for password
```

**Connection Details for DNSB Vakarai Production:**
- Host: `***********`
- Port: `54322`
- Database: `postgres`
- User: `postgres`
- Password: (You'll be prompted or set via PGPASSWORD)

### Prepare Target Instance

```bash
# 1. Initialize new Supabase project
supabase init

# 2. Start local development (for testing)
supabase start

# 3. Apply initial migrations
supabase db reset
```

### Export Database Schema

Run the schema export script:
```bash
node migration/scripts/01-export-schema.js
```

### Export Application Data

Run the data export script:
```bash
node migration/scripts/02-export-data.js
```

### Import to Target Instance

```bash
# 1. Import schema
node migration/scripts/03-import-schema.js

# 2. Import data
node migration/scripts/04-import-data.js

# 3. Import auth data
node migration/scripts/05-import-auth.js
```

### Migrate PGMQ Configuration

```bash
node migration/scripts/06-migrate-pgmq.js
```

### Migrate Edge Functions

```bash
# Deploy edge functions to new instance
supabase functions deploy --project-ref YOUR_NEW_PROJECT_REF
```

### Update Application Configuration

1. Update environment variables in your deployment platform (Coolify)
2. Update DNS records if using custom domain
3. Update any hardcoded URLs in configuration

### Verify Migration

```bash
node migration/scripts/07-verify-migration.js
```

## Manual Steps Required

Some steps require manual intervention:

1. **Supabase Dashboard Settings**: 
   - Auth providers configuration
   - Email templates
   - Storage bucket policies (if any)

2. **Environment Variables**:
   - Update NEXT_PUBLIC_SUPABASE_URL
   - Update NEXT_PUBLIC_SUPABASE_ANON_KEY
   - Update SUPABASE_SERVICE_ROLE_KEY

3. **DNS/Domain Configuration**:
   - Update DNS records if using custom domain
   - Update SSL certificates if needed

4. **SMTP Configuration**:
   - SMTP settings are stored in database and will be migrated
   - Verify SMTP connectivity from new instance

## Rollback Plan

If migration fails:

1. **Keep source instance running** until migration is verified
2. **Database rollback**: Restore from backup if needed
3. **Application rollback**: Revert environment variables
4. **DNS rollback**: Point back to source instance

## Testing Checklist

After migration, verify:

- [ ] User authentication works
- [ ] Database queries return expected data
- [ ] Email queue processing works
- [ ] Admin functions accessible
- [ ] All user roles and permissions work
- [ ] PGMQ queues are functional
- [ ] Edge functions respond correctly

## Estimated Downtime

- **Preparation**: 30 minutes
- **Schema Migration**: 5 minutes
- **Data Migration**: 10-30 minutes (depends on data size)
- **Configuration**: 15 minutes
- **Verification**: 15 minutes
- **Total**: 1-2 hours

## Support

If you encounter issues:
1. Check migration logs in `migration/logs/`
2. Verify database connections
3. Check Supabase dashboard for errors
4. Review application logs

## Next Steps

After successful migration:
1. Monitor application for 24-48 hours
2. Verify all scheduled jobs are running
3. Check email delivery
4. Update documentation with new instance details
5. Decommission old instance after verification period
