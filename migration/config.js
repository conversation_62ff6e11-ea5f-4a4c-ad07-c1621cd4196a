/**
 * Migration Configuration
 * 
 * Update these values with your source and target Supabase instances
 */

const config = {
  // Source Supabase instance (current - DNSB Vakarai production)
  source: {
    supabaseUrl: process.env.SOURCE_SUPABASE_URL || 'http://supabasekong-y40ggo44c8gg4cw4ssws8c4c.***********.sslip.io',
    supabaseAnonKey: process.env.SOURCE_SUPABASE_ANON_KEY || 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTc0ODY5NDMwMCwiZXhwIjo0OTA0MzY3OTAwLCJyb2xlIjoiYW5vbiJ9.AWA6gE60PWFpqUMFXWHkSAIHM4UPmHKBCqHCasTvs40',
    supabaseServiceKey: process.env.SOURCE_SUPABASE_SERVICE_KEY || 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTc0ODY5NDMwMCwiZXhwIjo0OTA0MzY3OTAwLCJyb2xlIjoic2VydmljZV9yb2xlIn0.3JShkzlla8zbM3gJJZbwDggp5rO-z0nqLuygxR-ezrY',

    // Direct database connection for advanced operations
    database: {
      host: process.env.SOURCE_DB_HOST || '***********',
      port: process.env.SOURCE_DB_PORT || 54322,
      database: process.env.SOURCE_DB_NAME || 'postgres',
      username: process.env.SOURCE_DB_USER || 'postgres',
      password: process.env.SOURCE_DB_PASSWORD || 'your-postgres-password'
    }
  },

  // Target Supabase instance (new)
  target: {
    supabaseUrl: process.env.TARGET_SUPABASE_URL || 'https://your-new-project.supabase.co',
    supabaseAnonKey: process.env.TARGET_SUPABASE_ANON_KEY || 'your-new-anon-key',
    supabaseServiceKey: process.env.TARGET_SUPABASE_SERVICE_KEY || 'your-new-service-key',
    
    // Direct database connection for advanced operations
    database: {
      host: process.env.TARGET_DB_HOST || 'db.your-new-project.supabase.co',
      port: process.env.TARGET_DB_PORT || 5432,
      database: process.env.TARGET_DB_NAME || 'postgres',
      username: process.env.TARGET_DB_USER || 'postgres',
      password: process.env.TARGET_DB_PASSWORD || 'your-new-postgres-password'
    }
  },

  // Migration settings
  migration: {
    // Batch size for data migration
    batchSize: 1000,
    
    // Tables to migrate (in order)
    tables: [
      'streets',
      'houses', 
      'flats',
      'users',
      'emergency_contacts',
      'announcements',
      'announcement_streets',
      'announcement_houses',
      'announcement_flats',
      'announcement_users',
      'polls',
      'poll_options',
      'poll_responses',
      'poll_audience',
      'poll_houses',
      'poll_flats',
      'poll_users',
      'contact_messages',
      'feedback',
      'email_queue',
      'email_templates',
      'user_email_preferences',
      'admin_settings',
      'smtp_settings',
      'tags',
      'entity_tags',
      'audit_logs'
    ],

    // Tables to exclude from data migration
    excludeTables: [
      'auth.users',
      'auth.identities',
      'auth.sessions',
      'auth.refresh_tokens',
      'storage.objects',
      'storage.buckets',
      'realtime.messages',
      'supabase_functions.hooks'
    ],

    // Auth tables to migrate separately
    authTables: [
      'auth.users',
      'auth.identities'
    ],

    // PGMQ queues to recreate
    pgmqQueues: [
      'email_queue'
    ],

    // Edge functions to deploy
    edgeFunctions: [
      'process-emails'
    ]
  },

  // Logging configuration
  logging: {
    level: 'info', // debug, info, warn, error
    logFile: `migration/logs/migration-${new Date().toISOString().split('T')[0]}.log`
  }
};

module.exports = config;
