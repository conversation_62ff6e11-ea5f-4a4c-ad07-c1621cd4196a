#!/usr/bin/env node

/**
 * Import Application Data to Target Supabase Instance
 * 
 * This script imports all application data in the correct order
 */

const fs = require('fs');
const path = require('path');
const { Client } = require('pg');
const config = require('../config');

// Logging function
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(config.logging.logFile, logMessage + '\n');
}

async function importData() {
  const client = new Client(config.target.database);
  
  try {
    log('Connecting to target database...');
    await client.connect();
    
    log('Importing application data...');
    
    const exportDir = path.join(__dirname, '../exports');

    // Disable triggers temporarily for faster import
    log('Disabling triggers for faster import...');
    await client.query('SET session_replication_role = replica;');

    // Import data for each table in dependency order
    for (const tableName of config.migration.tables) {
      const fileName = `05-data-${tableName}.sql`;
      const filePath = path.join(exportDir, fileName);
      
      if (!fs.existsSync(filePath)) {
        log(`Data file for ${tableName} not found, skipping...`);
        continue;
      }

      log(`Importing data for table: ${tableName}`);
      
      try {
        // Check if table exists in target
        const tableExistsQuery = `
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          );
        `;
        
        const tableExistsResult = await client.query(tableExistsQuery, [tableName]);
        
        if (!tableExistsResult.rows[0].exists) {
          log(`Table ${tableName} does not exist in target, skipping...`, 'warn');
          continue;
        }

        // Read and execute the SQL file
        const sql = fs.readFileSync(filePath, 'utf8');
        
        if (sql.trim().length === 0) {
          log(`Data file for ${tableName} is empty, skipping...`);
          continue;
        }

        // Count existing rows
        const countBeforeQuery = `SELECT COUNT(*) FROM ${tableName}`;
        const countBeforeResult = await client.query(countBeforeQuery);
        const rowsBefore = parseInt(countBeforeResult.rows[0].count);

        // Execute the import
        await client.query('BEGIN');
        
        try {
          // Split SQL into individual statements and execute them
          const statements = sql
            .split('\n')
            .filter(line => line.trim() && !line.trim().startsWith('--'))
            .join('\n')
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0);

          let importedCount = 0;
          for (const statement of statements) {
            if (statement.trim() && statement.toUpperCase().startsWith('INSERT')) {
              await client.query(statement);
              importedCount++;
              
              if (importedCount % 100 === 0) {
                log(`Imported ${importedCount} rows for ${tableName}...`);
              }
            }
          }
          
          await client.query('COMMIT');
          
          // Count rows after import
          const countAfterResult = await client.query(countBeforeQuery);
          const rowsAfter = parseInt(countAfterResult.rows[0].count);
          const newRows = rowsAfter - rowsBefore;
          
          log(`Successfully imported ${newRows} rows for ${tableName} (total: ${rowsAfter})`);
          
        } catch (error) {
          await client.query('ROLLBACK');
          throw error;
        }
        
      } catch (error) {
        log(`Error importing data for ${tableName}: ${error.message}`, 'error');
        
        // Check if it's a constraint violation that we can handle
        if (error.message.includes('duplicate key') || error.message.includes('already exists')) {
          log(`Some data already exists in ${tableName}, continuing...`, 'warn');
        } else {
          // For other errors, we might want to continue or stop
          log(`Continuing with next table despite error in ${tableName}`, 'warn');
        }
      }
    }

    // Re-enable triggers
    log('Re-enabling triggers...');
    await client.query('SET session_replication_role = DEFAULT;');

    // Update sequences to correct values
    log('Updating sequence values...');
    const sequencesQuery = `
      SELECT schemaname, sequencename, last_value
      FROM pg_sequences
      WHERE schemaname = 'public';
    `;
    
    try {
      const sequencesResult = await client.query(sequencesQuery);
      
      for (const seq of sequencesResult.rows) {
        // Find the table and column that uses this sequence
        const seqName = seq.sequencename;
        const tableName = seqName.replace(/_id_seq$/, '').replace(/_seq$/, '');
        
        // Get the maximum ID from the table
        try {
          const maxIdQuery = `SELECT MAX(id) as max_id FROM ${tableName}`;
          const maxIdResult = await client.query(maxIdQuery);
          
          if (maxIdResult.rows[0].max_id) {
            const maxId = parseInt(maxIdResult.rows[0].max_id);
            const setvalQuery = `SELECT setval('${seqName}', ${maxId});`;
            await client.query(setvalQuery);
            log(`Updated sequence ${seqName} to ${maxId}`);
          }
        } catch (error) {
          log(`Could not update sequence ${seqName}: ${error.message}`, 'warn');
        }
      }
    } catch (error) {
      log(`Error updating sequences: ${error.message}`, 'warn');
    }

    // Import PGMQ data if available
    const pgmqFile = path.join(exportDir, '06-pgmq-data.sql');
    if (fs.existsSync(pgmqFile)) {
      log('Importing PGMQ queue data...');
      
      try {
        const pgmqSQL = fs.readFileSync(pgmqFile, 'utf8');
        
        if (pgmqSQL.trim().length > 0) {
          const statements = pgmqSQL
            .split('\n')
            .filter(line => line.trim() && !line.trim().startsWith('--'))
            .filter(line => line.trim().startsWith('SELECT pgmq.send'));

          for (const statement of statements) {
            try {
              await client.query(statement);
            } catch (error) {
              log(`Warning: Could not import PGMQ message: ${error.message}`, 'warn');
            }
          }
          
          log(`Imported ${statements.length} PGMQ messages`);
        }
      } catch (error) {
        log(`Error importing PGMQ data: ${error.message}`, 'warn');
      }
    }

    log('Data import completed successfully!');
    
  } catch (error) {
    log(`Error importing data: ${error.message}`, 'error');
    throw error;
  } finally {
    await client.end();
  }
}

// Run the import
if (require.main === module) {
  importData()
    .then(() => {
      log('Data import completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      log(`Data import failed: ${error.message}`, 'error');
      process.exit(1);
    });
}

module.exports = { importData };
