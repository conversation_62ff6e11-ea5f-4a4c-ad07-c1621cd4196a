#!/usr/bin/env node

/**
 * Export Application Data from Source Supabase Instance
 * 
 * This script exports all application data excluding auth and system tables
 */

const fs = require('fs');
const path = require('path');
const { Client } = require('pg');
const config = require('../config');

// Logging function
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(config.logging.logFile, logMessage + '\n');
}

async function exportData() {
  const client = new Client(config.source.database);
  
  try {
    log('Connecting to source database...');
    await client.connect();
    
    log('Exporting application data...');
    
    // Create export directory
    const exportDir = path.join(__dirname, '../exports');
    if (!fs.existsSync(exportDir)) {
      fs.mkdirSync(exportDir, { recursive: true });
    }

    // Export data for each table
    for (const tableName of config.migration.tables) {
      try {
        log(`Exporting data from table: ${tableName}`);
        
        // Check if table exists
        const tableExistsQuery = `
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          );
        `;
        
        const tableExistsResult = await client.query(tableExistsQuery, [tableName]);
        
        if (!tableExistsResult.rows[0].exists) {
          log(`Table ${tableName} does not exist, skipping...`, 'warn');
          continue;
        }

        // Get table columns
        const columnsQuery = `
          SELECT column_name, data_type
          FROM information_schema.columns
          WHERE table_schema = 'public' AND table_name = $1
          ORDER BY ordinal_position;
        `;
        
        const columnsResult = await client.query(columnsQuery, [tableName]);
        const columns = columnsResult.rows.map(row => row.column_name);

        // Get row count
        const countQuery = `SELECT COUNT(*) FROM ${tableName}`;
        const countResult = await client.query(countQuery);
        const totalRows = parseInt(countResult.rows[0].count);
        
        log(`Table ${tableName} has ${totalRows} rows`);

        if (totalRows === 0) {
          log(`Table ${tableName} is empty, skipping data export`);
          continue;
        }

        // Export data in batches
        let exportedRows = 0;
        let offset = 0;
        let dataSQL = `-- Data for table: ${tableName}\n`;
        
        while (offset < totalRows) {
          const dataQuery = `
            SELECT * FROM ${tableName}
            ORDER BY (SELECT NULL)
            LIMIT ${config.migration.batchSize} OFFSET ${offset}
          `;
          
          const dataResult = await client.query(dataQuery);
          
          if (dataResult.rows.length === 0) {
            break;
          }

          // Generate INSERT statements
          for (const row of dataResult.rows) {
            const values = columns.map(col => {
              const value = row[col];
              if (value === null) {
                return 'NULL';
              } else if (typeof value === 'string') {
                return `'${value.replace(/'/g, "''")}'`;
              } else if (typeof value === 'boolean') {
                return value ? 'TRUE' : 'FALSE';
              } else if (value instanceof Date) {
                return `'${value.toISOString()}'`;
              } else if (typeof value === 'object') {
                return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
              } else {
                return value;
              }
            });
            
            dataSQL += `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values.join(', ')});\n`;
          }

          exportedRows += dataResult.rows.length;
          offset += config.migration.batchSize;
          
          log(`Exported ${exportedRows}/${totalRows} rows from ${tableName}`);
        }

        // Write data to file
        const fileName = `05-data-${tableName}.sql`;
        fs.writeFileSync(path.join(exportDir, fileName), dataSQL);
        
        log(`Completed export of ${tableName}: ${exportedRows} rows`);
        
      } catch (error) {
        log(`Error exporting table ${tableName}: ${error.message}`, 'error');
        // Continue with next table instead of failing completely
      }
    }

    // Export PGMQ queue data separately
    log('Exporting PGMQ queue data...');
    try {
      const pgmqQuery = `
        SELECT queue_name, msg_id, read_ct, enqueued_at, vt, message
        FROM pgmq.q_email_queue
        WHERE vt > NOW()
        ORDER BY enqueued_at;
      `;
      
      const pgmqResult = await client.query(pgmqQuery);
      
      if (pgmqResult.rows.length > 0) {
        let pgmqSQL = '-- PGMQ Queue Data\n';
        pgmqSQL += '-- Note: This data should be imported after PGMQ setup\n';
        
        for (const row of pgmqResult.rows) {
          const message = JSON.stringify(row.message).replace(/'/g, "''");
          pgmqSQL += `SELECT pgmq.send('${row.queue_name}', '${message}');\n`;
        }
        
        fs.writeFileSync(path.join(exportDir, '06-pgmq-data.sql'), pgmqSQL);
        log(`Exported ${pgmqResult.rows.length} PGMQ messages`);
      } else {
        log('No pending PGMQ messages found');
      }
    } catch (error) {
      log(`Error exporting PGMQ data: ${error.message}`, 'warn');
      // PGMQ might not be set up yet, continue
    }

    log('Data export completed successfully!');
    
  } catch (error) {
    log(`Error exporting data: ${error.message}`, 'error');
    throw error;
  } finally {
    await client.end();
  }
}

// Run the export
if (require.main === module) {
  exportData()
    .then(() => {
      log('Data export completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      log(`Data export failed: ${error.message}`, 'error');
      process.exit(1);
    });
}

module.exports = { exportData };
