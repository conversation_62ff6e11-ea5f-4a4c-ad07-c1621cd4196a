#!/usr/bin/env node

/**
 * Verify Migration Completeness and Data Integrity
 * 
 * This script verifies that the migration was successful by checking:
 * - Data counts match between source and target
 * - Key functionality works
 * - Authentication is properly set up
 * - PGMQ is functional
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
const { Client } = require('pg');
const config = require('../config');

// Logging function
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(config.logging.logFile, logMessage + '\n');
}

async function verifyMigration() {
  const sourceSupabase = createClient(config.source.supabaseUrl, config.source.supabaseServiceKey);
  const targetSupabase = createClient(config.target.supabaseUrl, config.target.supabaseServiceKey);
  const sourceClient = new Client(config.source.database);
  const targetClient = new Client(config.target.database);
  
  let verificationResults = {
    dataIntegrity: {},
    authentication: {},
    pgmq: {},
    functionality: {},
    overall: 'PENDING'
  };

  try {
    log('Starting migration verification...');
    
    // Connect to both databases
    await sourceClient.connect();
    await targetClient.connect();

    // 1. Verify data counts
    log('Verifying data integrity...');
    
    for (const tableName of config.migration.tables) {
      try {
        // Check if table exists in both databases
        const sourceTableExists = await checkTableExists(sourceClient, tableName);
        const targetTableExists = await checkTableExists(targetClient, tableName);

        if (!sourceTableExists) {
          log(`Table ${tableName} does not exist in source, skipping...`);
          continue;
        }

        if (!targetTableExists) {
          log(`ERROR: Table ${tableName} missing in target!`, 'error');
          verificationResults.dataIntegrity[tableName] = { status: 'MISSING', sourceCount: 0, targetCount: 0 };
          continue;
        }

        // Get row counts
        const sourceCountResult = await sourceClient.query(`SELECT COUNT(*) FROM ${tableName}`);
        const targetCountResult = await targetClient.query(`SELECT COUNT(*) FROM ${tableName}`);
        
        const sourceCount = parseInt(sourceCountResult.rows[0].count);
        const targetCount = parseInt(targetCountResult.rows[0].count);

        const status = sourceCount === targetCount ? 'OK' : 'MISMATCH';
        verificationResults.dataIntegrity[tableName] = {
          status,
          sourceCount,
          targetCount,
          difference: targetCount - sourceCount
        };

        if (status === 'OK') {
          log(`✓ ${tableName}: ${sourceCount} rows (matches)`);
        } else {
          log(`✗ ${tableName}: Source ${sourceCount}, Target ${targetCount} (difference: ${targetCount - sourceCount})`, 'warn');
        }

      } catch (error) {
        log(`Error verifying ${tableName}: ${error.message}`, 'error');
        verificationResults.dataIntegrity[tableName] = { status: 'ERROR', error: error.message };
      }
    }

    // 2. Verify authentication setup
    log('Verifying authentication setup...');
    
    try {
      // Check users with auth_user_id
      const { data: usersWithAuth, error: usersError } = await targetSupabase
        .from('users')
        .select('id, username, email, auth_user_id')
        .not('auth_user_id', 'is', null);

      if (usersError) {
        throw new Error(`Error fetching users: ${usersError.message}`);
      }

      verificationResults.authentication.usersWithAuth = usersWithAuth.length;
      log(`✓ Found ${usersWithAuth.length} users with authentication`);

      // Test a few auth users
      let authTestsPassed = 0;
      const testUsers = usersWithAuth.slice(0, 3); // Test first 3 users

      for (const user of testUsers) {
        try {
          const { data: authUser, error: authError } = await targetSupabase.auth.admin.getUserById(user.auth_user_id);
          
          if (authError) {
            log(`✗ Auth user ${user.username} (${user.auth_user_id}): ${authError.message}`, 'warn');
          } else if (authUser.user) {
            log(`✓ Auth user ${user.username} exists and is accessible`);
            authTestsPassed++;
          }
        } catch (error) {
          log(`✗ Error testing auth user ${user.username}: ${error.message}`, 'warn');
        }
      }

      verificationResults.authentication.authTestsPassed = authTestsPassed;
      verificationResults.authentication.authTestsTotal = testUsers.length;

    } catch (error) {
      log(`Error verifying authentication: ${error.message}`, 'error');
      verificationResults.authentication.error = error.message;
    }

    // 3. Verify PGMQ setup
    log('Verifying PGMQ setup...');
    
    try {
      // Check if PGMQ extension is installed
      const pgmqExtensionResult = await targetClient.query(`
        SELECT installed_version FROM pg_extension WHERE extname = 'pgmq';
      `);

      if (pgmqExtensionResult.rows.length === 0) {
        throw new Error('PGMQ extension not installed');
      }

      verificationResults.pgmq.extensionInstalled = true;
      verificationResults.pgmq.version = pgmqExtensionResult.rows[0].installed_version;
      log(`✓ PGMQ extension installed: version ${verificationResults.pgmq.version}`);

      // Check queues
      const { data: queues, error: queuesError } = await targetSupabase
        .rpc('pgmq_list_queues');

      if (queuesError) {
        throw new Error(`Error listing queues: ${queuesError.message}`);
      }

      verificationResults.pgmq.queues = queues.map(q => q.queue_name);
      log(`✓ PGMQ queues available: ${verificationResults.pgmq.queues.join(', ')}`);

      // Test queue functionality
      const testQueueName = 'email_queue';
      if (verificationResults.pgmq.queues.includes(testQueueName)) {
        const testMessage = {
          test: true,
          verification: true,
          timestamp: new Date().toISOString()
        };

        // Send test message
        const { data: sendResult, error: sendError } = await targetSupabase
          .rpc('pgmq_send', {
            queue_name: testQueueName,
            message: testMessage
          });

        if (sendError) {
          throw new Error(`Error sending test message: ${sendError.message}`);
        }

        // Read test message
        const { data: readResult, error: readError } = await targetSupabase
          .rpc('pgmq_read', {
            queue_name: testQueueName,
            visibility_timeout: 30,
            quantity: 1
          });

        if (readError) {
          throw new Error(`Error reading test message: ${readError.message}`);
        }

        if (readResult && readResult.length > 0) {
          // Archive test message
          await targetSupabase.rpc('pgmq_archive', {
            queue_name: testQueueName,
            msg_id: readResult[0].msg_id
          });

          verificationResults.pgmq.functionalityTest = 'PASSED';
          log(`✓ PGMQ functionality test passed`);
        } else {
          verificationResults.pgmq.functionalityTest = 'FAILED';
          log(`✗ PGMQ functionality test failed: no message received`, 'warn');
        }
      }

    } catch (error) {
      log(`Error verifying PGMQ: ${error.message}`, 'error');
      verificationResults.pgmq.error = error.message;
    }

    // 4. Verify key functionality
    log('Verifying key application functionality...');
    
    try {
      // Test basic queries
      const { data: adminSettings, error: settingsError } = await targetSupabase
        .from('admin_settings')
        .select('*')
        .limit(5);

      if (settingsError) {
        throw new Error(`Error querying admin_settings: ${settingsError.message}`);
      }

      verificationResults.functionality.adminSettings = adminSettings.length;
      log(`✓ Admin settings accessible: ${adminSettings.length} records`);

      // Test SMTP settings
      const { data: smtpSettings, error: smtpError } = await targetSupabase
        .from('smtp_settings')
        .select('*')
        .limit(1);

      if (smtpError) {
        log(`Warning: Could not access SMTP settings: ${smtpError.message}`, 'warn');
      } else {
        verificationResults.functionality.smtpSettings = smtpSettings.length;
        log(`✓ SMTP settings accessible: ${smtpSettings.length} records`);
      }

      // Test RLS policies
      const { data: userProfile, error: rlsError } = await targetSupabase
        .from('users')
        .select('id, username, role')
        .limit(1);

      if (rlsError) {
        log(`Warning: RLS test failed: ${rlsError.message}`, 'warn');
        verificationResults.functionality.rlsTest = 'FAILED';
      } else {
        verificationResults.functionality.rlsTest = 'PASSED';
        log(`✓ RLS policies working`);
      }

    } catch (error) {
      log(`Error verifying functionality: ${error.message}`, 'error');
      verificationResults.functionality.error = error.message;
    }

    // 5. Overall assessment
    log('Generating overall assessment...');
    
    const dataIntegrityIssues = Object.values(verificationResults.dataIntegrity)
      .filter(result => result.status !== 'OK').length;
    
    const hasAuthIssues = verificationResults.authentication.error || 
      verificationResults.authentication.authTestsPassed < verificationResults.authentication.authTestsTotal;
    
    const hasPgmqIssues = verificationResults.pgmq.error || 
      verificationResults.pgmq.functionalityTest === 'FAILED';
    
    const hasFunctionalityIssues = verificationResults.functionality.error ||
      verificationResults.functionality.rlsTest === 'FAILED';

    if (dataIntegrityIssues === 0 && !hasAuthIssues && !hasPgmqIssues && !hasFunctionalityIssues) {
      verificationResults.overall = 'SUCCESS';
      log('🎉 Migration verification PASSED! All systems operational.', 'info');
    } else if (dataIntegrityIssues > 0 || hasAuthIssues) {
      verificationResults.overall = 'CRITICAL_ISSUES';
      log('❌ Migration verification FAILED! Critical issues found.', 'error');
    } else {
      verificationResults.overall = 'MINOR_ISSUES';
      log('⚠️  Migration verification completed with minor issues.', 'warn');
    }

    // Save verification report
    const reportPath = path.join(__dirname, '../reports', `verification-${new Date().toISOString().split('T')[0]}.json`);
    const reportsDir = path.dirname(reportPath);
    
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    fs.writeFileSync(reportPath, JSON.stringify(verificationResults, null, 2));
    log(`Verification report saved to: ${reportPath}`);

    return verificationResults;
    
  } catch (error) {
    log(`Error during verification: ${error.message}`, 'error');
    verificationResults.overall = 'ERROR';
    verificationResults.error = error.message;
    throw error;
  } finally {
    await sourceClient.end();
    await targetClient.end();
  }
}

async function checkTableExists(client, tableName) {
  const result = await client.query(`
    SELECT EXISTS (
      SELECT FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = $1
    );
  `, [tableName]);
  
  return result.rows[0].exists;
}

// Run the verification
if (require.main === module) {
  verifyMigration()
    .then((results) => {
      log('Migration verification completed!');
      console.log('\n=== VERIFICATION SUMMARY ===');
      console.log(`Overall Status: ${results.overall}`);
      console.log(`Data Integrity Issues: ${Object.values(results.dataIntegrity).filter(r => r.status !== 'OK').length}`);
      console.log(`Authentication Status: ${results.authentication.error ? 'ERROR' : 'OK'}`);
      console.log(`PGMQ Status: ${results.pgmq.error ? 'ERROR' : 'OK'}`);
      console.log(`Functionality Status: ${results.functionality.error ? 'ERROR' : 'OK'}`);
      
      process.exit(results.overall === 'SUCCESS' ? 0 : 1);
    })
    .catch((error) => {
      log(`Migration verification failed: ${error.message}`, 'error');
      process.exit(1);
    });
}

module.exports = { verifyMigration };
