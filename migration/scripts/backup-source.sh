#!/bin/bash

# DNSB Vakarai Source Instance Backup Script
# This script creates a complete backup of your current Supabase instance

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "🔄 DNSB Vakarai Source Instance Backup"
echo "====================================="

# DNSB Vakarai Production Database Connection Details
DB_HOST="***********"
DB_PORT="54322"
DB_NAME="postgres"
DB_USER="postgres"

# Create backup directory with timestamp
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

print_status "Created backup directory: $BACKUP_DIR"

# Check if pg_dump is available
if ! command -v pg_dump &> /dev/null; then
    print_error "pg_dump is not installed. Please install PostgreSQL client tools."
    exit 1
fi

print_step "Starting backup process..."

# Option 1: Interactive password prompt (default)
if [ -z "$PGPASSWORD" ]; then
    print_warning "You will be prompted for the PostgreSQL password for each backup operation."
    print_warning "To avoid prompts, set PGPASSWORD environment variable:"
    print_warning "export PGPASSWORD='your-postgres-password'"
    echo ""
fi

print_step "1. Backing up database schema..."
if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
    --schema-only --no-owner --no-privileges > "$BACKUP_DIR/schema_backup.sql"; then
    print_status "Schema backup completed: $BACKUP_DIR/schema_backup.sql"
else
    print_error "Schema backup failed"
    exit 1
fi

print_step "2. Backing up application data..."
if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
    --data-only --no-owner --no-privileges \
    --exclude-table=auth.* --exclude-table=storage.* \
    --exclude-table=realtime.* --exclude-table=supabase_functions.* \
    --exclude-table=supabase_migrations.* --exclude-table=pgmq.* > "$BACKUP_DIR/data_backup.sql"; then
    print_status "Application data backup completed: $BACKUP_DIR/data_backup.sql"
else
    print_error "Application data backup failed"
    exit 1
fi

print_step "3. Backing up authentication data..."
if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
    --data-only --no-owner --no-privileges \
    --table=auth.users --table=auth.identities > "$BACKUP_DIR/auth_backup.sql"; then
    print_status "Authentication data backup completed: $BACKUP_DIR/auth_backup.sql"
else
    print_warning "Authentication data backup failed (this is normal if auth tables are empty)"
fi

print_step "4. Backing up PGMQ data..."
if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
    --data-only --no-owner --no-privileges \
    --table=pgmq.* > "$BACKUP_DIR/pgmq_backup.sql" 2>/dev/null; then
    print_status "PGMQ data backup completed: $BACKUP_DIR/pgmq_backup.sql"
else
    print_warning "PGMQ data backup failed (this is normal if PGMQ is not set up)"
fi

print_step "5. Creating complete database backup..."
if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
    --no-owner --no-privileges > "$BACKUP_DIR/complete_backup.sql"; then
    print_status "Complete database backup completed: $BACKUP_DIR/complete_backup.sql"
else
    print_error "Complete database backup failed"
    exit 1
fi

# Get backup file sizes
print_step "6. Backup summary..."
echo ""
echo "📊 Backup Files Created:"
echo "========================"
for file in "$BACKUP_DIR"/*.sql; do
    if [ -f "$file" ]; then
        size=$(du -h "$file" | cut -f1)
        filename=$(basename "$file")
        echo "  $filename: $size"
    fi
done

echo ""
print_status "✅ Backup completed successfully!"
print_status "📁 Backup location: $(pwd)/$BACKUP_DIR"
echo ""
print_warning "⚠️  Important notes:"
print_warning "   - Keep these backups safe until migration is verified"
print_warning "   - Test restore procedures before starting migration"
print_warning "   - Store backups in a secure location"
echo ""
print_status "🚀 You can now proceed with the migration using:"
print_status "   cd migration && npm run migrate"
