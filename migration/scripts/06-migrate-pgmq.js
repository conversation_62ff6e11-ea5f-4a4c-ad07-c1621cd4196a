#!/usr/bin/env node

/**
 * Migrate PGMQ Configuration to Target Supabase Instance
 * 
 * This script sets up PGMQ extension and recreates queues
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
const { Client } = require('pg');
const config = require('../config');

// Logging function
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(config.logging.logFile, logMessage + '\n');
}

async function migratePGMQ() {
  const targetSupabase = createClient(config.target.supabaseUrl, config.target.supabaseServiceKey);
  const targetClient = new Client(config.target.database);
  
  try {
    log('Connecting to target database...');
    await targetClient.connect();
    
    log('Setting up PGMQ extension and queues...');

    // Check if PGMQ extension is available
    log('Checking PGMQ extension availability...');
    const extensionQuery = `
      SELECT name, default_version, installed_version
      FROM pg_available_extensions
      WHERE name = 'pgmq';
    `;
    
    const extensionResult = await targetClient.query(extensionQuery);
    
    if (extensionResult.rows.length === 0) {
      throw new Error('PGMQ extension is not available on the target instance. Please install it first.');
    }

    const extension = extensionResult.rows[0];
    log(`PGMQ extension found: version ${extension.default_version}`);

    // Install PGMQ extension if not already installed
    if (!extension.installed_version) {
      log('Installing PGMQ extension...');
      await targetClient.query('CREATE EXTENSION IF NOT EXISTS pgmq;');
      log('PGMQ extension installed successfully');
    } else {
      log(`PGMQ extension already installed: version ${extension.installed_version}`);
    }

    // Create PGMQ queues
    log('Creating PGMQ queues...');
    
    for (const queueName of config.migration.pgmqQueues) {
      try {
        log(`Creating queue: ${queueName}`);
        
        // Check if queue already exists
        const { data: queueExists, error: checkError } = await targetSupabase
          .rpc('pgmq_list_queues');
        
        if (checkError) {
          log(`Warning: Could not check existing queues: ${checkError.message}`, 'warn');
        }

        const existingQueues = queueExists || [];
        const queueAlreadyExists = existingQueues.some(q => q.queue_name === queueName);

        if (queueAlreadyExists) {
          log(`Queue ${queueName} already exists`);
          continue;
        }

        // Create the queue
        const { error: createError } = await targetSupabase
          .rpc('pgmq_create', { queue_name: queueName });

        if (createError) {
          log(`Error creating queue ${queueName}: ${createError.message}`, 'error');
          continue;
        }

        log(`Successfully created queue: ${queueName}`);

        // Test the queue by sending a test message
        const testMessage = {
          test: true,
          created_at: new Date().toISOString(),
          message: 'PGMQ migration test message'
        };

        const { data: sendResult, error: sendError } = await targetSupabase
          .rpc('pgmq_send', {
            queue_name: queueName,
            message: testMessage
          });

        if (sendError) {
          log(`Warning: Could not send test message to ${queueName}: ${sendError.message}`, 'warn');
        } else {
          log(`Test message sent to ${queueName}, message ID: ${sendResult}`);
          
          // Read and archive the test message
          const { data: readResult, error: readError } = await targetSupabase
            .rpc('pgmq_read', {
              queue_name: queueName,
              visibility_timeout: 30,
              quantity: 1
            });

          if (!readError && readResult && readResult.length > 0) {
            const testMsg = readResult[0];
            
            // Archive the test message
            await targetSupabase.rpc('pgmq_archive', {
              queue_name: queueName,
              msg_id: testMsg.msg_id
            });
            
            log(`Test message processed and archived for ${queueName}`);
          }
        }

      } catch (error) {
        log(`Error setting up queue ${queueName}: ${error.message}`, 'error');
      }
    }

    // Set up PGMQ functions that might be needed
    log('Setting up additional PGMQ helper functions...');
    
    const helperFunctions = [
      {
        name: 'get_queue_metrics',
        sql: `
          CREATE OR REPLACE FUNCTION get_queue_metrics(queue_name TEXT)
          RETURNS TABLE(
            queue_name TEXT,
            queue_length BIGINT,
            newest_msg_age_sec INTEGER,
            oldest_msg_age_sec INTEGER,
            total_messages BIGINT
          ) AS $$
          BEGIN
            RETURN QUERY
            SELECT 
              $1::TEXT as queue_name,
              pgmq.queue_length($1) as queue_length,
              pgmq.newest_msg_age_sec($1) as newest_msg_age_sec,
              pgmq.oldest_msg_age_sec($1) as oldest_msg_age_sec,
              pgmq.total_messages($1) as total_messages;
          END;
          $$ LANGUAGE plpgsql;
        `
      }
    ];

    for (const func of helperFunctions) {
      try {
        await targetClient.query(func.sql);
        log(`Created helper function: ${func.name}`);
      } catch (error) {
        log(`Warning: Could not create helper function ${func.name}: ${error.message}`, 'warn');
      }
    }

    // Verify PGMQ setup
    log('Verifying PGMQ setup...');
    
    try {
      const { data: queues, error: listError } = await targetSupabase
        .rpc('pgmq_list_queues');

      if (listError) {
        log(`Warning: Could not list queues: ${listError.message}`, 'warn');
      } else {
        log(`PGMQ setup verified. Available queues: ${queues.map(q => q.queue_name).join(', ')}`);
      }

      // Test metrics function
      for (const queueName of config.migration.pgmqQueues) {
        try {
          const { data: metrics, error: metricsError } = await targetSupabase
            .rpc('pgmq_metrics', { queue_name: queueName });

          if (metricsError) {
            log(`Warning: Could not get metrics for ${queueName}: ${metricsError.message}`, 'warn');
          } else {
            log(`Queue ${queueName} metrics: ${JSON.stringify(metrics)}`);
          }
        } catch (error) {
          log(`Warning: Error getting metrics for ${queueName}: ${error.message}`, 'warn');
        }
      }

    } catch (error) {
      log(`Warning: Could not verify PGMQ setup: ${error.message}`, 'warn');
    }

    log('PGMQ migration completed successfully!');
    
  } catch (error) {
    log(`Error migrating PGMQ: ${error.message}`, 'error');
    throw error;
  } finally {
    await targetClient.end();
  }
}

// Run the migration
if (require.main === module) {
  migratePGMQ()
    .then(() => {
      log('PGMQ migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      log(`PGMQ migration failed: ${error.message}`, 'error');
      process.exit(1);
    });
}

module.exports = { migratePGMQ };
