#!/usr/bin/env node

/**
 * Export Database Schema from Source Supabase Instance
 * 
 * This script exports the complete database schema including:
 * - Tables, indexes, constraints
 * - Functions and triggers
 * - RLS policies
 * - Custom types and enums
 */

const fs = require('fs');
const path = require('path');
const { Client } = require('pg');
const config = require('../config');

// Create logs directory if it doesn't exist
const logsDir = path.dirname(config.logging.logFile);
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Logging function
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(config.logging.logFile, logMessage + '\n');
}

async function exportSchema() {
  const client = new Client(config.source.database);
  
  try {
    log('Connecting to source database...');
    await client.connect();
    
    log('Exporting database schema...');
    
    // Create export directory
    const exportDir = path.join(__dirname, '../exports');
    if (!fs.existsSync(exportDir)) {
      fs.mkdirSync(exportDir, { recursive: true });
    }

    // Export custom types and enums
    log('Exporting custom types and enums...');
    const typesQuery = `
      SELECT 
        t.typname,
        t.typtype,
        array_agg(e.enumlabel ORDER BY e.enumsortorder) as enum_values
      FROM pg_type t
      LEFT JOIN pg_enum e ON t.oid = e.enumtypid
      WHERE t.typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
        AND t.typtype = 'e'
      GROUP BY t.typname, t.typtype
      ORDER BY t.typname;
    `;
    
    const typesResult = await client.query(typesQuery);
    let typesSQL = '-- Custom Types and Enums\n';
    
    for (const row of typesResult.rows) {
      if (row.enum_values && row.enum_values.length > 0) {
        const enumValues = row.enum_values.map(v => `'${v}'`).join(', ');
        typesSQL += `CREATE TYPE ${row.typname} AS ENUM (${enumValues});\n`;
      }
    }
    
    fs.writeFileSync(path.join(exportDir, '01-types.sql'), typesSQL);
    log(`Exported ${typesResult.rows.length} custom types`);

    // Export table schemas
    log('Exporting table schemas...');
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `;
    
    const tablesResult = await client.query(tablesQuery);
    let tablesSQL = '-- Table Schemas\n';
    
    for (const table of tablesResult.rows) {
      const tableName = table.table_name;
      
      // Get table definition
      const tableDefQuery = `
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default,
          character_maximum_length,
          numeric_precision,
          numeric_scale
        FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = $1
        ORDER BY ordinal_position;
      `;
      
      const tableDefResult = await client.query(tableDefQuery, [tableName]);
      
      tablesSQL += `\n-- Table: ${tableName}\n`;
      tablesSQL += `CREATE TABLE IF NOT EXISTS ${tableName} (\n`;
      
      const columns = tableDefResult.rows.map(col => {
        let columnDef = `  ${col.column_name} ${col.data_type}`;
        
        if (col.character_maximum_length) {
          columnDef += `(${col.character_maximum_length})`;
        } else if (col.numeric_precision) {
          columnDef += `(${col.numeric_precision}`;
          if (col.numeric_scale) {
            columnDef += `,${col.numeric_scale}`;
          }
          columnDef += ')';
        }
        
        if (col.is_nullable === 'NO') {
          columnDef += ' NOT NULL';
        }
        
        if (col.column_default) {
          columnDef += ` DEFAULT ${col.column_default}`;
        }
        
        return columnDef;
      });
      
      tablesSQL += columns.join(',\n') + '\n);\n';
    }
    
    fs.writeFileSync(path.join(exportDir, '02-tables.sql'), tablesSQL);
    log(`Exported ${tablesResult.rows.length} table schemas`);

    // Export constraints and indexes
    log('Exporting constraints and indexes...');
    const constraintsQuery = `
      SELECT 
        tc.table_name,
        tc.constraint_name,
        tc.constraint_type,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM information_schema.table_constraints tc
      LEFT JOIN information_schema.key_column_usage kcu
        ON tc.constraint_name = kcu.constraint_name
      LEFT JOIN information_schema.constraint_column_usage ccu
        ON ccu.constraint_name = tc.constraint_name
      WHERE tc.table_schema = 'public'
      ORDER BY tc.table_name, tc.constraint_type;
    `;
    
    const constraintsResult = await client.query(constraintsQuery);
    let constraintsSQL = '-- Constraints and Indexes\n';
    
    // Group constraints by table and type
    const constraintsByTable = {};
    for (const row of constraintsResult.rows) {
      if (!constraintsByTable[row.table_name]) {
        constraintsByTable[row.table_name] = {};
      }
      if (!constraintsByTable[row.table_name][row.constraint_type]) {
        constraintsByTable[row.table_name][row.constraint_type] = [];
      }
      constraintsByTable[row.table_name][row.constraint_type].push(row);
    }
    
    // Generate constraint SQL
    for (const [tableName, constraints] of Object.entries(constraintsByTable)) {
      constraintsSQL += `\n-- Constraints for ${tableName}\n`;
      
      for (const [constraintType, constraintList] of Object.entries(constraints)) {
        for (const constraint of constraintList) {
          if (constraintType === 'PRIMARY KEY') {
            constraintsSQL += `ALTER TABLE ${tableName} ADD CONSTRAINT ${constraint.constraint_name} PRIMARY KEY (${constraint.column_name});\n`;
          } else if (constraintType === 'FOREIGN KEY') {
            constraintsSQL += `ALTER TABLE ${tableName} ADD CONSTRAINT ${constraint.constraint_name} FOREIGN KEY (${constraint.column_name}) REFERENCES ${constraint.foreign_table_name}(${constraint.foreign_column_name});\n`;
          } else if (constraintType === 'UNIQUE') {
            constraintsSQL += `ALTER TABLE ${tableName} ADD CONSTRAINT ${constraint.constraint_name} UNIQUE (${constraint.column_name});\n`;
          }
        }
      }
    }
    
    fs.writeFileSync(path.join(exportDir, '03-constraints.sql'), constraintsSQL);
    log(`Exported constraints for ${Object.keys(constraintsByTable).length} tables`);

    // Export functions and triggers
    log('Exporting functions and triggers...');
    const functionsQuery = `
      SELECT 
        p.proname as function_name,
        pg_get_functiondef(p.oid) as function_definition
      FROM pg_proc p
      JOIN pg_namespace n ON p.pronamespace = n.oid
      WHERE n.nspname = 'public'
      ORDER BY p.proname;
    `;
    
    const functionsResult = await client.query(functionsQuery);
    let functionsSQL = '-- Functions and Triggers\n';
    
    for (const func of functionsResult.rows) {
      functionsSQL += `\n-- Function: ${func.function_name}\n`;
      functionsSQL += func.function_definition + ';\n';
    }
    
    fs.writeFileSync(path.join(exportDir, '04-functions.sql'), functionsSQL);
    log(`Exported ${functionsResult.rows.length} functions`);

    log('Schema export completed successfully!');
    
  } catch (error) {
    log(`Error exporting schema: ${error.message}`, 'error');
    throw error;
  } finally {
    await client.end();
  }
}

// Run the export
if (require.main === module) {
  exportSchema()
    .then(() => {
      log('Schema export completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      log(`Schema export failed: ${error.message}`, 'error');
      process.exit(1);
    });
}

module.exports = { exportSchema };
