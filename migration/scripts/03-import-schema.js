#!/usr/bin/env node

/**
 * Import Database Schema to Target Supabase Instance
 * 
 * This script imports the complete database schema including:
 * - Custom types and enums
 * - Tables and columns
 * - Constraints and indexes
 * - Functions and triggers
 */

const fs = require('fs');
const path = require('path');
const { Client } = require('pg');
const config = require('../config');

// Logging function
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(config.logging.logFile, logMessage + '\n');
}

async function importSchema() {
  const client = new Client(config.target.database);
  
  try {
    log('Connecting to target database...');
    await client.connect();
    
    log('Importing database schema...');
    
    const exportDir = path.join(__dirname, '../exports');
    
    // Check if export files exist
    const requiredFiles = [
      '01-types.sql',
      '02-tables.sql',
      '03-constraints.sql',
      '04-functions.sql'
    ];
    
    for (const file of requiredFiles) {
      const filePath = path.join(exportDir, file);
      if (!fs.existsSync(filePath)) {
        throw new Error(`Required export file not found: ${file}`);
      }
    }

    // Enable required extensions
    log('Enabling required extensions...');
    await client.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
    await client.query('CREATE EXTENSION IF NOT EXISTS "pgcrypto";');
    
    // Try to enable PGMQ extension
    try {
      await client.query('CREATE EXTENSION IF NOT EXISTS "pgmq";');
      log('PGMQ extension enabled');
    } catch (error) {
      log(`Warning: Could not enable PGMQ extension: ${error.message}`, 'warn');
      log('You may need to install PGMQ manually on the target instance', 'warn');
    }

    // Import in order
    const importOrder = [
      { file: '01-types.sql', description: 'custom types and enums' },
      { file: '02-tables.sql', description: 'table schemas' },
      { file: '03-constraints.sql', description: 'constraints and indexes' },
      { file: '04-functions.sql', description: 'functions and triggers' }
    ];

    for (const item of importOrder) {
      log(`Importing ${item.description}...`);
      
      const filePath = path.join(exportDir, item.file);
      const sql = fs.readFileSync(filePath, 'utf8');
      
      if (sql.trim().length === 0) {
        log(`File ${item.file} is empty, skipping...`);
        continue;
      }

      try {
        // Split SQL into individual statements and execute them
        const statements = sql
          .split(';')
          .map(stmt => stmt.trim())
          .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

        for (const statement of statements) {
          if (statement.trim()) {
            await client.query(statement);
          }
        }
        
        log(`Successfully imported ${item.description}`);
        
      } catch (error) {
        log(`Error importing ${item.description}: ${error.message}`, 'error');
        
        // For some errors, we might want to continue
        if (error.message.includes('already exists')) {
          log(`Some objects already exist, continuing...`, 'warn');
        } else {
          throw error;
        }
      }
    }

    // Create indexes that might be missing
    log('Creating additional indexes...');
    const additionalIndexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);',
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);',
      'CREATE INDEX IF NOT EXISTS idx_users_auth_user_id ON users(auth_user_id);',
      'CREATE INDEX IF NOT EXISTS idx_users_flat_id ON users(flat_id);',
      'CREATE INDEX IF NOT EXISTS idx_houses_street_id ON houses(street_id);',
      'CREATE INDEX IF NOT EXISTS idx_flats_house_id ON flats(house_id);',
      'CREATE INDEX IF NOT EXISTS idx_announcements_sender_id ON announcements(sender_id);',
      'CREATE INDEX IF NOT EXISTS idx_announcements_status ON announcements(is_draft, sent_at);',
      'CREATE INDEX IF NOT EXISTS idx_polls_creator_id ON polls(creator_id);',
      'CREATE INDEX IF NOT EXISTS idx_polls_status ON polls(status);',
      'CREATE INDEX IF NOT EXISTS idx_email_queue_status ON email_queue(status, scheduled_for);',
      'CREATE INDEX IF NOT EXISTS idx_email_queue_entity ON email_queue(entity_type, entity_id);',
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_entity ON audit_logs(entity_type, entity_id);'
    ];

    for (const indexSQL of additionalIndexes) {
      try {
        await client.query(indexSQL);
      } catch (error) {
        log(`Warning: Could not create index: ${error.message}`, 'warn');
      }
    }

    // Enable Row Level Security on tables
    log('Enabling Row Level Security...');
    const rlsTables = [
      'users',
      'emergency_contacts',
      'announcements',
      'polls',
      'contact_messages',
      'feedback',
      'email_queue'
    ];

    for (const table of rlsTables) {
      try {
        await client.query(`ALTER TABLE ${table} ENABLE ROW LEVEL SECURITY;`);
      } catch (error) {
        log(`Warning: Could not enable RLS on ${table}: ${error.message}`, 'warn');
      }
    }

    // Create update triggers
    log('Creating update triggers...');
    const updateTriggers = [
      'users',
      'announcements',
      'polls',
      'email_queue',
      'admin_settings',
      'smtp_settings'
    ];

    for (const table of updateTriggers) {
      try {
        const triggerSQL = `
          CREATE TRIGGER update_${table}_updated_at 
          BEFORE UPDATE ON ${table}
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        `;
        await client.query(triggerSQL);
      } catch (error) {
        log(`Warning: Could not create trigger for ${table}: ${error.message}`, 'warn');
      }
    }

    log('Schema import completed successfully!');
    
  } catch (error) {
    log(`Error importing schema: ${error.message}`, 'error');
    throw error;
  } finally {
    await client.end();
  }
}

// Run the import
if (require.main === module) {
  importSchema()
    .then(() => {
      log('Schema import completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      log(`Schema import failed: ${error.message}`, 'error');
      process.exit(1);
    });
}

module.exports = { importSchema };
