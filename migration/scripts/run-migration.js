#!/usr/bin/env node

/**
 * Complete Migration Runner
 * 
 * This script runs the complete migration process in the correct order
 */

const fs = require('fs');
const path = require('path');
const config = require('../config');

// Import all migration scripts
const { exportSchema } = require('./01-export-schema');
const { exportData } = require('./02-export-data');
const { importSchema } = require('./03-import-schema');
const { importData } = require('./04-import-data');
const { importAuth } = require('./05-import-auth');
const { migratePGMQ } = require('./06-migrate-pgmq');
const { verifyMigration } = require('./07-verify-migration');

// Create logs directory if it doesn't exist
const logsDir = path.dirname(config.logging.logFile);
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Logging function
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(config.logging.logFile, logMessage + '\n');
}

async function runCompleteMigration() {
  const startTime = new Date();
  log('🚀 Starting complete DNSB Vakarai Supabase migration...');
  
  try {
    // Validate configuration
    log('Validating migration configuration...');
    validateConfig();
    
    // Phase 1: Export from source
    log('📤 Phase 1: Exporting from source instance...');
    
    log('Step 1.1: Exporting database schema...');
    await exportSchema();
    
    log('Step 1.2: Exporting application data...');
    await exportData();
    
    // Phase 2: Import to target
    log('📥 Phase 2: Importing to target instance...');
    
    log('Step 2.1: Importing database schema...');
    await importSchema();
    
    log('Step 2.2: Importing application data...');
    await importData();
    
    log('Step 2.3: Importing authentication data...');
    await importAuth();
    
    // Phase 3: Configure services
    log('⚙️  Phase 3: Configuring services...');
    
    log('Step 3.1: Setting up PGMQ...');
    await migratePGMQ();
    
    // Phase 4: Verification
    log('✅ Phase 4: Verifying migration...');
    
    log('Step 4.1: Running verification checks...');
    const verificationResults = await verifyMigration();
    
    // Migration summary
    const endTime = new Date();
    const duration = Math.round((endTime - startTime) / 1000);
    
    log('🎉 Migration completed successfully!');
    log(`Total duration: ${duration} seconds`);
    log(`Overall status: ${verificationResults.overall}`);
    
    // Print next steps
    printNextSteps(verificationResults);
    
    return verificationResults;
    
  } catch (error) {
    const endTime = new Date();
    const duration = Math.round((endTime - startTime) / 1000);
    
    log(`❌ Migration failed after ${duration} seconds: ${error.message}`, 'error');
    log('Check the logs for detailed error information.', 'error');
    
    // Print rollback instructions
    printRollbackInstructions();
    
    throw error;
  }
}

function validateConfig() {
  const requiredSourceFields = [
    'source.supabaseUrl',
    'source.supabaseServiceKey',
    'source.database.host',
    'source.database.password'
  ];
  
  const requiredTargetFields = [
    'target.supabaseUrl',
    'target.supabaseServiceKey',
    'target.database.host',
    'target.database.password'
  ];
  
  const allRequired = [...requiredSourceFields, ...requiredTargetFields];
  
  for (const field of allRequired) {
    const value = getNestedValue(config, field);
    if (!value || value.includes('your-') || value.includes('placeholder')) {
      throw new Error(`Configuration field '${field}' is not properly set. Please update migration/config.js`);
    }
  }
  
  log('✓ Configuration validation passed');
}

function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current && current[key], obj);
}

function printNextSteps(verificationResults) {
  console.log('\n' + '='.repeat(60));
  console.log('🎯 NEXT STEPS');
  console.log('='.repeat(60));
  
  if (verificationResults.overall === 'SUCCESS') {
    console.log('✅ Migration completed successfully! Next steps:');
    console.log('');
    console.log('1. 🔧 Update your application environment variables:');
    console.log(`   - NEXT_PUBLIC_SUPABASE_URL=${config.target.supabaseUrl}`);
    console.log(`   - NEXT_PUBLIC_SUPABASE_ANON_KEY=${config.target.supabaseAnonKey}`);
    console.log(`   - SUPABASE_SERVICE_ROLE_KEY=${config.target.supabaseServiceKey}`);
    console.log('');
    console.log('2. 🌐 Update DNS/domain configuration (if applicable)');
    console.log('');
    console.log('3. 🧪 Test your application thoroughly:');
    console.log('   - User authentication');
    console.log('   - Email functionality');
    console.log('   - Admin features');
    console.log('   - Data integrity');
    console.log('');
    console.log('4. 📧 Deploy edge functions:');
    console.log('   supabase functions deploy --project-ref YOUR_NEW_PROJECT_REF');
    console.log('');
    console.log('5. 🗑️  After 48-72 hours of successful operation:');
    console.log('   - Decommission the old Supabase instance');
    console.log('   - Update documentation');
    
  } else if (verificationResults.overall === 'MINOR_ISSUES') {
    console.log('⚠️  Migration completed with minor issues. Next steps:');
    console.log('');
    console.log('1. 🔍 Review the verification report for details');
    console.log('2. 🛠️  Address minor issues (usually non-critical)');
    console.log('3. 🧪 Test your application');
    console.log('4. 📝 Update environment variables when ready');
    
  } else {
    console.log('❌ Migration completed with critical issues. Next steps:');
    console.log('');
    console.log('1. 🔍 Review the verification report and logs');
    console.log('2. 🛠️  Fix critical issues before proceeding');
    console.log('3. 🔄 Consider re-running specific migration steps');
    console.log('4. 📞 Contact support if needed');
  }
  
  console.log('');
  console.log('📊 Verification report saved to: migration/reports/');
  console.log('📝 Full logs available at:', config.logging.logFile);
  console.log('='.repeat(60));
}

function printRollbackInstructions() {
  console.log('\n' + '='.repeat(60));
  console.log('🔄 ROLLBACK INSTRUCTIONS');
  console.log('='.repeat(60));
  console.log('If you need to rollback:');
  console.log('');
  console.log('1. 🔧 Revert environment variables to source instance');
  console.log('2. 🌐 Revert DNS/domain changes (if made)');
  console.log('3. 🗑️  Clean up target instance (optional)');
  console.log('4. 📝 Keep source instance running until issues are resolved');
  console.log('');
  console.log('⚠️  DO NOT decommission source instance until migration is verified!');
  console.log('='.repeat(60));
}

// Run the complete migration
if (require.main === module) {
  // Check for command line arguments
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('DNSB Vakarai Supabase Migration Tool');
    console.log('');
    console.log('Usage: node run-migration.js [options]');
    console.log('');
    console.log('Options:');
    console.log('  --help, -h     Show this help message');
    console.log('  --dry-run      Validate configuration without running migration');
    console.log('  --verify-only  Only run verification (assumes migration already done)');
    console.log('');
    console.log('Before running:');
    console.log('1. Update migration/config.js with your source and target details');
    console.log('2. Ensure both Supabase instances are accessible');
    console.log('3. Backup your source instance');
    console.log('');
    process.exit(0);
  }
  
  if (args.includes('--dry-run')) {
    console.log('🧪 Running configuration validation (dry run)...');
    try {
      validateConfig();
      console.log('✅ Configuration is valid. Ready for migration.');
      process.exit(0);
    } catch (error) {
      console.error('❌ Configuration validation failed:', error.message);
      process.exit(1);
    }
  }
  
  if (args.includes('--verify-only')) {
    console.log('🔍 Running verification only...');
    verifyMigration()
      .then((results) => {
        console.log(`Verification completed with status: ${results.overall}`);
        process.exit(results.overall === 'SUCCESS' ? 0 : 1);
      })
      .catch((error) => {
        console.error('Verification failed:', error.message);
        process.exit(1);
      });
    return;
  }
  
  // Run complete migration
  runCompleteMigration()
    .then((results) => {
      process.exit(results.overall === 'SUCCESS' ? 0 : 1);
    })
    .catch((error) => {
      process.exit(1);
    });
}

module.exports = { runCompleteMigration };
