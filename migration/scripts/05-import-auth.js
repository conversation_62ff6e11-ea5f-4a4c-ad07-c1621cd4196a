#!/usr/bin/env node

/**
 * Import Authentication Data to Target Supabase Instance
 * 
 * This script imports auth users and links them with application users
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
const { Client } = require('pg');
const config = require('../config');

// Logging function
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(config.logging.logFile, logMessage + '\n');
}

async function importAuth() {
  const sourceSupabase = createClient(config.source.supabaseUrl, config.source.supabaseServiceKey);
  const targetSupabase = createClient(config.target.supabaseUrl, config.target.supabaseServiceKey);
  const targetClient = new Client(config.target.database);
  
  try {
    log('Connecting to target database...');
    await targetClient.connect();
    
    log('Starting authentication data migration...');

    // Get all users from source that have auth_user_id
    log('Fetching users with authentication from source...');
    const { data: sourceUsers, error: sourceError } = await sourceSupabase
      .from('users')
      .select('*')
      .not('auth_user_id', 'is', null);

    if (sourceError) {
      throw new Error(`Error fetching source users: ${sourceError.message}`);
    }

    log(`Found ${sourceUsers.length} users with authentication to migrate`);

    // Get auth users from source
    const authUserIds = sourceUsers.map(user => user.auth_user_id);
    const authUsers = [];
    
    // Fetch auth users in batches (Supabase admin API limitation)
    for (const authUserId of authUserIds) {
      try {
        const { data: authUser, error: authError } = await sourceSupabase.auth.admin.getUserById(authUserId);
        
        if (authError) {
          log(`Warning: Could not fetch auth user ${authUserId}: ${authError.message}`, 'warn');
          continue;
        }
        
        if (authUser.user) {
          authUsers.push(authUser.user);
        }
      } catch (error) {
        log(`Warning: Error fetching auth user ${authUserId}: ${error.message}`, 'warn');
      }
    }

    log(`Successfully fetched ${authUsers.length} auth users from source`);

    // Create auth users in target and update application users
    let migratedCount = 0;
    let errorCount = 0;

    for (const sourceUser of sourceUsers) {
      try {
        log(`Migrating user: ${sourceUser.username} (${sourceUser.email})`);
        
        // Find corresponding auth user
        const authUser = authUsers.find(au => au.id === sourceUser.auth_user_id);
        
        if (!authUser) {
          log(`Warning: Auth user not found for ${sourceUser.username}`, 'warn');
          continue;
        }

        // Generate auth email (same format as in your app)
        const authEmail = sourceUser.email || `${sourceUser.username.toLowerCase().replace('-', '_')}@dnsb.local`;

        // Check if user already exists in target auth
        let targetAuthUser = null;
        try {
          const { data: existingUser } = await targetSupabase.auth.admin.getUserById(authUser.id);
          if (existingUser.user) {
            targetAuthUser = existingUser.user;
            log(`Auth user already exists in target: ${authEmail}`);
          }
        } catch (error) {
          // User doesn't exist, we'll create it
        }

        // Create auth user in target if it doesn't exist
        if (!targetAuthUser) {
          const { data: newAuthUser, error: createError } = await targetSupabase.auth.admin.createUser({
            id: authUser.id, // Preserve the same ID
            email: authEmail,
            password: 'temp-password-' + Math.random().toString(36).substring(7), // Temporary password
            email_confirm: true,
            user_metadata: {
              username: sourceUser.username,
              name: sourceUser.name,
              user_id: sourceUser.id,
              migrated: true,
              migrated_at: new Date().toISOString()
            }
          });

          if (createError) {
            log(`Error creating auth user for ${sourceUser.username}: ${createError.message}`, 'error');
            errorCount++;
            continue;
          }

          targetAuthUser = newAuthUser.user;
          log(`Created auth user in target: ${authEmail}`);
        }

        // Update the application user in target to link with auth user
        const { error: updateError } = await targetSupabase
          .from('users')
          .update({ 
            auth_user_id: targetAuthUser.id,
            email: authEmail // Ensure email is set
          })
          .eq('id', sourceUser.id);

        if (updateError) {
          log(`Error updating application user ${sourceUser.username}: ${updateError.message}`, 'error');
          errorCount++;
          continue;
        }

        migratedCount++;
        log(`Successfully migrated user: ${sourceUser.username}`);
        
      } catch (error) {
        log(`Error migrating user ${sourceUser.username}: ${error.message}`, 'error');
        errorCount++;
      }
    }

    // Create RLS policies for auth integration
    log('Creating RLS policies for auth integration...');
    
    const rlsPolicies = [
      {
        name: 'users_select_own',
        table: 'users',
        operation: 'SELECT',
        policy: 'auth.uid() = auth_user_id'
      },
      {
        name: 'users_update_own',
        table: 'users', 
        operation: 'UPDATE',
        policy: 'auth.uid() = auth_user_id'
      },
      {
        name: 'users_admin_all',
        table: 'users',
        operation: 'ALL',
        policy: `EXISTS (
          SELECT 1 FROM users
          WHERE auth_user_id = auth.uid()
          AND role IN ('developer', 'super_admin', 'editor')
        )`
      }
    ];

    for (const policy of rlsPolicies) {
      try {
        // Drop policy if it exists
        await targetClient.query(`DROP POLICY IF EXISTS ${policy.name} ON ${policy.table};`);
        
        // Create new policy
        const policySQL = `
          CREATE POLICY ${policy.name} ON ${policy.table}
          FOR ${policy.operation} USING (${policy.policy});
        `;
        
        await targetClient.query(policySQL);
        log(`Created RLS policy: ${policy.name}`);
        
      } catch (error) {
        log(`Warning: Could not create RLS policy ${policy.name}: ${error.message}`, 'warn');
      }
    }

    log(`Authentication migration completed!`);
    log(`Successfully migrated: ${migratedCount} users`);
    log(`Errors encountered: ${errorCount} users`);
    
    if (errorCount > 0) {
      log('Some users could not be migrated. Check the logs for details.', 'warn');
      log('You may need to manually create auth accounts for these users.', 'warn');
    }
    
  } catch (error) {
    log(`Error importing auth data: ${error.message}`, 'error');
    throw error;
  } finally {
    await targetClient.end();
  }
}

// Run the import
if (require.main === module) {
  importAuth()
    .then(() => {
      log('Auth import completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      log(`Auth import failed: ${error.message}`, 'error');
      process.exit(1);
    });
}

module.exports = { importAuth };
