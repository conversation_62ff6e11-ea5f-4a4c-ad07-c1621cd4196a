# DNSB Vakarai Supabase Migration Checklist

## Pre-Migration Checklist

### 1. Preparation (30 minutes)
- [ ] **Backup source instance**
  - [ ] Create database dump: `pg_dump -h 91.99.129.9 -p 54322 -U postgres -d postgres > backup_$(date +%Y%m%d_%H%M%S).sql`
  - [ ] Export environment variables from current deployment
  - [ ] Document current Supabase dashboard settings
  - [ ] Take screenshots of auth settings, email templates, etc.

- [ ] **Prepare target instance**
  - [ ] New Supabase instance is running and accessible
  - [ ] PGMQ extension is available (check with your Supabase provider)
  - [ ] Database credentials are available
  - [ ] Service role key has been generated

- [ ] **Setup migration environment**
  - [ ] Clone/update DNSB Vakarai repository
  - [ ] Navigate to migration directory: `cd migration/`
  - [ ] Install dependencies: `npm install`
  - [ ] Copy environment template: `cp .env.template .env`
  - [ ] Update `.env` with actual source and target credentials
  - [ ] Test configuration: `npm run migrate:dry-run`

### 2. Validation (15 minutes)
- [ ] **Source instance health check**
  - [ ] Application is running normally
  - [ ] Database is accessible
  - [ ] No ongoing maintenance or heavy operations
  - [ ] Email queue is not heavily loaded

- [ ] **Target instance readiness**
  - [ ] Instance is accessible via provided URL
  - [ ] Database connection works
  - [ ] Service role key has proper permissions
  - [ ] PGMQ extension is available

- [ ] **Network connectivity**
  - [ ] Migration machine can access both source and target
  - [ ] No firewall restrictions
  - [ ] Stable internet connection

## Migration Execution

### Phase 1: Data Export (15-30 minutes)
- [ ] **Export database schema**
  ```bash
  npm run export:schema
  ```
  - [ ] Check `migration/exports/01-types.sql` exists
  - [ ] Check `migration/exports/02-tables.sql` exists
  - [ ] Check `migration/exports/03-constraints.sql` exists
  - [ ] Check `migration/exports/04-functions.sql` exists

- [ ] **Export application data**
  ```bash
  npm run export:data
  ```
  - [ ] Check data files in `migration/exports/05-data-*.sql`
  - [ ] Verify row counts in logs match expectations
  - [ ] Check for any export errors in logs

### Phase 2: Schema Import (10 minutes)
- [ ] **Import database schema**
  ```bash
  npm run import:schema
  ```
  - [ ] Verify no critical errors in logs
  - [ ] Check that all tables were created
  - [ ] Verify indexes and constraints were applied

### Phase 3: Data Import (20-45 minutes)
- [ ] **Import application data**
  ```bash
  npm run import:data
  ```
  - [ ] Monitor progress in logs
  - [ ] Verify row counts match source
  - [ ] Check for constraint violations or errors

### Phase 4: Authentication Migration (15 minutes)
- [ ] **Import authentication data**
  ```bash
  npm run import:auth
  ```
  - [ ] Verify auth users were created
  - [ ] Check auth_user_id links are correct
  - [ ] Test a few user authentications

### Phase 5: Service Configuration (10 minutes)
- [ ] **Setup PGMQ**
  ```bash
  npm run migrate:pgmq
  ```
  - [ ] Verify PGMQ extension is installed
  - [ ] Check email_queue was created
  - [ ] Test queue functionality

### Phase 6: Verification (15 minutes)
- [ ] **Run verification checks**
  ```bash
  npm run verify
  ```
  - [ ] Review verification report
  - [ ] Address any critical issues
  - [ ] Document any minor issues for later

## Post-Migration Tasks

### 1. Manual Configuration (30 minutes)
- [ ] **Supabase Dashboard Settings**
  - [ ] Configure auth providers (if any)
  - [ ] Set up email templates
  - [ ] Configure storage buckets (if needed)
  - [ ] Set up webhooks (if any)

- [ ] **Edge Functions Deployment**
  ```bash
  supabase functions deploy --project-ref YOUR_NEW_PROJECT_REF
  ```
  - [ ] Deploy process-emails function
  - [ ] Test function execution
  - [ ] Verify function logs

### 2. Application Configuration (15 minutes)
- [ ] **Update environment variables**
  - [ ] `NEXT_PUBLIC_SUPABASE_URL`
  - [ ] `NEXT_PUBLIC_SUPABASE_ANON_KEY`
  - [ ] `SUPABASE_SERVICE_ROLE_KEY`

- [ ] **Deploy application**
  - [ ] Update Coolify environment variables
  - [ ] Trigger new deployment
  - [ ] Monitor deployment logs

### 3. DNS/Domain Configuration (if applicable)
- [ ] **Update DNS records**
  - [ ] Point domain to new Supabase instance
  - [ ] Update SSL certificates if needed
  - [ ] Test domain resolution

### 4. Testing & Verification (45 minutes)
- [ ] **Authentication testing**
  - [ ] Test user login with username
  - [ ] Test user login with email
  - [ ] Test admin functions
  - [ ] Verify user roles and permissions

- [ ] **Application functionality**
  - [ ] Create test announcement
  - [ ] Send test email
  - [ ] Test contact form
  - [ ] Test user management
  - [ ] Test polls functionality

- [ ] **Email system testing**
  - [ ] Check SMTP settings in admin
  - [ ] Send test email
  - [ ] Verify email queue processing
  - [ ] Check email delivery

- [ ] **Data integrity checks**
  - [ ] Verify user data is complete
  - [ ] Check announcement history
  - [ ] Verify contact messages
  - [ ] Check admin settings

## Rollback Plan (if needed)

### Immediate Rollback (5 minutes)
- [ ] **Revert environment variables**
  - [ ] Change back to source Supabase URLs
  - [ ] Redeploy application
  - [ ] Verify application is working

### DNS Rollback (if applicable)
- [ ] **Revert DNS changes**
  - [ ] Point domain back to source instance
  - [ ] Wait for DNS propagation
  - [ ] Test domain access

## Post-Migration Monitoring

### First 24 Hours
- [ ] **Monitor application logs**
  - [ ] Check for authentication errors
  - [ ] Monitor email delivery
  - [ ] Watch for database errors
  - [ ] Check user activity

- [ ] **Performance monitoring**
  - [ ] Monitor response times
  - [ ] Check database performance
  - [ ] Monitor email queue processing
  - [ ] Watch resource usage

### First Week
- [ ] **User feedback**
  - [ ] Monitor user reports
  - [ ] Check for login issues
  - [ ] Verify email delivery
  - [ ] Test all major features

- [ ] **Data consistency**
  - [ ] Compare data between instances
  - [ ] Check for any missing records
  - [ ] Verify all integrations work

## Cleanup (After 1 Week)

### Source Instance Decommission
- [ ] **Final verification**
  - [ ] Confirm all data migrated correctly
  - [ ] Verify no ongoing dependencies
  - [ ] Check all users can access new instance

- [ ] **Cleanup**
  - [ ] Export final backup of source
  - [ ] Document migration completion
  - [ ] Decommission source instance
  - [ ] Update documentation

## Emergency Contacts

- **Technical Lead**: [Your contact]
- **Database Admin**: [Your contact]
- **Supabase Support**: [Support contact]
- **Infrastructure Team**: [Your contact]

## Notes Section

Use this space to document any issues, decisions, or important information during the migration:

```
Date: ___________
Issue: 
Resolution:

Date: ___________
Issue:
Resolution:
```
