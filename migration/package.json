{"name": "dnsb-vakarai-migration", "version": "1.0.0", "description": "Migration scripts for DNSB Vakarai Supabase instance migration", "main": "scripts/run-migration.js", "scripts": {"backup": "./scripts/backup-source.sh", "migrate": "node scripts/run-migration.js", "migrate:dry-run": "node scripts/run-migration.js --dry-run", "migrate:verify": "node scripts/run-migration.js --verify-only", "export:schema": "node scripts/01-export-schema.js", "export:data": "node scripts/02-export-data.js", "import:schema": "node scripts/03-import-schema.js", "import:data": "node scripts/04-import-data.js", "import:auth": "node scripts/05-import-auth.js", "migrate:pgmq": "node scripts/06-migrate-pgmq.js", "verify": "node scripts/07-verify-migration.js", "install-deps": "npm install"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "pg": "^8.11.3"}, "devDependencies": {}, "keywords": ["supabase", "migration", "postgresql", "pgmq", "dnsb-vakarai"], "author": "DNSB Vakarai Team", "license": "MIT", "engines": {"node": ">=18.0.0"}}