# Sprint 3: Frontend Components & User Experience

## Sprint Overview
This sprint focuses on creating an exceptional user experience for file attachments. We'll build modern, responsive components with drag-and-drop functionality, real-time upload progress, image previews, and an intuitive file management interface.

## Sprint Goals
1. Implement drag-and-drop file upload interface
2. Create image preview and gallery components
3. Build real-time upload progress indicators
4. Develop file management UI (reorder, rename, delete)
5. Add accessibility features and mobile optimization

## User Stories

### Story 3.1: Drag-and-Drop Upload Interface
**As a** content creator  
**I want to** drag and drop files directly into the announcement form  
**So that** I can quickly add attachments without clicking through file dialogs

**Acceptance Criteria:**
- [ ] Drag-and-drop zone is clearly visible and intuitive
- [ ] Visual feedback when dragging files over the zone
- [ ] Support for dropping multiple files
- [ ] Paste images from clipboard (Ctrl+V)
- [ ] Traditional file picker remains available
- [ ] Mobile-friendly touch interface

**Technical Tasks:**
- Enhance `AttachmentUpload` component:
  ```typescript
  // components/announcements/attachment-upload-enhanced.tsx
  interface DragDropZoneProps {
    onFilesSelected: (files: File[]) => void;
    maxFiles?: number;
    maxSize?: number;
    acceptedTypes?: string[];
    disabled?: boolean;
  }

  export function DragDropZone({ onFilesSelected, ...props }: DragDropZoneProps) {
    const [isDragging, setIsDragging] = useState(false);
    
    const handleDrop = (e: DragEvent) => {
      e.preventDefault();
      const files = Array.from(e.dataTransfer.files);
      validateAndProcessFiles(files);
    };

    const handlePaste = (e: ClipboardEvent) => {
      const items = Array.from(e.clipboardData.items);
      const files = items
        .filter(item => item.type.indexOf('image') !== -1)
        .map(item => item.getAsFile())
        .filter(Boolean);
      validateAndProcessFiles(files);
    };

    return (
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-8 text-center transition-all",
          isDragging ? "border-indigo-500 bg-indigo-50" : "border-gray-300",
          "hover:border-gray-400"
        )}
        onDrop={handleDrop}
        onDragOver={(e) => { e.preventDefault(); setIsDragging(true); }}
        onDragLeave={() => setIsDragging(false)}
        onPaste={handlePaste}
      >
        <Upload className="mx-auto h-12 w-12 text-gray-400" />
        <p className="mt-2 text-sm text-gray-600">
          Nuvilkite failus čia arba <button>pasirinkite</button>
        </p>
        <p className="text-xs text-gray-500 mt-1">
          Maks. {maxFiles} failų, iki {formatBytes(maxSize)} kiekvienas
        </p>
      </div>
    );
  }
  ```
- Add file validation with user-friendly error messages
- Implement file type icons and previews
- Add animation for drag interactions

**Story Points:** 4

---

### Story 3.2: Real-time Upload Progress
**As a** user uploading large files  
**I want to** see detailed upload progress  
**So that** I know the upload is working and how long it will take

**Acceptance Criteria:**
- [ ] Individual progress bars for each file
- [ ] Upload speed and time remaining estimation
- [ ] Ability to pause/resume uploads
- [ ] Cancel individual uploads
- [ ] Retry failed uploads
- [ ] Background upload support (continue after navigation)

**Technical Tasks:**
- Create upload progress manager:
  ```typescript
  // components/announcements/upload-progress-manager.tsx
  interface UploadProgress {
    fileId: string;
    fileName: string;
    progress: number;
    speed: number; // bytes per second
    timeRemaining: number; // seconds
    status: 'pending' | 'uploading' | 'paused' | 'completed' | 'failed';
    error?: string;
  }

  export function UploadProgressManager() {
    const [uploads, setUploads] = useState<Map<string, UploadProgress>>();
    
    useEffect(() => {
      // WebSocket connection for progress updates
      const ws = new WebSocket('/api/ws/upload-progress');
      
      ws.onmessage = (event) => {
        const update = JSON.parse(event.data);
        updateProgress(update);
      };
      
      return () => ws.close();
    }, []);

    return (
      <div className="fixed bottom-4 right-4 w-96 space-y-2">
        {Array.from(uploads.values()).map(upload => (
          <UploadProgressCard key={upload.fileId} upload={upload} />
        ))}
      </div>
    );
  }
  ```
- Implement chunked upload for large files
- Add upload queue management
- Create service worker for background uploads

**Story Points:** 5

---

### Story 3.3: Image Preview and Gallery
**As a** user viewing announcements  
**I want to** preview images without downloading them  
**So that** I can quickly browse visual content

**Acceptance Criteria:**
- [ ] Thumbnail grid view for all images
- [ ] Lightbox for full-size image viewing
- [ ] Image zoom and pan functionality
- [ ] Keyboard navigation (arrow keys)
- [ ] Download original image option
- [ ] Share image URL functionality

**Technical Tasks:**
- Create image gallery component:
  ```typescript
  // components/announcements/image-gallery.tsx
  interface ImageGalleryProps {
    images: AttachmentImage[];
    initialIndex?: number;
    onDownload?: (image: AttachmentImage) => void;
  }

  export function ImageGallery({ images, initialIndex = 0 }: ImageGalleryProps) {
    const [currentIndex, setCurrentIndex] = useState(initialIndex);
    const [isZoomed, setIsZoomed] = useState(false);
    const [scale, setScale] = useState(1);

    return (
      <>
        {/* Thumbnail Grid */}
        <div className="grid grid-cols-4 gap-2">
          {images.map((image, index) => (
            <button
              key={image.id}
              onClick={() => setCurrentIndex(index)}
              className="relative aspect-square overflow-hidden rounded-lg"
            >
              <img
                src={image.thumbnailUrl}
                alt={image.fileName}
                className="object-cover w-full h-full"
                loading="lazy"
              />
            </button>
          ))}
        </div>

        {/* Lightbox Modal */}
        <Dialog open={currentIndex !== null}>
          <DialogContent className="max-w-full h-full p-0">
            <ImageViewer
              src={images[currentIndex].cdnUrl}
              alt={images[currentIndex].fileName}
              onZoom={setScale}
              scale={scale}
            />
          </DialogContent>
        </Dialog>
      </>
    );
  }
  ```
- Implement pinch-to-zoom for mobile
- Add swipe gestures for navigation
- Create loading states with skeleton screens

**Story Points:** 4

---

### Story 3.4: File Management Interface
**As a** content creator  
**I want to** manage my attached files easily  
**So that** I can organize and maintain my attachments

**Acceptance Criteria:**
- [ ] Drag to reorder attachments
- [ ] Inline rename functionality
- [ ] Bulk selection and actions
- [ ] File details panel (size, type, upload date)
- [ ] Search within attachments
- [ ] Sort by name, size, date

**Technical Tasks:**
- Create file manager component:
  ```typescript
  // components/announcements/file-manager.tsx
  interface FileManagerProps {
    attachments: Attachment[];
    onReorder: (attachments: Attachment[]) => void;
    onRename: (id: string, newName: string) => void;
    onDelete: (ids: string[]) => void;
    editable?: boolean;
  }

  export function FileManager({ attachments, ...props }: FileManagerProps) {
    const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
    const [sortBy, setSortBy] = useState<'name' | 'size' | 'date'>('date');
    const [searchQuery, setSearchQuery] = useState('');

    return (
      <div className="space-y-4">
        {/* Toolbar */}
        <div className="flex items-center justify-between">
          <SearchInput
            value={searchQuery}
            onChange={setSearchQuery}
            placeholder="Ieškoti failų..."
          />
          <div className="flex items-center gap-2">
            <SortDropdown value={sortBy} onChange={setSortBy} />
            <BulkActions
              selectedCount={selectedIds.size}
              onDelete={() => props.onDelete(Array.from(selectedIds))}
            />
          </div>
        </div>

        {/* File List */}
        <DndContext onDragEnd={handleDragEnd}>
          <SortableContext items={attachments.map(a => a.id)}>
            {sortedAndFilteredAttachments.map(attachment => (
              <SortableFileItem
                key={attachment.id}
                attachment={attachment}
                selected={selectedIds.has(attachment.id)}
                onSelect={(selected) => updateSelection(attachment.id, selected)}
                onRename={(name) => props.onRename(attachment.id, name)}
              />
            ))}
          </SortableContext>
        </DndContext>
      </div>
    );
  }
  ```
- Implement drag-and-drop reordering with @dnd-kit
- Add inline editing with auto-save
- Create responsive grid/list view toggle

**Story Points:** 5

---

### Story 3.5: Attachment Preview Components
**As a** user  
**I want to** preview different file types without downloading  
**So that** I can quickly assess file contents

**Acceptance Criteria:**
- [ ] PDF preview with page navigation
- [ ] Office document preview (basic)
- [ ] Video player with controls
- [ ] Audio player with waveform
- [ ] Text file syntax highlighting
- [ ] "Preview not available" fallback

**Technical Tasks:**
- Create file preview factory:
  ```typescript
  // components/attachments/preview-factory.tsx
  interface PreviewProps {
    attachment: Attachment;
    className?: string;
  }

  export function AttachmentPreview({ attachment, className }: PreviewProps) {
    const previewComponent = useMemo(() => {
      switch (attachment.fileType) {
        case 'application/pdf':
          return <PDFPreview url={attachment.cdnUrl} />;
        case 'video/mp4':
        case 'video/webm':
          return <VideoPlayer url={attachment.cdnUrl} poster={attachment.thumbnailUrl} />;
        case 'audio/mpeg':
        case 'audio/wav':
          return <AudioPlayer url={attachment.cdnUrl} />;
        case 'text/plain':
        case 'text/markdown':
          return <TextPreview url={attachment.cdnUrl} language={detectLanguage(attachment.fileName)} />;
        default:
          if (attachment.isImage) {
            return <ImagePreview url={attachment.cdnUrl} alt={attachment.fileName} />;
          }
          return <GenericFilePreview attachment={attachment} />;
      }
    }, [attachment]);

    return (
      <div className={cn("attachment-preview", className)}>
        <Suspense fallback={<PreviewSkeleton />}>
          {previewComponent}
        </Suspense>
      </div>
    );
  }
  ```
- Integrate PDF.js for PDF rendering
- Add video.js for video playback
- Implement lazy loading for previews

**Story Points:** 4

---

### Story 3.6: Mobile Optimization
**As a** mobile user  
**I want to** easily manage attachments on my phone  
**So that** I can use all features on any device

**Acceptance Criteria:**
- [ ] Touch-optimized interface elements
- [ ] Responsive layout for all screen sizes
- [ ] Native share functionality integration
- [ ] Optimized image loading for mobile data
- [ ] Offline mode for viewing cached attachments
- [ ] Camera integration for direct photo uploads

**Technical Tasks:**
- Create mobile-specific components:
  ```typescript
  // components/attachments/mobile-upload.tsx
  export function MobileUploadButton() {
    const handleCameraCapture = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
          video: { facingMode: 'environment' } 
        });
        // Handle camera capture
      } catch (error) {
        // Fallback to file input
      }
    };

    return (
      <div className="grid grid-cols-2 gap-2">
        <Button
          onClick={handleCameraCapture}
          className="flex flex-col items-center p-4"
        >
          <Camera className="h-8 w-8 mb-2" />
          <span>Fotografuoti</span>
        </Button>
        <Button
          onClick={() => fileInputRef.current?.click()}
          className="flex flex-col items-center p-4"
        >
          <FileUp className="h-8 w-8 mb-2" />
          <span>Iš galerijos</span>
        </Button>
      </div>
    );
  }
  ```
- Implement progressive web app features
- Add service worker for offline support
- Optimize touch gestures and interactions

**Story Points:** 3

---

## UI/UX Design Guidelines

### Component Hierarchy
```
AttachmentSection
├── DragDropZone
├── UploadProgressManager
├── FileManager
│   ├── FileToolbar
│   ├── FileList
│   │   └── FileItem
│   └── FileDetailsPanel
└── AttachmentPreviews
    ├── ImageGallery
    ├── PDFViewer
    └── GenericPreview
```

### Design Tokens
```scss
// Colors
$attachment-primary: #4F46E5; // Indigo-600
$attachment-hover: #4338CA;   // Indigo-700
$attachment-border: #E5E7EB;  // Gray-200
$attachment-bg: #F9FAFB;      // Gray-50

// Spacing
$attachment-padding: 1rem;
$attachment-gap: 0.5rem;

// Animation
$upload-transition: all 0.2s ease-in-out;
$drag-transition: all 0.15s ease-out;
```

## Accessibility Requirements

1. **Keyboard Navigation**
   - All interactive elements accessible via keyboard
   - Clear focus indicators
   - Logical tab order

2. **Screen Reader Support**
   - Descriptive ARIA labels
   - Progress announcements
   - File type descriptions

3. **Color Contrast**
   - WCAG AA compliance minimum
   - High contrast mode support

4. **Reduced Motion**
   - Respect prefers-reduced-motion
   - Provide alternative feedback

## Performance Optimization

1. **Lazy Loading**
   - Load thumbnails on demand
   - Virtualize long file lists
   - Progressive image loading

2. **Caching Strategy**
   - Cache thumbnails locally
   - Prefetch likely previews
   - Service worker for offline

3. **Bundle Optimization**
   - Code split preview components
   - Dynamic imports for large dependencies
   - Tree-shake unused file type handlers

## Testing Requirements

### Unit Tests
- Drag-and-drop event handlers
- File validation logic
- Progress calculation
- Accessibility compliance

### Integration Tests
- Upload flow end-to-end
- Preview component loading
- Mobile gesture handling
- Offline functionality

### E2E Tests
- Complete attachment workflow
- Multi-file upload scenarios
- Error recovery flows
- Mobile device testing

## Definition of Done
- [ ] All components are fully responsive
- [ ] Accessibility audit passes
- [ ] Performance metrics meet targets
- [ ] Cross-browser testing completed
- [ ] Documentation with examples
- [ ] Storybook stories created

## Dependencies
- BunnyCDN integration (Sprint 1)
- Backend APIs (Sprint 2)
- UI component library updates
- Third-party libraries (PDF.js, video.js)

## Risks and Mitigation
1. **Risk:** Browser compatibility issues
   - **Mitigation:** Progressive enhancement, polyfills

2. **Risk:** Large dependency size
   - **Mitigation:** Lazy load preview libraries

3. **Risk:** Mobile performance
   - **Mitigation:** Adaptive loading based on connection

## Sprint Metrics
- **Total Story Points:** 25
- **Team Capacity:** 25 points
- **Buffer:** 0 points (tight sprint)

## Notes
- Consider A/B testing different upload UIs
- Plan for user testing sessions mid-sprint
- Prepare for Sprint 4 advanced features