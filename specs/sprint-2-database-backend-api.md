# Sprint 2: Database Schema & Backend API

## Sprint Overview
This sprint focuses on updating the database schema to support CDN-based file storage, modifying the existing attachment APIs to use BunnyCDN, and implementing advanced file handling features including image optimization and thumbnail generation.

## Sprint Goals
1. Update database schema for CDN storage metadata
2. Modify attachment upload/download APIs to use BunnyCDN
3. Implement image optimization and thumbnail generation
4. Add batch upload support
5. Create attachment analytics and reporting

## User Stories

### Story 2.1: Database Schema Updates
**As a** system architect  
**I want to** update the database schema to support CDN storage  
**So that** we can track CDN-specific metadata and optimize file delivery

**Acceptance Criteria:**
- [ ] Announcement attachments table is updated with CDN fields
- [ ] Migration scripts are created and tested
- [ ] Indexes are optimized for common queries
- [ ] Backward compatibility is maintained during migration
- [ ] Audit trail for attachment operations is implemented

**Technical Tasks:**
- Create migration script:
  ```sql
  -- Add CDN-specific columns to announcement_attachments
  ALTER TABLE announcement_attachments
  ADD COLUMN cdn_path VARCHAR(500),
  ADD COLUMN cdn_url VARCHAR(1000),
  ADD COLUMN thumbnail_cdn_path VARCHAR(500),
  ADD COLUMN thumbnail_cdn_url VARCHAR(1000),
  ADD COLUMN cdn_region VARCHAR(50) DEFAULT 'de',
  ADD COLUMN processing_status VARCHAR(50) DEFAULT 'pending',
  ADD COLUMN processed_at TIMESTAMP,
  ADD COLUMN metadata JSONB DEFAULT '{}',
  ADD COLUMN file_hash VARCHAR(64),
  ADD COLUMN download_count INTEGER DEFAULT 0,
  ADD COLUMN last_accessed_at TIMESTAMP;

  -- Create attachment_operations audit table
  CREATE TABLE attachment_operations (
    id SERIAL PRIMARY KEY,
    attachment_id INTEGER REFERENCES announcement_attachments(id),
    operation_type VARCHAR(50) NOT NULL, -- upload, download, delete, optimize
    user_id UUID REFERENCES users(id),
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );

  -- Add indexes for performance
  CREATE INDEX idx_attachments_cdn_path ON announcement_attachments(cdn_path);
  CREATE INDEX idx_attachments_processing_status ON announcement_attachments(processing_status);
  CREATE INDEX idx_attachments_announcement_id ON announcement_attachments(announcement_id);
  CREATE INDEX idx_operations_attachment_id ON attachment_operations(attachment_id);
  ```
- Update TypeScript interfaces in `/lib/db/schema.ts`
- Create data migration script for existing attachments
- Add database triggers for automatic timestamp updates

**Story Points:** 3

---

### Story 2.2: Attachment Upload API Enhancement
**As a** content creator  
**I want to** upload files that are automatically optimized and stored in CDN  
**So that** my attachments load quickly for all users

**Acceptance Criteria:**
- [ ] Files are uploaded directly to BunnyCDN
- [ ] Images are automatically optimized (WebP conversion, compression)
- [ ] Thumbnails are generated for images
- [ ] Upload progress is tracked and can be queried
- [ ] Duplicate files are detected using hash comparison

**Technical Tasks:**
- Update `/app/api/announcements/attachments/route.ts`:
  ```typescript
  export async function POST(request: NextRequest) {
    // 1. Validate user permissions and file
    // 2. Calculate file hash for deduplication
    // 3. Check if file already exists in CDN
    // 4. Upload to BunnyCDN with progress tracking
    // 5. Queue image optimization job if applicable
    // 6. Save metadata to database
    // 7. Return upload response with CDN URLs
  }
  ```
- Create image optimization service:
  ```typescript
  // lib/services/image-optimizer.ts
  interface OptimizationOptions {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png';
    generateThumbnail?: boolean;
  }

  class ImageOptimizer {
    async optimizeImage(input: Buffer, options: OptimizationOptions): Promise<OptimizedImage>
    async generateThumbnail(input: Buffer, size: ThumbnailSize): Promise<Buffer>
    async convertToWebP(input: Buffer, quality?: number): Promise<Buffer>
  }
  ```
- Implement file deduplication logic
- Add progress tracking using Redis or in-memory cache

**Story Points:** 5

---

### Story 2.3: Batch Upload Support
**As a** content creator  
**I want to** upload multiple files at once  
**So that** I can efficiently attach multiple documents to announcements

**Acceptance Criteria:**
- [ ] Support uploading up to 10 files simultaneously
- [ ] Individual file progress is tracked
- [ ] Failed uploads can be retried individually
- [ ] Total upload size limit is enforced (200MB)
- [ ] Atomic operation - all succeed or all fail option

**Technical Tasks:**
- Create batch upload endpoint:
  ```typescript
  // app/api/announcements/attachments/batch/route.ts
  export async function POST(request: NextRequest) {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const announcementId = formData.get('announcementId');
    const atomic = formData.get('atomic') === 'true';

    // Process files in parallel with concurrency limit
    const results = await processFilesWithConcurrency(files, {
      concurrency: 3,
      atomic,
      onProgress: (fileId, progress) => {
        // Update progress in cache
      }
    });

    return NextResponse.json({ results });
  }
  ```
- Implement concurrent upload manager
- Create WebSocket endpoint for real-time progress updates
- Add batch operation UI components

**Story Points:** 4

---

### Story 2.4: Attachment Download and Analytics API
**As a** system administrator  
**I want to** track attachment downloads and access patterns  
**So that** I can understand content engagement and optimize delivery

**Acceptance Criteria:**
- [ ] Downloads are tracked with user and timestamp
- [ ] Anonymous download tracking for non-authenticated users
- [ ] Download analytics API provides usage statistics
- [ ] Secure download URLs with expiration support
- [ ] Rate limiting for download requests

**Technical Tasks:**
- Update download endpoint:
  ```typescript
  // app/api/announcements/attachments/[id]/route.ts
  export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
    // 1. Validate access permissions
    // 2. Track download in database
    // 3. Generate signed CDN URL with expiration
    // 4. Update last_accessed_at timestamp
    // 5. Return redirect to CDN URL
  }
  ```
- Create analytics endpoint:
  ```typescript
  // app/api/announcements/attachments/analytics/route.ts
  interface AttachmentAnalytics {
    totalDownloads: number;
    uniqueUsers: number;
    downloadsByDay: Array<{ date: string; count: number }>;
    topAttachments: Array<{ id: string; name: string; downloads: number }>;
    averageFileSize: number;
    storageUsed: number;
  }
  ```
- Implement rate limiting middleware
- Add caching for analytics queries

**Story Points:** 3

---

### Story 2.5: Attachment Search and Filtering
**As a** user  
**I want to** search and filter attachments  
**So that** I can quickly find relevant files

**Acceptance Criteria:**
- [ ] Full-text search in file names
- [ ] Filter by file type, size, upload date
- [ ] Sort by relevance, date, size, downloads
- [ ] Pagination with customizable page size
- [ ] Search results include preview thumbnails

**Technical Tasks:**
- Create search endpoint:
  ```typescript
  // app/api/announcements/attachments/search/route.ts
  interface SearchParams {
    query?: string;
    fileTypes?: string[];
    minSize?: number;
    maxSize?: number;
    uploadedAfter?: Date;
    uploadedBefore?: Date;
    announcementId?: string;
    sortBy?: 'relevance' | 'date' | 'size' | 'downloads';
    page?: number;
    pageSize?: number;
  }

  export async function GET(request: NextRequest) {
    const searchParams = parseSearchParams(request.url);
    const results = await searchAttachments(searchParams);
    return NextResponse.json(results);
  }
  ```
- Implement full-text search using PostgreSQL
- Add search indexes to database
- Create search result ranking algorithm

**Story Points:** 4

---

### Story 2.6: Virus Scanning Integration
**As a** security officer  
**I want to** ensure all uploaded files are scanned for viruses  
**So that** malicious files cannot be distributed through our platform

**Acceptance Criteria:**
- [ ] Files are queued for scanning immediately after upload
- [ ] Infected files are automatically quarantined
- [ ] Users are notified if their file is infected
- [ ] Admin dashboard shows scanning statistics
- [ ] Scanning doesn't block the upload process

**Technical Tasks:**
- Integrate with ClamAV or similar service:
  ```typescript
  // lib/services/virus-scanner.ts
  interface ScanResult {
    clean: boolean;
    threats?: string[];
    scanTime: number;
    engine: string;
    engineVersion: string;
  }

  class VirusScanner {
    async scanFile(filePath: string): Promise<ScanResult>
    async scanBuffer(buffer: Buffer): Promise<ScanResult>
    async quarantineFile(attachmentId: string, reason: string): Promise<void>
  }
  ```
- Create background job for virus scanning
- Implement quarantine workflow
- Add admin UI for reviewing quarantined files

**Story Points:** 5

---

## API Specifications

### Upload API Response
```json
{
  "success": true,
  "attachment": {
    "id": "123",
    "fileName": "document.pdf",
    "fileSize": 1024000,
    "fileType": "application/pdf",
    "cdnUrl": "https://dnsb.b-cdn.net/attachments/123/document.pdf",
    "thumbnailUrl": "https://dnsb.b-cdn.net/attachments/123/thumb_document.jpg",
    "uploadedAt": "2024-01-15T10:30:00Z",
    "processingStatus": "completed"
  }
}
```

### Batch Upload Response
```json
{
  "success": true,
  "results": [
    {
      "fileName": "file1.jpg",
      "success": true,
      "attachment": { /* attachment object */ }
    },
    {
      "fileName": "file2.pdf",
      "success": false,
      "error": "File too large"
    }
  ],
  "summary": {
    "total": 2,
    "succeeded": 1,
    "failed": 1
  }
}
```

## Testing Requirements

### Unit Tests
- File validation and sanitization
- Image optimization functions
- Virus scanning integration
- Search query builders

### Integration Tests
- End-to-end upload flow with CDN
- Batch upload scenarios
- Download tracking accuracy
- Search functionality

### Performance Tests
- Large file upload (50MB)
- Batch upload of 10 files
- Concurrent upload stress test
- Image optimization benchmarks

## Definition of Done
- [ ] All database migrations are tested and reversible
- [ ] API endpoints have >95% test coverage
- [ ] Performance benchmarks meet SLA requirements
- [ ] Security scan passes with no critical issues
- [ ] API documentation is updated
- [ ] Monitoring and alerts are configured

## Dependencies
- BunnyCDN integration from Sprint 1
- Image processing library (Sharp.js)
- Virus scanning service account
- Redis for progress tracking (optional)

## Risks and Mitigation
1. **Risk:** Image processing blocking uploads
   - **Mitigation:** Process images asynchronously in background jobs

2. **Risk:** Database migration affecting production
   - **Mitigation:** Test migration on staging, use transaction for atomicity

3. **Risk:** Virus scanner false positives
   - **Mitigation:** Implement manual review process for quarantined files

## Sprint Metrics
- **Total Story Points:** 24
- **Team Capacity:** 25 points
- **Buffer:** 1 point for unexpected issues

## Notes
- Consider implementing OCR for searchable PDFs in future sprint
- Evaluate need for video transcoding based on usage patterns
- Monitor image optimization impact on storage costs