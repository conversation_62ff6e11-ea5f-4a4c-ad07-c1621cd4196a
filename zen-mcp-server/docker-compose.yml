services:
  redis:
    image: redis:7-alpine
    container_name: zen-mcp-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --save 60 1 --loglevel warning --maxmemory 64mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 256M

  zen-mcp:
    build: .
    image: zen-mcp-server:latest
    container_name: zen-mcp-server
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY:-}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - DEFAULT_MODEL=${DEFAULT_MODEL:-auto}
      - DEFAULT_THINKING_MODE_THINKDEEP=${DEFAULT_THINKING_MODE_THINKDEEP:-high}
      - REDIS_URL=redis://redis:6379/0
      # Use HOME not PWD: <PERSON> needs access to any absolute file path, not just current project,
      # and <PERSON> Code could be running from multiple locations at the same time
      - WORKSPACE_ROOT=${WORKSPACE_ROOT:-${HOME}}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - PYTHONUNBUFFERED=1
    volumes:
      - ${HOME:-/tmp}:/workspace:ro
      - mcp_logs:/tmp  # Shared volume for logs
      - /etc/localtime:/etc/localtime:ro
    stdin_open: true
    tty: true
    entrypoint: ["python"]
    command: ["server.py"]

  log-monitor:
    build: .
    image: zen-mcp-server:latest
    container_name: zen-mcp-log-monitor
    restart: unless-stopped
    depends_on:
      - zen-mcp
    environment:
      - PYTHONUNBUFFERED=1
    volumes:
      - mcp_logs:/tmp  # Shared volume for logs
      - /etc/localtime:/etc/localtime:ro
    entrypoint: ["python"]
    command: ["log_monitor.py"]

volumes:
  redis_data:
  mcp_logs: