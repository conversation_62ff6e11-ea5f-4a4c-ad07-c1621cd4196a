# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
*.egg
*.egg-info/
dist/
build/
*.so
.coverage
.pytest_cache/
htmlcov/
.tox/
.mypy_cache/
.ruff_cache/

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Docker
.dockerignore
Dockerfile
docker-compose*.yml

# Environment files (contain secrets)
.env
.env.*
*.env

# Documentation
*.md
docs/
examples/

# Tests
tests/
test_*.py
*_test.py

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
*.tmp

# OS specific
Thumbs.db