{"comment": "Docker configuration that mounts your home directory", "comment2": "Update paths: /path/to/zen-mcp-server/.env and /Users/<USER>", "comment3": "The container auto-detects /workspace as sandbox from WORKSPACE_ROOT", "mcpServers": {"zen": {"command": "docker", "args": ["run", "--rm", "-i", "--env-file", "/path/to/zen-mcp-server/.env", "-e", "WORKSPACE_ROOT=/Users/<USER>", "-v", "/Users/<USER>/workspace:ro", "zen-mcp-server:latest"]}}}