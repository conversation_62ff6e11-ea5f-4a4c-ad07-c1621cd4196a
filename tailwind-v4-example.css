/**
 * Example Tailwind CSS 4.0 configuration
 * This demonstrates how to migrate from tailwind.config.js to CSS-first configuration
 */

@import "tailwindcss";

@theme {
  /* Colors */
  --color-primary: oklch(0.61 0.29 271.22);
  --color-primary-foreground: oklch(0.98 0.02 0);
  
  --color-secondary: oklch(0.91 0.04 254.53);
  --color-secondary-foreground: oklch(0.28 0.02 251.22);
  
  --color-destructive: oklch(0.67 0.30 22.18);
  --color-destructive-foreground: oklch(0.98 0.02 0);
  
  --color-muted: oklch(0.93 0.03 276.77);
  --color-muted-foreground: oklch(0.57 0.01 274.32);
  
  --color-accent: oklch(0.91 0.04 254.53);
  --color-accent-foreground: oklch(0.28 0.02 251.22);
  
  --color-background: oklch(0.98 0.02 0);
  --color-foreground: oklch(0.20 0.01 278.32);
  
  --color-border: oklch(0.89 0.01 270.22);
  --color-input: oklch(0.89 0.01 270.22);
  --color-ring: oklch(0.71 0.11 270.22);
  
  --color-card: oklch(0.98 0.02 0);
  --color-card-foreground: oklch(0.20 0.01 278.32);
  
  --color-popover: oklch(0.98 0.02 0);
  --color-popover-foreground: oklch(0.20 0.01 278.32);
  
  /* Dark Mode Colors */
  &.dark {
    --color-primary: oklch(0.61 0.29 271.22);
    --color-primary-foreground: oklch(0.98 0.02 0);
    
    --color-secondary: oklch(0.36 0.05 252.33);
    --color-secondary-foreground: oklch(0.98 0.02 0);
    
    --color-destructive: oklch(0.67 0.30 22.18);
    --color-destructive-foreground: oklch(0.98 0.02 0);
    
    --color-muted: oklch(0.30 0.02 252.32);
    --color-muted-foreground: oklch(0.71 0.01 263.35);
    
    --color-accent: oklch(0.36 0.05 252.33);
    --color-accent-foreground: oklch(0.98 0.02 0);
    
    --color-background: oklch(0.13 0.01 289.33);
    --color-foreground: oklch(0.95 0.01 276.77);
    
    --color-border: oklch(0.30 0.02 252.32);
    --color-input: oklch(0.30 0.02 252.32);
    --color-ring: oklch(0.51 0.19 271.22);
    
    --color-card: oklch(0.16 0.01 275.32);
    --color-card-foreground: oklch(0.95 0.01 276.77);
    
    --color-popover: oklch(0.16 0.01 275.32);
    --color-popover-foreground: oklch(0.95 0.01 276.77);
  }
  
  /* Typography */
  --font-sans: "Inter", sans-serif;
  
  /* Spacing and Sizing */
  --radius: 0.5rem;
  
  /* Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1400px;
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
  }
}

/* Example of custom utilities */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .text-pretty {
    text-wrap: pretty;
  }
  
  .animate-once {
    animation-iteration-count: 1;
  }
  
  .animate-infinite {
    animation-iteration-count: infinite;
  }
}

/* Example of custom keyframes */
@layer theme {
  @keyframes slide-in {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(0);
    }
  }
  
  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}

/* Example of custom component classes */
@layer components {
  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm p-6;
  }
  
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 transition-colors;
  }
  
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/90 rounded-md px-4 py-2 transition-colors;
  }
} 