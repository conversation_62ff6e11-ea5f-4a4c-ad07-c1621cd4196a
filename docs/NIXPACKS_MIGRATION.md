# Nixpacks Migration Guide

This guide covers migrating the DNSB Vakarai application from Docker Compose to Nixpacks build system in Coolify.

## Overview

Nixpacks is an open-source build pack that automatically detects your application type and generates optimized Docker images. This migration will simplify our deployment process while maintaining all current functionality.

## Benefits of Migration

### ✅ Advantages
- **Simplified Configuration**: No need to maintain custom Dockerfile
- **Automatic Optimization**: Nixpacks optimizes builds for Next.js applications
- **Faster Builds**: Improved caching and build performance
- **Reduced Maintenance**: Less infrastructure code to maintain
- **Better Resource Usage**: Optimized container images

### ⚠️ Considerations
- **Build Process Changes**: Environment variables handled differently
- **Custom Scripts**: Need to verify all npm scripts work correctly
- **Volume Mounts**: Same functionality, different configuration method

## Migration Steps

### 1. Pre-Migration Checklist

- [ ] Backup current deployment configuration
- [ ] Document current environment variables
- [ ] Test application locally with Node.js 22
- [ ] Verify all npm scripts work correctly

### 2. Nixpacks Configuration

The project includes a `nixpacks.toml` file with optimized settings:

```toml
[variables]
NODE_VERSION = "22"
NPM_VERSION = "10"

[phases.setup]
nixPkgs = ["nodejs_22", "npm"]

[phases.install]
cmds = ["npm ci"]

[phases.build]
cmds = ["npm run build"]
dependsOn = ["install"]

[start]
cmd = "node server.js"
```

### 3. Environment Variables Migration

#### Current Docker Compose Variables
All current environment variables will work with Nixpacks:

```bash
# Application
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Security
CSRF_SECRET=your-csrf-secret
CRON_API_KEY=your-cron-api-key

# Analytics
NEXT_PUBLIC_POSTHOG_KEY=your-posthog-key
NEXT_PUBLIC_POSTHOG_HOST=https://eu.i.posthog.com

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_PASSWORD_RESET=true
```

#### Key Differences
- **Build Arguments**: No longer needed - environment variables are handled automatically
- **Runtime Variables**: All variables available at runtime as before
- **SMTP Settings**: Still stored in database (no change)

### 4. Coolify Configuration Changes

#### Current Setup (Docker Compose)
- Build Pack: Docker Compose
- Configuration: docker-compose.yml + Dockerfile
- Environment: Set in Coolify interface

#### New Setup (Nixpacks)
- Build Pack: Nixpacks
- Configuration: nixpacks.toml
- Environment: Set in Coolify interface (same as before)

### 5. Migration Process

#### Step 1: Backup Current Configuration
1. Export current environment variables from Coolify
2. Document current deployment settings
3. Create backup of working deployment

#### Step 2: Create New Nixpacks Application
1. In Coolify, create new application
2. Select same Git repository
3. Choose "Nixpacks" as build pack
4. Configure same environment variables

#### Step 3: Configure Nixpacks Settings
1. **Base Directory**: `/` (root of repository)
2. **Port**: `3000` (same as current)
3. **Branch**: `main` (or your deployment branch)
4. **Build Command**: Automatic (handled by nixpacks.toml)
5. **Start Command**: Automatic (handled by nixpacks.toml)

#### Step 4: Volume Configuration
Configure persistent volumes in Coolify:
- **Logs**: `/app/logs` → `./logs`
- **Uploads**: `/app/uploads` → `./uploads`

#### Step 5: Deploy and Test
1. Deploy the new Nixpacks application
2. Run verification script: `npm run deploy:verify`
3. Test all functionality thoroughly
4. Monitor performance and logs

### 6. Verification Checklist

After migration, verify:

- [ ] Application starts successfully
- [ ] Health check endpoint responds: `/api/health`
- [ ] Supabase connection works
- [ ] Authentication functions correctly
- [ ] Email functionality works
- [ ] File uploads work (if applicable)
- [ ] All API endpoints respond correctly
- [ ] PostHog analytics tracking works
- [ ] Admin settings accessible
- [ ] Database operations function correctly

### 7. Rollback Plan

If issues occur:

1. **Immediate Rollback**: Switch traffic back to Docker Compose deployment
2. **Debug Issues**: Use Coolify logs to identify problems
3. **Fix and Retry**: Address issues and attempt migration again

### 8. Performance Comparison

Monitor these metrics before and after migration:

- **Build Time**: Nixpacks should be faster
- **Image Size**: Should be smaller and more optimized
- **Memory Usage**: Should be similar or better
- **Startup Time**: Should be similar or faster
- **Response Times**: Should remain the same

### 9. Troubleshooting

#### Common Issues and Solutions

**Build Failures**:
- Check Node.js version in nixpacks.toml
- Verify all dependencies are compatible
- Review build logs for specific errors

**Environment Variable Issues**:
- Ensure all variables are set in Coolify
- Check variable names match exactly
- Verify NEXT_PUBLIC_ variables are available at build time

**Application Won't Start**:
- Check start command in nixpacks.toml
- Verify port configuration (3000)
- Review application logs

**Performance Issues**:
- Monitor resource usage
- Check for memory leaks
- Verify caching is working correctly

### 10. Post-Migration Tasks

- [ ] Update deployment documentation
- [ ] Train team on new deployment process
- [ ] Set up monitoring for new deployment
- [ ] Schedule regular performance reviews
- [ ] Plan for future optimizations

## Support and Resources

- **Nixpacks Documentation**: https://nixpacks.com/docs
- **Coolify Nixpacks Guide**: https://coolify.io/docs/builds/packs/nixpacks
- **Next.js Deployment**: https://nextjs.org/docs/deployment

## Quick Start Commands

Use these npm scripts to help with the migration:

```bash
# Validate current setup and get migration steps
npm run nixpacks:prepare

# Compare Docker Compose vs Nixpacks configuration
npm run nixpacks:compare

# Validate nixpacks.toml configuration
npm run nixpacks:validate

# Generate environment variables for Nixpacks
npm run nixpacks:env
```

## Configuration Comparison

| Aspect | Docker Compose (Current) | Nixpacks (Target) |
|--------|-------------------------|-------------------|
| **Configuration Files** | `Dockerfile` + `docker-compose.yml` | `nixpacks.toml` |
| **Build Process** | Multi-stage custom build | Automatic detection + optimization |
| **Environment Variables** | Build args + runtime env | Runtime env only |
| **Maintenance** | Manual Dockerfile updates | Automatic optimization |
| **Build Speed** | Standard Docker build | Optimized with better caching |
| **Image Size** | Custom optimization | Automatic optimization |
| **Node.js Version** | Manually specified | Automatically detected/specified |
| **Deployment Complexity** | Medium (custom config) | Low (automatic) |

## Conclusion

The migration to Nixpacks will simplify our deployment process while maintaining all current functionality. The automated build optimization should improve performance and reduce maintenance overhead.
