# DNSB Vakarai System Documentation

This documentation provides essential guides for the DNSB Vakarai housing community management system.

## Table of Contents

1. [System Overview](#system-overview)
2. [Testing Guide](./JEST_TESTING_GUIDE.md)
3. [Language Guidelines](./LANGUAGE_GUIDELINES.md)
4. [MCP Setup](./MCP_SETUP.md)
5. [Archived Documentation](./archive/)

## System Overview

DNSB Vakarai is a comprehensive housing community management system designed to facilitate communication and management within residential buildings. The system serves multiple user types including administrators, editors, and residents.

### Key Features

1. **User Management**
   - Role-based access control
   - User profile management
   - Emergency contact management

2. **Property Management**
   - Houses and flats management
   - Hierarchical structure (Streets → Houses → Flats → Users)

3. **Communication System**
   - Announcements with targeted distribution
   - Notifications system
   - Contact messaging for user support
   - Feedback collection

4. **Polling System**
   - Create and manage community polls
   - Voting mechanism
   - Results visualization

5. **Administration Tools**
   - User management dashboard
   - Message and feedback management
   - System settings configuration
   - Audit logging for system actions

### Technical Stack

- **Frontend**: Next.js 15.2.4 (App Router), React 19.0.0
- **Database**: Supabase PostgreSQL
- **Authentication**: Supabase Auth with custom username wrapper
- **Email Queue**: PGMQ (PostgreSQL Message Queue)
- **Query Management**: TanStack Query
- **Styling**: Tailwind CSS 3.4, shadcn/ui components
- **Error Tracking**: Sentry
- **Analytics**: PostHog
- **Deployment**: Coolify (self-hosted PaaS)

### Current Status

The system has been successfully migrated from PostgreSQL + NextAuth + BullMQ to Supabase + PGMQ + TanStack Query. The migration is complete and the system is fully operational with the new architecture.

For detailed information about specific aspects of the system, refer to the corresponding documentation sections listed in the Table of Contents.