# Jest Testing Guide for Email Queue

This guide explains how to run and maintain the Jest tests for the email queue system.

## Running Tests

```bash
# Install test dependencies
npm install --save-dev jest ts-jest @types/jest jest-environment-node

# Run all tests
npm test

# Run specific test suite
npm run test:email-queue

# Run tests with coverage
npm run test:coverage
```

## Test Setup

The tests use integration testing approach without mocks to test real functionality.

### Key Files

- `__tests__/setup.ts`: Global test setup and environment variable configuration

### Structure of Test Files

Each test file follows this structure:

1. Import dependencies and mocks
2. Set up mocks before each test
3. Reset mocks after each test
4. Group tests by function/feature
5. Test both success and error cases

## Common Issues and Solutions

### "Cannot find module" errors

If you see errors like:
```
Cannot find module '../../lib/logger' from '__tests__/lib/queue/mocks.ts'
```

Fix path references in mock implementation:
```javascript
// Change this:
jest.mock('../../lib/logger', () => ({...}))

// To this:
jest.mock('../../../lib/logger', () => ({...}))
```

### "is not a function" errors

If you see errors like:
```
TypeError: (0 , bull_config_1.loadRedisSettingsFromDb) is not a function
```

This project follows a no-mocks testing philosophy. Instead of mocking, use real test instances or skip tests that require external services.

### Test hanging and not completing

This is often caused by unresolved promises or unclosed connections. Add this to `__tests__/setup.ts`:

```javascript
// Clean up after all tests
afterAll(() => {
  // Close any open handles
  jest.useRealTimers();
});
```

## Maintaining Tests

When adding new features to the email queue:

1. Add corresponding tests that cover the new functionality
2. Cover both success and error cases
3. Mock any new dependencies
4. Run the tests to verify that all pass

## Example Test File

Here's a simplified example of a test file:

```javascript
import { someFunction } from '../../../lib/some-module';
import { mockDependency } from './mocks';

// Mock dependencies
jest.mock('../../../lib/some-dependency', () => ({
  dependencyFunction: jest.fn().mockResolvedValue('mocked-result')
}));

// Reset mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
});

describe('someFunction', () => {
  it('should work correctly', async () => {
    // Setup
    const input = 'test-input';
    
    // Execute
    const result = await someFunction(input);
    
    // Verify
    expect(result).toBe('expected-output');
    expect(mockDependency.someMethod).toHaveBeenCalledWith(input);
  });
  
  it('should handle errors', async () => {
    // Setup error case
    mockDependency.someMethod.mockRejectedValueOnce(new Error('test-error'));
    
    // Execute and verify
    await expect(someFunction('test-input')).rejects.toThrow('test-error');
  });
});
```