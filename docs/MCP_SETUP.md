# Model Context Protocol (MCP) Setup

## Overview

This document describes the Model Context Protocol (MCP) configuration for the DNSB Vakarai project. MCP enables AI assistants to interact with various services and tools in a standardized way.

## Installation

The MCP SDK has been installed:

```bash
npm install @modelcontextprotocol/sdk
```

## Configuration

The MCP configuration is stored in `.mcp.json` at the project root. The file configures several MCP servers:

### 1. Supabase Server

Provides access to Supabase services (for future migration):

```json
{
  "command": "npx",
  "args": [
    "-y",
    "@supabase/mcp-server-supabase@latest",
    "--access-token",
    "<ACCESS_TOKEN>"
  ]
}
```

**Capabilities:**
- Query Supabase database
- Manage Supabase authentication
- Access Supabase storage
- Real-time subscriptions

### 2. PostgreSQL Server

Direct access to the PostgreSQL database:

```json
{
  "command": "npx",
  "args": ["-y", "@modelcontextprotocol/server-postgres"],
  "env": {
    "DATABASE_URL": "postgres://dnsb_user:dnsb_password@localhost:5432/dnsb_db"
  }
}
```

**Capabilities:**
- Execute SQL queries
- View table schemas
- Manage database records
- Run migrations

**Security Note:** Update the DATABASE_URL to use environment variables in production.

### 3. Filesystem Server

Access to project files:

```json
{
  "command": "npx",
  "args": [
    "-y",
    "@modelcontextprotocol/server-filesystem",
    "/Users/<USER>/Desktop/Dev/dnsb-vakarai"
  ]
}
```

**Capabilities:**
- Read and write project files
- Navigate directory structure
- Search for files
- Manage project assets

### 4. Git Server

Git repository operations:

```json
{
  "command": "npx",
  "args": ["-y", "@modelcontextprotocol/server-git"],
  "env": {
    "GIT_REPO_PATH": "/Users/<USER>/Desktop/Dev/dnsb-vakarai"
  }
}
```

**Capabilities:**
- View commit history
- Check file changes
- Branch management
- Repository status

## Usage

### With Claude Desktop

1. Ensure Claude Desktop is installed
2. The `.mcp.json` file will be automatically detected
3. Claude will have access to the configured servers

### With Other MCP Clients

1. Install the MCP client
2. Point it to the `.mcp.json` configuration file
3. The client will start the configured servers

## Security Considerations

1. **Access Tokens**: The Supabase access token should be stored in environment variables
2. **Database Credentials**: Use environment variables for database credentials
3. **File Access**: The filesystem server is restricted to the project directory
4. **Git Access**: Only allows read operations by default

## Environment Variables

For production use, update `.mcp.json` to use environment variables:

```json
{
  "env": {
    "DATABASE_URL": "${DATABASE_URL}",
    "SUPABASE_ACCESS_TOKEN": "${SUPABASE_ACCESS_TOKEN}"
  }
}
```

## Troubleshooting

### MCP Server Not Starting

1. Check if all required npm packages are installed
2. Verify the paths in configuration are correct
3. Ensure database is running (for PostgreSQL server)
4. Check console for error messages

### Permission Issues

1. Ensure the user has read/write access to the project directory
2. Check database user permissions
3. Verify Git repository access

### Connection Issues

1. Check if PostgreSQL is running on the specified port
2. Verify database credentials
3. Ensure Supabase access token is valid

## Best Practices

1. **Environment-Specific Config**: Use different MCP configurations for development and production
2. **Access Control**: Limit server access to necessary operations only
3. **Credential Management**: Never commit sensitive credentials to version control
4. **Server Selection**: Only enable servers that are actively needed

## Future Enhancements

As the project migrates to Supabase, the MCP configuration will evolve:

1. Remove direct PostgreSQL server once fully migrated
2. Add Supabase-specific tools and integrations
3. Implement custom MCP servers for project-specific operations
4. Add monitoring and logging servers

## References

- [MCP Documentation](https://modelcontextprotocol.io/)
- [MCP SDK](https://github.com/modelcontextprotocol/sdk)
- [Supabase MCP Server](https://github.com/supabase/mcp-server-supabase)