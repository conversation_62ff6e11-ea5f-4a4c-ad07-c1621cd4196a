# Database Structure

This document details the database architecture for the DNSB Vakarai housing community management system.

## Entity Relationship Diagram

```mermaid
erDiagram
    %% Core Entities
    streets {
        int id PK
        string name
        string city
        timestamp created_at
        timestamp updated_at
    }
    
    houses {
        int id PK
        string name
        string address
        int floors
        int street_id FK
        timestamp created_at
        timestamp updated_at
    }
    
    flats {
        int id PK
        int house_id FK
        string number
        int floor
        decimal area
        timestamp created_at
        timestamp updated_at
    }
    
    users {
        int id PK
        string username
        string name
        string email
        string password_hash
        string phone
        string street
        string house_number
        string flat_number
        enum role
        boolean is_profile_updated
        int flat_id FK
        timestamp created_at
        timestamp updated_at
        timestamp last_login
        timestamp email_verified
    }
    
    %% User-Related Tables
    emergency_contacts {
        int id PK
        int user_id FK
        int flat_id FK
        string name
        string phone
        string email
        string relationship
        string custom_relationship
        boolean is_emergency_contact
        timestamp created_at
        timestamp updated_at
    }
    
    contact_messages {
        uuid id PK
        uuid user_id FK
        string name
        string email
        string category
        string subject
        string message
        string phone_number
        enum status
        string admin_notes
        uuid assigned_to FK
        boolean is_anonymous
        timestamp created_at
        timestamp updated_at
        timestamp resolved_at
    }
    
    feedback {
        uuid id PK
        uuid user_id FK
        string name
        string email
        string category
        string title
        string content
        int rating
        boolean allow_contact
        boolean is_anonymous
        enum status
        string admin_response
        timestamp created_at
        timestamp updated_at
        timestamp responded_at
    }
    
    %% Announcements System
    announcements {
        int id PK
        string title
        string content
        int sender_id FK
        string importance
        boolean is_draft
        string recipient_type
        timestamp created_at
        timestamp updated_at
        timestamp sent_at
    }
    
    announcement_houses {
        int id PK
        int announcement_id FK
        int house_id FK
        timestamp created_at
    }
    
    announcement_flats {
        int id PK
        int announcement_id FK
        int flat_id FK
        timestamp created_at
    }
    
    announcement_users {
        int id PK
        int announcement_id FK
        int user_id FK
        timestamp created_at
    }
    
    %% Notification System
    notifications {
        int id PK
        string title
        string content
        string type
        int announcement_id FK
        timestamp created_at
        timestamp sent_at
    }
    
    notification_targets {
        int id PK
        int notification_id FK
        int user_id FK
        boolean is_read
        timestamp read_at
    }
    
    %% Polls System
    polls {
        int id PK
        string title
        string description
        enum status
        int creator_id FK
        timestamp created_at
        timestamp start_date
        timestamp end_date
    }
    
    poll_options {
        int id PK
        int poll_id FK
        string text
        timestamp created_at
    }
    
    poll_votes {
        int id PK
        int poll_id FK
        int option_id FK
        int user_id FK
        timestamp created_at
    }
    
    %% System Tables
    admin_settings {
        uuid id PK
        string key
        string value
        string description
        timestamp created_at
        timestamp updated_at
    }
    
    audit_logs {
        int id PK
        int user_id FK
        string action
        string entity_type
        int entity_id
        jsonb changes
        string ip_address
        timestamp created_at
    }
    
    %% Define relationships
    streets ||--o{ houses : "contains"
    houses ||--o{ flats : "contains"
    flats ||--o{ users : "houses"
    users ||--o{ emergency_contacts : "has"
    users ||--o{ contact_messages : "submits"
    users ||--o{ feedback : "provides"
    users ||--o{ announcements : "creates"
    announcements ||--o{ announcement_houses : "targets"
    announcements ||--o{ announcement_flats : "targets"
    announcements ||--o{ announcement_users : "targets"
    houses ||--o{ announcement_houses : "receives"
    flats ||--o{ announcement_flats : "receives"
    users ||--o{ announcement_users : "receives"
    announcements ||--o{ notifications : "generates"
    notifications ||--o{ notification_targets : "targets"
    users ||--o{ notification_targets : "receives"
    users ||--o{ polls : "creates"
    polls ||--o{ poll_options : "contains"
    users ||--o{ poll_votes : "submits"
    poll_options ||--o{ poll_votes : "receives"
```

## Database Tables

### Core Entities

#### Streets
Stores information about streets in the housing community.
- Primary Key: `id`
- Contains street name and city
- Has one-to-many relationship with houses

#### Houses
Stores information about residential buildings managed by the housing community.
- Primary Key: `id`
- Foreign Key: `street_id` references `streets(id)`
- Contains basic building information like name, address, and number of floors
- Has one-to-many relationship with flats

#### Flats
Represents individual apartments within the houses.
- Primary Key: `id`
- Foreign Key: `house_id` references `houses(id)`
- Contains details like flat number, floor, and area
- Has one-to-many relationship with users

#### Users
Stores user account information for residents, administrators, and editors.
- Primary Key: `id`
- Foreign Key: `flat_id` references `flats(id)` (optional)
- Contains authentication details, personal information, and role assignment
- Roles: `developer`, `super_admin`, `editor`, `user`
- Legacy fields include `street`, `house_number`, and `flat_number` which are being replaced by the `flat_id` relationship

### User-Related Tables

#### Emergency Contacts
Stores emergency contact information for users.
- Primary Key: `id`
- Foreign Keys: 
  - `user_id` references `users(id)`
  - `flat_id` references `flats(id)`
- Contains contact details and relationship information

#### Contact Messages
Stores messages submitted by users through the contact form.
- Primary Key: `id`
- Foreign Keys:
  - `user_id` references `users(id)`
  - `assigned_to` references `users(id)` (optional)
- Contains message content, category, and administrative tracking
- Status options: `NEW`, `IN_PROGRESS`, `RESOLVED`, `CLOSED`

#### Feedback
Stores feedback submitted by users about the community services.
- Primary Key: `id`
- Foreign Key: `user_id` references `users(id)`
- Contains feedback details, ratings, and administrator responses
- Status options match contact messages for consistency

### Communication System

#### Announcements
Stores announcements created by administrators for residents.
- Primary Key: `id`
- Foreign Key: `sender_id` references `users(id)`
- Contains announcement content, importance level, and targeting info
- Importance levels: `normal`, `important`, `urgent`
- Recipient types: `all`, `houses`, `flats`, `users`
- Can target entire community or specific houses, flats, or users through junction tables

#### Announcement Junction Tables
Three tables that implement many-to-many relationships for targeting announcements:
- `announcement_houses`: links announcements to specific houses
- `announcement_flats`: links announcements to specific flats
- `announcement_users`: links announcements to specific users

#### Notifications
Stores notifications generated from announcements.
- Primary Key: `id`
- Foreign Key: `announcement_id` references `announcements(id)` (optional)
- Contains notification content and metadata

#### Notification Targets
Tracks delivery and read status of notifications to users.
- Primary Key: `id`
- Foreign Keys:
  - `notification_id` references `notifications(id)`
  - `user_id` references `users(id)`
- Contains read status and timestamp

### Polling System

#### Polls
Stores community polls.
- Primary Key: `id`
- Foreign Key: `creator_id` references `users(id)`
- Contains poll details, status, and timing information
- Status options: `draft`, `active`, `closed`

#### Poll Options
Stores options for polls.
- Primary Key: `id`
- Foreign Key: `poll_id` references `polls(id)`
- Contains option text

#### Poll Votes
Tracks user votes on polls.
- Primary Key: `id`
- Foreign Keys:
  - `poll_id` references `polls(id)`
  - `option_id` references `poll_options(id)`
  - `user_id` references `users(id)`
- Records which option a user voted for

### System Tables

#### Admin Settings
Stores application configuration settings.
- Primary Key: `id`
- Contains key-value pairs for system configuration
- Includes setting descriptions and timestamps

#### Audit Logs
Tracks changes made to the system for accountability.
- Primary Key: `id`
- Foreign Key: `user_id` references `users(id)` (optional)
- Contains details about actions performed, entity affected, and changes made

## Data Hierarchies and Relationships

The system follows a hierarchical structure:

```
Streets → Houses → Flats → Users → Emergency Contacts
```

This hierarchy is reflected in the database relationships and enforced through foreign key constraints. It allows for efficient filtering and targeting of information:

1. When an announcement targets a house, all flats in that house (and their residents) receive it
2. When accessing user information, the system can traverse the hierarchy to determine their location
3. For administrative purposes, users can be grouped by their housing location

## Data Integrity Rules

1. **Cascading Deletes:**
   - When a street is deleted, all associated houses are deleted
   - When a house is deleted, all associated flats are deleted
   - When a flat is deleted, all users' flat_id fields are set to NULL (not deleted)

2. **Required Relationships:**
   - All houses must have a valid street_id
   - All flats must have a valid house_id
   - Users should have a valid flat_id (can be NULL during initial setup)
   - Emergency contacts must have a valid user_id

3. **Data Consistency:**
   - Always use the relationship fields (flat_id, house_id, street_id) instead of the legacy text fields
   - The legacy fields (street, house_number, flat_number) in the users table are being deprecated 