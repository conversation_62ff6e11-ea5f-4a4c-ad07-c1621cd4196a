# API Reference

This document provides details about the API endpoints available in the DNSB Vakarai system.

## Authentication

### POST /api/auth/login
Authenticates a user and creates a session.

**Request:**
```json
{
  "username": "string", // Username or email
  "password": "string"
}
```

**Response:**
```json
{
  "user": {
    "id": "string",
    "name": "string",
    "email": "string",
    "role": "string", // "super_admin", "editor", "user"
    "isProfileUpdated": "boolean"
  }
}
```

### POST /api/auth/logout
Ends the current user session.

**Response:**
```json
{
  "success": true
}
```

## Users

### GET /api/users
Retrieves a list of users. Requires admin privileges.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search term for name, email, or username
- `role`: Filter by role

**Response:**
```json
{
  "users": [
    {
      "id": "string",
      "name": "string",
      "email": "string",
      "username": "string",
      "role": "string",
      "flat_id": "string",
      "isProfileUpdated": "boolean",
      "lastLogin": "string"
    }
  ],
  "meta": {
    "totalItems": "number",
    "itemsPerPage": "number",
    "totalPages": "number",
    "currentPage": "number"
  }
}
```

### GET /api/users/:id
Retrieves details about a specific user. Requires admin privileges or to be the user.

**Response:**
```json
{
  "id": "string",
  "name": "string",
  "email": "string",
  "username": "string",
  "role": "string",
  "phone": "string",
  "flat_id": "string",
  "flat": {
    "id": "string",
    "number": "string",
    "floor": "number",
    "house": {
      "id": "string",
      "name": "string",
      "address": "string"
    }
  },
  "isProfileUpdated": "boolean",
  "lastLogin": "string",
  "createdAt": "string"
}
```

### POST /api/users
Creates a new user. Requires admin privileges.

**Request:**
```json
{
  "name": "string",
  "email": "string",
  "username": "string",
  "password": "string",
  "role": "string",
  "phone": "string",
  "flat_id": "string"
}
```

**Response:**
```json
{
  "id": "string",
  "name": "string",
  "email": "string",
  "username": "string",
  "role": "string",
  "isProfileUpdated": "boolean"
}
```

### PUT /api/users/:id
Updates user information. Requires admin privileges or to be the user.

**Request:**
```json
{
  "name": "string",
  "email": "string",
  "phone": "string",
  "role": "string", // Admin only
  "flat_id": "string" // Admin only
}
```

**Response:**
```json
{
  "id": "string",
  "name": "string",
  "email": "string",
  "username": "string",
  "role": "string",
  "isProfileUpdated": "boolean"
}
```

### DELETE /api/users/:id
Deletes a user. Requires admin privileges.

**Response:**
```json
{
  "success": true
}
```

## Announcements

### GET /api/announcements
Retrieves a list of announcements visible to the current user.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search term for title or content
- `importance`: Filter by importance level
- `isDraft`: Filter drafts (admin only)

**Response:**
```json
{
  "announcements": [
    {
      "id": "string",
      "title": "string",
      "content": "string",
      "importance": "string",
      "sender_id": "string",
      "sender_name": "string",
      "recipient_type": "string",
      "created_at": "string",
      "sent_at": "string",
      "is_draft": "boolean"
    }
  ],
  "meta": {
    "totalItems": "number",
    "itemsPerPage": "number",
    "totalPages": "number",
    "currentPage": "number"
  }
}
```

### GET /api/announcements/:id
Retrieves details about a specific announcement.

**Response:**
```json
{
  "id": "string",
  "title": "string",
  "content": "string",
  "importance": "string",
  "sender_id": "string",
  "sender_name": "string",
  "recipient_type": "string",
  "created_at": "string",
  "updated_at": "string",
  "sent_at": "string",
  "is_draft": "boolean",
  "targets": {
    "houses": ["string"],
    "flats": ["string"],
    "users": ["string"]
  }
}
```

### POST /api/announcements
Creates a new announcement. Requires admin privileges.

**Request:**
```json
{
  "title": "string",
  "content": "string",
  "importance": "string", // "normal", "important", "urgent"
  "is_draft": "boolean",
  "recipient_type": "string", // "all", "houses", "flats", "users"
  "targets": {
    "houses": ["string"],
    "flats": ["string"],
    "users": ["string"]
  }
}
```

**Response:**
```json
{
  "id": "string",
  "title": "string",
  "created_at": "string",
  "is_draft": "boolean"
}
```

### PUT /api/announcements/:id
Updates an announcement. Requires admin privileges.

**Request:**
```json
{
  "title": "string",
  "content": "string",
  "importance": "string",
  "is_draft": "boolean",
  "recipient_type": "string",
  "targets": {
    "houses": ["string"],
    "flats": ["string"],
    "users": ["string"]
  }
}
```

**Response:**
```json
{
  "id": "string",
  "title": "string",
  "updated_at": "string",
  "is_draft": "boolean"
}
```

### POST /api/announcements/:id/send
Sends a draft announcement. Requires admin privileges.

**Response:**
```json
{
  "id": "string",
  "title": "string",
  "sent_at": "string"
}
```

### DELETE /api/announcements/:id
Deletes an announcement. Requires admin privileges.

**Response:**
```json
{
  "success": true
}
```

## Polls

### GET /api/polls
Retrieves a list of polls available to the current user.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `status`: Filter by status

**Response:**
```json
{
  "polls": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "status": "string", // "draft", "active", "closed"
      "creator_id": "string",
      "creator_name": "string",
      "created_at": "string",
      "start_date": "string",
      "end_date": "string",
      "hasVoted": "boolean",
      "total_votes": "number"
    }
  ],
  "meta": {
    "totalItems": "number",
    "itemsPerPage": "number",
    "totalPages": "number",
    "currentPage": "number"
  }
}
```

### GET /api/polls/:id
Retrieves details about a specific poll.

**Response:**
```json
{
  "id": "string",
  "title": "string",
  "description": "string",
  "status": "string",
  "creator_id": "string",
  "creator_name": "string",
  "created_at": "string",
  "start_date": "string",
  "end_date": "string",
  "options": [
    {
      "id": "string",
      "text": "string",
      "votes": "number" // Only included for closed polls or for admins
    }
  ],
  "userVote": "string", // ID of the option the user voted for, if any
  "total_votes": "number"
}
```

### POST /api/polls
Creates a new poll. Requires admin privileges.

**Request:**
```json
{
  "title": "string",
  "description": "string",
  "status": "string", // "draft", "active", "closed"
  "start_date": "string", // ISO date string
  "end_date": "string", // ISO date string
  "options": [
    { "text": "string" }
  ]
}
```

**Response:**
```json
{
  "id": "string",
  "title": "string",
  "status": "string",
  "created_at": "string"
}
```

### PUT /api/polls/:id
Updates a poll. Requires admin privileges.

**Request:**
```json
{
  "title": "string",
  "description": "string",
  "status": "string",
  "start_date": "string",
  "end_date": "string",
  "options": [
    { "id": "string", "text": "string" }
  ]
}
```

**Response:**
```json
{
  "id": "string",
  "title": "string",
  "status": "string",
  "updated_at": "string"
}
```

### POST /api/polls/:id/vote
Submit a vote for a poll option.

**Request:**
```json
{
  "option_id": "string"
}
```

**Response:**
```json
{
  "success": true,
  "poll_id": "string",
  "option_id": "string"
}
```

### DELETE /api/polls/:id
Deletes a poll. Requires admin privileges.

**Response:**
```json
{
  "success": true
}
```

## Contact Messages

### GET /api/contact-messages
Retrieves a list of contact messages. For users, shows only their own messages. For admins, shows all messages.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `status`: Filter by status
- `category`: Filter by category

**Response:**
```json
{
  "messages": [
    {
      "id": "string",
      "name": "string",
      "email": "string",
      "category": "string",
      "subject": "string",
      "message": "string",
      "status": "string",
      "created_at": "string",
      "updated_at": "string",
      "resolved_at": "string"
    }
  ],
  "meta": {
    "totalItems": "number",
    "itemsPerPage": "number",
    "totalPages": "number",
    "currentPage": "number"
  }
}
```

### GET /api/contact-messages/:id
Retrieves details about a specific contact message. Users can only access their own messages.

**Response:**
```json
{
  "id": "string",
  "user_id": "string",
  "name": "string",
  "email": "string",
  "category": "string",
  "subject": "string",
  "message": "string",
  "phone_number": "string",
  "status": "string",
  "admin_notes": "string", // Admin only
  "assigned_to": "string", // Admin only
  "created_at": "string",
  "updated_at": "string",
  "resolved_at": "string"
}
```

### POST /api/contact-messages
Submits a new contact message.

**Request:**
```json
{
  "name": "string",
  "email": "string",
  "category": "string",
  "subject": "string",
  "message": "string",
  "phone_number": "string",
  "is_anonymous": "boolean"
}
```

**Response:**
```json
{
  "id": "string",
  "subject": "string",
  "status": "string",
  "created_at": "string"
}
```

### PUT /api/contact-messages/:id
Updates a contact message. For admins to update status, assign, or add notes.

**Request:**
```json
{
  "status": "string",
  "admin_notes": "string",
  "assigned_to": "string"
}
```

**Response:**
```json
{
  "id": "string",
  "subject": "string",
  "status": "string",
  "updated_at": "string"
}
```

## Feedback

### GET /api/feedback
Retrieves a list of feedback. For users, shows only their own feedback. For admins, shows all feedback.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `status`: Filter by status
- `category`: Filter by category
- `rating`: Filter by rating

**Response:**
```json
{
  "feedback": [
    {
      "id": "string",
      "title": "string",
      "category": "string",
      "rating": "number",
      "status": "string",
      "created_at": "string",
      "responded_at": "string"
    }
  ],
  "meta": {
    "totalItems": "number",
    "itemsPerPage": "number",
    "totalPages": "number",
    "currentPage": "number"
  }
}
```

### GET /api/feedback/:id
Retrieves details about specific feedback. Users can only access their own feedback.

**Response:**
```json
{
  "id": "string",
  "user_id": "string",
  "name": "string",
  "email": "string",
  "category": "string",
  "title": "string",
  "content": "string",
  "rating": "number",
  "allow_contact": "boolean",
  "is_anonymous": "boolean",
  "status": "string",
  "admin_response": "string",
  "created_at": "string",
  "updated_at": "string",
  "responded_at": "string"
}
```

### POST /api/feedback
Submits new feedback.

**Request:**
```json
{
  "name": "string",
  "email": "string",
  "category": "string",
  "title": "string",
  "content": "string",
  "rating": "number",
  "allow_contact": "boolean",
  "is_anonymous": "boolean"
}
```

**Response:**
```json
{
  "id": "string",
  "title": "string",
  "rating": "number",
  "created_at": "string"
}
```

### PUT /api/feedback/:id
Updates feedback. For admins to update status or add a response.

**Request:**
```json
{
  "status": "string",
  "admin_response": "string"
}
```

**Response:**
```json
{
  "id": "string",
  "title": "string",
  "status": "string",
  "updated_at": "string"
}
```

## Emergency Contacts

### GET /api/emergency-contacts
Retrieves a list of user's emergency contacts. Admins can include a flat_id parameter to view specific flat's contacts.

**Query Parameters:**
- `flat_id`: Filter by flat (admin only)

**Response:**
```json
{
  "contacts": [
    {
      "id": "string",
      "name": "string",
      "phone": "string",
      "email": "string",
      "relationship": "string",
      "custom_relationship": "string",
      "is_emergency_contact": "boolean",
      "created_at": "string"
    }
  ]
}
```

### POST /api/emergency-contacts
Creates a new emergency contact.

**Request:**
```json
{
  "name": "string",
  "phone": "string",
  "email": "string",
  "relationship": "string",
  "custom_relationship": "string",
  "is_emergency_contact": "boolean"
}
```

**Response:**
```json
{
  "id": "string",
  "name": "string",
  "phone": "string",
  "created_at": "string"
}
```

### PUT /api/emergency-contacts/:id
Updates an emergency contact.

**Request:**
```json
{
  "name": "string",
  "phone": "string",
  "email": "string",
  "relationship": "string",
  "custom_relationship": "string",
  "is_emergency_contact": "boolean"
}
```

**Response:**
```json
{
  "id": "string",
  "name": "string",
  "updated_at": "string"
}
```

### DELETE /api/emergency-contacts/:id
Deletes an emergency contact.

**Response:**
```json
{
  "success": true
}
``` 