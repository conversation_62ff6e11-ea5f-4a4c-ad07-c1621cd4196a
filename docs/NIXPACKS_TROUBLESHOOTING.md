# Nixpacks Deployment Troubleshooting Guide

## Common Nixpacks Deployment Issues

### Issue 1: Deno Detection Instead of Node.js

#### Problem
```
NIXPACKS_METADATA = 'deno'
```
Nixpacks incorrectly detects the Next.js application as a Deno project.

#### Root Cause
- Conflicting detection signals in the project
- Node.js version mismatch between package.json and nixpacks.toml
- Nixpacks detection algorithm confusion

#### Solution
Force Node.js provider detection:
```toml
[providers]
node = true
deno = false
```

### Issue 2: Node.js 22 "undefined variable" Error

#### Problem
```
error: undefined variable 'nodejs_22'
at /app/.nixpacks/nixpkgs-[hash].nix:19:9
```

#### Root Cause
The nixpkgs commit used by Nixpacks doesn't include `nodejs_22`. This is a known issue where the default nixpkgs archive is outdated.

### Issue 3: "No such file or directory" Error

#### Problem
```
Creating Nix environment file → No such file or directory (os error 2)
```

#### Root Cause
- Wrong provider (Deno) being used instead of Node.js
- Missing directory structure for Nix environment
- File system permissions or path issues

### Solutions (in order of recommendation)

#### ✅ Solution 1: Force Node.js Detection (RECOMMENDED)
Force Node.js provider and disable Deno detection:

**Quick Fix:**
```bash
npm run nixpacks:force-node
git add nixpacks.toml
git commit -m "fix: Force Node.js detection, disable Deno"
git push origin main
```

**Manual Configuration:**
```toml
[providers]
node = true
deno = false

[variables]
NIXPACKS_NODE_VERSION = "22"

[start]
cmd = "node server.js"
```

#### 🔄 Solution 2: Use Node.js 20 (Stable Fallback)
Node.js 20 is LTS, stable, and fully compatible with Next.js 15.

**Update your `nixpacks.toml`:**
```toml
[variables]
NIXPACKS_NODE_VERSION = "20"

[phases.setup]
nixPkgs = ["nodejs_20", "npm"]
nixpkgsArchive = "nixos-unstable"

[phases.install]
cmds = ["npm ci"]
dependsOn = ["setup"]

[phases.build]
cmds = ["npm run build"]
dependsOn = ["install"]

[start]
cmd = "node server.js"

[buildEnvs]
NODE_ENV = "production"
NEXT_TELEMETRY_DISABLED = "1"
```

#### 🔄 Solution 2: Environment Variable Method
Set `NIXPACKS_NODE_VERSION=20` in Coolify environment variables and use minimal nixpacks.toml:

```toml
[start]
cmd = "node server.js"
```

#### ⚠️ Solution 3: Force Node.js 22 (Advanced)
If you specifically need Node.js 22, try this configuration:

```toml
[variables]
NIXPACKS_NODE_VERSION = "22"

[phases.setup]
nixPkgs = ["nodejs_22", "npm"]
nixpkgsArchive = "https://github.com/NixOS/nixpkgs/archive/nixos-unstable.tar.gz"

[phases.install]
cmds = ["npm ci"]
dependsOn = ["setup"]

[phases.build]
cmds = ["npm run build"]
dependsOn = ["install"]

[start]
cmd = "node server.js"

[buildEnvs]
NODE_ENV = "production"
NEXT_TELEMETRY_DISABLED = "1"
```

### Step-by-Step Deployment Fix

1. **Update Configuration**:
   ```bash
   # Use the recommended Node.js 20 configuration
   cp nixpacks-alternatives.toml nixpacks.toml
   ```

2. **Commit Changes**:
   ```bash
   git add nixpacks.toml
   git commit -m "fix: Use Node.js 20 for Nixpacks compatibility"
   git push origin main
   ```

3. **Update Coolify Environment Variables**:
   - Remove any `NIXPACKS_NODE_VERSION` environment variable if set
   - Or set it to `NIXPACKS_NODE_VERSION=20`

4. **Deploy**:
   - Trigger a new deployment in Coolify
   - Monitor the build logs for successful Node.js installation

### Verification Steps

After deployment, verify the Node.js version:

1. **Check Build Logs**:
   Look for successful installation of `nodejs_20` in the setup phase.

2. **Runtime Verification**:
   ```bash
   # In your application, you can log the Node.js version
   console.log('Node.js version:', process.version);
   ```

3. **Health Check**:
   ```bash
   curl http://your-domain/api/health
   ```

### Common Issues and Solutions

#### Issue: Build still fails with Node.js 20
**Solution**: Use environment variable method instead of nixpacks.toml configuration.

#### Issue: Application doesn't start
**Solution**: Verify the start command matches your Next.js build output:
- For standalone builds: `node server.js`
- For standard builds: `npm start`

#### Issue: Environment variables not available
**Solution**: Ensure all required environment variables are set in Coolify, not in nixpacks.toml.

### Node.js Version Compatibility

| Node.js Version | Next.js 15 Support | Nixpacks Availability | Recommendation |
|----------------|-------------------|---------------------|----------------|
| Node.js 18     | ✅ Supported       | ✅ Available         | ⚠️ EOL April 2025 |
| Node.js 20     | ✅ Supported       | ✅ Available         | ✅ **Recommended** |
| Node.js 22     | ✅ Supported       | ⚠️ Limited          | ⚠️ Use if needed |

### Why Node.js 20 is Recommended

1. **LTS Status**: Long-term support until April 2026
2. **Stability**: Mature and well-tested
3. **Compatibility**: Full Next.js 15 support
4. **Nixpacks Support**: Readily available in nixpkgs
5. **Performance**: Excellent performance for Next.js applications

### Rollback Plan

If Nixpacks deployment fails:

1. **Immediate**: Switch back to Docker Compose deployment
2. **Debug**: Check Coolify build logs for specific errors
3. **Fix**: Apply the recommended Node.js 20 configuration
4. **Retry**: Deploy again with fixed configuration

### Additional Resources

- [Nixpacks Node.js Provider Documentation](https://nixpacks.com/docs/providers/node)
- [Next.js Node.js Requirements](https://nextjs.org/docs/getting-started/installation#system-requirements)
- [Coolify Nixpacks Guide](https://coolify.io/docs/builds/packs/nixpacks)

### Support

If you continue to experience issues:

1. Check the [Coolify GitHub Issues](https://github.com/coollabsio/coolify/issues) for similar problems
2. Verify your Next.js application works locally with Node.js 20
3. Test the Docker Compose deployment as a fallback
4. Consider using the environment variable method instead of nixpacks.toml configuration
