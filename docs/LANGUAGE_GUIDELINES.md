# Language Guidelines for DNSB Vakarai

This document provides guidelines and examples for using English in code but Lithuanian in user interface elements.

## Core Principles

1. **Code in English**: All code elements (variables, functions, comments, documentation) should be in English
2. **UI in Lithuanian**: All user-facing text and content should be in Lithuanian
3. **Separate Logic from Presentation**: Structure components to clearly separate business logic from UI text

## Examples

### Good Practices

#### Component Structure

```tsx
// Good: English variable names and logic, Lithuanian UI text
function UserProfile({ userData }) {
  const [isEditing, setIsEditing] = useState(false);
  const { trackEvent } = useAnalytics();
  
  const handleSave = () => {
    trackEvent('profile_save');
    setIsEditing(false);
  };
  
  return (
    <div>
      <h1>Vartotojo Profilis</h1>
      {isEditing ? (
        <Button onClick={handleSave}>Išsaugoti pakeitimus</Button>
      ) : (
        <Button onClick={() => setIsEditing(true)}>Redaguoti profilį</Button>
      )}
    </div>
  );
}
```

#### Error Messages

```tsx
// Good: English variable names, Lithuanian error messages
function validateForm(data) {
  const errors = {};
  
  if (!data.username) {
    errors.username = "Vartotojo vardas yra privalomas";
  }
  
  if (!data.email) {
    errors.email = "El. paštas yra privalomas";
  } else if (!isValidEmail(data.email)) {
    errors.email = "Neteisingas el. pašto formatas";
  }
  
  return errors;
}
```

#### Comments

```tsx
// Good: English code and comments
/**
 * Calculates the total amount of fees to be paid
 * for a given property based on its size and type.
 *
 * @param {number} size - Property size in square meters
 * @param {string} type - Property type (flat, house, etc.)
 * @returns {number} Total fee amount in euros
 */
function calculateFees(size, type) {
  // Base fee depends on property type
  const baseFee = type === 'flat' ? 10 : 15;
  
  // Additional fee is calculated per square meter
  const additionalFee = size * 0.5;
  
  return baseFee + additionalFee;
}

// Then in the UI:
<div>
  <h2>Mokesčių skaičiuoklė</h2>
  <p>Bendras mokestis: {calculateFees(size, type)} €</p>
</div>
```

### Avoid These Practices

#### Mixed Language Variables

```tsx
// Bad: Mixed language in variable names
function vartotojoProfilisComponent({ userData }) {
  const [redaguojama, setRedaguojama] = useState(false);
  
  return (
    <div>
      <h1>Vartotojo Profilis</h1>
      {redaguojama ? (
        <Button onClick={() => setRedaguojama(false)}>Išsaugoti</Button>
      ) : (
        <Button onClick={() => setRedaguojama(true)}>Redaguoti</Button>
      )}
    </div>
  );
}
```

#### Mixed Language Comments

```tsx
// Bad: Mixed language in comments
/**
 * Skaičiuoja mokesčius pagal būsto dydį
 * 
 * @param {number} size - Property size in square meters
 * @returns {number} Total fee
 */
function calculateFees(size) {
  // Pradinis mokestis
  const baseFee = 10;
  
  // Add size-based fee
  return baseFee + (size * 0.5);
}
```

## Implementation Strategies

### Text Constants

For larger applications, consider organizing UI text in dedicated constants:

```tsx
// constants/text.ts
export const ProfileTexts = {
  title: "Vartotojo Profilis",
  editButton: "Redaguoti profilį",
  saveButton: "Išsaugoti pakeitimus",
  cancelButton: "Atšaukti",
  // etc.
};

// components/ProfileComponent.tsx
import { ProfileTexts } from '../constants/text';

function ProfileComponent() {
  return (
    <div>
      <h1>{ProfileTexts.title}</h1>
      <Button>{ProfileTexts.editButton}</Button>
    </div>
  );
}
```

### Error Message Organization

Organize error messages in a structured way:

```tsx
// constants/errorMessages.ts
export const FormErrors = {
  required: "Laukas yra privalomas",
  email: {
    invalid: "Neteisingas el. pašto formatas",
    taken: "Šis el. paštas jau naudojamas"
  },
  password: {
    tooShort: "Slaptažodis turi būti bent 8 simbolių",
    requiresNumber: "Slaptažodis turi turėti bent vieną skaičių"
  }
};

// Usage in form validation
if (!data.email) {
  errors.email = FormErrors.required;
} else if (!isValidEmail(data.email)) {
  errors.email = FormErrors.email.invalid;
}
```

## Special Considerations

### Technical Terms

Some technical terms might not have good Lithuanian equivalents. In these cases:

1. Use the English term but with Lithuanian grammar if needed
2. Add a brief explanation in Lithuanian for users who might not be familiar with the term

Example:
```tsx
<Tooltip content="'Cookie' yra maža duomenų dalis, kurią svetainė išsaugo jūsų naršyklėje">
  <div>Slapukai (cookies)</div>
</Tooltip>
```

### Analytics Events

Keep analytics event names in English for consistency and easier analysis:

```tsx
// Good: English event names, properties and values
trackEvent('poll_vote', {
  poll_id: pollId,
  option_id: selectedOption,
  user_role: userRole
});

// But use Lithuanian for any text that might be captured
trackEvent('error_occurred', {
  error_message: "Nepavyko prisijungti prie serverio",
  user_facing: true
});
```

## Code Review Checklist

When reviewing code, check for:

- [ ] All variable, function, and class names are in English
- [ ] All comments and documentation are in English
- [ ] All user-facing text is in Lithuanian
- [ ] No hardcoded UI text in the component logic (use constants)
- [ ] Consistent terminology across similar components