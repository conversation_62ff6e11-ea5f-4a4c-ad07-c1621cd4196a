# BunnyCDN Integration Guide

## Overview

This document describes the BunnyCDN integration for handling announcement attachments in the DNSB Vakarai application. The integration replaces Supabase Storage with BunnyCDN for better global performance and cost efficiency.

## Architecture

### Storage Factory Pattern

The application uses a storage factory pattern to switch between different storage backends:

- **Production**: BunnyCDN for global CDN distribution
- **Development**: Local file storage for rapid development

```typescript
// Automatic selection based on environment
const storageService = getStorageService();
```

### File Upload Flow

1. User uploads file through the attachment component
2. File is validated (type, size, name)
3. File hash is generated for deduplication
4. File is uploaded to CDN/local storage
5. Metadata is saved to PostgreSQL database
6. CDN URL is returned to the client

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# BunnyCDN Configuration
BUNNYCDN_API_KEY=your-bunnycdn-api-key
BUNNYCDN_STORAGE_ZONE_NAME=dnsb-attachments
BUNNYCDN_STORAGE_ZONE_PASSWORD=your-storage-zone-password
BUNNYCDN_STORAGE_ZONE_REGION=de # Options: de, uk, ny, la, sg, syd
BUNNYCDN_PULL_ZONE_URL=https://your-pullzone.b-cdn.net
BUNNYCDN_STORAGE_API_URL=https://storage.bunnycdn.com

# Optional: Force BunnyCDN in development
USE_BUNNYCDN=true
```

### BunnyCDN Setup

1. **Create Storage Zone**:
   - Log in to BunnyCDN panel
   - Go to Storage → Add Storage Zone
   - Name: `dnsb-attachments`
   - Region: Frankfurt (de) recommended for EU
   - Enable: Geo-Replication if needed

2. **Create Pull Zone**:
   - Go to CDN → Add Pull Zone
   - Name: `dnsb-cdn`
   - Origin Type: Storage Zone
   - Select your storage zone
   - Enable: Optimize for images

3. **Configure CORS**:
   - In Pull Zone settings → Headers
   - Add CORS headers:
     ```
     Access-Control-Allow-Origin: https://yourdomain.com
     Access-Control-Allow-Methods: GET, HEAD, OPTIONS
     Access-Control-Allow-Headers: Content-Type
     ```

## File Structure

### CDN Path Structure
```
announcements/
├── {announcement-id}/
│   ├── attachments/
│   │   ├── document_1234567890_abc123.pdf
│   │   └── image_1234567890_def456.jpg
│   └── thumbnails/
│       └── thumb_image_1234567890_def456.jpg
```

### Database Schema

The attachment system adds these fields to the database:

```sql
-- announcement_attachments table additions
cdn_path VARCHAR(500),          -- Path in CDN storage
cdn_url VARCHAR(1000),          -- Full CDN URL
thumbnail_cdn_path VARCHAR(500), -- Thumbnail path (future)
thumbnail_cdn_url VARCHAR(1000), -- Thumbnail URL (future)
cdn_region VARCHAR(50),         -- CDN region
processing_status VARCHAR(50),   -- Upload/processing status
file_hash VARCHAR(64),          -- SHA-256 hash for deduplication
download_count INTEGER,         -- Download tracking
last_accessed_at TIMESTAMP      -- Last access time

-- attachment_operations table (new)
CREATE TABLE attachment_operations (
  id SERIAL PRIMARY KEY,
  attachment_id INTEGER REFERENCES announcement_attachments(id),
  operation_type VARCHAR(50),   -- upload, download, delete
  user_id UUID REFERENCES users(id),
  ip_address INET,
  user_agent TEXT,
  metadata JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Monitoring

### Health Check

The `/api/health` endpoint includes CDN status:

```json
{
  "checks": {
    "application": true,
    "supabase": true,
    "environment": true,
    "cdn": true
  },
  "cdnProvider": "BunnyCDN"
}
```

### Metrics

The CDN monitor tracks:
- Upload/download latency
- Error rates by type
- Active uploads
- Storage usage
- Bandwidth usage

Access metrics:
```typescript
const metrics = cdnMonitor.getMetrics();
```

### Error Handling

The system includes automatic retry logic and alerting:

- **Retry Logic**: 3 attempts with exponential backoff
- **Critical Errors**: Authentication failures, service outages
- **Error Tracking**: Recent errors tracked for rate limiting
- **Alerts**: Logged errors (integrate with your alerting service)

## Security

### File Validation

- **Allowed Types**: Images, PDFs, Office documents, text files
- **Size Limits**: 10MB for images, 50MB for other files
- **Name Sanitization**: Removes unsafe characters, adds timestamp
- **Path Traversal Protection**: Validates all file paths

### Access Control

- Only authenticated users can upload
- Only admins/editors can delete attachments
- Draft announcement attachments restricted to admins
- Download tracking for audit trail

## Development

### Local Storage

In development, files are stored locally:
- Path: `./uploads/`
- Served via: `/uploads/[...path]` route
- Auto-created directories
- `.gitignore` configured

### Testing

```bash
# Test file upload
curl -X POST http://localhost:3000/api/announcements/attachments \
  -H "Cookie: $AUTH_COOKIE" \
  -F "file=@test.pdf" \
  -F "announcementId=123"

# Test health check
curl http://localhost:3000/api/health
```

## Migration Guide

### From Supabase Storage

1. **Export existing files** from Supabase Storage
2. **Upload to BunnyCDN** using migration script
3. **Update database** with new CDN paths
4. **Test downloads** to verify migration

### Migration Script Example

```typescript
// scripts/migrate-to-bunnycdn.ts
async function migrateAttachments() {
  const attachments = await getAttachmentsFromDB();
  
  for (const attachment of attachments) {
    // Download from Supabase
    const file = await downloadFromSupabase(attachment.file_path);
    
    // Upload to BunnyCDN
    const cdnResult = await bunnyCDN.uploadFile(
      file,
      generateCDNPath(attachment.announcement_id, attachment.file_name)
    );
    
    // Update database
    await updateAttachmentCDNInfo(attachment.id, {
      cdn_path: cdnResult.path,
      cdn_url: cdnResult.url
    });
  }
}
```

## Troubleshooting

### Common Issues

1. **Upload Fails**
   - Check BunnyCDN API key and permissions
   - Verify storage zone password
   - Check file size and type limits

2. **CORS Errors**
   - Verify Pull Zone CORS configuration
   - Check allowed origins match your domain

3. **404 on Downloads**
   - Verify file exists in CDN
   - Check CDN path in database
   - Ensure Pull Zone URL is correct

### Debug Mode

Enable debug logging:
```typescript
// Set in environment
LOG_LEVEL=debug

// Or in code
import pino from 'pino';
const logger = pino({ level: 'debug' });
```

## Cost Optimization

### BunnyCDN Pricing (as of 2024)
- Storage: $0.01/GB/month
- Bandwidth: $0.01-$0.06/GB depending on region
- Requests: Free

### Optimization Tips
1. **Enable image optimization** in Pull Zone
2. **Set appropriate cache headers** (1 year for immutable files)
3. **Use WebP format** for images when possible
4. **Monitor bandwidth usage** via BunnyCDN panel
5. **Consider regional replication** based on user distribution

## Future Enhancements

- [ ] Image thumbnail generation
- [ ] Video transcoding support
- [ ] Bulk upload optimization
- [ ] CDN cache purging interface
- [ ] Advanced analytics dashboard
- [ ] Virus scanning integration