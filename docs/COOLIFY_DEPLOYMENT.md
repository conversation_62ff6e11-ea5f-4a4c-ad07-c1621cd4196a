# Coolify Deployment Guide

This guide covers deploying the DNSB Vakarai application using Coolify, a self-hosted PaaS platform.

## Prerequisites

- Coolify instance running and accessible
- Docker and Docker Compose support
- Self-hosted Supabase instance or Supabase Cloud project
- Domain name configured (optional but recommended)
- GitHub repository access for automatic deployments

## Deployment Steps

### 1. Prepare Your Coolify Instance

Ensure your Coolify instance is properly configured:

- <PERSON><PERSON> and Docker Compose are installed
- Sufficient resources allocated (minimum 2GB RAM, 2 CPU cores)
- Network access to external services (Supabase, SMTP)

### 2. Create a New Project in Coolify

1. Log into your Coolify dashboard
2. Create a new project
3. Choose "Docker Compose" as the deployment method
4. Connect your Git repository (GitHub/GitLab)

### 3. Configure Environment Variables

Generate environment variables using the provided script:

```bash
npm run deploy:env
```

Or manually set these environment variables in Coolify:

#### Required Environment Variables

```bash
# Application
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Email Configuration (SMTP settings are stored in database)
# Configure SMTP via Admin → Settings → SMTP Settings after deployment

# Security
CSRF_SECRET=your-csrf-secret-key-at-least-32-chars
CRON_API_KEY=your-cron-api-key

# Analytics & Logging
NEXT_PUBLIC_POSTHOG_KEY=your-posthog-project-api-key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
LOG_LEVEL=info

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_PASSWORD_RESET=true
```

#### Optional Environment Variables

```bash
# External Services
CLOUDINARY_URL=cloudinary://api_key:api_secret@cloud_name
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Email Development Settings
EMAIL_DEV_MODE=live
EMAIL_DEV_RECIPIENT=<EMAIL>
```

### 4. Configure Docker Compose

The project includes a `docker-compose.yml` file optimized for Coolify. Key features:

- **Health checks**: Automatic health monitoring
- **Persistent volumes**: For logs and uploads
- **Environment variable support**: All variables passed through
- **Coolify labels**: For automatic discovery and management

### 5. Deploy the Application

1. In Coolify, configure the deployment:
   - Set the repository URL
   - Choose the main branch (or your preferred branch)
   - Set the build context to the root directory
   - Ensure Docker Compose file is detected

2. Configure domain and SSL:
   - Add your domain name
   - Enable automatic SSL certificate generation
   - Configure any necessary redirects

3. Deploy the application:
   - Click "Deploy" in Coolify
   - Monitor the build and deployment logs
   - Wait for the application to start successfully

### 6. Verify Deployment

Run the deployment verification script:

```bash
npm run deploy:verify
```

This will check:
- ✅ Supabase connection
- ✅ API endpoints
- ✅ Email functionality
- ✅ Application health

**Note**: For development environment verification, use:
```bash
npm run dev:verify
```

This checks environment variables, required files, and package scripts without requiring a running application.

### 7. Configure Monitoring and Backups

#### Health Monitoring
The application includes built-in health checks at `/api/health`. Coolify will automatically monitor this endpoint.

#### Log Management
Logs are persisted in the `./logs` volume. Configure log rotation as needed.

#### Backup Strategy
- **Database**: Use Supabase's built-in backup features
- **Application data**: Backup the `./uploads` volume if used
- **Environment variables**: Keep a secure backup of your environment configuration

## Troubleshooting

### Common Issues

#### 1. Build Failures
- Check that all environment variables are set
- Verify Docker has sufficient resources
- Review build logs for specific error messages

#### 2. Application Won't Start
- Check health check endpoint: `curl http://your-domain/api/health`
- Review application logs in Coolify
- Verify Supabase connection settings

#### 3. Email Not Working
- Test SMTP settings manually
- Check firewall rules for SMTP ports
- Verify email provider allows connections from your server

#### 4. Database Connection Issues
- Verify Supabase URL and keys are correct
- Check network connectivity to Supabase
- Ensure Supabase project is not paused

### Debugging Commands

```bash
# Check application logs
docker logs dnsb-vakarai-web

# Test Supabase connection
npm run supabase:test

# Verify deployment
npm run deploy:verify

# Check environment variables
docker exec dnsb-vakarai-web env | grep -E "(SUPABASE|SMTP)"
```

## Scaling and Performance

### Horizontal Scaling
Coolify supports scaling the application across multiple containers:

1. In Coolify dashboard, increase the replica count
2. Configure load balancing if needed
3. Monitor resource usage and adjust as necessary

### Performance Optimization
- Enable Next.js caching
- Configure CDN for static assets
- Monitor and optimize database queries
- Use PostHog analytics to identify performance bottlenecks

## Security Considerations

- Keep environment variables secure and never commit them to Git
- Regularly update dependencies and base images
- Configure proper firewall rules
- Use HTTPS for all connections
- Regularly backup and test restore procedures
- Monitor logs for suspicious activity

## Maintenance

### Regular Tasks
- Monitor application health and performance
- Review and rotate security keys periodically
- Update dependencies and security patches
- Backup environment configuration
- Test disaster recovery procedures

### Updates and Deployments
Coolify supports automatic deployments from Git:

1. Push changes to your repository
2. Coolify will automatically detect changes
3. Build and deploy the new version
4. Monitor deployment success

For manual deployments:
1. Use Coolify's manual deployment trigger
2. Monitor build and deployment logs
3. Verify application functionality post-deployment

## Support and Resources

- **Coolify Documentation**: [https://coolify.io/docs](https://coolify.io/docs)
- **Supabase Documentation**: [https://supabase.com/docs](https://supabase.com/docs)
- **Next.js Deployment**: [https://nextjs.org/docs/deployment](https://nextjs.org/docs/deployment)

For application-specific issues, check the project's README.md and other documentation files.
