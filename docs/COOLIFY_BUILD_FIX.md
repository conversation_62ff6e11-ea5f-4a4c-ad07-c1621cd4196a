# Coolify Build Fix for Supabase Environment Variables

## Problem Description

The DNSB Vakarai Next.js application was failing to build on Coolify during the Docker build process with the error:

```
Error: Missing or invalid Supabase environment variables. Please check NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY
```

This error occurred during the static page generation phase, specifically when prerendering the "/_not-found" page.

## Root Cause

The issue was caused by:

1. **Build-time validation**: The Supabase client validation code was being executed during Next.js static generation
2. **Layout rendering**: The `SupabaseAuthProvider` component in the root layout calls `createClient()` during component initialization
3. **Static generation**: During build, Next.js renders layouts for static pages (including `/_not-found`)
4. **Placeholder detection**: The validation logic detected placeholder values and threw an error
5. **Build failure**: This caused the entire build process to fail with exit code 1

## Solution Overview

The fix involves multiple changes to make the build process more robust:

### 1. Build-time Detection Logic

Modified Supabase client creation to detect build-time vs runtime:

- **Build time**: Allow placeholder values to prevent build failures
- **Runtime**: Enforce strict validation when the client is actually used
- **Detection**: Use `NODE_ENV === 'production'` + absence of `RUNTIME_ENV` flag

### 2. Docker Build Arguments

Updated the Dockerfile to accept build arguments:

- Pass real Supabase environment variables during build
- Fall back to placeholders if not provided
- More flexible build process

### 3. Docker Compose Configuration

Enhanced docker-compose.yml to:

- Pass environment variables as build arguments
- Set `RUNTIME_ENV=true` to distinguish runtime from build time
- Maintain backward compatibility

## Implementation Details

### Modified Files

1. **`lib/supabase/client.ts`**
   - Added build-time detection logic
   - Skip validation during static generation
   - Use fallback values when needed

2. **`lib/supabase/server.ts`**
   - Applied same build-time logic to server client
   - Updated both `createClient()` and `createServiceClient()`

3. **`Dockerfile`**
   - Added ARG declarations for Supabase variables
   - Use build args with fallback to placeholders
   - More flexible environment variable handling

4. **`docker-compose.yml`**
   - Added build args section
   - Pass Supabase environment variables to build process
   - Added `RUNTIME_ENV=true` for runtime detection

## Deployment Instructions

### For Coolify Deployment

1. **Set Environment Variables in Coolify**:
   ```bash
   NEXT_PUBLIC_SUPABASE_URL=https://your-supabase-url.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
   ```

2. **Deploy with Docker Compose**:
   - Coolify will automatically use the environment variables
   - Build arguments will be passed to the Docker build process
   - Runtime environment will have proper validation

### For Manual Docker Deployment

1. **Create `.env` file**:
   ```bash
   NEXT_PUBLIC_SUPABASE_URL=https://your-supabase-url.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
   ```

2. **Build and run**:
   ```bash
   docker-compose up --build
   ```

## Verification

### Build Success
- Docker build should complete without Supabase validation errors
- Static pages should generate successfully
- No "placeholder" related errors during build

### Runtime Validation
- Application should start successfully
- Supabase client should validate environment variables at runtime
- Proper error handling for missing/invalid credentials

### Health Check
- `/api/health` endpoint should report Supabase connection status
- Authentication should work properly
- Database operations should function correctly

## Troubleshooting

### Build Still Fails
1. Check that environment variables are set in Coolify
2. Verify docker-compose.yml has correct build args
3. Ensure Dockerfile ARG declarations are present

### Runtime Errors
1. Verify environment variables are correctly set
2. Check Supabase URL and keys are valid
3. Test connection with `/api/health` endpoint

### Placeholder Values in Production
1. Ensure `RUNTIME_ENV=true` is set in production
2. Check that real environment variables are provided
3. Verify build args are being passed correctly

## Benefits

1. **Robust Build Process**: Builds succeed even with missing environment variables
2. **Flexible Deployment**: Works with various deployment platforms
3. **Runtime Validation**: Proper error handling when application actually runs
4. **Backward Compatibility**: Existing deployments continue to work
5. **Better Error Messages**: Clear distinction between build and runtime issues

## Future Considerations

- Consider adding environment variable validation middleware
- Implement graceful degradation for missing Supabase connection
- Add more comprehensive health checks for external dependencies
