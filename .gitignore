# Dependencies
/node_modules
/.pnp
.pnp.js
node_modules/

# Testing
/coverage
*.lcov
.nyc_output

# Next.js
/.next/
/out/
.next/

# Production builds
/build
/dist

# Environment files (all variants)
.env
.env.*
.env*.local
.env.local
.env.development
.env.development.local
.env.test
.env.test.local
.env.production
.env.production.local
.env*.production
.env.sentry-build-plugin

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts
*.d.ts.map

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.vim/
.emacs.d/
.sublime-*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.tmp
*.temp

# Logs
*.log
*.log.*
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Security and credentials
*.pem
*.key
*.p12
*.pfx
*.crt
*.cer
*.der
auth.json
credentials.json
service-account.json

# Backup files
*.backup
*.bak
*.orig
*.rej
*~

# Database files and backups
/backups
*.sql
*.sql.gz
*.dump
*.sqlite
*.sqlite3
*.db
/data/import/*.csv
/data/import/*.xlsx
/data/export/

# Temporary files and directories
.tmp/
.temp/
tmp/
temp/

# Local file uploads (development)
/uploads/
uploads/

# MCP and Cursor configuration (contains sensitive tokens)
.cursor/mcp.json
.mcp.json

# Supabase local development
supabase/.temp/
supabase/.branches/
supabase/.env.keys
supabase/.env.local
supabase/.env.*.local

# Sentry
.env.sentry-build-plugin
.sentryclirc

# PostHog
.posthog/

# Docker
.dockerignore.backup
docker-compose.override.yml

# Coolify
.coolify/
coolify.yml
.env.coolify

# Package manager lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
