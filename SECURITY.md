# Security Guidelines for DNSB Vakarai

This document outlines security best practices and guidelines for the DNSB Vakarai application. All developers working on this project should follow these guidelines to ensure the application remains secure.

## Environment Variables

- **Never commit .env files** to the repository
- Use `.env.example` as a template for required environment variables
- Generate strong, unique secrets for all security-related environment variables
- Rotate secrets periodically, especially in production environments

## Authentication & Authorization

- Always use server-side authentication checks
- Implement role-based access control consistently across all API endpoints
- Never trust client-side role information
- Use the `withApiHandler` utility for all API routes to ensure consistent auth checks
- Session tokens should have appropriate expiration times
- Implement proper logout functionality that invalidates sessions

## API Security

- All state-changing operations (POST, PUT, DELETE) must use CSRF protection
- Validate all input data on the server side using the validation schemas
- Use parameterized queries for all database operations
- Sanitize all user-generated content before storing or displaying it
- Return appropriate error messages without leaking sensitive information
- Use the centralized error handling system

## Frontend Security

- Never store sensitive information in localStorage or sessionStorage
- Use the secure client-side fetcher for all API calls
- Implement proper form validation on the client side
- Sanitize any user-generated content before rendering it
- Use Content Security Policy headers to prevent XSS attacks

## Database Security

- Use parameterized queries for all database operations
- Never concatenate user input directly into SQL queries
- Implement proper database access controls
- Encrypt sensitive data at rest
- Regularly backup the database

## Password Security

- Always hash passwords using bcrypt with appropriate cost factor
- Implement secure password reset functionality
- Enforce strong password policies
- Never log or display passwords in plain text
- Implement rate limiting for login attempts

## Secure Coding Practices

- Keep dependencies up to date
- Run security audits regularly (`npm audit`)
- Follow the principle of least privilege
- Implement proper error handling
- Use TypeScript for type safety
- Document security-related code

## Reporting Security Issues

If you discover a security vulnerability in the application, please report it by sending an <NAME_EMAIL>. Do not disclose security vulnerabilities publicly until they have been addressed by the maintainers.

## Security Checklist for Code Reviews

Before merging any code, ensure that:

- [ ] Authentication and authorization checks are in place
- [ ] Input validation is implemented
- [ ] SQL queries are parameterized
- [ ] User-generated content is sanitized
- [ ] Error handling doesn't leak sensitive information
- [ ] CSRF protection is implemented for state-changing operations
- [ ] Sensitive data is not logged or exposed
- [ ] Security headers are properly configured

## Additional Resources

- [OWASP Top Ten](https://owasp.org/www-project-top-ten/)
- [OWASP Cheat Sheet Series](https://cheatsheetseries.owasp.org/)
- [Next.js Security Documentation](https://nextjs.org/docs/advanced-features/security-headers)
- [PostgreSQL Security Best Practices](https://www.postgresql.org/docs/current/security.html) 