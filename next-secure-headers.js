/**
 * Next.js Security Configuration
 * 
 * This file configures security-related settings for Next.js
 */

// Get the list of trusted hosts from the environment or define defaults
function getTrustedHosts() {
  // Get environment variable
  const hosts = process.env.NEXTAUTH_URL 
    ? [new URL(process.env.NEXTAUTH_URL).host] 
    : [];
  
  // Always include these hostnames
  return [
    'localhost:3000',
    'dnsbvakarai.lt',
    'www.dnsbvakarai.lt',
    ...hosts
  ];
}

module.exports = {
  // Configure trusted hosts for authentication
  headers: async () => {
    return [
      {
        // Apply these headers to all routes
        source: '/:path*',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload'
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          }
        ]
      }
    ];
  },
  
  // Environment variables with special handling
  env: {
    AUTH_TRUSTED_HOSTS: getTrustedHosts().join(',')
  }
};