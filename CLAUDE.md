# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

DNSB Vakarai is a modern communication platform for a housing community, built with Next.js 15, React 19, PostgreSQL, and Tailwind CSS. The application enables administrators and residents to communicate effectively, share important information, and manage building documents.

The system follows a hierarchical structure:
- Streets contain houses
- Houses contain flats  
- Users are associated with flats

## Current Architecture

### Technology Stack
- **Frontend**: Next.js 15.2.4 (App Router), React 19.0.0
- **Database**: Supabase PostgreSQL with Row Level Security (RLS)
- **State Management**: TanStack Query 5.x for server state
- **Authentication**: Supabase Auth with custom username/password wrapper
- **Email Queue**: PGMQ (PostgreSQL Message Queue)
- **UI Components**: shadcn/ui with Radix UI primitives
- **Styling**: Tailwind CSS 3.4
- **Forms**: React Hook Form with Zod validation
- **Security**: bcryptjs (password hashing), isomorphic-dompurify (HTML sanitization)
- **Deployment**: Docker Compose, Coolify, Nixpacks (in progress)
- **Error Tracking**: Sentry
- **Analytics**: PostHog
- **Logging**: Pino logger

### Key Features
- Username-based authentication (users log in with username or email)
- Role-based access control (developer, super_admin, editor, user)
- Hierarchical data structure (Streets → Houses → Flats → Users)
- Email queue with templates and user preferences (PGMQ)
- Announcements with audience targeting
- Polls with real-time updates
- Emergency contacts system
- Magic link authentication
- GDPR consent tracking

## Authentication Implementation

### Username Login Strategy with Supabase

Since Supabase Auth is email-based, we implement a custom solution:

```typescript
// Custom username login flow
async function signInWithUsername(username: string, password: string) {
  // 1. Look up email by username
  const { data: user } = await supabase
    .from('users')
    .select('email')
    .eq('username', username)
    .single();

  if (!user) throw new Error('User not found');

  // 2. Sign in with Supabase Auth using email
  const { data, error } = await supabase.auth.signInWithPassword({
    email: user.email,
    password
  });

  return { data, error };
}
```

### Migration Status

#### ✅ Completed
- Database migration to Supabase (657 users, all tables)
- Authentication system (Supabase Auth with username wrapper)
- Email queue migration (from BullMQ/Redis to PGMQ)
- Login form and API routes
- Middleware and session management
- TanStack Query integration
- Row Level Security (RLS) policies
- Real-time features implementation

## Development Commands

### Core Commands

```bash
# Start development server
npm run dev

# Safe development mode (cross-env)
npm run dev:safe

# Build the project
npm run build

# Start production server
npm run start

# Lint the code (ALWAYS run before committing)
npm run lint

# Run all tests (ALWAYS run before committing)
npm run test

# Watch tests during development
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test suites
npm run test:email-queue    # Email queue tests
npm run test:email-api      # Email API tests
```

### Email Queue Commands

```bash
# Process email queue (manually with PGMQ)
npm run email:process

# Test PGMQ email functionality
npm run email:test-pgmq

# Test PGMQ directly
npm run email:test-pgmq-direct
```

### Utility Commands

```bash
# Fix styles issues
npm run fix-styles

# Initialize SMTP reply-to settings
npm run init:smtp-reply

# Verify deployment
npm run deploy:verify

# Test Supabase connection
npm run supabase:test
```

## High-Level Architecture

### Authentication Flow
1. **Username Login**: Users enter username (e.g., "31-7") or email
2. **Email Lookup**: System finds associated email in users table
3. **Supabase Auth**: Authenticates using email + password with Supabase
4. **Session Management**: Creates session using Supabase SSR cookies
5. **Middleware Protection**: Routes protected via middleware.ts

### Data Flow Architecture
```
Client (React 19)
    ↓
API Routes (Next.js 15 App Router)
    ↓
Supabase Client (with RLS)
    ↓
PostgreSQL Database
```

### Email Queue Architecture
```
API Endpoint → PGMQ Queue → PostgreSQL → Email Worker → SMTP
                    ↓
              Email Queue DB
```

### Component Architecture
- **Server Components**: Default for all pages and layouts (no "use client" directive)
- **Client Components**: Used for interactivity (forms, modals, dropdowns, real-time updates)
- **Server Actions**: Form submissions using React 19 patterns with `useActionState`
- **API Routes**: RESTful endpoints for complex operations and third-party integrations

## Key Patterns and Conventions

### API Route Pattern
```typescript
// app/api/[resource]/route.ts
export async function GET(request: Request) {
  // Auth check
  const session = await getServerSession();
  if (!session) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  
  // CSRF verification for state-changing operations
  if (request.method !== 'GET') {
    const csrfToken = request.headers.get('X-CSRF-Token');
    if (!validateCSRFToken(csrfToken)) {
      return NextResponse.json({ error: 'Invalid CSRF token' }, { status: 403 });
    }
  }
  
  // Business logic
  const data = await fetchData();
  
  // Disable caching for API routes
  return NextResponse.json(data, {
    headers: {
      'Cache-Control': 'no-store'
    }
  });
}
```

### Supabase Query Pattern
```typescript
// With error handling
const { data, error } = await supabase
  .from('table_name')
  .select('*')
  .eq('column', value);

if (error) {
  console.error('Error:', error);
  return null;
}

return data;
```

### Form Handling Pattern (React 19)
```typescript
// Server Action
'use server';
export async function submitForm(prevState: any, formData: FormData) {
  // Validate and process
  return { success: true };
}

// Client Component
const [state, formAction] = useActionState(submitForm, initialState);
```

### CSRF Protection Pattern
```typescript
// Get CSRF token
const csrfResponse = await fetch('/api/csrf');
const { token } = await csrfResponse.json();

// Include in requests
const response = await fetch('/api/resource', {
  method: 'POST',
  headers: {
    'X-CSRF-Token': token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(data)
});
```

## Testing Strategy

### No Mocks Philosophy
This project follows a **no mocks** testing approach:
- **Integration tests** against real Supabase test instances
- **Unit tests** for pure functions without external dependencies
- **E2E tests** using Playwright for full user journeys
- **Validation tests** for business logic and data processing
- **Tests skip gracefully** when test environment is not available

### Test Structure
```
__tests__/
├── setup.ts              # Test environment configuration
├── integration/           # Integration tests
│   ├── utils.test.ts     # Pure function tests
│   └── pgmq.test.ts      # PGMQ integration tests
└── README.md             # Testing documentation
```

### Running Tests
```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run integration tests only
npm run test:integration

# Run specific test file
npm run test:utils

# Run tests with coverage
npm run test:coverage
```

### Testing Approach
- **Pure Functions**: Test business logic, calculations, validations directly
- **Integration**: Test against real services when test environment exists
- **Validation**: Test data formatting, parsing, sanitization
- **E2E**: Use Playwright for complete user journey testing

Example test patterns:
```typescript
// Skip when environment not available
if (!process.env.SUPABASE_URL) {
  return describe.skip('Supabase integration tests', () => {});
}

// Test pure functions without mocks
describe('formatUsername', () => {
  it('formats house-flat correctly', () => {
    expect(formatUsername(31, 7)).toBe('31-7');
  });
});
```

## Environment Variables

### Current Setup (Development)
```bash
# Application
NODE_ENV=development
NEXT_TELEMETRY_DISABLED=1

# Supabase
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Email (SMTP settings stored in database)
# Configure via Admin → Settings → SMTP Settings

# Security
CSRF_SECRET=your-csrf-secret
CRON_API_KEY=your-cron-api-key

# Analytics (optional)
NEXT_PUBLIC_POSTHOG_KEY=your-posthog-key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
LOG_LEVEL=info

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_PASSWORD_RESET=true

# Development
EMAIL_DEV_MODE=true
EMAIL_DEV_RECIPIENT=<EMAIL>
```

### Deployment Options

The project supports multiple deployment strategies:

1. **Docker Compose** (Primary method)
   - Standalone Next.js output
   - Health check endpoint at `/api/health`
   - Persistent volumes for logs and uploads

2. **Coolify** (Self-hosted PaaS)
   ```bash
   # Verify development environment setup
   npm run dev:verify

   # Generate environment variables for deployment
   npm run deploy:env

   # Verify deployment after setup (requires running application)
   npm run deploy:verify
   ```

3. **Nixpacks** (Migration in progress)
   - Simplified deployment with automatic detection
   - No Dockerfile required

4. **Dokploy** (Alternative self-hosted PaaS)

See deployment documentation in `docs/` for detailed instructions.

## Security Considerations

### Current Implementation
- CSRF protection on all state-changing operations
- Role-based access control at API level
- SQL injection prevention via parameterized queries
- Secure session management with Supabase Auth
- Content Security Policy (CSP) headers
- Magic link authentication with expiring tokens
- Account disabling with reason tracking

### Authentication & Authorization
- Username/email login with Supabase Auth
- Four user roles: developer, super_admin, editor, user
- Role checks in API routes and middleware
- Session validation on every request
- Magic link tokens expire after 15 minutes

## Common Tasks

### Adding a New Feature
1. Plan database schema changes
2. Create/update Supabase queries or API routes
3. Build UI components (prefer server components)
4. Add proper validation (Zod schemas)
5. Implement comprehensive tests
6. Update relevant documentation

### Debugging Authentication
- Check Supabase auth logs in dashboard
- Verify username exists in users table
- Check Supabase session cookies (sb-* prefix)
- Verify email-username mapping
- Auth accounts created on first successful login
- Check for disabled accounts with reason

### Working with Email Queue
- Monitor queue in admin dashboard
- Check logs for failures
- Test with development email modes
- Development mode only sends to users with `developer` role

### Database Migrations
1. Create migration file in `/migrations`
2. Run migration using Supabase CLI
3. Update TypeScript types if needed
4. Test thoroughly in development
5. Verify with `npm run supabase:test`

### Running Tests in Development
```bash
# Quick iteration on a single test file
npm run test:watch -- polls/polls.test.ts

# Check coverage for a specific feature
npm run test:coverage -- --testPathPattern=email

# Debug failing tests with verbose output
npm run test -- --verbose --no-coverage
```

## Important Notes

1. **Username logins are critical** - Never break this functionality. Format: "house-flat" (e.g., "31-7")
2. **Always run lint and tests** before committing: `npm run lint && npm run test`
3. **User-facing UI in Lithuanian** - Keep all user interfaces in Lithuanian
4. **Code and comments in English** - All code, comments, and documentation in English
5. **Preserve hierarchical structure** - Streets → Houses → Flats → Users relationship is fundamental
6. **Test email sending carefully** - Use development mode to prevent accidental emails
7. **Handle errors gracefully** - Always provide user-friendly error messages in Lithuanian
8. **CSRF tokens required** - All POST/PUT/DELETE requests must include CSRF token
9. **Check user roles** - Always verify user permissions in API routes
10. **Mock data for testing** - Use `npm run db:mock-messages` to generate test data
11. **TypeScript relaxed mode** - Project uses `strict: false` for gradual migration
12. **Health monitoring** - Always check `/api/health` endpoint after deployment

## MCP (Model Context Protocol) Integration

The project includes MCP configuration for AI-assisted development:
- Configuration template: `.mcp.json.template`
- Supports filesystem operations, Supabase management, and more
- See `docs/MCP_SETUP.md` for setup instructions
- Use supabase MCP for direct access to supabase database.
- Use playwright MCP for tests.
- Use Context7 MCP to check latest documentation for specific framework we are working on.

## File Organization

### Directory Structure
```
app/                  # Next.js App Router
├── actions/         # Server Actions (React 19)
├── api/            # API routes
├── dashboard/      # Protected admin/user areas
├── auth/          # Authentication pages
└── (pages)/       # Public-facing pages

components/
├── ui/            # Reusable UI components (shadcn/ui)
├── dashboard/     # Dashboard-specific components
└── [feature]/     # Feature-specific components

lib/
├── supabase/      # Database client and auth
├── hooks/         # Custom React hooks
├── utils/         # Utility functions
├── tanstack/      # Query configuration
└── validators/    # Zod schemas

public/            # Static assets
docs/             # Documentation
__tests__/        # Test files
migrations/       # Database migrations
```

## Documentation Resources

- Testing guide: `docs/JEST_TESTING_GUIDE.md`
- Language guidelines: `docs/LANGUAGE_GUIDELINES.md`
- MCP setup: `docs/MCP_SETUP.md`
- Security documentation: `SECURITY.md`
- Deployment guides in `docs/` directory

## Cron Jobs

The application uses critical background processes:
1. **Email Queue Processing**: PGMQ worker processes queued emails
2. **Announcement Status Update**: Automated status updates for announcements

These are handled by the PGMQ worker and Supabase functions.