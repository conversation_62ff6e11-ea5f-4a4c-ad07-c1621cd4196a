# DNSB Vakarai - Production Environment Variables for Coolify
# Copy these to your Coolify environment variables configuration

# ===== CORE APPLICATION =====
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
PORT=3000
HOSTNAME=0.0.0.0
RUNTIME_ENV=true

# ===== SUPABASE CONFIGURATION =====
# Use your actual values from .env.local
NEXT_PUBLIC_SUPABASE_URL=http://supabasekong-y40ggo44c8gg4cw4ssws8c4c.***********.sslip.io
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTc0ODY5NDMwMCwiZXhwIjo0OTA0MzY3OTAwLCJyb2xlIjoiYW5vbiJ9.AWA6gE60PWFpqUMFXWHkSAIHM4UPmHKBCqHCasTvs40
SUPABASE_SERVICE_ROLE_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTc0ODY5NDMwMCwiZXhwIjo0OTA0MzY3OTAwLCJyb2xlIjoic2VydmljZV9yb2xlIn0.3JShkzlla8zbM3gJJZbwDggp5rO-z0nqLuygxR-ezrY

# ===== SECURITY =====
# Generate new values for production
CSRF_SECRET=1538f0bf594db566439efd6017505fb2a158136a8bd6d8f3ee122ce9fe11f061
CRON_API_KEY=f4faceac789d8de6e601909d233b53370e344fe9ae6f115b

# ===== ANALYTICS & LOGGING =====
NEXT_PUBLIC_POSTHOG_KEY=phc_4Qg337unB09c2duGIgep6jmn6wcKKKvMULIllrvi44f
NEXT_PUBLIC_POSTHOG_HOST=https://eu.i.posthog.com
LOG_LEVEL=info

# ===== FEATURE FLAGS =====
ENABLE_REGISTRATION=true
ENABLE_PASSWORD_RESET=true

# ===== EMAIL CONFIGURATION =====
# Set to 'live' for production
EMAIL_DEV_MODE=live
# Optional: Set a fallback email for development testing
EMAIL_DEV_RECIPIENT=<EMAIL>

# ===== OPTIONAL EXTERNAL SERVICES =====
# Uncomment and configure if needed
# CLOUDINARY_URL=cloudinary://api_key:api_secret@cloud_name
# GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# ===== NOTES =====
# 1. DO NOT include DATABASE_URL - Supabase client handles connections
# 2. SMTP settings are configured via Admin → Settings → SMTP Settings
# 3. All NEXT_PUBLIC_ variables are exposed to the browser
# 4. Keep secrets secure and rotate regularly
