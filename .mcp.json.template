{"mcpServers": {"supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "YOUR_SUPABASE_ACCESS_TOKEN"]}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "env": {"DATABASE_URL": "postgres://dnsb_user:dnsb_password@localhost:5432/dnsb_db"}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/your/project"]}, "git": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-git"], "env": {"GIT_REPO_PATH": "/path/to/your/project"}}}}