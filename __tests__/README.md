# Testing Strategy

This project uses an integration testing approach that avoids mocks in favor of testing real functionality.

## Philosophy

**No Mocks Policy**: We avoid mocking dependencies because:
- Mocks can hide real integration issues
- They require maintenance when APIs change
- They don't test actual behavior
- They can give false confidence

Instead, we focus on:
- **Integration tests** against real services (test Supabase instances)
- **Unit tests** for pure functions without external dependencies
- **E2E tests** using <PERSON>wright for full user journeys

## Test Structure

```
__tests__/
├── setup.ts              # Test environment configuration
├── integration/           # Integration tests
│   ├── utils.test.ts     # Pure function tests
│   └── pgmq.test.ts      # PGMQ integration tests
└── README.md             # This file
```

## Running Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run integration tests only
npm run test:integration

# Run specific test file
npm run test:utils

# Run with coverage
npm run test:coverage
```

## Test Environment Setup

### For Local Development

1. **Pure Function Tests**: No setup required - these test business logic without external dependencies.

2. **Integration Tests**: Require test services:
   ```bash
   # Set up test Supabase instance
   export TEST_SUPABASE_URL="https://your-test-project.supabase.co"
   export TEST_SUPABASE_ANON_KEY="your-test-anon-key"
   export TEST_SUPABASE_SERVICE_KEY="your-test-service-key"
   ```

3. **Email Tests**: Use MailHog or similar for SMTP testing:
   ```bash
   # Start MailHog (optional)
   docker run -d -p 1025:1025 -p 8025:8025 mailhog/mailhog
   ```

### For CI/CD

Tests are designed to work in CI environments:
- Pure function tests always pass
- Integration tests are skipped if no test environment is configured
- Use GitHub Actions secrets for test credentials

## Writing Tests

### Pure Function Tests

Test business logic without external dependencies:

```typescript
describe('Business Logic', () => {
  it('should calculate percentages correctly', () => {
    const votes = [{ count: 5 }, { count: 3 }, { count: 2 }];
    const total = votes.reduce((sum, vote) => sum + vote.count, 0);
    
    const results = votes.map(vote => ({
      ...vote,
      percentage: Math.round((vote.count / total) * 100)
    }));
    
    expect(results[0].percentage).toBe(50);
  });
});
```

### Integration Tests

Test against real services when available:

```typescript
describe('Supabase Integration', () => {
  const skipTests = !process.env.TEST_SUPABASE_URL;
  
  it.skipIf(skipTests)('should connect to database', async () => {
    const { createClient } = await import('@/lib/supabase/client');
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    expect(error).toBeNull();
  });
});
```

### Validation Tests

Test data validation and formatting:

```typescript
describe('Data Validation', () => {
  it('should validate email format', () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    expect(emailRegex.test('<EMAIL>')).toBe(true);
    expect(emailRegex.test('invalid-email')).toBe(false);
  });
});
```

## Test Categories

### ✅ What We Test

1. **Pure Functions**: Business logic, calculations, validations
2. **Data Transformations**: Formatting, parsing, sanitization
3. **Configuration**: Environment setup, feature flags
4. **Integration Points**: Real API calls when test environment exists

### ❌ What We Don't Test

1. **External Service Internals**: We trust Supabase, SMTP providers work
2. **Framework Behavior**: We trust Next.js, React work correctly
3. **Third-party Libraries**: We trust dependencies work as documented

## Benefits

- **Reliable**: Tests fail when real functionality breaks
- **Maintainable**: No mock maintenance when APIs change
- **Fast**: Pure function tests run instantly
- **Realistic**: Integration tests catch real issues
- **Simple**: Less test code, easier to understand

## Migration from Mocks

If you find old mock-based tests:
1. Identify what business logic is being tested
2. Extract pure functions and test them directly
3. For integration points, test against real services or skip
4. Remove mock setup and focus on actual behavior

## Playwright E2E Tests

For full user journey testing, use Playwright:

```bash
# Install Playwright
npx playwright install

# Run E2E tests
npx playwright test
```

E2E tests complement integration tests by testing the full user experience.
