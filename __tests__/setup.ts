/**
 * Test setup file for integration testing
 *
 * This setup focuses on integration testing with real Supabase instances
 * rather than mocking dependencies.
 */

// Set test environment
process.env.NODE_ENV = 'test';

// Supabase test environment variables
// These should point to a dedicated test Supabase project
process.env.NEXT_PUBLIC_SUPABASE_URL = process.env.TEST_SUPABASE_URL || 'https://test-project.supabase.co';
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = process.env.TEST_SUPABASE_ANON_KEY || 'test-anon-key';
process.env.SUPABASE_SERVICE_ROLE_KEY = process.env.TEST_SUPABASE_SERVICE_KEY || 'test-service-key';

// Email testing (use test SMTP or disable)
process.env.EMAIL_DEV_MODE = 'true';
process.env.SMTP_HOST = 'localhost';
process.env.SMTP_PORT = '1025'; // MailHog or similar test SMTP
process.env.SMTP_USER = 'test';
process.env.SMTP_PASSWORD = 'test';
process.env.SMTP_FROM = '<EMAIL>';

// Analytics (disabled in tests)
process.env.NEXT_PUBLIC_POSTHOG_KEY = '';
process.env.NEXT_PUBLIC_SENTRY_DSN = '';

// Security
process.env.CSRF_SECRET = 'test-csrf-secret';

// Suppress console noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

console.error = (...args) => {
  const message = args[0] || '';
  if (typeof message === 'string') {
    // Filter out expected test warnings
    if (message.includes('environment variable is not set') ||
        message.includes('Warning: ReactDOM.render is no longer supported') ||
        message.includes('Warning: React.createFactory() is deprecated')) {
      return;
    }
  }
  originalConsoleError(...args);
};

console.warn = (...args) => {
  const message = args[0] || '';
  if (typeof message === 'string') {
    // Filter out expected test warnings
    if (message.includes('componentWillReceiveProps') ||
        message.includes('componentWillUpdate')) {
      return;
    }
  }
  originalConsoleWarn(...args);
};

// Global test timeout
jest.setTimeout(30000);

// Clean up after all tests
afterAll(() => {
  // Restore console methods
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;

  // Clean up timers
  jest.useRealTimers();
});
