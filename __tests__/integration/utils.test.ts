/**
 * Integration tests for utility functions
 * 
 * These tests focus on testing pure functions and utilities
 * without external dependencies or mocks.
 */

import { describe, it, expect } from '@jest/globals';

describe('Utility Functions', () => {
  
  describe('Data Validation', () => {
    it('should validate email addresses correctly', () => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      
      // Valid emails
      expect(emailRegex.test('<EMAIL>')).toBe(true);
      expect(emailRegex.test('<EMAIL>')).toBe(true);
      expect(emailRegex.test('<EMAIL>')).toBe(true);
      
      // Invalid emails
      expect(emailRegex.test('invalid-email')).toBe(false);
      expect(emailRegex.test('@domain.com')).toBe(false);
      expect(emailRegex.test('user@')).toBe(false);
      expect(emailRegex.test('user@domain')).toBe(false);
    });

    it('should validate username format', () => {
      // Username should be in format "house-flat" (e.g., "31-7")
      const usernameRegex = /^\d+-\d+$/;
      
      // Valid usernames
      expect(usernameRegex.test('31-7')).toBe(true);
      expect(usernameRegex.test('1-1')).toBe(true);
      expect(usernameRegex.test('123-456')).toBe(true);
      
      // Invalid usernames
      expect(usernameRegex.test('31')).toBe(false);
      expect(usernameRegex.test('31-')).toBe(false);
      expect(usernameRegex.test('-7')).toBe(false);
      expect(usernameRegex.test('house-flat')).toBe(false);
      expect(usernameRegex.test('31_7')).toBe(false);
    });

    it('should validate phone numbers', () => {
      // Lithuanian phone number format
      const phoneRegex = /^(\+370|8)[0-9]{8}$/;
      
      // Valid phone numbers
      expect(phoneRegex.test('+37061234567')).toBe(true);
      expect(phoneRegex.test('861234567')).toBe(true);
      
      // Invalid phone numbers
      expect(phoneRegex.test('61234567')).toBe(false);
      expect(phoneRegex.test('+370612345678')).toBe(false);
      expect(phoneRegex.test('370612345678')).toBe(false);
    });
  });

  describe('Data Formatting', () => {
    it('should format dates correctly', () => {
      const date = new Date('2024-01-15T10:30:00Z');
      
      // Lithuanian date format
      const lithuanianDate = date.toLocaleDateString('lt-LT');
      expect(lithuanianDate).toMatch(/\d{4}-\d{2}-\d{2}/);
      
      // ISO date format
      const isoDate = date.toISOString().split('T')[0];
      expect(isoDate).toBe('2024-01-15');
    });

    it('should format currency correctly', () => {
      const amount = 123.45;
      
      // Euro formatting
      const formatted = new Intl.NumberFormat('lt-LT', {
        style: 'currency',
        currency: 'EUR'
      }).format(amount);
      
      expect(formatted).toContain('123');
      expect(formatted).toContain('45');
      expect(formatted).toContain('€');
    });

    it('should format percentages correctly', () => {
      const percentage = 0.75;
      
      const formatted = new Intl.NumberFormat('lt-LT', {
        style: 'percent',
        minimumFractionDigits: 0,
        maximumFractionDigits: 1
      }).format(percentage);
      
      expect(formatted).toContain('75');
      expect(formatted).toContain('%');
    });
  });

  describe('Business Logic Calculations', () => {
    it('should calculate poll percentages correctly', () => {
      const votes = [
        { option: 'Option 1', count: 5 },
        { option: 'Option 2', count: 3 },
        { option: 'Option 3', count: 2 }
      ];
      
      const totalVotes = votes.reduce((sum, vote) => sum + vote.count, 0);
      
      const results = votes.map(vote => ({
        ...vote,
        percentage: Math.round((vote.count / totalVotes) * 100)
      }));
      
      expect(results[0].percentage).toBe(50); // 5/10 = 50%
      expect(results[1].percentage).toBe(30); // 3/10 = 30%
      expect(results[2].percentage).toBe(20); // 2/10 = 20%
      
      // Total should be 100%
      const totalPercentage = results.reduce((sum, result) => sum + result.percentage, 0);
      expect(totalPercentage).toBe(100);
    });

    it('should handle zero votes gracefully', () => {
      const votes = [
        { option: 'Option 1', count: 0 },
        { option: 'Option 2', count: 0 }
      ];
      
      const totalVotes = votes.reduce((sum, vote) => sum + vote.count, 0);
      
      const results = votes.map(vote => ({
        ...vote,
        percentage: totalVotes > 0 ? Math.round((vote.count / totalVotes) * 100) : 0
      }));
      
      expect(results[0].percentage).toBe(0);
      expect(results[1].percentage).toBe(0);
    });

    it('should calculate participation rates correctly', () => {
      const totalUsers = 100;
      const votedUsers = 25;
      const participationRate = Math.round((votedUsers / totalUsers) * 100);
      
      expect(participationRate).toBe(25);
      expect(totalUsers - votedUsers).toBe(75);
    });
  });

  describe('Permission Logic', () => {
    it('should check admin roles correctly', () => {
      const adminRoles = ['developer', 'super_admin', 'editor'];
      
      const isAdmin = (role: string) => adminRoles.includes(role);
      
      expect(isAdmin('developer')).toBe(true);
      expect(isAdmin('super_admin')).toBe(true);
      expect(isAdmin('editor')).toBe(true);
      expect(isAdmin('user')).toBe(false);
      expect(isAdmin('guest')).toBe(false);
    });

    it('should check permissions by role', () => {
      const permissions = {
        developer: ['all'],
        super_admin: ['create', 'read', 'update', 'delete'],
        editor: ['create', 'read', 'update'],
        user: ['read']
      };
      
      const hasPermission = (role: string, action: string) => {
        const rolePermissions = permissions[role as keyof typeof permissions] || [];
        return rolePermissions.includes('all') || rolePermissions.includes(action);
      };
      
      // Developer has all permissions
      expect(hasPermission('developer', 'delete')).toBe(true);
      expect(hasPermission('developer', 'create')).toBe(true);
      
      // Super admin has CRUD
      expect(hasPermission('super_admin', 'delete')).toBe(true);
      expect(hasPermission('super_admin', 'create')).toBe(true);
      
      // Editor cannot delete
      expect(hasPermission('editor', 'create')).toBe(true);
      expect(hasPermission('editor', 'delete')).toBe(false);
      
      // User can only read
      expect(hasPermission('user', 'read')).toBe(true);
      expect(hasPermission('user', 'create')).toBe(false);
    });
  });

  describe('Text Processing', () => {
    it('should sanitize HTML content', () => {
      const dangerousHtml = '<script>alert("xss")</script><p>Safe content</p>';
      
      // Simple HTML sanitization (remove script tags)
      const sanitized = dangerousHtml.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).toContain('<p>Safe content</p>');
    });

    it('should truncate text correctly', () => {
      const longText = 'This is a very long text that should be truncated at some point';
      const maxLength = 20;
      
      const truncated = longText.length > maxLength 
        ? longText.substring(0, maxLength) + '...'
        : longText;
      
      expect(truncated.length).toBeLessThanOrEqual(maxLength + 3); // +3 for "..."
      expect(truncated).toContain('...');
    });

    it('should extract mentions from text', () => {
      const text = 'Hello @user1 and @user2, please check this @admin';
      const mentionRegex = /@(\w+)/g;
      
      const mentions = [];
      let match;
      while ((match = mentionRegex.exec(text)) !== null) {
        mentions.push(match[1]);
      }
      
      expect(mentions).toEqual(['user1', 'user2', 'admin']);
    });
  });
});
